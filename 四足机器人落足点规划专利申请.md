# 一种基于多约束优化的四足机器人落足点规划方法

## 摘要

本发明公开了一种基于多约束优化的四足机器人落足点规划方法，涉及机器人控制技术领域。该方法通过构建包含轨迹跟踪代价、运动学约束惩罚、地形适应代价、稳定性代价和摩擦约束代价的多目标优化函数，结合动力学约束、地形约束、关节限制约束、摩擦锥约束等多种约束条件，采用序列二次规划算法实时求解最优落足点位置。该方法还引入了基于零力矩点理论的启发式落足点调整机制，能够根据当前基体速度和期望速度的偏差进行反应性调整。通过多源传感器信息融合获取地形参数和机器人状态信息，实现了四足机器人在复杂地形环境下的稳定运动控制。本发明提高了四足机器人的地形适应能力和运动稳定性，具有重要的工程应用价值。

关键词：四足机器人；落足点规划；多约束优化；模型预测控制；地形感知

## 背景技术

四足机器人作为一种重要的移动机器人平台，在野外勘探、救援任务、军事应用等领域具有广阔的应用前景。相比于轮式和履带式机器人，四足机器人具有更强的地形适应能力，能够在崎岖不平的地面上稳定行走。然而，四足机器人的运动控制，特别是在复杂地形环境下的落足点规划，仍然是一个具有挑战性的技术难题。

传统的四足机器人落足点规划方法主要包括：

1. **基于预设步态的方法**：通过预先定义的步态模式确定落足点位置，如对角小跑步态、踱步步态等。这类方法实现简单，但缺乏对地形的适应性，在复杂地形下容易失稳。

2. **基于几何搜索的方法**：在机器人周围搜索可行的落足点位置，通常考虑地形坡度、障碍物等因素。然而，这类方法往往忽略了机器人的动力学特性和稳定性要求。

3. **基于强化学习的方法**：通过大量的仿真训练学习最优的落足点策略。虽然这类方法在某些场景下表现良好，但缺乏理论保证，且对训练数据的依赖性强。

现有技术存在以下不足：

（1）缺乏对多种约束条件的统一考虑，难以在运动学限制、地形约束、稳定性要求等多个目标之间取得平衡；

（2）对地形信息的利用不充分，未能有效融合视觉、激光雷达等多源感知信息；

（3）缺乏实时性保证，难以满足动态环境下的快速响应需求；

（4）稳定性分析不完善，特别是在不平地形上的零力矩点控制。

因此，亟需开发一种综合考虑多种约束条件、具备实时性能、能够适应复杂地形的四足机器人落足点规划方法。

## 发明内容

### 技术问题

本发明要解决的技术问题是：现有四足机器人落足点规划方法在复杂地形环境下稳定性差、实时性不足、多约束优化能力有限的问题。

### 技术方案

为解决上述技术问题，本发明提供一种基于多约束优化的四足机器人落足点规划方法，包括以下步骤：

**步骤S1：多源信息获取**

通过激光雷达、深度相机、IMU等传感器获取机器人周围环境信息和自身状态信息，包括：
- 三维点云数据用于地形重建
- 机器人基体位姿和速度信息
- 关节角度和角速度信息
- 足端接触力信息

**步骤S2：地形表示与约束生成**

对获取的点云数据进行处理，生成地形约束参数：

（1）点云预处理：去噪、降采样、坐标变换
（2）平面分割：提取地形中的平面区域
（3）凸区域分解：将复杂地形分解为多个凸多面体
（4）约束参数计算：生成线性不等式约束系数$A_{terrain}$和$b_{terrain}$

**步骤S3：多目标优化函数构建**

构建包含多个代价项的目标函数：

$$J_{total} = J_{tracking} + J_{kinematic} + J_{terrain} + J_{stability} + J_{friction} + J_{limits}$$

其中各代价项定义如下：

（1）轨迹跟踪代价：

$$J_{tracking} = \sum_{t} \left[(x(t) - x_{ref}(t))^T Q (x(t) - x_{ref}(t)) + (u(t) - u_{ref}(t))^T R (u(t) - u_{ref}(t))\right]$$

其中$Q$和$R$分别为状态权重矩阵和控制输入权重矩阵。

（2）运动学约束惩罚：

$$J_{kinematic} = \sum_{leg} \left[w_{instep} \cdot d_{instep}^2 + w_{extension} \cdot d_{extension}^2\right]$$

其中：
$$d_{instep} = \max(0, r_{hip} - \|p_{foot,xy} - p_{hip,xy}\|)$$
$$d_{extension} = \max(0, \|p_{foot} - p_{hip}\| - L_{leg,max})$$

（3）地形适应代价：

$$J_{terrain} = \sum_{i} \text{penalty}(A_i \cdot p_{foot} + b_i) + \sum_{leg} \text{penalty}(\text{SDF}(p_{foot,leg}) - c_{clear})$$

（4）稳定性代价：

$$J_{stability} = \|p_{ZMP} - p_{support}\|^2 + w_L \|L - L_{ref}\|^2 + w_H \|H - H_{ref}\|^2$$

其中零力矩点位置为：
$$p_{ZMP} = \frac{\sum_{contact} f_{z,i} \cdot p_i}{\sum_{contact} f_{z,i}}$$

（5）摩擦约束代价：

$$J_{friction} = \sum_{leg} \text{penalty}(\|f_{tangential,leg}\| - \mu \cdot f_{normal,leg})$$

**步骤S4：约束条件定义**

建立完整的约束条件集合：

（1）动力学等式约束：

$$M(q)\ddot{q} + C(q,\dot{q})\dot{q} + G(q) = S^T \tau + \sum_{contact} J_c^T f_c$$

（2）接触约束：

$$J_c(q) \cdot \dot{q} = 0$$

（3）地形不等式约束：

$$A_{terrain} \cdot p_{foot} + b_{terrain} \leq 0$$

（4）关节限制约束：

$$q_{min} \leq q \leq q_{max}, \quad \dot{q}_{min} \leq \dot{q} \leq \dot{q}_{max}, \quad \tau_{min} \leq \tau \leq \tau_{max}$$

（5）摩擦锥约束：

$$\|f_{tangential}\| \leq \mu \cdot f_{normal}, \quad f_{normal} \geq 0$$

（6）碰撞避免约束：

$$\text{SDF}(p_{foot}) \geq c_{min}$$

**步骤S5：ZMP启发式调整**

基于零力矩点理论进行反应性落足点调整：

$$p_{foot,adjusted} = p_{foot,nominal} + \Delta p_{reactive}$$

其中反应性偏移量为：

$$\Delta p_{reactive} = \sqrt{\frac{h_{pendulum}}{g}} \cdot (v_{base} - v_{desired})$$

式中$h_{pendulum}$为倒立摆等效高度，$g$为重力加速度，$v_{base}$为当前基体速度，$v_{desired}$为期望基体速度。

**步骤S6：优化求解**

采用序列二次规划（SQP）算法求解上述多约束优化问题：

$$\min_{x} \frac{1}{2}x^T H x + g^T x$$

$$\text{s.t.} \quad A_{eq} x = b_{eq}, \quad A_{ineq} x \leq b_{ineq}, \quad x_{min} \leq x \leq x_{max}$$

其中$x$为包含基体状态、关节状态、足端位置和接触力的优化变量向量。

**步骤S7：实时执行与反馈**

将求解得到的最优落足点位置发送给底层控制器执行，同时实时更新传感器信息和约束参数，形成闭环控制系统。

### 有益效果

本发明具有以下有益效果：

（1）**多约束统一优化**：将运动学、动力学、地形、稳定性等多种约束条件统一纳入优化框架，实现了全局最优的落足点规划。

（2）**实时性能优异**：采用高效的SQP算法和松弛障碍惩罚函数，能够在毫秒级时间内完成优化求解，满足实时控制需求。

（3）**地形适应性强**：通过多源传感器融合和凸区域分解技术，能够准确建模复杂地形约束，显著提高机器人的地形适应能力。

（4）**稳定性保证**：基于ZMP理论的启发式调整机制能够主动维持系统稳定性，有效防止机器人跌倒。

（5）**鲁棒性好**：松弛障碍惩罚函数的使用增强了算法对约束违反的容忍性，提高了系统的鲁棒性。

## 具体实施方式

下面结合附图和具体实施例对本发明进行详细说明。

### 实施例1：平地行走场景

在平地行走场景下，四足机器人Go2在水平地面上以1.0 m/s的速度前进。

**步骤1：传感器配置**
- IMU：提供基体姿态和角速度信息，采样频率1000 Hz
- 关节编码器：提供关节角度信息，采样频率1000 Hz
- 足端力传感器：提供接触力信息，采样频率1000 Hz

**步骤2：参数设置**
轨迹跟踪权重矩阵Q的对角元素设置为：
- 基体位置权重：$W_x = W_y = 1000.0, W_z = 1500.0$
- 基体姿态权重：$W_{roll} = 100.0, W_{pitch} = W_{yaw} = 300.0$
- 基体速度权重：$W_{v_x} = W_{v_y} = 15.0, W_{v_z} = 30.0$

运动学约束参数：
- 内步惩罚权重：$w_{instep} = 500.0$
- 过伸惩罚权重：$w_{extension} = 500.0$
- 最大腿长：$L_{leg,max} = 0.4$ m

**步骤3：优化求解**
目标函数简化为：

$$J = J_{tracking} + J_{kinematic} + J_{stability}$$

由于地面平坦，地形约束项$J_{terrain} = 0$。

求解得到的最优落足点在基体前方0.3 m、侧向±0.2 m位置，实现了稳定的对角小跑步态。

### 实施例2：崎岖地形场景

四足机器人在包含台阶、坑洞的崎岖地形上行走。

**步骤1：地形感知**
- 激光雷达：Livox Mid-360，扫描频率100 Hz
- 深度相机：RealSense D435i，30 Hz
- 感知范围：前方5 m，侧向±2 m

**步骤2：地形建模**
通过凸平面分解算法将地形分解为127个凸区域，每个区域的约束参数$A_i$和$b_i$实时更新。

高度为0.2 m的台阶产生的约束为：
$$A_{step} = \begin{bmatrix} 0 \\ 0 \\ 1 \end{bmatrix}, \quad b_{step} = -0.2$$

**步骤3：多约束优化**
完整目标函数：

$$J_{total} = J_{tracking} + J_{kinematic} + J_{terrain} + J_{stability} + J_{friction}$$

地形约束项采用松弛障碍惩罚函数：

$$\text{penalty}(h) = \begin{cases}
\mu (h + \delta)^2 & \text{if } h \leq -\delta \\
\mu (-\ln(-h) + 0.5\delta - h - \delta) & \text{if } -\delta < h < 0 \\
0 & \text{if } h \geq 0
\end{cases}$$

其中$\mu = 0.1$，$\delta = 0.005$。

**步骤4：ZMP调整**
当检测到基体速度偏差时，进行反应性调整：

$$\Delta p_{reactive} = \sqrt{\frac{0.3}{9.81}} \cdot (v_{measured} - v_{desired})$$

**步骤5：实验结果**
机器人成功通过高度0.2 m的台阶，最大基体姿态偏差小于5°，验证了方法的有效性。

### 实施例3：实时性能优化

为满足1000 Hz控制频率的要求，采用以下优化策略：

**1. 分层求解**
将优化问题分解为：
- 上层：足端位置规划（100 Hz）
- 下层：关节力矩计算（1000 Hz）

**2. 热启动**
使用前一时刻的最优解作为当前优化的初值，减少迭代次数。

**3. 并行计算**
在多核处理器上并行计算不同腿的约束条件。

**4. 约束预筛选**
仅考虑机器人可达范围内的地形约束，减少约束数量。

通过上述优化，单次优化求解时间控制在0.5 ms以内，满足实时控制要求。

### 实施例4：参数自适应调整

根据地形复杂度和运动速度动态调整权重参数：

**1. 地形复杂度评估**
定义地形复杂度指标：
$$C_{terrain} = \frac{1}{N} \sum_{i=1}^N \|\nabla h_i\|$$

其中$h_i$为第$i$个地形点的高度，$N$为采样点数量。

**2. 权重自适应**
$$w_{terrain} = w_{base} \cdot (1 + \alpha \cdot C_{terrain})$$
$$w_{stability} = w_{base} \cdot (1 + \beta \cdot v_{speed})$$

其中$\alpha = 2.0$，$\beta = 1.5$为调节参数。

**3. 实验验证**
在不同地形上的实验表明，自适应参数调整能够提高30%的通过成功率。

## 权利要求书

**1.** 一种基于多约束优化的四足机器人落足点规划方法，其特征在于包括以下步骤：

步骤S1：通过多源传感器获取机器人环境信息和状态信息；

步骤S2：对获取的点云数据进行地形建模，生成地形约束参数；

步骤S3：构建包含轨迹跟踪代价、运动学约束惩罚、地形适应代价、稳定性代价和摩擦约束代价的多目标优化函数；

步骤S4：建立包含动力学约束、地形约束、关节限制约束、摩擦锥约束的完整约束条件集合；

步骤S5：基于零力矩点理论进行反应性落足点调整；

步骤S6：采用序列二次规划算法求解多约束优化问题；

步骤S7：实时执行优化结果并更新系统状态。

**2.** 根据权利要求1所述的方法，其特征在于，所述步骤S3中的多目标优化函数为：

$$J_{total} = J_{tracking} + J_{kinematic} + J_{terrain} + J_{stability} + J_{friction} + J_{limits}$$

其中轨迹跟踪代价为：

$$J_{tracking} = \sum_{t} \left[(x(t) - x_{ref}(t))^T Q (x(t) - x_{ref}(t)) + (u(t) - u_{ref}(t))^T R (u(t) - u_{ref}(t))\right]$$

**3.** 根据权利要求1所述的方法，其特征在于，所述步骤S4中的动力学约束为：

$$M(q)\ddot{q} + C(q,\dot{q})\dot{q} + G(q) = S^T \tau + \sum_{contact} J_c^T f_c$$

地形约束为：

$$A_{terrain} \cdot p_{foot} + b_{terrain} \leq 0$$

**4.** 根据权利要求1所述的方法，其特征在于，所述步骤S5中的反应性调整公式为：

$$p_{foot,adjusted} = p_{foot,nominal} + \Delta p_{reactive}$$

其中：

$$\Delta p_{reactive} = \sqrt{\frac{h_{pendulum}}{g}} \cdot (v_{base} - v_{desired})$$

**5.** 根据权利要求1所述的方法，其特征在于，所述地形约束参数通过凸区域分解算法生成，将复杂地形分解为多个凸多面体区域。

**6.** 根据权利要求1所述的方法，其特征在于，采用松弛障碍惩罚函数处理不等式约束：

$$\text{penalty}(h) = \begin{cases}
\mu (h + \delta)^2 & \text{if } h \leq -\delta \\
\mu (-\ln(-h) + 0.5\delta - h - \delta) & \text{if } -\delta < h < 0 \\
0 & \text{if } h \geq 0
\end{cases}$$

**7.** 根据权利要求1所述的方法，其特征在于，所述多源传感器包括激光雷达、深度相机、IMU、关节编码器和足端力传感器。

**8.** 根据权利要求1所述的方法，其特征在于，采用分层求解策略，将足端位置规划和关节力矩计算分别在不同频率下执行。

**9.** 根据权利要求1所述的方法，其特征在于，所述运动学约束惩罚包括内步约束和腿部过伸约束：

$$J_{kinematic} = \sum_{leg} \left[w_{instep} \cdot d_{instep}^2 + w_{extension} \cdot d_{extension}^2\right]$$

**10.** 一种四足机器人控制系统，其特征在于采用权利要求1-9中任一项所述的落足点规划方法进行运动控制。