#!/usr/bin/env python3
"""
测试D435i ROS2检测节点
检查话题是否正常发布
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image, PointCloud2
from vision_msgs.msg import Detection2DArray
import time


class TestSubscriber(Node):
    """测试订阅者节点"""
    
    def __init__(self):
        super().__init__('test_subscriber')
        
        # 话题接收计数
        self.topic_counts = {
            'image_raw': 0,
            'image_detection': 0,
            'image_combined': 0,
            'depth_image': 0,
            'pointcloud_raw': 0,
            'pointcloud_segmented': 0,
            'detections': 0
        }
        
        # 创建订阅者
        self.create_subscription(Image, '/d435i_detection/image_raw', 
                               lambda msg: self.count_callback('image_raw'), 10)
        self.create_subscription(Image, '/d435i_detection/image_detection', 
                               lambda msg: self.count_callback('image_detection'), 10)
        self.create_subscription(Image, '/d435i_detection/image_combined', 
                               lambda msg: self.count_callback('image_combined'), 10)
        self.create_subscription(Image, '/d435i_detection/depth_image', 
                               lambda msg: self.count_callback('depth_image'), 10)
        self.create_subscription(PointCloud2, '/d435i_detection/pointcloud_raw', 
                               lambda msg: self.count_callback('pointcloud_raw'), 10)
        self.create_subscription(PointCloud2, '/d435i_detection/pointcloud_segmented', 
                               lambda msg: self.count_callback('pointcloud_segmented'), 10)
        self.create_subscription(Detection2DArray, '/d435i_detection/detections', 
                               self.detections_callback, 10)
        
        # 创建定时器打印统计信息
        self.timer = self.create_timer(5.0, self.print_stats)
        self.start_time = time.time()
        
        self.get_logger().info("测试订阅者已启动，监听D435i检测话题...")
    
    def count_callback(self, topic_name):
        """计数回调"""
        self.topic_counts[topic_name] += 1
    
    def detections_callback(self, msg):
        """检测结果回调"""
        self.topic_counts['detections'] += 1
        if len(msg.detections) > 0:
            self.get_logger().info(f"检测到 {len(msg.detections)} 个对象")
    
    def print_stats(self):
        """打印统计信息"""
        elapsed = time.time() - self.start_time
        self.get_logger().info(f"\n=== 运行时间: {elapsed:.1f}秒 ===")
        
        for topic, count in self.topic_counts.items():
            rate = count / elapsed if elapsed > 0 else 0
            self.get_logger().info(f"{topic}: {count} 消息 ({rate:.1f} Hz)")
        
        # 检查是否有话题没有数据
        missing_topics = [topic for topic, count in self.topic_counts.items() if count == 0]
        if missing_topics:
            self.get_logger().warn(f"以下话题没有收到数据: {missing_topics}")
        else:
            self.get_logger().info("所有话题都在正常发布数据！")


def main():
    """主函数"""
    rclpy.init()
    
    try:
        node = TestSubscriber()
        rclpy.spin(node)
    except KeyboardInterrupt:
        print("\n测试结束")
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
