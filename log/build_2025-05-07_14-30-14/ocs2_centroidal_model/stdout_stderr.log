[ 14%] Built target gtest
[ 28%] Built target gtest_main
[ 85%] Built target ocs2_centroidal_model
[100%] Built target ocs2_centroidal_model_test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/AccessHelperFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/CentroidalModelInfo.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/CentroidalModelPinocchioMapping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/CentroidalModelRbdConversions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/FactoryFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/ModelHelperFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/PinocchioCentroidalDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/PinocchioCentroidalDynamicsAD.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/implementation/AccessHelperFunctionsImpl.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/implementation/ModelHelperFunctionsImpl.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ament_index/resource_index/package_run_dependencies/ocs2_centroidal_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ament_index/resource_index/parent_prefix_path/ocs2_centroidal_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ament_index/resource_index/packages/ocs2_centroidal_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/pinocchio_config.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib/libocs2_centroidal_model.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/export_ocs2_centroidal_modelExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/export_ocs2_centroidal_modelExport-release.cmake
