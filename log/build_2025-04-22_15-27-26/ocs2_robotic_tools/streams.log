[0.009s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_robotic_tools': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_robotic_tools -- -j32 -l32
[0.052s] [ 40%] Built target gtest_main
[0.052s] [ 40%] Built target gtest
[0.062s] [ 70%] Built target ocs2_robotic_tools
[0.088s] [100%] Built target rotation_transform_tests
[0.117s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_robotic_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_robotic_tools -- -j32 -l32
[0.118s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_robotic_tools': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_robotic_tools
[0.125s] -- Install configuration: "Release"
[0.125s] -- Execute custom install script
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/AngularVelocityMapping.h
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/LoopshapingRobotInterface.h
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/RobotInterface.h
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/RotationDerivativesTransforms.h
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/RotationTransforms.h
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/common/SkewSymmetricMatrix.h
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/include/ocs2_robotic_tools/ocs2_robotic_tools/end_effector/EndEffectorKinematics.h
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/library_path.sh
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/library_path.dsv
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ament_index/resource_index/package_run_dependencies/ocs2_robotic_tools
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ament_index/resource_index/parent_prefix_path/ocs2_robotic_tools
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/ament_prefix_path.sh
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/ament_prefix_path.dsv
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/path.sh
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/environment/path.dsv
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/local_setup.bash
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/local_setup.sh
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/local_setup.zsh
[0.126s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/local_setup.dsv
[0.126s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/package.dsv
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ament_index/resource_index/packages/ocs2_robotic_tools
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/ament_cmake_export_dependencies-extras.cmake
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/ament_cmake_export_targets-extras.cmake
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/ocs2_robotic_toolsConfig.cmake
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/ocs2_robotic_toolsConfig-version.cmake
[0.132s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/package.xml
[0.132s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib/libocs2_robotic_tools.a
[0.132s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/export_ocs2_robotic_toolsExport.cmake
[0.132s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake/export_ocs2_robotic_toolsExport-release.cmake
[0.134s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_robotic_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_robotic_tools
