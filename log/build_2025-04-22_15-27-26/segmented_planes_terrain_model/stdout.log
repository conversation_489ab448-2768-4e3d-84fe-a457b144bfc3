-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found convex_plane_decomposition_ros: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem 
-- Found ocs2_switched_model_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- Found grid_map_sdf: 2.2.1 (/home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/segmented_planes_terrain_model
[35m[1mConsolidate compiler generated dependencies of target segmented_planes_terrain_model[0m
[ 25%] [32mBuilding CXX object CMakeFiles/segmented_planes_terrain_model.dir/src/SegmentedPlanesTerrainModel.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/segmented_planes_terrain_model.dir/src/SegmentedPlanesTerrainModelRos.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/segmented_planes_terrain_model.dir/src/SegmentedPlanesTerrainVisualization.cpp.o[0m
[100%] [32m[1mLinking CXX static library libsegmented_planes_terrain_model.a[0m
[100%] Built target segmented_planes_terrain_model
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/include/segmented_planes_terrain_model/segmented_planes_terrain_model/SegmentedPlanesSignedDistanceField.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/include/segmented_planes_terrain_model/segmented_planes_terrain_model/SegmentedPlanesTerrainModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/include/segmented_planes_terrain_model/segmented_planes_terrain_model/SegmentedPlanesTerrainModelRos.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/include/segmented_planes_terrain_model/segmented_planes_terrain_model/SegmentedPlanesTerrainVisualization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/ament_index/resource_index/package_run_dependencies/segmented_planes_terrain_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/ament_index/resource_index/parent_prefix_path/segmented_planes_terrain_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/ament_index/resource_index/packages/segmented_planes_terrain_model
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/segmented_planes_terrain_modelConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/segmented_planes_terrain_modelConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/package.xml
-- Installing: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib/libsegmented_planes_terrain_model.a
-- Up-to-date: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/export_segmented_planes_terrain_modelExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake/export_segmented_planes_terrain_modelExport-release.cmake
