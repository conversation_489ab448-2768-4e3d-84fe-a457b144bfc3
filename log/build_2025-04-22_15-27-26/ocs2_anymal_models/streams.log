[0.017s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_models -- -j32 -l32
[0.037s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.108s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.111s] -- Found ocs2_switched_model_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake)
[0.129s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.130s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.133s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.139s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.146s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.164s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.165s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.245s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.302s] -- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
[0.350s] -- Found ocs2_pinocchio_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake)
[0.351s] -- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
[0.351s] -- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
[0.351s] -- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
[0.352s] -- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
[0.352s] -- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
[0.352s] -- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[0.359s] [33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
[0.359s]   Please update your CMake from 'hpp-fcl' to 'coal'
[0.359s] Call Stack (most recent call first):
[0.359s]   /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
[0.359s]   /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
[0.359s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.359s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
[0.359s]   CMakeLists.txt:12 (find_package)
[0.359s] 
[0.359s] [0m
[0.359s] -- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
[0.365s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
[0.564s] -- Default C++ standard: 201703
[0.564s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.565s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.569s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.641s] -- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.642s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.658s] -- Configuring done
[0.703s] -- Generating done
[0.716s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_anymal_models
[0.755s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.756s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.771s] [35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_models[0m
[0.773s] [ 10%] Built target gtest_main
[0.773s] [ 20%] Built target gtest
[0.864s] [ 25%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/AnymalModels.cpp.o[0m
[0.866s] [ 30%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/FrameDeclaration.cpp.o[0m
[0.873s] [ 35%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedPinocchioMapping.cpp.o[0m
[0.875s] [ 40%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedInverseKinematics.cpp.o[0m
[0.879s] [ 45%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedKinematics.cpp.o[0m
[0.882s] [ 50%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedCom.cpp.o[0m
[5.922s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[5.922s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
[5.922s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedInverseKinematics.cpp:4[m[K:
[5.922s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[5.923s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[5.923s]       |                                                                       [01;36m[K^[m[K
[7.263s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[7.263s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[7.264s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[7.264s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[7.264s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[7.264s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[7.264s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedPinocchioMapping.cpp:4[m[K:
[7.264s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[7.264s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[7.264s]       |                                                                       [01;36m[K^[m[K
[8.279s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[8.279s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[8.280s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[8.280s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[8.280s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[8.280s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[8.280s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedCom.cpp:9[m[K:
[8.280s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[8.280s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[8.280s]       |                                                                       [01;36m[K^[m[K
[8.315s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[8.315s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[8.315s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[8.315s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[8.315s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[8.315s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[8.315s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedKinematics.cpp:4[m[K:
[8.315s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[8.315s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[8.315s]       |                                                                       [01;36m[K^[m[K
[51.825s] [ 55%] [32m[1mLinking CXX static library libocs2_anymal_models.a[0m
[51.873s] [ 55%] Built target ocs2_anymal_models
[51.882s] [35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_models_switched_model_test[0m
[51.886s] [35m[1mConsolidate compiler generated dependencies of target TestQuadrupedPinocchio[0m
[51.897s] [ 60%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models_switched_model_test.dir/test/TestDynamicsHelpers.cpp.o[0m
[51.915s] [ 65%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/camel/AnymalCamelKinematics.cpp.o[0m
[51.916s] [ 70%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/camel/AnymalCamelCom.cpp.o[0m
[51.917s] [ 75%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestFrameDeclaration.cpp.o[0m
[51.918s] [ 80%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestInverseKinematics.cpp.o[0m
[51.919s] [ 85%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestQuadrupedPinocchioCom.cpp.o[0m
[51.921s] [ 90%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestQuadrupedPinocchioKinematics.cpp.o[0m
[56.690s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[56.690s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[56.690s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[56.690s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[56.690s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[56.690s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[56.690s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/test/TestInverseKinematics.cpp:6[m[K:
[56.690s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[56.690s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[56.690s]       |                                                                       [01;36m[K^[m[K
[60.590s] [ 95%] [32m[1mLinking CXX executable ocs2_anymal_models_switched_model_test[0m
[60.712s] [ 95%] Built target ocs2_anymal_models_switched_model_test
[61.792s] [100%] [32m[1mLinking CXX executable TestQuadrupedPinocchio[0m
[61.989s] [100%] Built target TestQuadrupedPinocchio
[62.001s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_models -- -j32 -l32
[62.002s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_models
[62.007s] -- Install configuration: "Release"
[62.008s] -- Execute custom install script
[62.008s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/.gitignore
[62.008s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/AnymalModels.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/DynamicsHelpers.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/FrameDeclaration.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedCom.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedInverseKinematics.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedKinematics.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedPinocchioMapping.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h.in
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//config/visualize_urdf.rviz
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//launch/visualize.launch.py
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/anymal_camel_rsl.urdf
[62.009s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/frame_declaration_anymal_c.info
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.sh
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.dsv
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_models
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_models
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.sh
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.dsv
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.sh
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.dsv
[62.010s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.bash
[62.011s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.sh
[62.011s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.zsh
[62.011s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.dsv
[62.011s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.dsv
[62.016s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/packages/ocs2_anymal_models
[62.016s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_dependencies-extras.cmake
[62.016s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_targets-extras.cmake
[62.017s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig.cmake
[62.017s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig-version.cmake
[62.017s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.xml
[62.017s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_models/lib/libocs2_anymal_models.a
[62.022s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport.cmake
[62.022s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport-release.cmake
[62.023s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_models
