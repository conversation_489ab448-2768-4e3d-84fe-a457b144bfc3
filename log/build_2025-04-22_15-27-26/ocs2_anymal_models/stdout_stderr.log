-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_switched_model_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- Found ocs2_pinocchio_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake)
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)

[0m
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_anymal_models
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_models[0m
[ 10%] Built target gtest_main
[ 20%] Built target gtest
[ 25%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/AnymalModels.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/FrameDeclaration.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedPinocchioMapping.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedInverseKinematics.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedKinematics.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models.dir/src/QuadrupedCom.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedInverseKinematics.cpp:4[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedPinocchioMapping.cpp:4[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedCom.cpp:9[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/src/QuadrupedKinematics.cpp:4[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
[ 55%] [32m[1mLinking CXX static library libocs2_anymal_models.a[0m
[ 55%] Built target ocs2_anymal_models
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_models_switched_model_test[0m
[35m[1mConsolidate compiler generated dependencies of target TestQuadrupedPinocchio[0m
[ 60%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_models_switched_model_test.dir/test/TestDynamicsHelpers.cpp.o[0m
[ 65%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/camel/AnymalCamelKinematics.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/camel/AnymalCamelCom.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestFrameDeclaration.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestInverseKinematics.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestQuadrupedPinocchioCom.cpp.o[0m
[ 90%] [32mBuilding CXX object CMakeFiles/TestQuadrupedPinocchio.dir/test/TestQuadrupedPinocchioKinematics.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models/test/TestInverseKinematics.cpp:6[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
[ 95%] [32m[1mLinking CXX executable ocs2_anymal_models_switched_model_test[0m
[ 95%] Built target ocs2_anymal_models_switched_model_test
[100%] [32m[1mLinking CXX executable TestQuadrupedPinocchio[0m
[100%] Built target TestQuadrupedPinocchio
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/AnymalModels.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/DynamicsHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/FrameDeclaration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedCom.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedInverseKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedPinocchioMapping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h.in
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//config/visualize_urdf.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//launch/visualize.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/anymal_camel_rsl.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/frame_declaration_anymal_c.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/packages/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_models/lib/libocs2_anymal_models.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport-release.cmake
