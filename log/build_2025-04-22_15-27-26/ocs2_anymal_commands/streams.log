[0.017s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_commands -- -j32 -l32
[0.033s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.106s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.112s] -- Found ocs2_switched_model_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake)
[0.131s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.132s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.135s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.141s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.148s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.166s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.167s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.247s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.353s] -- Found grid_map_filters_rsl: 0.1.0 (/home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake)
[0.377s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.428s] -- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.428s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.435s] -- Configuring done
[0.485s] -- Generating done
[0.495s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_anymal_commands
[0.518s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.518s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.528s] [35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_commands[0m
[0.528s] [  8%] Built target gtest
[0.530s] [ 17%] Built target gtest_main
[0.571s] [ 21%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/LoadMotions.cpp.o[0m
[0.574s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/MotionCommandController.cpp.o[0m
[0.575s] [ 30%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/ModeSequenceKeyboard.cpp.o[0m
[0.577s] [ 34%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/MotionCommandDummy.cpp.o[0m
[0.578s] [ 39%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/MotionCommandInterface.cpp.o[0m
[0.579s] [ 43%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/ReferenceExtrapolation.cpp.o[0m
[0.579s] [ 47%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/TerrainAdaptation.cpp.o[0m
[0.580s] [ 52%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_commands.dir/src/PoseCommandToCostDesiredRos.cpp.o[0m
[16.793s] [ 56%] [32m[1mLinking CXX static library libocs2_anymal_commands.a[0m
[16.864s] [ 56%] Built target ocs2_anymal_commands
[16.877s] [35m[1mConsolidate compiler generated dependencies of target gait_command_node[0m
[16.877s] [35m[1mConsolidate compiler generated dependencies of target motion_command_node[0m
[16.878s] [35m[1mConsolidate compiler generated dependencies of target target_command_node[0m
[16.879s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_anymal_commands[0m
[16.905s] [ 60%] [32mBuilding CXX object CMakeFiles/test_ocs2_anymal_commands.dir/test/testLoadMotions.cpp.o[0m
[16.905s] [ 65%] [32mBuilding CXX object CMakeFiles/motion_command_node.dir/src/AnymalMotionCommandNode.cpp.o[0m
[16.905s] [ 73%] [32mBuilding CXX object CMakeFiles/gait_command_node.dir/src/AnymalGaitNode.cpp.o[0m
[16.905s] [ 73%] [32mBuilding CXX object CMakeFiles/test_ocs2_anymal_commands.dir/test/testReferenceExtrapolation.cpp.o[0m
[16.905s] [ 78%] [32mBuilding CXX object CMakeFiles/test_ocs2_anymal_commands.dir/test/testTerrainAdaptation.cpp.o[0m
[16.914s] [ 82%] [32mBuilding CXX object CMakeFiles/target_command_node.dir/src/AnymalPoseCommandNode.cpp.o[0m
[21.468s] [ 86%] [32m[1mLinking CXX executable gait_command_node[0m
[21.655s] [ 86%] Built target gait_command_node
[21.881s] [ 91%] [32m[1mLinking CXX executable test_ocs2_anymal_commands[0m
[22.195s] [ 91%] Built target test_ocs2_anymal_commands
[22.465s] [ 95%] [32m[1mLinking CXX executable target_command_node[0m
[22.672s] [ 95%] Built target target_command_node
[23.164s] [100%] [32m[1mLinking CXX executable motion_command_node[0m
[23.350s] [100%] Built target motion_command_node
[23.362s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_commands -- -j32 -l32
[23.363s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_commands
[23.370s] -- Install configuration: "Release"
[23.371s] -- Execute custom install script
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/LoadMotions.h
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ModeSequenceKeyboard.h
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandController.h
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandDummy.h
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandInterface.h
[23.371s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/PoseCommandToCostDesiredRos.h
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ReferenceExtrapolation.h
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/TerrainAdaptation.h
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/target_command_node
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/gait_command_node
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/motion_command_node
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/gait.info
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions.info
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/demo_motion.txt
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/walking.txt
[23.372s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.sh
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.dsv
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_commands
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_commands
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.sh
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.dsv
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.sh
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.dsv
[23.373s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.bash
[23.374s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.sh
[23.374s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.zsh
[23.374s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.dsv
[23.374s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.dsv
[23.381s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/packages/ocs2_anymal_commands
[23.381s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_dependencies-extras.cmake
[23.381s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_targets-extras.cmake
[23.381s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig.cmake
[23.381s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig-version.cmake
[23.382s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.xml
[23.382s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/libocs2_anymal_commands.a
[23.389s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport.cmake
[23.389s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport-release.cmake
[23.391s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_commands
