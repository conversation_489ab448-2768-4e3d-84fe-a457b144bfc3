[0.017s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_switched_model_interface -- -j32 -l32
[0.034s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.114s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.118s] -- Found ocs2_ros_interfaces: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake)
[0.136s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.138s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.141s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.145s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.152s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.184s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.186s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.261s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.364s] -- Found ocs2_robotic_tools: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake)
[0.365s] -- Found ocs2_switched_model_msgs: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/share/ocs2_switched_model_msgs/cmake)
[0.373s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.416s] -- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.416s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.430s] -- Configuring done
[0.459s] -- Generating done
[0.469s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
[0.488s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.488s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.496s] [  2%] Built target gtest_main
[0.497s] [  5%] Built target gtest
[0.514s] [35m[1mConsolidate compiler generated dependencies of target ocs2_switched_model_interface[0m
[0.597s] [  7%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/EndEffectorVelocityConstraint.cpp.o[0m
[0.598s] [ 10%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/analytical_inverse_kinematics/LegInverseKinematicParameters.cpp.o[0m
[0.598s] [ 10%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/analytical_inverse_kinematics/AnalyticalInverseKinematics.cpp.o[0m
[0.600s] [ 11%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/FootNormalConstraint.cpp.o[0m
[0.600s] [ 13%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/FrictionConeConstraint.cpp.o[0m
[0.601s] [ 14%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/dynamics/ComKinoSystemDynamicsAd.cpp.o[0m
[0.601s] [ 16%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/ZeroForceConstraint.cpp.o[0m
[0.603s] [ 17%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/dynamics/ComKinoDynamicsParameters.cpp.o[0m
[0.603s] [ 19%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/FootPlacementCost.cpp.o[0m
[0.603s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/CollisionAvoidanceCost.cpp.o[0m
[0.603s] [ 22%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/FrictionConeCost.cpp.o[0m
[0.606s] [ 23%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/LinearStateInequalitySoftConstraint.cpp.o[0m
[0.607s] [ 25%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/MotionTrackingCost.cpp.o[0m
[0.609s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/MotionTrackingTerminalCost.cpp.o[0m
[0.612s] [ 27%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/TorqueLimitsSoftConstraint.cpp.o[0m
[0.613s] [ 29%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/KinematicFootPlacementPenalty.cpp.o[0m
[0.614s] [ 30%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/CubicSpline.cpp.o[0m
[0.615s] [ 32%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/FootPhase.cpp.o[0m
[0.615s] [ 33%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/QuinticSplineSwing.cpp.o[0m
[0.616s] [ 35%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SplineCpg.cpp.o[0m
[0.617s] [ 36%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SwingSpline3d.cpp.o[0m
[0.620s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SwingTrajectoryPlanner.cpp.o[0m
[0.621s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/Gait.cpp.o[0m
[0.621s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/initialization/ComKinoInitializer.cpp.o[0m
[0.623s] [ 42%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitAdaptation.cpp.o[0m
[0.627s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitReceiver.cpp.o[0m
[0.627s] [ 45%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitSchedule.cpp.o[0m
[0.631s] [ 47%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/ModeSequenceTemplate.cpp.o[0m
[0.632s] [ 48%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/SingleLegLogic.cpp.o[0m
[0.632s] [ 50%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/DynamicsParametersSynchronizedModule.cpp.o[0m
[0.637s] [ 51%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/SwitchedModelModeScheduleManager.cpp.o[0m
[0.644s] [ 52%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/ros_msg_conversions/RosMsgConversions.cpp.o[0m
[3.041s] [ 54%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlanarSignedDistanceField.cpp.o[0m
[3.923s] [ 55%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlanarTerrainModel.cpp.o[0m
[5.983s] [ 57%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlaneFitting.cpp.o[0m
[6.129s] [ 58%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/TerrainPlane.cpp.o[0m
[6.149s] [ 60%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/ComModelBase.cpp.o[0m
[6.232s] [ 61%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/KinematicsModelBase.cpp.o[0m
[7.047s] [ 63%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/ModelSettings.cpp.o[0m
[7.377s] [ 64%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/SwitchedModelPrecomputation.cpp.o[0m
[7.486s] [ 66%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/TorqueApproximation.cpp.o[0m
[18.830s] [ 67%] [32m[1mLinking CXX static library libocs2_switched_model_interface.a[0m
[18.894s] [ 67%] Built target ocs2_switched_model_interface
[18.902s] [35m[1mConsolidate compiler generated dependencies of target test_constraints[0m
[18.904s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_core[0m
[18.904s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_footplanner[0m
[18.905s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_terrain[0m
[18.905s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_convexTerrain[0m
[18.906s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_cost[0m
[18.908s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_logic[0m
[18.918s] [ 70%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_core.dir/test/core/testRotation.cpp.o[0m
[18.918s] [ 70%] [32mBuilding CXX object CMakeFiles/test_constraints.dir/test/constraint/testZeroForceConstraint.cpp.o[0m
[18.920s] [ 72%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_terrain.dir/test/terrain/testTerrainPlane.cpp.o[0m
[18.920s] [ 73%] [32mBuilding CXX object CMakeFiles/test_constraints.dir/test/constraint/testFrictionConeConstraint.cpp.o[0m
[18.921s] [ 75%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_footplanner.dir/test/foot_planner/testSwingPhase.cpp.o[0m
[18.922s] [ 76%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_convexTerrain.dir/test/terrain/testConvexTerrain.cpp.o[0m
[18.927s] [ 77%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testTorqueLimitsSoftConstraint.cpp.o[0m
[18.928s] [ 79%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testFootplacementCost.cpp.o[0m
[18.929s] [ 80%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testFrictionConeCost.cpp.o[0m
[18.938s] [ 82%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testEarlyTouchDown.cpp.o[0m
[18.939s] [ 83%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testExtractContractTimings.cpp.o[0m
[18.939s] [ 85%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGait.cpp.o[0m
[18.940s] [ 86%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGaitSwitching.cpp.o[0m
[18.943s] [ 88%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGaitSchedule.cpp.o[0m
[18.944s] [ 89%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testSingleLegLogic.cpp.o[0m
[24.965s] [ 91%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_core[0m
[25.058s] [ 92%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_footplanner[0m
[25.161s] [ 92%] Built target test_ocs2_switched_model_interface_core
[25.226s] [ 92%] Built target test_ocs2_switched_model_interface_footplanner
[25.288s] [ 94%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_logic[0m
[25.438s] [ 94%] Built target test_ocs2_switched_model_interface_logic
[25.523s] [ 95%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_convexTerrain[0m
[25.648s] [ 95%] Built target test_ocs2_switched_model_interface_convexTerrain
[25.729s] [ 97%] [32m[1mLinking CXX executable test_constraints[0m
[25.887s] [ 97%] Built target test_constraints
[26.027s] [ 98%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_terrain[0m
[26.137s] [ 98%] Built target test_ocs2_switched_model_interface_terrain
[26.627s] [100%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_cost[0m
[26.762s] [100%] Built target test_ocs2_switched_model_interface_cost
[26.773s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_switched_model_interface -- -j32 -l32
[26.774s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
[26.778s] -- Install configuration: "Release"
[26.778s] -- Execute custom install script
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/AnalyticalInverseKinematics.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/LegInverseKinematicParameters.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/EndEffectorVelocityConstraint.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FootNormalConstraint.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FrictionConeConstraint.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/ZeroForceConstraint.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ComModelBase.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/InverseKinematicsModelBase.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/KinematicsModelBase.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ModelSettings.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/MotionPhaseDefinition.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/Rotations.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModel.h
[26.779s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModelPrecomputation.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/TorqueApproximation.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CollisionAvoidanceCost.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CostElements.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FootPlacementCost.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FrictionConeCost.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/LinearStateInequalitySoftconstraint.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingCost.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingTerminalCost.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/TorqueLimitsSoftConstraint.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoDynamicsParameters.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoSystemDynamicsAd.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/CubicSpline.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/FootPhase.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/KinematicFootPlacementPenalty.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/QuinticSplineSwing.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SplineCpg.h
[26.780s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingSpline3d.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingTrajectoryPlanner.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/initialization/ComKinoInitializer.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/DynamicsParametersSynchronizedModule.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/Gait.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitAdaptation.h
[26.781s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitReceiver.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSchedule.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSwitching.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/ModeSequenceTemplate.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SingleLegLogic.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SwitchedModelModeScheduleManager.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/ros_msg_conversions/RosMsgConversions.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/ConvexTerrain.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarSignedDistanceField.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarTerrainModel.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlaneFitting.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/SignedDistanceField.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainModel.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainPlane.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/test/TestEvaluateConstraints.h
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.sh
[26.789s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.dsv
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_switched_model_interface
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_switched_model_interface
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.sh
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.dsv
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.sh
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.dsv
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.bash
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.sh
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.zsh
[26.790s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.dsv
[26.790s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.dsv
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/packages/ocs2_switched_model_interface
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_dependencies-extras.cmake
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_targets-extras.cmake
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig.cmake
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig-version.cmake
[26.796s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.xml
[26.796s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib/libocs2_switched_model_interface.a
[26.799s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport.cmake
[26.799s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport-release.cmake
[26.800s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
