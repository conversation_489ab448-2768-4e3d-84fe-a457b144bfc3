-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_ros_interfaces: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found ocs2_robotic_tools: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake)
-- Found ocs2_switched_model_msgs: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/share/ocs2_switched_model_msgs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[  2%] Built target gtest_main
[  5%] Built target gtest
[35m[1mConsolidate compiler generated dependencies of target ocs2_switched_model_interface[0m
[  7%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/EndEffectorVelocityConstraint.cpp.o[0m
[ 10%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/analytical_inverse_kinematics/LegInverseKinematicParameters.cpp.o[0m
[ 10%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/analytical_inverse_kinematics/AnalyticalInverseKinematics.cpp.o[0m
[ 11%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/FootNormalConstraint.cpp.o[0m
[ 13%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/FrictionConeConstraint.cpp.o[0m
[ 14%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/dynamics/ComKinoSystemDynamicsAd.cpp.o[0m
[ 16%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/constraint/ZeroForceConstraint.cpp.o[0m
[ 17%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/dynamics/ComKinoDynamicsParameters.cpp.o[0m
[ 19%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/FootPlacementCost.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/CollisionAvoidanceCost.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/FrictionConeCost.cpp.o[0m
[ 23%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/LinearStateInequalitySoftConstraint.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/MotionTrackingCost.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/MotionTrackingTerminalCost.cpp.o[0m
[ 27%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/cost/TorqueLimitsSoftConstraint.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/KinematicFootPlacementPenalty.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/CubicSpline.cpp.o[0m
[ 32%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/FootPhase.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/QuinticSplineSwing.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SplineCpg.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SwingSpline3d.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/foot_planner/SwingTrajectoryPlanner.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/Gait.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/initialization/ComKinoInitializer.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitAdaptation.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitReceiver.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/GaitSchedule.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/ModeSequenceTemplate.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/SingleLegLogic.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/DynamicsParametersSynchronizedModule.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/logic/SwitchedModelModeScheduleManager.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/ros_msg_conversions/RosMsgConversions.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlanarSignedDistanceField.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlanarTerrainModel.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/PlaneFitting.cpp.o[0m
[ 58%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/terrain/TerrainPlane.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/ComModelBase.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/KinematicsModelBase.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/ModelSettings.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/SwitchedModelPrecomputation.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/ocs2_switched_model_interface.dir/src/core/TorqueApproximation.cpp.o[0m
[ 67%] [32m[1mLinking CXX static library libocs2_switched_model_interface.a[0m
[ 67%] Built target ocs2_switched_model_interface
[35m[1mConsolidate compiler generated dependencies of target test_constraints[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_core[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_footplanner[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_terrain[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_convexTerrain[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_cost[0m
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_switched_model_interface_logic[0m
[ 70%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_core.dir/test/core/testRotation.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/test_constraints.dir/test/constraint/testZeroForceConstraint.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_terrain.dir/test/terrain/testTerrainPlane.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/test_constraints.dir/test/constraint/testFrictionConeConstraint.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_footplanner.dir/test/foot_planner/testSwingPhase.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_convexTerrain.dir/test/terrain/testConvexTerrain.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testTorqueLimitsSoftConstraint.cpp.o[0m
[ 79%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testFootplacementCost.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_cost.dir/test/cost/testFrictionConeCost.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testEarlyTouchDown.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testExtractContractTimings.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGait.cpp.o[0m
[ 86%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGaitSwitching.cpp.o[0m
[ 88%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testGaitSchedule.cpp.o[0m
[ 89%] [32mBuilding CXX object CMakeFiles/test_ocs2_switched_model_interface_logic.dir/test/logic/testSingleLegLogic.cpp.o[0m
[ 91%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_core[0m
[ 92%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_footplanner[0m
[ 92%] Built target test_ocs2_switched_model_interface_core
[ 92%] Built target test_ocs2_switched_model_interface_footplanner
[ 94%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_logic[0m
[ 94%] Built target test_ocs2_switched_model_interface_logic
[ 95%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_convexTerrain[0m
[ 95%] Built target test_ocs2_switched_model_interface_convexTerrain
[ 97%] [32m[1mLinking CXX executable test_constraints[0m
[ 97%] Built target test_constraints
[ 98%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_terrain[0m
[ 98%] Built target test_ocs2_switched_model_interface_terrain
[100%] [32m[1mLinking CXX executable test_ocs2_switched_model_interface_cost[0m
[100%] Built target test_ocs2_switched_model_interface_cost
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/AnalyticalInverseKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/LegInverseKinematicParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/EndEffectorVelocityConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FootNormalConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FrictionConeConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/ZeroForceConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ComModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/InverseKinematicsModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/KinematicsModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ModelSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/MotionPhaseDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/Rotations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModelPrecomputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/TorqueApproximation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CollisionAvoidanceCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CostElements.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FootPlacementCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FrictionConeCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/LinearStateInequalitySoftconstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingTerminalCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/TorqueLimitsSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoDynamicsParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoSystemDynamicsAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/CubicSpline.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/FootPhase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/KinematicFootPlacementPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/QuinticSplineSwing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SplineCpg.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingSpline3d.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingTrajectoryPlanner.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/initialization/ComKinoInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/DynamicsParametersSynchronizedModule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/Gait.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitAdaptation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSwitching.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/ModeSequenceTemplate.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SingleLegLogic.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SwitchedModelModeScheduleManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/ros_msg_conversions/RosMsgConversions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/ConvexTerrain.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarSignedDistanceField.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarTerrainModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlaneFitting.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/SignedDistanceField.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainPlane.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/test/TestEvaluateConstraints.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/packages/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib/libocs2_switched_model_interface.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport-release.cmake
