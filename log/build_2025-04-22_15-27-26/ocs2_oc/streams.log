[0.008s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[0.047s] [  3%] Built target gtest_main
[0.050s] [  7%] Built target gtest
[0.074s] [ 68%] Built target ocs2_oc
[0.094s] [ 72%] Built target test_precondition
[0.095s] [ 75%] Built target test_ocp_to_kkt
[0.096s] [ 79%] Built target test_change_of_variables
[0.096s] [ 85%] Built target test_ocs2_oc_rollout
[0.096s] [ 90%] Built target test_ocs2_oc_data
[0.096s] [ 92%] Built target test_trajectory_spreading
[0.100s] [100%] Built target test_ocs2_oc_multiple_shooting
[0.107s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[0.109s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
[0.114s] -- Install configuration: "Release"
[0.115s] -- Execute custom install script
[0.115s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/ChangeOfInputVariables.h
[0.115s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/LinearQuadraticApproximator.h
[0.115s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Helpers.h
[0.115s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Initialization.h
[0.115s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/LagrangianEvaluation.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/MetricsComputation.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/PerformanceIndexComputation.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/ProjectionMultiplierCoefficients.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Transcription.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/DualSolution.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/LoopshapingPrimalSolution.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PerformanceIndex.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PrimalSolution.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/ProblemMetrics.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/TimeDiscretization.h
[0.116s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/LoopshapingOptimalControlProblem.h
[0.117s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpSize.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpToKkt.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblem.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblemHelperFunction.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_solver/SolverBase.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/precondition/Ruzi.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/InitializerRollout.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/PerformanceIndicesRollout.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutBase.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutSettings.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinder.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinderType.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/StateTriggeredRollout.h
[0.118s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/TimeTriggeredRollout.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/search_strategy/FilterLinesearch.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingReferenceManager.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingSynchronizedModule.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManager.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerDecorator.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerInterface.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverObserver.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverSynchronizedModule.h
[0.119s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreading.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreadingHelperFunctions.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/DoubleIntegratorReachingTask.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP0.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP1.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/ball_dynamics_staterollout.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/circular_kinematics.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/dynamics_hybrid_slq_test.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/pendulum_dynamics_staterollout.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/testProblemsGeneration.h
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.sh
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.dsv
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/package_run_dependencies/ocs2_oc
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/parent_prefix_path/ocs2_oc
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.sh
[0.120s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.dsv
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.sh
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.dsv
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.bash
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.sh
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.zsh
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.dsv
[0.121s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.dsv
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/packages/ocs2_oc
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_dependencies-extras.cmake
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_targets-extras.cmake
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig.cmake
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig-version.cmake
[0.125s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.xml
[0.125s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/lib/libocs2_oc.a
[0.125s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport.cmake
[0.126s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport-release.cmake
[0.127s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
