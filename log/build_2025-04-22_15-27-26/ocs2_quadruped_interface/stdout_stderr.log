-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found tf2: 0.25.12 (/opt/ros/humble/share/tf2/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found robot_state_publisher: 3.0.3 (/opt/ros/humble/share/robot_state_publisher/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found ocs2_anymal_commands: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- Found ocs2_ddp: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake)
-- Found ocs2_sqp: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake)
-- Found segmented_planes_terrain_model: 0.0.0 (/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/share/segmented_planes_terrain_model/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_quadruped_interface
[35m[1mConsolidate compiler generated dependencies of target ocs2_quadruped_interface[0m
[  8%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedDummyNode.cpp.o[0m
[ 16%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedInterface.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedLogger.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedMpc.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedMpcNode.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedPointfootInterface.cpp.o[0m
[ 58%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedTfPublisher.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/QuadrupedVisualizer.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/SwingPlanningVisualizer.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/TerrainReceiver.cpp.o[0m
[ 91%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_interface.dir/src/TerrainPlaneVisualizer.cpp.o[0m
[100%] [32m[1mLinking CXX static library libocs2_quadruped_interface.a[0m
[100%] Built target ocs2_quadruped_interface
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedDummyNode.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedLogger.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedMpc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedMpcNode.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedPointfootInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedTfPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/SwingPlanningVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/TerrainPlaneVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/TerrainReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface//config/config.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface//launch/visualization.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/packages/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ocs2_quadruped_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ocs2_quadruped_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib/libocs2_quadruped_interface.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/export_ocs2_quadruped_interfaceExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/export_ocs2_quadruped_interfaceExport-release.cmake
