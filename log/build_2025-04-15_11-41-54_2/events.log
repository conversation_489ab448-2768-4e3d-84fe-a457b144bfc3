[0.000000] (-) TimerEvent: {}
[0.000411] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000658] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000736] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000787] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000806] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000827] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000874] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000889] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000904] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000919] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000932] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000945] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000970] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.001008] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001023] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001036] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001054] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001068] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001083] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001115] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001137] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001150] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001165] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001181] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001194] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001207] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001219] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001235] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001249] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001262] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001274] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001289] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001325] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001345] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001460] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001501] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001516] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001549] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001572] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001587] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001600] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001615] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001628] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001641] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001661] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001696] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001710] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001729] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001743] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001761] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001790] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001812] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001827] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001841] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001856] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001870] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001906] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001920] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.001934] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.001953] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002045] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002134] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002148] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002162] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002175] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002250] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002269] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002411] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002574] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002589] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002604] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002617] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002630] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002646] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002663] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002681] (go1_description) JobQueued: {'identifier': 'go1_description', 'dependencies': OrderedDict()}
[0.002698] (go1_description) JobStarted: {'identifier': 'go1_description'}
[0.006122] (go1_description) JobProgress: {'identifier': 'go1_description', 'progress': 'cmake'}
[0.006315] (go1_description) JobProgress: {'identifier': 'go1_description', 'progress': 'build'}
[0.006596] (go1_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/go1_description', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/go1_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3103'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=be9f571dc81d9ef4ded0d66f67fcf524'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3103,unix/cg215:/tmp/.ICE-unix/3103'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/72da0785_95d7_4918_9d75_e8fd6cf9b464'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go1_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=be9f571dc81d9ef4ded0d66f67fcf524'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.035253] (go1_description) CommandEnded: {'returncode': 0}
[0.035628] (go1_description) JobProgress: {'identifier': 'go1_description', 'progress': 'install'}
[0.044367] (go1_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/go1_description'], 'cwd': '/home/<USER>/ros2_ws/build/go1_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3103'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=be9f571dc81d9ef4ded0d66f67fcf524'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3103,unix/cg215:/tmp/.ICE-unix/3103'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/72da0785_95d7_4918_9d75_e8fd6cf9b464'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go1_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=be9f571dc81d9ef4ded0d66f67fcf524'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.054925] (go1_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.055807] (go1_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.056465] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/calf.dae\n'}
[0.056699] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/depthCamera.dae\n'}
[0.057100] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/hip.dae\n'}
[0.057351] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh.dae\n'}
[0.057448] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh_mirror.dae\n'}
[0.057590] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/trunk.dae\n'}
[0.058130] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/ultraSound.dae\n'}
[0.058317] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/const.xacro\n'}
[0.058428] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/depthCamera.xacro\n'}
[0.058527] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ft_sensor.xacro\n'}
[0.058625] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo.xacro\n'}
[0.058718] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo_classic.xacro\n'}
[0.058947] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/leg.xacro\n'}
[0.059051] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/materials.xacro\n'}
[0.059170] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/robot.xacro\n'}
[0.059263] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ros2_control.xacro\n'}
[0.059395] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/transmission.xacro\n'}
[0.059487] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ultraSound.xacro\n'}
[0.059578] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//launch/visualize.launch.py\n'}
[0.059776] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/gazebo.yaml\n'}
[0.059939] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/gait.info\n'}
[0.060101] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/reference.info\n'}
[0.060238] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/task.info\n'}
[0.060371] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/robot_control.yaml\n'}
[0.060502] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/visualize_urdf.rviz\n'}
[0.060807] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//urdf/robot.urdf\n'}
[0.061086] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/package_run_dependencies/go1_description\n'}
[0.061328] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/parent_prefix_path/go1_description\n'}
[0.061698] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.sh\n'}
[0.061865] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.dsv\n'}
[0.061993] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.sh\n'}
[0.062155] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.dsv\n'}
[0.062335] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.bash\n'}
[0.062513] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.sh\n'}
[0.062675] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.zsh\n'}
[0.062840] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.dsv\n'}
[0.062977] (go1_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.dsv\n'}
[0.072050] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/packages/go1_description\n'}
[0.072148] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig.cmake\n'}
[0.072200] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig-version.cmake\n'}
[0.072234] (go1_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.xml\n'}
[0.074301] (go1_description) CommandEnded: {'returncode': 0}
[0.087811] (go1_description) JobEnded: {'identifier': 'go1_description', 'rc': 0}
[0.088169] (-) EventReactorShutdown: {}
