[0.012s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[0.054s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.173s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.187s] -- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[0.188s] -- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
[0.220s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.222s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.228s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.239s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.251s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.409s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.414s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.438s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.450s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.467s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.477s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.492s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.584s] -- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
[0.601s] -- Compiling against Gazebo fortress
[0.619s] [31mCMake Error at CMakeLists.txt:34 (find_package):
[0.619s]   By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this
[0.619s]   project has asked CMake to find a package configuration file provided by
[0.619s]   "ignition-gazebo6", but CMake did not find one.
[0.619s] 
[0.619s]   Could not find a package configuration file provided by "ignition-gazebo6"
[0.619s]   with any of the following names:
[0.619s] 
[0.619s]     ignition-gazebo6Config.cmake
[0.619s]     ignition-gazebo6-config.cmake
[0.619s] 
[0.619s]   Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or
[0.619s]   set "ignition-gazebo6_DIR" to a directory containing one of the above
[0.620s]   files.  If "ignition-gazebo6" provides a separate development package or
[0.620s]   SDK, be sure it has been installed.
[0.620s] 
[0.620s] [0m
[0.620s] -- Configuring incomplete, errors occurred!
[0.620s] See also "/home/<USER>/ros2_ws/build/gz_quadruped_hardware/CMakeFiles/CMakeOutput.log".
[0.630s] gmake: *** [Makefile:308: cmake_check_build_system] Error 1
[0.633s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
