[0.000000] (-) TimerEvent: {}
[0.000134] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000163] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000182] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000198] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000214] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000234] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000252] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000268] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000283] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000299] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000315] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000330] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000346] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000390] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000511] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000584] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000603] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000633] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000651] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000723] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000741] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000761] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000780] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000808] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000863] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000887] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000904] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000964] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000983] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001003] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001020] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001069] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001102] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001120] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001328] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001392] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001434] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001486] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001532] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001561] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001579] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001597] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001616] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001632] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001658] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001702] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001719] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001743] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001791] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001817] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001835] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001853] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001873] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001890] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001939] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001960] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001985] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002004] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002023] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002157] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002180] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002315] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002419] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002446] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002464] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002693] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002979] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003000] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003045] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.003095] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003117] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003136] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003162] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003182] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.003200] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003224] (gz_quadruped_hardware) JobQueued: {'identifier': 'gz_quadruped_hardware', 'dependencies': OrderedDict()}
[0.003250] (gz_quadruped_playground) JobQueued: {'identifier': 'gz_quadruped_playground', 'dependencies': OrderedDict([('gz_quadruped_hardware', '/home/<USER>/ros2_ws/install/gz_quadruped_hardware')])}
[0.003283] (gz_quadruped_hardware) JobStarted: {'identifier': 'gz_quadruped_hardware'}
[0.012047] (gz_quadruped_hardware) JobProgress: {'identifier': 'gz_quadruped_hardware', 'progress': 'cmake'}
[0.012707] (gz_quadruped_hardware) JobProgress: {'identifier': 'gz_quadruped_hardware', 'progress': 'build'}
[0.013352] (gz_quadruped_hardware) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('TERM_PROGRAM_VERSION', '0.48.9'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/cursor/cursor'), ('MANAGERPID', '2739'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '3330'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '139766'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('JOURNAL_STREAM', '8:10384'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3312,unix/cg215:/tmp/.ICE-unix/3312'), ('INVOCATION_ID', 'ea8d92b7253b4e84837d899e44fa53ab'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '3b92c98edae149609f5bbf8639a0c536'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.057107] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.099427] (-) TimerEvent: {}
[0.175897] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.189867] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)\n'}
[0.190974] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)\n'}
[0.199515] (-) TimerEvent: {}
[0.222861] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.225235] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.231231] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.242092] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.254043] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.299638] (-) TimerEvent: {}
[0.400013] (-) TimerEvent: {}
[0.412474] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.417480] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.441531] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.452676] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.470061] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.479651] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.495403] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.500084] (-) TimerEvent: {}
[0.586950] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)\n'}
[0.600167] (-) TimerEvent: {}
[0.604542] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Compiling against Gazebo fortress\n'}
[0.621650] (gz_quadruped_hardware) StderrLine: {'line': b'\x1b[31mCMake Error at CMakeLists.txt:34 (find_package):\n'}
[0.621892] (gz_quadruped_hardware) StderrLine: {'line': b'  By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this\n'}
[0.621966] (gz_quadruped_hardware) StderrLine: {'line': b'  project has asked CMake to find a package configuration file provided by\n'}
[0.622031] (gz_quadruped_hardware) StderrLine: {'line': b'  "ignition-gazebo6", but CMake did not find one.\n'}
[0.622211] (gz_quadruped_hardware) StderrLine: {'line': b'\n'}
[0.622298] (gz_quadruped_hardware) StderrLine: {'line': b'  Could not find a package configuration file provided by "ignition-gazebo6"\n'}
[0.622363] (gz_quadruped_hardware) StderrLine: {'line': b'  with any of the following names:\n'}
[0.622422] (gz_quadruped_hardware) StderrLine: {'line': b'\n'}
[0.622480] (gz_quadruped_hardware) StderrLine: {'line': b'    ignition-gazebo6Config.cmake\n'}
[0.622545] (gz_quadruped_hardware) StderrLine: {'line': b'    ignition-gazebo6-config.cmake\n'}
[0.622603] (gz_quadruped_hardware) StderrLine: {'line': b'\n'}
[0.622660] (gz_quadruped_hardware) StderrLine: {'line': b'  Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or\n'}
[0.622717] (gz_quadruped_hardware) StderrLine: {'line': b'  set "ignition-gazebo6_DIR" to a directory containing one of the above\n'}
[0.622774] (gz_quadruped_hardware) StderrLine: {'line': b'  files.  If "ignition-gazebo6" provides a separate development package or\n'}
[0.622829] (gz_quadruped_hardware) StderrLine: {'line': b'  SDK, be sure it has been installed.\n'}
[0.622885] (gz_quadruped_hardware) StderrLine: {'line': b'\n'}
[0.622941] (gz_quadruped_hardware) StderrLine: {'line': b'\x1b[0m\n'}
[0.622997] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.623060] (gz_quadruped_hardware) StdoutLine: {'line': b'See also "/home/<USER>/ros2_ws/build/gz_quadruped_hardware/CMakeFiles/CMakeOutput.log".\n'}
[0.633407] (gz_quadruped_hardware) StderrLine: {'line': b'gmake: *** [Makefile:308: cmake_check_build_system] Error 1\n'}
[0.635914] (gz_quadruped_hardware) CommandEnded: {'returncode': 2}
[0.644530] (gz_quadruped_hardware) JobEnded: {'identifier': 'gz_quadruped_hardware', 'rc': 2}
[0.654942] (gz_quadruped_playground) JobSkipped: {'identifier': 'gz_quadruped_playground'}
[0.654997] (-) EventReactorShutdown: {}
