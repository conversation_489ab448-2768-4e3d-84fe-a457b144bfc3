[0.011s] Invoking command in '/home/<USER>/ros2_ws/build/go1_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go1_description -- -j32 -l32
[0.036s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.133s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.193s] -- Configuring done
[0.195s] -- Generating done
[0.196s] -- Build files have been written to: /home/<USER>/ros2_ws/build/go1_description
[0.227s] Invoked command in '/home/<USER>/ros2_ws/build/go1_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go1_description -- -j32 -l32
[0.237s] Invoking command in '/home/<USER>/ros2_ws/build/go1_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go1_description
[0.248s] -- Install configuration: ""
[0.248s] -- Execute custom install script
[0.248s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/calf.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/depthCamera.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/hip.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh_mirror.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/trunk.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/ultraSound.dae
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/const.xacro
[0.249s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/depthCamera.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ft_sensor.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo_classic.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/leg.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/materials.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/robot.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ros2_control.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/transmission.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ultraSound.xacro
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//launch/visualize.launch.py
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/gazebo.yaml
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/gait.info
[0.250s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/reference.info
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/task.info
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/robot_control.yaml
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/visualize_urdf.rviz
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//urdf/robot.urdf
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/package_run_dependencies/go1_description
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/parent_prefix_path/go1_description
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.sh
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.dsv
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.sh
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.dsv
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.bash
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.sh
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.zsh
[0.251s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.dsv
[0.251s] -- Symlinking: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.dsv
[0.260s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/packages/go1_description
[0.260s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig.cmake
[0.260s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig-version.cmake
[0.260s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.xml
[0.263s] Invoked command in '/home/<USER>/ros2_ws/build/go1_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go1_description
