-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/go1_description
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/calf.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/depthCamera.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/hip.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh_mirror.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/trunk.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/ultraSound.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/const.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/depthCamera.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ft_sensor.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo_classic.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/leg.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/materials.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/robot.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ros2_control.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/transmission.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ultraSound.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//launch/visualize.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/gazebo.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/gait.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/reference.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/robot_control.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/visualize_urdf.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//urdf/robot.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/package_run_dependencies/go1_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/parent_prefix_path/go1_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/packages/go1_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.xml
