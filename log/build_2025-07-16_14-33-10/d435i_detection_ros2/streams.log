[0.396s] Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
[0.483s] /home/<USER>/.local/lib/python3.10/site-packages/setuptools/_distutils/dist.py:289: UserWarning: Unknown distribution option: 'tests_require'
[0.483s]   warnings.warn(msg)
[0.534s] running egg_info
[0.543s] writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO
[0.543s] writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt
[0.544s] writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt
[0.544s] writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt
[0.544s] writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt
[0.562s] reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[0.563s] writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[0.563s] running build
[0.563s] running build_py
[0.564s] copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[0.564s] copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[0.564s] running install
[0.566s] running install_lib
[0.576s] running install_data
[0.576s] running install_egg_info
[0.585s] removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)
[0.585s] Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info
[0.585s] running install_scripts
[0.602s] Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.602s] Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.602s] Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.602s] writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'
[0.616s] Invoked command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
