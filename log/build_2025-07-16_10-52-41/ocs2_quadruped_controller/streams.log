[0.092s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/ocs2_quadruped_controller
[0.108s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.248s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.264s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.305s] -- Found controller_interface: 2.51.0 (/opt/ros/humble/share/controller_interface/cmake)
[0.327s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.330s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.337s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.349s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.363s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.550s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.556s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.581s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.593s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.622s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.629s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.649s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.745s] -- Found controller_common: 0.0.0 (/home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake)
[0.747s] -- Found ocs2_legged_robot_ros: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake)
[0.835s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.838s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.872s] -- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
[0.888s] -- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
[0.888s] -- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
[0.889s] -- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
[0.890s] -- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
[0.891s] -- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
[0.892s] -- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[1.057s] [33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
[1.057s]   Please update your CMake from 'hpp-fcl' to 'coal'
[1.057s] Call Stack (most recent call first):
[1.057s]   /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
[1.057s]   /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig.cmake:41 (include)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake:41 (include)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.057s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig.cmake:41 (include)
[1.057s]   CMakeLists.txt:35 (find_package)
[1.058s] 
[1.058s] [0m
[1.058s] -- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
[1.133s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
[1.498s] -- Default C++ standard: 201703
[1.499s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[1.500s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[1.546s] -- Found ocs2_self_collision: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake)
[1.547s] -- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)
[1.554s] -- Found angles: 1.15.0 (/opt/ros/humble/share/angles/cmake)
[1.556s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.572s] -- Found qpoases_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake)
[1.573s] -- Found convex_plane_decomposition_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/share/convex_plane_decomposition_msgs/cmake)
[1.603s] -- Found convex_plane_decomposition_ros: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake)
[1.749s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: system filesystem 
[1.761s] -- Found grid_map_sdf: 2.2.1 (/home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake)
[1.781s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.923s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.318s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.325s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.498s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[2.505s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.513s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.675s] -- Found Qhull version 8.0.2
[2.831s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.994s] -- Found ocs2_sphere_approximation: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake)
[3.111s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[3.183s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[3.183s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include>;/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src
[3.183s] -- Configured cppcheck exclude dirs and/or files: 
[3.184s] -- Added test 'flake8' to check Python code syntax and style conventions
[3.185s] -- Added test 'lint_cmake' to check CMake code style
[3.186s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[3.191s] -- Added test 'uncrustify' to check C / C++ code style
[3.191s] -- Configured uncrustify additional arguments: 
[3.192s] -- Added test 'xmllint' to check XML markup files
[3.194s] -- Configuring done
[3.318s] -- Generating done
[3.328s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
[3.346s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/ocs2_quadruped_controller
[3.348s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
[3.454s] [35m[1mConsolidate compiler generated dependencies of target ocs2_quadruped_controller[0m
[3.616s] [100%] Built target ocs2_quadruped_controller
[3.629s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
[3.631s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
[3.642s] -- Install configuration: "Release"
[3.642s] -- Execute custom install script
[3.642s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//ocs2_quadruped_controller.xml
[3.643s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/FSM/StateOCS2.h
[3.643s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/CtrlComponent.h
[3.643s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/GaitManager.h
[3.643s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/TargetManager.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/FromOdomTopic.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/GroundTruth.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/LinearKalmanFilter.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/StateEstimateBase.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedInterface.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/SwitchedModelReferenceManager.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/LeggedSelfCollisionConstraint.h
[3.644s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/SwingTrajectoryPlanner.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/initialization/LeggedRobotInitializer.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootCollisionConstraint.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootPlacementConstraint.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/ConvexRegionSelector.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedInterface.h
[3.645s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/synchronize/ElevationMapReceiver.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/SphereVisualization.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HierarchicalWbc.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HoQp.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/Task.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WbcBase.h
[3.646s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WeightedWbc.h
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/convex_plane_decomposition.yaml
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/convex_plane_decomposition_node.yaml
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/elevation_mapping.yaml
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/perceptive_constraints.yaml
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/visualize_ocs2.rviz
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/coco_objects_with_perception.launch.py
[3.647s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/gazebo.launch.py
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/mujoco.launch.py
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/plum_pile_course.launch.py
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/plum_pile_with_perception.launch.py
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.sh
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.dsv
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/package_run_dependencies/ocs2_quadruped_controller
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/parent_prefix_path/ocs2_quadruped_controller
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.sh
[3.648s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.dsv
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.sh
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.dsv
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.bash
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.sh
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.zsh
[3.649s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.dsv
[3.649s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.dsv
[3.659s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/packages/ocs2_quadruped_controller
[3.659s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/ocs2_quadruped_controller
[3.659s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[3.659s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_targets-extras.cmake
[3.660s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig.cmake
[3.660s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig-version.cmake
[3.660s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.xml
[3.660s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib/ocs2_quadruped_controller/libocs2_quadruped_controller.so
[3.660s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport.cmake
[3.660s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport-release.cmake
[3.662s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
