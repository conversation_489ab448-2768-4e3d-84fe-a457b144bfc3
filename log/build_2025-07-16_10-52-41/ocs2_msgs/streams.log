[0.056s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_msgs -- -j32 -l32
[0.133s] [  0%] Built target ocs2_msgs__cpp
[0.157s] [ 12%] Built target ocs2_msgs__rosidl_generator_c
[0.184s] [ 12%] Built target ament_cmake_python_symlink_ocs2_msgs
[0.237s] [ 24%] Built target ocs2_msgs__rosidl_typesupport_cpp
[0.238s] [ 35%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_cpp
[0.238s] [ 47%] Built target ocs2_msgs__rosidl_typesupport_introspection_cpp
[0.238s] [ 72%] Built target ocs2_msgs__rosidl_typesupport_introspection_c
[0.242s] [ 72%] Built target ocs2_msgs__rosidl_typesupport_c
[0.242s] [ 83%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_c
[0.268s] [ 83%] Built target ocs2_msgs
[0.271s] [ 84%] Built target ocs2_msgs__py
[0.273s] [ 95%] Built target ocs2_msgs__rosidl_generator_py
[0.300s] [ 97%] Built target ocs2_msgs__rosidl_typesupport_c__pyext
[0.300s] [ 98%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_c__pyext
[0.302s] [100%] Built target ocs2_msgs__rosidl_typesupport_introspection_c__pyext
[0.349s] running egg_info
[0.370s] writing ocs2_msgs.egg-info/PKG-INFO
[0.371s] writing dependency_links to ocs2_msgs.egg-info/dependency_links.txt
[0.371s] writing top-level names to ocs2_msgs.egg-info/top_level.txt
[0.440s] reading manifest file 'ocs2_msgs.egg-info/SOURCES.txt'
[0.442s] writing manifest file 'ocs2_msgs.egg-info/SOURCES.txt'
[0.503s] [100%] Built target ament_cmake_python_build_ocs2_msgs_egg
[0.517s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_msgs -- -j32 -l32
[0.521s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_msgs
[0.529s] -- Install configuration: ""
[0.530s] -- Execute custom install script
[0.530s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/rosidl_interfaces/ocs2_msgs
[0.531s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/constraint.h
[0.531s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/controller_data.h
[0.531s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__functions.h
[0.531s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__struct.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__type_support.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__functions.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__struct.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__type_support.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__functions.h
[0.532s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__struct.h
[0.537s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__type_support.h
[0.537s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__functions.h
[0.537s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__struct.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__type_support.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__functions.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__struct.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__type_support.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__functions.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__struct.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__type_support.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__functions.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__struct.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__type_support.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__functions.h
[0.538s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__struct.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__type_support.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__functions.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__struct.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__type_support.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__functions.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__struct.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__type_support.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__functions.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__struct.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__type_support.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/lagrangian_metrics.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mode_schedule.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_flattened_controller.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_input.h
[0.539s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_observation.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_performance_indices.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_state.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_target_trajectories.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/multiplier.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_generator_c__visibility_control.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__functions.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__struct.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__type_support.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/reset.h
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/library_path.sh
[0.540s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/library_path.dsv
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_fastrtps_c.h
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_fastrtps_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_fastrtps_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_fastrtps_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_fastrtps_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_fastrtps_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_introspection_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_introspection_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_introspection_c.h
[0.542s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_introspection_c.h
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/constraint.hpp
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/controller_data.hpp
[0.543s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__builder.hpp
[0.544s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__struct.hpp
[0.544s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__traits.hpp
[0.544s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__type_support.hpp
[0.544s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__builder.hpp
[0.544s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__struct.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__traits.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__type_support.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__builder.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__struct.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__traits.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__type_support.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__builder.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__struct.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__traits.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__type_support.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__builder.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__struct.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__traits.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__type_support.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__builder.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__struct.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__traits.hpp
[0.545s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__type_support.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__builder.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__struct.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__traits.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__type_support.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__builder.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__struct.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__traits.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__type_support.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__builder.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__struct.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__traits.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__type_support.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__builder.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__struct.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__traits.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__type_support.hpp
[0.546s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__builder.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__struct.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__traits.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__type_support.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/lagrangian_metrics.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mode_schedule.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_flattened_controller.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_input.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_observation.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_performance_indices.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_state.hpp
[0.547s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_target_trajectories.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/multiplier.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__builder.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__struct.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__traits.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__type_support.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/reset.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_fastrtps_cpp.hpp
[0.548s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_fastrtps_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_introspection_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_introspection_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_introspection_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_introspection_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_introspection_cpp.hpp
[0.549s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_introspection_cpp.hpp
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/pythonpath.sh
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/pythonpath.dsv
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/PKG-INFO
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/SOURCES.txt
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/dependency_links.txt
[0.550s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/top_level.txt
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/__init__.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_c.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_fastrtps_c.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_introspection_c.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/libocs2_msgs__rosidl_generator_py.so
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/__init__.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_constraint.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_constraint_s.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_controller_data.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_controller_data_s.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_lagrangian_metrics.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_lagrangian_metrics_s.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mode_schedule.py
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mode_schedule_s.c
[0.551s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_flattened_controller.py
[0.552s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_flattened_controller_s.c
[0.552s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_input.py
[0.552s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_input_s.c
[0.552s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_observation.py
[0.552s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_observation_s.c
[0.553s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_performance_indices.py
[0.553s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_performance_indices_s.c
[0.553s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_state.py
[0.553s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_state_s.c
[0.553s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_target_trajectories.py
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_target_trajectories_s.c
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_multiplier.py
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_multiplier_s.c
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/__init__.py
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/_reset.py
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/_reset_s.c
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcState.idl
[0.554s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcInput.idl
[0.555s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ModeSchedule.idl
[0.555s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcObservation.idl
[0.555s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcPerformanceIndices.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcTargetTrajectories.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ControllerData.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcFlattenedController.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/LagrangianMetrics.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Multiplier.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Constraint.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset.idl
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcState.msg
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcInput.msg
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ModeSchedule.msg
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcObservation.msg
[0.556s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcPerformanceIndices.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcTargetTrajectories.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ControllerData.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcFlattenedController.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/LagrangianMetrics.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Multiplier.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Constraint.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset.srv
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset_Request.msg
[0.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset_Response.msg
[0.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/package_run_dependencies/ocs2_msgs
[0.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/parent_prefix_path/ocs2_msgs
[0.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/ament_prefix_path.sh
[0.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/ament_prefix_path.dsv
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/path.sh
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/path.dsv
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.bash
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.sh
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.zsh
[0.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.dsv
[0.559s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/package.dsv
[0.571s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/packages/ocs2_msgs
[0.572s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake-extras.cmake
[0.572s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_include_directories-extras.cmake
[0.572s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_libraries-extras.cmake
[0.573s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_targets-extras.cmake
[0.573s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[0.574s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_dependencies-extras.cmake
[0.574s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[0.575s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgsConfig.cmake
[0.575s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgsConfig-version.cmake
[0.575s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/package.xml
[0.575s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_generator_c.so
[0.575s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_fastrtps_c.so
[0.575s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_introspection_c.so
[0.576s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_c.so
[0.576s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_fastrtps_cpp.so
[0.576s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_introspection_cpp.so
[0.576s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_cpp.so
[0.608s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs'...
[0.609s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg'...
[0.609s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv'...
[0.612s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_generator_py.so
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cExport.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cExport-noconfig.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cExport.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cExport.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cExport.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cExport-noconfig.cmake
[0.613s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cppExport.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cppExport.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cppExport.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cppExport.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cppExport-noconfig.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_pyExport.cmake
[0.614s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_pyExport-noconfig.cmake
[0.645s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_msgs
