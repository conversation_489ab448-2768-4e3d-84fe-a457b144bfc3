[ 18%] Built target gtest_main
[ 36%] Built target gtest
[ 81%] Built target ocs2_sphere_approximation
[100%] Built target PinocchioSphereKinematicsTest
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematicsCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/SphereApproximation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/package_run_dependencies/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/parent_prefix_path/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/packages/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ocs2_sphere_approximationConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ocs2_sphere_approximationConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib/libocs2_sphere_approximation.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/export_ocs2_sphere_approximationExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/export_ocs2_sphere_approximationExport-release.cmake
