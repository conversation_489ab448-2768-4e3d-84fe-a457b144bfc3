[0.016s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[0.045s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.145s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.151s] -- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[0.152s] -- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
[0.181s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.183s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.188s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.200s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.213s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.380s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.383s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.400s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.410s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.430s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.439s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.454s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.528s] -- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
[0.544s] -- Compiling against Gazebo fortress
[0.560s] [31mCMake Error at CMakeLists.txt:34 (find_package):
[0.560s]   By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this
[0.560s]   project has asked CMake to find a package configuration file provided by
[0.560s]   "ignition-gazebo6", but CMake did not find one.
[0.560s] 
[0.560s]   Could not find a package configuration file provided by "ignition-gazebo6"
[0.560s]   with any of the following names:
[0.560s] 
[0.560s]     ignition-gazebo6Config.cmake
[0.560s]     ignition-gazebo6-config.cmake
[0.561s] 
[0.561s]   Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or
[0.561s]   set "ignition-gazebo6_DIR" to a directory containing one of the above
[0.561s]   files.  If "ignition-gazebo6" provides a separate development package or
[0.561s]   SDK, be sure it has been installed.
[0.561s] 
[0.561s] [0m
[0.561s] -- Configuring incomplete, errors occurred!
[0.561s] See also "/home/<USER>/ros2_ws/build/gz_quadruped_hardware/CMakeFiles/CMakeOutput.log".
[0.569s] gmake: *** [Makefile:308: cmake_check_build_system] Error 1
[0.573s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
