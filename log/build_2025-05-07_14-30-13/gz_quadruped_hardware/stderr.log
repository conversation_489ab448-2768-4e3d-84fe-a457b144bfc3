[31mCMake Error at CMakeLists.txt:34 (find_package):
  By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "ignition-gazebo6", but CMake did not find one.

  Could not find a package configuration file provided by "ignition-gazebo6"
  with any of the following names:

    ignition-gazebo6Config.cmake
    ignition-gazebo6-config.cmake

  Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or
  set "ignition-gazebo6_DIR" to a directory containing one of the above
  files.  If "ignition-gazebo6" provides a separate development package or
  SDK, be sure it has been installed.

[0m
gmake: *** [Makefile:308: cmake_check_build_system] Error 1
