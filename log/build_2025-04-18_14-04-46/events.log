[0.000000] (-) TimerEvent: {}
[0.000086] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000105] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000119] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000132] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000145] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000157] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000170] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000183] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000195] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000208] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000220] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000232] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000245] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000257] (-) JobUnselected: {'identifier': 'elevation_mapping'}
[0.000269] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000281] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000294] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000306] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000318] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000331] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000343] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000355] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000367] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000380] (-) JobUnselected: {'identifier': 'kindr_msgs'}
[0.000392] (-) JobUnselected: {'identifier': 'kindr_ros'}
[0.000404] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000416] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000428] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000440] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000453] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000465] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000477] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000489] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000501] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000514] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000526] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.000538] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.000550] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.000562] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.000574] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.000586] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.000599] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.000611] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.000623] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.000635] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.000647] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.000659] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.000676] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.000699] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.000719] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.000732] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.000744] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.000756] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.000769] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.000781] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.000793] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.000805] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.000817] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.000829] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.000841] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.000853] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.000894] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.000907] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.000919] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.000930] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.000941] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.000953] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.000965] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.000976] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.000987] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.000999] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.001010] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001021] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001032] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001044] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001055] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001066] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.001083] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001097] (go2_description) JobQueued: {'identifier': 'go2_description', 'dependencies': OrderedDict()}
[0.001112] (go2_description) JobStarted: {'identifier': 'go2_description'}
[0.007405] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'cmake'}
[0.007858] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'build'}
[0.007933] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/go2_description', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/a5cef327_975e_45ae_938d_45096a734f69'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.056086] (go2_description) CommandEnded: {'returncode': 0}
[0.057612] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'install'}
[0.069043] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/go2_description'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/a5cef327_975e_45ae_938d_45096a734f69'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.078604] (go2_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.080003] (go2_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.081904] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf.dae\n'}
[0.082221] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf_mirror.dae\n'}
[0.082382] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/foot.dae\n'}
[0.082589] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/hip.dae\n'}
[0.083089] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh.dae\n'}
[0.083215] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh_mirror.dae\n'}
[0.083473] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/trunk.dae\n'}
[0.084532] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/const.xacro\n'}
[0.084610] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo.xacro\n'}
[0.084758] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo_classic.xacro\n'}
[0.084826] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/leg.xacro\n'}
[0.084978] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/materials.xacro\n'}
[0.085043] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/robot.xacro\n'}
[0.085199] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/ros2_control.xacro\n'}
[0.085357] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/transmission.xacro\n'}
[0.085457] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/gazebo_rl_control.launch.py\n'}
[0.085544] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/visualize.launch.py\n'}
[0.085878] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/gazebo.yaml\n'}
[0.085980] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config.yaml\n'}
[0.086080] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config_himloco.yaml\n'}
[0.086188] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/himloco.pt\n'}
[0.086283] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/policy.pt\n'}
[0.086378] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/gait.info\n'}
[0.086471] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/reference.info\n'}
[0.086577] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/task.info\n'}
[0.086685] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/robot_control.yaml\n'}
[0.086793] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/visualize_urdf.rviz\n'}
[0.087039] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//urdf/robot.urdf\n'}
[0.087275] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/package_run_dependencies/go2_description\n'}
[0.087475] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/parent_prefix_path/go2_description\n'}
[0.087678] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.sh\n'}
[0.087851] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.dsv\n'}
[0.088028] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.sh\n'}
[0.088190] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.dsv\n'}
[0.088355] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.bash\n'}
[0.088518] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.sh\n'}
[0.088692] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.zsh\n'}
[0.088865] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.dsv\n'}
[0.088991] (go2_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv\n'}
[0.099617] (-) TimerEvent: {}
[0.111448] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/packages/go2_description\n'}
[0.111693] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig.cmake\n'}
[0.112078] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig-version.cmake\n'}
[0.112244] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.xml\n'}
[0.116281] (go2_description) CommandEnded: {'returncode': 0}
[0.141242] (go2_description) JobEnded: {'identifier': 'go2_description', 'rc': 0}
[0.141583] (-) EventReactorShutdown: {}
