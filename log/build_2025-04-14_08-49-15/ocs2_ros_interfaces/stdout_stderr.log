[ 70%] Built target ocs2_ros_interfaces
[35m[1mConsolidate compiler generated dependencies of target perceptive_mpc_keyboard_control[0m
[ 80%] Built target test_custom_callback_queue
[ 90%] Built target multiplot_remap
[100%] Built target perceptive_mpc_keyboard_control
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/perceptive_mpc_keyboard_control
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesInteractiveMarker.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesKeyboardPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesRosPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgConversions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mpc/MPC_ROS_Interface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/DummyObserver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/LoopshapingDummyObserver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Dummy_Loop.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Interface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/RosReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/SolverObserverRosCallbacks.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationColors.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/multiplot/performance_indices.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/multiplot_remap
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/package_run_dependencies/ocs2_ros_interfaces
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/parent_prefix_path/ocs2_ros_interfaces
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/packages/ocs2_ros_interfaces
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/libocs2_ros_interfaces.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport-release.cmake
