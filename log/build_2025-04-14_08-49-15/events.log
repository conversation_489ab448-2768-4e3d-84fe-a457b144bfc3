[0.000000] (-) TimerEvent: {}
[0.000477] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000508] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000518] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000527] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000533] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000539] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000545] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000551] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000558] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000569] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000575] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000580] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000586] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000592] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000598] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000604] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000610] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000616] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000622] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000628] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000634] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000640] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000646] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000652] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000658] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000663] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000669] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000675] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000680] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000686] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000692] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000697] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000703] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000709] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.000715] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.000721] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.000727] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.000732] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.000738] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.000744] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.000750] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.000756] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.000762] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.000768] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.000774] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.000779] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.000785] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.000791] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.000797] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.000803] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.000809] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.000814] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.000820] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.000826] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.000832] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.000837] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.000843] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.000849] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.000855] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.000860] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.000866] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.000997] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.001015] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.001023] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.001030] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.001036] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.001042] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.001048] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.001054] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001059] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001065] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001071] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001077] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001083] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.001089] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001096] (ocs2_ros_interfaces) JobQueued: {'identifier': 'ocs2_ros_interfaces', 'dependencies': OrderedDict([('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc')])}
[0.001108] (ocs2_ros_interfaces) JobStarted: {'identifier': 'ocs2_ros_interfaces'}
[0.019908] (ocs2_ros_interfaces) JobProgress: {'identifier': 'ocs2_ros_interfaces', 'progress': 'cmake'}
[0.020165] (ocs2_ros_interfaces) JobProgress: {'identifier': 'ocs2_ros_interfaces', 'progress': 'build'}
[0.020485] (ocs2_ros_interfaces) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.099927] (-) TimerEvent: {}
[0.119122] (ocs2_ros_interfaces) StdoutLine: {'line': b'[ 70%] Built target ocs2_ros_interfaces\n'}
[0.131493] (ocs2_ros_interfaces) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target perceptive_mpc_keyboard_control\x1b[0m\n'}
[0.144903] (ocs2_ros_interfaces) StdoutLine: {'line': b'[ 80%] Built target test_custom_callback_queue\n'}
[0.146079] (ocs2_ros_interfaces) StdoutLine: {'line': b'[ 90%] Built target multiplot_remap\n'}
[0.146340] (ocs2_ros_interfaces) StdoutLine: {'line': b'[100%] Built target perceptive_mpc_keyboard_control\n'}
[0.156133] (ocs2_ros_interfaces) CommandEnded: {'returncode': 0}
[0.156519] (ocs2_ros_interfaces) JobProgress: {'identifier': 'ocs2_ros_interfaces', 'progress': 'install'}
[0.167524] (ocs2_ros_interfaces) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.182593] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.183389] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.183739] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/perceptive_mpc_keyboard_control\n'}
[0.184406] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesInteractiveMarker.h\n'}
[0.184577] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesKeyboardPublisher.h\n'}
[0.184913] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesRosPublisher.h\n'}
[0.185007] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgConversions.h\n'}
[0.185072] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgHelpers.h\n'}
[0.185132] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mpc/MPC_ROS_Interface.h\n'}
[0.185190] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/DummyObserver.h\n'}
[0.185250] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/LoopshapingDummyObserver.h\n'}
[0.185401] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Dummy_Loop.h\n'}
[0.185500] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Interface.h\n'}
[0.185562] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/RosReferenceManager.h\n'}
[0.185616] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/SolverObserverRosCallbacks.h\n'}
[0.185721] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationColors.h\n'}
[0.185805] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationHelpers.h\n'}
[0.185865] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch\n'}
[0.185974] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch.py\n'}
[0.186063] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/multiplot/performance_indices.xml\n'}
[0.186208] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/multiplot_remap\n'}
[0.186404] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.sh\n'}
[0.186547] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.dsv\n'}
[0.186767] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/package_run_dependencies/ocs2_ros_interfaces\n'}
[0.186901] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/parent_prefix_path/ocs2_ros_interfaces\n'}
[0.186997] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.sh\n'}
[0.187075] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.dsv\n'}
[0.187152] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.sh\n'}
[0.187226] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.dsv\n'}
[0.187298] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.bash\n'}
[0.187404] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.sh\n'}
[0.187501] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.zsh\n'}
[0.187595] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.dsv\n'}
[0.187671] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.dsv\n'}
[0.195253] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/packages/ocs2_ros_interfaces\n'}
[0.195368] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.195512] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.195546] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig.cmake\n'}
[0.195571] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig-version.cmake\n'}
[0.195594] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.xml\n'}
[0.195637] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/libocs2_ros_interfaces.a\n'}
[0.195668] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport.cmake\n'}
[0.195692] (ocs2_ros_interfaces) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport-release.cmake\n'}
[0.197028] (ocs2_ros_interfaces) CommandEnded: {'returncode': 0}
[0.200012] (-) TimerEvent: {}
[0.204095] (ocs2_ros_interfaces) JobEnded: {'identifier': 'ocs2_ros_interfaces', 'rc': 0}
[0.204324] (-) EventReactorShutdown: {}
