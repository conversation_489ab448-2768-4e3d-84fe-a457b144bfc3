[0.000000] (-) TimerEvent: {}
[0.000584] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000619] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000639] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000657] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000721] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000740] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000758] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000776] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000793] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000809] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000839] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000884] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000902] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000945] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000971] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000989] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001042] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001210] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001255] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001278] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001316] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001344] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001362] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001379] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001402] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001449] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001483] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001509] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001527] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001542] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001558] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001600] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001616] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001633] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001650] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001665] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001681] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001790] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001812] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001920] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001942] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002030] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002052] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002224] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002374] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002523] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002562] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002591] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002630] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002680] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002739] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002768] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002866] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002884] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002900] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002919] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002938] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002956] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002974] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002989] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.003034] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.003136] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.003154] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.003168] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.003183] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.003197] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.003212] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.003227] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003242] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003270] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.003294] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003311] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003328] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003349] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003369] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003390] (ocs2_anymal_loopshaping_mpc) JobQueued: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'dependencies': OrderedDict([('blasfeo_colcon', '/home/<USER>/ros2_ws/install/blasfeo_colcon'), ('cgal5_colcon', '/home/<USER>/ros2_ws/install/cgal5_colcon'), ('convex_plane_decomposition_msgs', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs'), ('grid_map_filters_rsl', '/home/<USER>/ros2_ws/install/grid_map_filters_rsl'), ('grid_map_sdf', '/home/<USER>/ros2_ws/install/grid_map_sdf'), ('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('convex_plane_decomposition', '/home/<USER>/ros2_ws/install/convex_plane_decomposition'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_switched_model_msgs', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs'), ('convex_plane_decomposition_ros', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc'), ('ocs2_qp_solver', '/home/<USER>/ros2_ws/install/ocs2_qp_solver'), ('ocs2_robotic_tools', '/home/<USER>/ros2_ws/install/ocs2_robotic_tools'), ('hpipm_colcon', '/home/<USER>/ros2_ws/install/hpipm_colcon'), ('ocs2_ddp', '/home/<USER>/ros2_ws/install/ocs2_ddp'), ('ocs2_pinocchio_interface', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface'), ('ocs2_ros_interfaces', '/home/<USER>/ros2_ws/install/ocs2_ros_interfaces'), ('ocs2_sqp', '/home/<USER>/ros2_ws/install/ocs2_sqp'), ('ocs2_switched_model_interface', '/home/<USER>/ros2_ws/install/ocs2_switched_model_interface'), ('ocs2_anymal_commands', '/home/<USER>/ros2_ws/install/ocs2_anymal_commands'), ('ocs2_anymal_models', '/home/<USER>/ros2_ws/install/ocs2_anymal_models'), ('segmented_planes_terrain_model', '/home/<USER>/ros2_ws/install/segmented_planes_terrain_model'), ('ocs2_quadruped_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_interface'), ('ocs2_anymal_mpc', '/home/<USER>/ros2_ws/install/ocs2_anymal_mpc'), ('ocs2_quadruped_loopshaping_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface')])}
[0.003444] (ocs2_anymal_loopshaping_mpc) JobStarted: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.063504] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'cmake'}
[0.065427] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'build'}
[0.065490] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.099765] (-) TimerEvent: {}
[0.124046] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 11%] Built target gtest_main\n'}
[0.124512] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 22%] Built target gtest\n'}
[0.134888] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 33%] Built target ocs2_anymal_loopshaping_mpc\n'}
[0.160773] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o\x1b[0m\n'}
[0.173976] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 50%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[0.177494] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 61%] Built target ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[0.181088] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 77%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[0.181237] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 94%] Built target ocs2_anymal_loopshaping_mpc_test\n'}
[0.199875] (-) TimerEvent: {}
[0.300177] (-) TimerEvent: {}
[0.400402] (-) TimerEvent: {}
[0.500647] (-) TimerEvent: {}
[0.600894] (-) TimerEvent: {}
[0.701137] (-) TimerEvent: {}
[0.801350] (-) TimerEvent: {}
[0.901591] (-) TimerEvent: {}
[1.001837] (-) TimerEvent: {}
[1.102115] (-) TimerEvent: {}
[1.202532] (-) TimerEvent: {}
[1.303006] (-) TimerEvent: {}
[1.403492] (-) TimerEvent: {}
[1.503919] (-) TimerEvent: {}
[1.604378] (-) TimerEvent: {}
[1.704652] (-) TimerEvent: {}
[1.805013] (-) TimerEvent: {}
[1.905298] (-) TimerEvent: {}
[2.005610] (-) TimerEvent: {}
[2.105991] (-) TimerEvent: {}
[2.206306] (-) TimerEvent: {}
[2.306586] (-) TimerEvent: {}
[2.406901] (-) TimerEvent: {}
[2.507197] (-) TimerEvent: {}
[2.607496] (-) TimerEvent: {}
[2.707883] (-) TimerEvent: {}
[2.808292] (-) TimerEvent: {}
[2.908613] (-) TimerEvent: {}
[3.008923] (-) TimerEvent: {}
[3.109136] (-) TimerEvent: {}
[3.209467] (-) TimerEvent: {}
[3.309699] (-) TimerEvent: {}
[3.410118] (-) TimerEvent: {}
[3.510482] (-) TimerEvent: {}
[3.610785] (-) TimerEvent: {}
[3.711101] (-) TimerEvent: {}
[3.811514] (-) TimerEvent: {}
[3.911987] (-) TimerEvent: {}
[4.012367] (-) TimerEvent: {}
[4.112815] (-) TimerEvent: {}
[4.213298] (-) TimerEvent: {}
[4.313639] (-) TimerEvent: {}
[4.413965] (-) TimerEvent: {}
[4.514273] (-) TimerEvent: {}
[4.614598] (-) TimerEvent: {}
[4.714886] (-) TimerEvent: {}
[4.815182] (-) TimerEvent: {}
[4.915520] (-) TimerEvent: {}
[5.015916] (-) TimerEvent: {}
[5.116259] (-) TimerEvent: {}
[5.216572] (-) TimerEvent: {}
[5.316963] (-) TimerEvent: {}
[5.417285] (-) TimerEvent: {}
[5.517577] (-) TimerEvent: {}
[5.617869] (-) TimerEvent: {}
[5.718163] (-) TimerEvent: {}
[5.818510] (-) TimerEvent: {}
[5.918786] (-) TimerEvent: {}
[6.019234] (-) TimerEvent: {}
[6.119592] (-) TimerEvent: {}
[6.219913] (-) TimerEvent: {}
[6.320232] (-) TimerEvent: {}
[6.420570] (-) TimerEvent: {}
[6.520886] (-) TimerEvent: {}
[6.621183] (-) TimerEvent: {}
[6.721460] (-) TimerEvent: {}
[6.821834] (-) TimerEvent: {}
[6.922202] (-) TimerEvent: {}
[7.022478] (-) TimerEvent: {}
[7.122813] (-) TimerEvent: {}
[7.223179] (-) TimerEvent: {}
[7.323551] (-) TimerEvent: {}
[7.423824] (-) TimerEvent: {}
[7.524075] (-) TimerEvent: {}
[7.624300] (-) TimerEvent: {}
[7.724551] (-) TimerEvent: {}
[7.824803] (-) TimerEvent: {}
[7.925044] (-) TimerEvent: {}
[8.025268] (-) TimerEvent: {}
[8.125503] (-) TimerEvent: {}
[8.225737] (-) TimerEvent: {}
[8.325983] (-) TimerEvent: {}
[8.426216] (-) TimerEvent: {}
[8.526444] (-) TimerEvent: {}
[8.626688] (-) TimerEvent: {}
[8.726941] (-) TimerEvent: {}
[8.827150] (-) TimerEvent: {}
[8.927403] (-) TimerEvent: {}
[9.027706] (-) TimerEvent: {}
[9.127990] (-) TimerEvent: {}
[9.228263] (-) TimerEvent: {}
[9.328535] (-) TimerEvent: {}
[9.428800] (-) TimerEvent: {}
[9.529042] (-) TimerEvent: {}
[9.629227] (-) TimerEvent: {}
[9.729457] (-) TimerEvent: {}
[9.829687] (-) TimerEvent: {}
[9.929919] (-) TimerEvent: {}
[10.030208] (-) TimerEvent: {}
[10.130480] (-) TimerEvent: {}
[10.230777] (-) TimerEvent: {}
[10.331044] (-) TimerEvent: {}
[10.431324] (-) TimerEvent: {}
[10.531624] (-) TimerEvent: {}
[10.631933] (-) TimerEvent: {}
[10.732292] (-) TimerEvent: {}
[10.832620] (-) TimerEvent: {}
[10.932966] (-) TimerEvent: {}
[11.033232] (-) TimerEvent: {}
[11.133480] (-) TimerEvent: {}
[11.233787] (-) TimerEvent: {}
[11.334129] (-) TimerEvent: {}
[11.434337] (-) TimerEvent: {}
[11.534571] (-) TimerEvent: {}
[11.634911] (-) TimerEvent: {}
[11.735259] (-) TimerEvent: {}
[11.806270] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control\x1b[0m\n'}
[11.835388] (-) TimerEvent: {}
[11.935799] (-) TimerEvent: {}
[12.036158] (-) TimerEvent: {}
[12.136435] (-) TimerEvent: {}
[12.236761] (-) TimerEvent: {}
[12.337106] (-) TimerEvent: {}
[12.437440] (-) TimerEvent: {}
[12.537696] (-) TimerEvent: {}
[12.637902] (-) TimerEvent: {}
[12.738155] (-) TimerEvent: {}
[12.838565] (-) TimerEvent: {}
[12.939019] (-) TimerEvent: {}
[13.039376] (-) TimerEvent: {}
[13.139797] (-) TimerEvent: {}
[13.240063] (-) TimerEvent: {}
[13.340348] (-) TimerEvent: {}
[13.440637] (-) TimerEvent: {}
[13.541005] (-) TimerEvent: {}
[13.641327] (-) TimerEvent: {}
[13.680956] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[100%] Built target ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control\n'}
[13.692309] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 0}
[13.692811] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'install'}
[13.700196] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[13.706270] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[13.706515] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Execute custom install script\n'}
[13.706613] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/include/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc/AnymalLoopshapingInterface.h\n'}
[13.706756] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[13.706844] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[13.706879] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[13.706928] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control\n'}
[13.715320] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/frame_declaration.info\n'}
[13.715548] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/loopshaping.info\n'}
[13.715611] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/multiple_shooting.info\n'}
[13.715648] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/task.info\n'}
[13.715675] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/rviz/demo_config.rviz\n'}
[13.715699] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/anymal_c.launch.py\n'}
[13.715723] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/mpc.launch.py\n'}
[13.715747] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_demo.launch.py\n'}
[13.715771] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_keyboard_control.launch.py\n'}
[13.722962] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.sh\n'}
[13.723072] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.dsv\n'}
[13.723125] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_loopshaping_mpc\n'}
[13.723159] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_loopshaping_mpc\n'}
[13.723184] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.sh\n'}
[13.723209] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.dsv\n'}
[13.723234] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.sh\n'}
[13.723262] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.dsv\n'}
[13.723318] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.bash\n'}
[13.723366] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.sh\n'}
[13.723392] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.zsh\n'}
[13.723419] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.dsv\n'}
[13.723471] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv\n'}
[13.730509] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/packages/ocs2_anymal_loopshaping_mpc\n'}
[13.730626] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[13.730690] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_targets-extras.cmake\n'}
[13.730731] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig.cmake\n'}
[13.730759] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig-version.cmake\n'}
[13.730786] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.xml\n'}
[13.730837] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/libocs2_anymal_loopshaping_mpc.a\n'}
[13.731050] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Old export file "/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake].\n'}
[13.731144] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake\n'}
[13.731180] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake\n'}
[13.732813] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 0}
[13.741793] (-) TimerEvent: {}
[13.742125] (ocs2_anymal_loopshaping_mpc) JobEnded: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'rc': 0}
[13.742839] (-) EventReactorShutdown: {}
