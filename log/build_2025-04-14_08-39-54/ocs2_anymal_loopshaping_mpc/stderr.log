[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:223:25:[m[K [01;31m[Kerror: [m[K‘[01m[KgetSqpMpc[m[K’ is not a member of ‘[01m[Kocs2[m[K’; did you mean ‘[01m[Kswitched_model_loopshaping::getSqpMpc[m[K’?
  223 |     auto mpcPtr = ocs2::[01;31m[KgetSqpMpc[m[K(*anymalInterface, mpcSettings, sqpSettings);
      |                         [01;31m[K^~~~~~~~~[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
