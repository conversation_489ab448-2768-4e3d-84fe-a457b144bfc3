[0.018s] Invoking command in '/home/<USER>/ros2_ws/build/leg_pd_controller': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/leg_pd_controller -- -j32 -l32
[0.052s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.180s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.193s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.235s] -- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)
[0.255s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.259s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.266s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.279s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.292s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.467s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.469s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.485s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.495s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.515s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.524s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.540s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.581s] -- Found realtime_tools: 2.12.0 (/opt/ros/humble/share/realtime_tools/cmake)
[0.593s] -- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
[0.618s] -- ros2_control version below 3.0.0. Change the implementation to support ros2_control version 2.
[0.646s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.722s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.722s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/include>
[0.722s] -- Configured cppcheck exclude dirs and/or files: 
[0.722s] -- Added test 'lint_cmake' to check CMake code style
[0.723s] -- Added test 'uncrustify' to check C / C++ code style
[0.723s] -- Configured uncrustify additional arguments: 
[0.724s] -- Added test 'xmllint' to check XML markup files
[0.725s] -- Configuring done
[0.754s] -- Generating done
[0.759s] -- Build files have been written to: /home/<USER>/ros2_ws/build/leg_pd_controller
[0.796s] [35m[1mConsolidate compiler generated dependencies of target leg_pd_controller[0m
[0.820s] [ 50%] [32mBuilding CXX object CMakeFiles/leg_pd_controller.dir/src/LegPdController.cpp.o[0m
[2.471s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/include/leg_pd_controller/LegPdController.h:8[m[K,
[2.471s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/src/LegPdController.cpp:5[m[K:
[2.471s] [01m[K/opt/ros/humble/include/realtime_tools/realtime_tools/realtime_buffer.h:22:100:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header.[m[K’
[2.472s]    22 |   "This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header."[01;36m[K)[m[K  //NOLINT
[2.472s]       |                                                                                                    [01;36m[K^[m[K
[3.487s] [100%] [32m[1mLinking CXX shared library libleg_pd_controller.so[0m
[3.618s] [100%] Built target leg_pd_controller
[3.632s] Invoked command in '/home/<USER>/ros2_ws/build/leg_pd_controller' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/leg_pd_controller -- -j32 -l32
[3.648s] Invoking command in '/home/<USER>/ros2_ws/build/leg_pd_controller': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/leg_pd_controller
[3.661s] -- Install configuration: "Release"
[3.661s] -- Execute custom install script
[3.661s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller//leg_pd_controller.xml
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/include/leg_pd_controller/leg_pd_controller/LegPdController.h
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.sh
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.dsv
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/package_run_dependencies/leg_pd_controller
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/parent_prefix_path/leg_pd_controller
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.sh
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.dsv
[3.662s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.sh
[3.663s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.dsv
[3.663s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.bash
[3.663s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.sh
[3.663s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.zsh
[3.663s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.dsv
[3.663s] -- Symlinking: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.dsv
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/packages/leg_pd_controller
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/leg_pd_controller
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_targets-extras.cmake
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig.cmake
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig-version.cmake
[3.673s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.xml
[3.683s] -- Installing: /home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so
[3.683s] -- Set runtime path of "/home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so" to ""
[3.683s] -- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport.cmake
[3.683s] -- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport-release.cmake
[3.686s] Invoked command in '/home/<USER>/ros2_ws/build/leg_pd_controller' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/leg_pd_controller
