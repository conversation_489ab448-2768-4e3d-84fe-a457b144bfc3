-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
-- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found realtime_tools: 2.12.0 (/opt/ros/humble/share/realtime_tools/cmake)
-- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
-- ros2_control version below 3.0.0. Change the implementation to support ros2_control version 2.
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/leg_pd_controller
[35m[1mConsolidate compiler generated dependencies of target leg_pd_controller[0m
[ 50%] [32mBuilding CXX object CMakeFiles/leg_pd_controller.dir/src/LegPdController.cpp.o[0m
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/include/leg_pd_controller/LegPdController.h:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/leg_pd_controller/src/LegPdController.cpp:5[m[K:
[01m[K/opt/ros/humble/include/realtime_tools/realtime_tools/realtime_buffer.h:22:100:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header.[m[K’
   22 |   "This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header."[01;36m[K)[m[K  //NOLINT
      |                                                                                                    [01;36m[K^[m[K
[100%] [32m[1mLinking CXX shared library libleg_pd_controller.so[0m
[100%] Built target leg_pd_controller
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller//leg_pd_controller.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/include/leg_pd_controller/leg_pd_controller/LegPdController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/package_run_dependencies/leg_pd_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/parent_prefix_path/leg_pd_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/packages/leg_pd_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/leg_pd_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.xml
-- Installing: /home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so
-- Set runtime path of "/home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so" to ""
-- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport-release.cmake
