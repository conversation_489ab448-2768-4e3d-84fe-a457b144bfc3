[0.005s] Invoking command in '/home/<USER>/ros2_ws/build/go1_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go1_description -- -j32 -l32
[0.035s] Invoked command in '/home/<USER>/ros2_ws/build/go1_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go1_description -- -j32 -l32
[0.057s] Invoking command in '/home/<USER>/ros2_ws/build/go1_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go1_description
[0.079s] -- Install configuration: ""
[0.080s] -- Execute custom install script
[0.081s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/calf.dae
[0.081s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/depthCamera.dae
[0.082s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/hip.dae
[0.082s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh.dae
[0.082s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/thigh_mirror.dae
[0.082s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/trunk.dae
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//meshes/ultraSound.dae
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/const.xacro
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/depthCamera.xacro
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ft_sensor.xacro
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo.xacro
[0.083s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/gazebo_classic.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/leg.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/materials.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/robot.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ros2_control.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/transmission.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//xacro/ultraSound.xacro
[0.084s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//launch/visualize.launch.py
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/gazebo.yaml
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/gait.info
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/reference.info
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/ocs2/task.info
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/robot_control.yaml
[0.085s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//config/visualize_urdf.rviz
[0.086s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description//urdf/robot.urdf
[0.086s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/package_run_dependencies/go1_description
[0.086s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/parent_prefix_path/go1_description
[0.086s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.sh
[0.086s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/ament_prefix_path.dsv
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.sh
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/environment/path.dsv
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.bash
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.sh
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.zsh
[0.087s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/local_setup.dsv
[0.087s] -- Symlinking: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.dsv
[0.097s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/ament_index/resource_index/packages/go1_description
[0.097s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig.cmake
[0.097s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/cmake/go1_descriptionConfig-version.cmake
[0.097s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/go1_description/share/go1_description/package.xml
[0.099s] Invoked command in '/home/<USER>/ros2_ws/build/go1_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go1_description
