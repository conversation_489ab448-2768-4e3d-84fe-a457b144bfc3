[0.015s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[0.087s] [ 50%] Built target gz_quadruped_hardware-system
[0.088s] [100%] Built target gz_quadruped_plugins
[0.098s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[0.112s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/gz_quadruped_hardware
[0.120s] -- Install configuration: ""
[0.121s] -- Execute custom install script
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_plugins.so
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_quadruped_plugin.hpp
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system.hpp
[0.121s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system_interface.hpp
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//xacro/foot_force_sensor.xacro
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.sh
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.dsv
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_hardware-system.so
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//gz_quadruped_hardware.xml
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_hardware
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_hardware
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.sh
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.dsv
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.sh
[0.122s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.dsv
[0.123s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.bash
[0.123s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.sh
[0.123s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.zsh
[0.123s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.dsv
[0.123s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.dsv
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/packages/gz_quadruped_hardware
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/gz_quadruped_hardware__pluginlib__plugin/gz_quadruped_hardware
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_include_directories-extras.cmake
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_libraries-extras.cmake
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig.cmake
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig-version.cmake
[0.135s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.xml
[0.137s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/gz_quadruped_hardware
