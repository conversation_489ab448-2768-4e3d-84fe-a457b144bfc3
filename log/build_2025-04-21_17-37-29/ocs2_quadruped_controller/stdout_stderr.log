[35m[1mConsolidate compiler generated dependencies of target ocs2_quadruped_controller[0m
[  2%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/Ocs2QuadrupedController.cpp.o[0m
[  5%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/FSM/StateOCS2.cpp.o[0m
[  8%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o[0m
[ 11%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/control/CtrlComponent.cpp.o[0m
[ 14%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/interface/PerceptiveLeggedInterface.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:7[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:23[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:24[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:26[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:27[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:28[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:29[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::LeggedRobotStateQuadraticCost::getStateDeviation(ocs2::scalar_t, const vector_t&, const ocs2::TargetTrajectories&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:93:24:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[KcontactFlags[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
   93 |             const auto [01;35m[KcontactFlags[m[K = referenceManagerPtr_->getContactFlags(time);
      |                        [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedInterface::setupOptimalControlProblem(const string&, const string&, const string&, bool)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:163:93:[m[K [01;31m[Kerror: [m[K‘[01m[Kprefix[m[K’ was not declared in this scope; did you mean ‘[01m[Kprofil[m[K’?
  163 |                                                       *pinocchio_interface_ptr_, task_file, [01;31m[Kprefix[m[K,
      |                                                                                             [01;31m[K^~~~~~[m[K
      |                                                                                             [32m[Kprofil[m[K
compilation terminated due to -Wfatal-errors.
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
gmake[2]: *** [CMakeFiles/ocs2_quadruped_controller.dir/build.make:342: CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o] Error 1
gmake[2]: *** Waiting for unfinished jobs....
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:11[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PerceptiveLeggedInterface::setupOptimalControlProblem(const string&, const string&, const string&, bool)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:85:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KthighExcess[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   85 |         scalar_t [01;35m[KthighExcess[m[K = 0.025;
      |                  [01;35m[K^~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:21[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PlanarTerrainReceiver::postSolverRun(const ocs2::PrimalSolution&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:32:50:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KprimalSolution[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   32 |         void postSolverRun([01;35m[Kconst PrimalSolution& primalSolution[m[K) override
      |                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/ocs2_quadruped_controller.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
