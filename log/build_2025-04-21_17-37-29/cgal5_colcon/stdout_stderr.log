[100%] Built target cgal
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/ament_index/resource_index/package_run_dependencies/cgal5_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/ament_index/resource_index/parent_prefix_path/cgal5_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/ament_index/resource_index/packages/cgal5_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/cmake/cgal-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/cmake/cgal5_colconConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/cmake/cgal5_colconConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/package.xml
