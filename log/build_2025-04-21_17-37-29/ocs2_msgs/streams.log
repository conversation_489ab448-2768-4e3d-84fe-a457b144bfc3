[0.028s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_msgs -- -j32 -l32
[0.105s] [  0%] Built target ocs2_msgs__cpp
[0.143s] [ 12%] Built target ocs2_msgs__rosidl_generator_c
[0.145s] [ 12%] Built target ament_cmake_python_symlink_ocs2_msgs
[0.240s] [ 24%] Built target ocs2_msgs__rosidl_typesupport_c
[0.251s] [ 36%] Built target ocs2_msgs__rosidl_typesupport_cpp
[0.253s] [ 47%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_cpp
[0.260s] [ 71%] Built target ocs2_msgs__rosidl_typesupport_introspection_cpp
[0.260s] [ 71%] Built target ocs2_msgs__rosidl_typesupport_introspection_c
[0.266s] [ 83%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_c
[0.276s] [ 83%] Built target ocs2_msgs
[0.276s] [ 84%] Built target ocs2_msgs__py
[0.280s] [ 95%] Built target ocs2_msgs__rosidl_generator_py
[0.283s] [ 97%] Built target ocs2_msgs__rosidl_typesupport_introspection_c__pyext
[0.284s] [ 99%] Built target ocs2_msgs__rosidl_typesupport_fastrtps_c__pyext
[0.284s] [100%] Built target ocs2_msgs__rosidl_typesupport_c__pyext
[0.393s] running egg_info
[0.393s] writing ocs2_msgs.egg-info/PKG-INFO
[0.393s] writing dependency_links to ocs2_msgs.egg-info/dependency_links.txt
[0.394s] writing top-level names to ocs2_msgs.egg-info/top_level.txt
[0.398s] reading manifest file 'ocs2_msgs.egg-info/SOURCES.txt'
[0.398s] writing manifest file 'ocs2_msgs.egg-info/SOURCES.txt'
[0.427s] [100%] Built target ament_cmake_python_build_ocs2_msgs_egg
[0.443s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_msgs -- -j32 -l32
[0.445s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_msgs
[0.461s] -- Install configuration: ""
[0.465s] -- Execute custom install script
[0.465s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/rosidl_interfaces/ocs2_msgs
[0.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/constraint.h
[0.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/controller_data.h
[0.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__functions.h
[0.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__struct.h
[0.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__type_support.h
[0.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__functions.h
[0.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__struct.h
[0.469s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__type_support.h
[0.470s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__functions.h
[0.470s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__struct.h
[0.471s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__type_support.h
[0.472s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__functions.h
[0.472s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__struct.h
[0.473s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__type_support.h
[0.473s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__functions.h
[0.473s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__struct.h
[0.473s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__type_support.h
[0.474s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__functions.h
[0.474s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__struct.h
[0.474s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__type_support.h
[0.474s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__functions.h
[0.474s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__struct.h
[0.475s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__type_support.h
[0.475s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__functions.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__struct.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__type_support.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__functions.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__struct.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__type_support.h
[0.476s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__functions.h
[0.477s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__struct.h
[0.477s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__type_support.h
[0.477s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__functions.h
[0.477s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__struct.h
[0.477s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__type_support.h
[0.478s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/lagrangian_metrics.h
[0.478s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mode_schedule.h
[0.478s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_flattened_controller.h
[0.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_input.h
[0.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_observation.h
[0.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_performance_indices.h
[0.480s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_state.h
[0.480s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_target_trajectories.h
[0.480s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/multiplier.h
[0.480s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_generator_c__visibility_control.h
[0.481s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__functions.h
[0.481s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__struct.h
[0.481s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__type_support.h
[0.481s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/reset.h
[0.482s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/library_path.sh
[0.483s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/library_path.dsv
[0.483s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_fastrtps_c.h
[0.484s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_fastrtps_c.h
[0.484s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_fastrtps_c.h
[0.484s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_fastrtps_c.h
[0.484s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_fastrtps_c.h
[0.484s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_fastrtps_c.h
[0.485s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_fastrtps_c.h
[0.485s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_fastrtps_c.h
[0.485s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_fastrtps_c.h
[0.485s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_fastrtps_c.h
[0.486s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_fastrtps_c.h
[0.486s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[0.486s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_fastrtps_c.h
[0.486s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_introspection_c.h
[0.486s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_introspection_c.h
[0.487s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_introspection_c.h
[0.488s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h
[0.488s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_introspection_c.h
[0.489s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/constraint.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/controller_data.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__builder.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__struct.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__traits.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__type_support.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__builder.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__struct.hpp
[0.490s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__traits.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__type_support.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__builder.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__struct.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__traits.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__type_support.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__builder.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__struct.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__traits.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__type_support.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__builder.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__struct.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__traits.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__type_support.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__builder.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__struct.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__traits.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__type_support.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__builder.hpp
[0.491s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__struct.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__traits.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__type_support.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__builder.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__struct.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__traits.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__type_support.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__builder.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__struct.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__traits.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__type_support.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__builder.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__struct.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__traits.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__type_support.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__builder.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__struct.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__traits.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__type_support.hpp
[0.492s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/lagrangian_metrics.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mode_schedule.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_flattened_controller.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_input.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_observation.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_performance_indices.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_state.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/mpc_target_trajectories.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/multiplier.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__builder.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__struct.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__traits.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__type_support.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/reset.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_fastrtps_cpp.hpp
[0.493s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_fastrtps_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/constraint__rosidl_typesupport_introspection_cpp.hpp
[0.494s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/controller_data__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/lagrangian_metrics__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mode_schedule__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_flattened_controller__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_input__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_observation__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_performance_indices__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_state__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/mpc_target_trajectories__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/msg/detail/multiplier__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/include/ocs2_msgs/ocs2_msgs/srv/detail/reset__rosidl_typesupport_introspection_cpp.hpp
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/pythonpath.sh
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/pythonpath.dsv
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/PKG-INFO
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/SOURCES.txt
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/dependency_links.txt
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs-0.0.0-py3.10.egg-info/top_level.txt
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/__init__.py
[0.495s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_c.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_fastrtps_c.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/_ocs2_msgs_s.ep.rosidl_typesupport_introspection_c.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/libocs2_msgs__rosidl_generator_py.so
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/__init__.py
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_constraint.py
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_constraint_s.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_controller_data.py
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_controller_data_s.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_lagrangian_metrics.py
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_lagrangian_metrics_s.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mode_schedule.py
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mode_schedule_s.c
[0.496s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_flattened_controller.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_flattened_controller_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_input.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_input_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_observation.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_observation_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_performance_indices.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_performance_indices_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_state.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_state_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_target_trajectories.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_mpc_target_trajectories_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_multiplier.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg/_multiplier_s.c
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/__init__.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/_reset.py
[0.497s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv/_reset_s.c
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/ocs2_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcState.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcInput.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ModeSchedule.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcObservation.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcPerformanceIndices.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcTargetTrajectories.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ControllerData.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcFlattenedController.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/LagrangianMetrics.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Multiplier.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Constraint.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset.idl
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcState.msg
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcInput.msg
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ModeSchedule.msg
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcObservation.msg
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcPerformanceIndices.msg
[0.498s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcTargetTrajectories.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/ControllerData.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/MpcFlattenedController.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/LagrangianMetrics.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Multiplier.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/msg/Constraint.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset.srv
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset_Request.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/srv/Reset_Response.msg
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/package_run_dependencies/ocs2_msgs
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/parent_prefix_path/ocs2_msgs
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/ament_prefix_path.sh
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/ament_prefix_path.dsv
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/path.sh
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/environment/path.dsv
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.bash
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.sh
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.zsh
[0.499s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/local_setup.dsv
[0.499s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/package.dsv
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ament_index/resource_index/packages/ocs2_msgs
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_include_directories-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_libraries-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_targets-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ament_cmake_export_dependencies-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgsConfig.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgsConfig-version.cmake
[0.500s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/package.xml
[0.500s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_generator_c.so
[0.501s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_fastrtps_c.so
[0.501s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_introspection_c.so
[0.501s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_c.so
[0.501s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_fastrtps_cpp.so
[0.501s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_introspection_cpp.so
[0.502s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_typesupport_cpp.so
[0.538s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs'...
[0.539s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/msg'...
[0.539s] Listing '/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages/ocs2_msgs/srv'...
[0.543s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/lib/libocs2_msgs__rosidl_generator_py.so
[0.544s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cExport.cmake
[0.545s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cExport-noconfig.cmake
[0.545s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cExport.cmake
[0.545s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[0.546s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cExport.cmake
[0.546s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake
[0.546s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cExport.cmake
[0.546s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cExport-noconfig.cmake
[0.547s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_cppExport.cmake
[0.547s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cppExport.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cppExport.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cppExport.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/ocs2_msgs__rosidl_typesupport_cppExport-noconfig.cmake
[0.548s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_pyExport.cmake
[0.549s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake/export_ocs2_msgs__rosidl_generator_pyExport-noconfig.cmake
[0.571s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_msgs
