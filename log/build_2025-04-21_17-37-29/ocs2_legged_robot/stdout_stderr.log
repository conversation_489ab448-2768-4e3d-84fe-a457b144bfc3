[  7%] Built target gtest_main
[ 14%] Built target gtest
[ 82%] Built target ocs2_legged_robot
[100%] Built target ocs2_legged_robot_test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotPreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/ModelSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/utils.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/EndEffectorLinearConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/FrictionConeConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/NormalVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroForceConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/cost/LeggedRobotQuadraticTrackingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/dynamics/LeggedRobotDynamicsAD.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/CubicSpline.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SplineCpg.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SwingTrajectoryPlanner.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/Gait.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/GaitSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/LegLogic.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/ModeSequenceTemplate.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/MotionPhaseDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/initialization/LeggedRobotInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/package_path.h.in
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/reference_manager/SwitchedModelReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/gait.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/reference.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/mpc/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/friction_cone.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/zero_velocity.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/package_run_dependencies/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/parent_prefix_path/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/packages/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/lib/libocs2_legged_robot.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport-release.cmake
