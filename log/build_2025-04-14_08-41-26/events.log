[0.000000] (-) TimerEvent: {}
[0.000409] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000435] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000446] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000454] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000463] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000471] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000478] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000486] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000494] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000502] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000509] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000517] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000525] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000532] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000539] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000547] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000555] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000563] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000571] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000579] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000586] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000594] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000602] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000610] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000617] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000625] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000633] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000641] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000648] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000656] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000664] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000671] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000678] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.000686] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.000694] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.000701] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.000709] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.000716] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.000724] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.000732] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.000740] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.000747] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.000755] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.000763] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.000771] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.000778] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.000786] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.000793] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.000801] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.000808] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.000816] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.000824] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.000831] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.000839] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.000846] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.000854] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.000862] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.000869] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.000877] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.000884] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.000892] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.000910] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.000918] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.000926] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.000962] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.000970] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.000978] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.000986] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.000993] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001001] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001009] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001016] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001024] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001032] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.001039] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001049] (ocs2_anymal_loopshaping_mpc) JobQueued: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'dependencies': OrderedDict([('blasfeo_colcon', '/home/<USER>/ros2_ws/install/blasfeo_colcon'), ('cgal5_colcon', '/home/<USER>/ros2_ws/install/cgal5_colcon'), ('convex_plane_decomposition_msgs', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs'), ('grid_map_filters_rsl', '/home/<USER>/ros2_ws/install/grid_map_filters_rsl'), ('grid_map_sdf', '/home/<USER>/ros2_ws/install/grid_map_sdf'), ('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('convex_plane_decomposition', '/home/<USER>/ros2_ws/install/convex_plane_decomposition'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_switched_model_msgs', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs'), ('convex_plane_decomposition_ros', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc'), ('ocs2_qp_solver', '/home/<USER>/ros2_ws/install/ocs2_qp_solver'), ('ocs2_robotic_tools', '/home/<USER>/ros2_ws/install/ocs2_robotic_tools'), ('hpipm_colcon', '/home/<USER>/ros2_ws/install/hpipm_colcon'), ('ocs2_ddp', '/home/<USER>/ros2_ws/install/ocs2_ddp'), ('ocs2_pinocchio_interface', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface'), ('ocs2_ros_interfaces', '/home/<USER>/ros2_ws/install/ocs2_ros_interfaces'), ('ocs2_sqp', '/home/<USER>/ros2_ws/install/ocs2_sqp'), ('ocs2_switched_model_interface', '/home/<USER>/ros2_ws/install/ocs2_switched_model_interface'), ('ocs2_anymal_commands', '/home/<USER>/ros2_ws/install/ocs2_anymal_commands'), ('ocs2_anymal_models', '/home/<USER>/ros2_ws/install/ocs2_anymal_models'), ('segmented_planes_terrain_model', '/home/<USER>/ros2_ws/install/segmented_planes_terrain_model'), ('ocs2_quadruped_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_interface'), ('ocs2_anymal_mpc', '/home/<USER>/ros2_ws/install/ocs2_anymal_mpc'), ('ocs2_quadruped_loopshaping_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface')])}
[0.001084] (ocs2_anymal_loopshaping_mpc) JobStarted: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.064641] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'cmake'}
[0.064836] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'build'}
[0.065462] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.099666] (-) TimerEvent: {}
[0.121293] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 11%] Built target gtest\n'}
[0.121633] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 22%] Built target gtest_main\n'}
[0.134563] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 33%] Built target ocs2_anymal_loopshaping_mpc\n'}
[0.158259] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o\x1b[0m\n'}
[0.171561] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 50%] Built target ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[0.172888] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 61%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[0.177921] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 83%] Built target ocs2_anymal_loopshaping_mpc_test\n'}
[0.182274] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 94%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[0.199759] (-) TimerEvent: {}
[0.300036] (-) TimerEvent: {}
[0.400285] (-) TimerEvent: {}
[0.500539] (-) TimerEvent: {}
[0.600794] (-) TimerEvent: {}
[0.701017] (-) TimerEvent: {}
[0.801266] (-) TimerEvent: {}
[0.901597] (-) TimerEvent: {}
[1.001865] (-) TimerEvent: {}
[1.102114] (-) TimerEvent: {}
[1.202364] (-) TimerEvent: {}
[1.302648] (-) TimerEvent: {}
[1.402967] (-) TimerEvent: {}
[1.503297] (-) TimerEvent: {}
[1.603577] (-) TimerEvent: {}
[1.703897] (-) TimerEvent: {}
[1.804142] (-) TimerEvent: {}
[1.904397] (-) TimerEvent: {}
[2.004706] (-) TimerEvent: {}
[2.104930] (-) TimerEvent: {}
[2.205222] (-) TimerEvent: {}
[2.305534] (-) TimerEvent: {}
[2.405772] (-) TimerEvent: {}
[2.506022] (-) TimerEvent: {}
[2.606275] (-) TimerEvent: {}
[2.706542] (-) TimerEvent: {}
[2.806850] (-) TimerEvent: {}
[2.907195] (-) TimerEvent: {}
[3.007501] (-) TimerEvent: {}
[3.107834] (-) TimerEvent: {}
[3.208104] (-) TimerEvent: {}
[3.308372] (-) TimerEvent: {}
[3.408652] (-) TimerEvent: {}
[3.508932] (-) TimerEvent: {}
[3.609252] (-) TimerEvent: {}
[3.709528] (-) TimerEvent: {}
[3.809788] (-) TimerEvent: {}
[3.910063] (-) TimerEvent: {}
[4.010318] (-) TimerEvent: {}
[4.110537] (-) TimerEvent: {}
[4.210842] (-) TimerEvent: {}
[4.311167] (-) TimerEvent: {}
[4.411411] (-) TimerEvent: {}
[4.511714] (-) TimerEvent: {}
[4.612106] (-) TimerEvent: {}
[4.712361] (-) TimerEvent: {}
[4.812693] (-) TimerEvent: {}
[4.913021] (-) TimerEvent: {}
[5.013270] (-) TimerEvent: {}
[5.113486] (-) TimerEvent: {}
[5.213763] (-) TimerEvent: {}
[5.314115] (-) TimerEvent: {}
[5.414428] (-) TimerEvent: {}
[5.514704] (-) TimerEvent: {}
[5.615039] (-) TimerEvent: {}
[5.715305] (-) TimerEvent: {}
[5.815626] (-) TimerEvent: {}
[5.915862] (-) TimerEvent: {}
[6.016110] (-) TimerEvent: {}
[6.116364] (-) TimerEvent: {}
[6.216590] (-) TimerEvent: {}
[6.316861] (-) TimerEvent: {}
[6.417122] (-) TimerEvent: {}
[6.517354] (-) TimerEvent: {}
[6.617616] (-) TimerEvent: {}
[6.717983] (-) TimerEvent: {}
[6.818312] (-) TimerEvent: {}
[6.918657] (-) TimerEvent: {}
[7.019072] (-) TimerEvent: {}
[7.119266] (-) TimerEvent: {}
[7.219592] (-) TimerEvent: {}
[7.319809] (-) TimerEvent: {}
[7.420074] (-) TimerEvent: {}
[7.520296] (-) TimerEvent: {}
[7.620497] (-) TimerEvent: {}
[7.720756] (-) TimerEvent: {}
[7.726452] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.726602] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:237:13:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass ocs2::MPC_BASE\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KsetReferenceManager\x1b[m\x1b[K\xe2\x80\x99\n'}
[7.726668] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'  237 |     mpcPtr->\x1b[01;31m\x1b[KsetReferenceManager\x1b[m\x1b[K(rosReferenceManagerPtr);\n'}
[7.726725] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'      |             \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[7.726780] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'compilation terminated due to -Wfatal-errors.\n'}
[7.781336] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1\n'}
[7.781620] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2\n'}
[7.781715] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[7.783810] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 2}
[7.790753] (ocs2_anymal_loopshaping_mpc) JobEnded: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'rc': 2}
[7.801211] (-) EventReactorShutdown: {}
