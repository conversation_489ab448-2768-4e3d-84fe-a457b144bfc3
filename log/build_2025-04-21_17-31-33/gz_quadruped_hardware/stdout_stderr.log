-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
-- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
-- Compiling against Gazebo fortress
-- Looking for ignition-gazebo6 -- found version 6.17.0
-- Searching for dependencies of ignition-gazebo6
-- Looking for sdformat12 -- found version 12.8.0
-- Searching for dependencies of sdformat12
-- Checking for module 'tinyxml2'
--   Found tinyxml2, version 9.0.0
-- Looking for ignition-math6 -- found version 6.15.1
-- Searching for dependencies of ignition-math6
-- Looking for ignition-utils1 -- found version 1.5.1
-- Searching for dependencies of ignition-utils1
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Searching for dependencies of ignition-plugin1
-- Searching for <ignition-plugin1> component [loader]
-- Looking for ignition-plugin1-loader -- found version 1.4.0
-- Searching for dependencies of ignition-plugin1-loader
-- Searching for <ignition-plugin1> component [register]
-- Looking for ignition-plugin1-register -- found version 1.4.0
-- Searching for dependencies of ignition-plugin1-register
-- Looking for ignition-transport11 -- found version 11.4.1
-- Searching for dependencies of ignition-transport11
-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") 
-- Config-file not installed for ZeroMQ -- checking for pkg-config
-- Checking for module 'libzmq >= 4'
--   Found libzmq , version 4.3.4
-- Found ZeroMQ: TRUE (Required is at least version "4") 
-- Checking for module 'uuid'
--   Found uuid, version 2.37.2
-- Found UUID: TRUE  
-- Looking for ignition-utils1 -- found version 1.5.1
-- Searching for dependencies of ignition-utils1
-- Searching for <ignition-utils1> component [cli]
-- Looking for ignition-utils1-cli -- found version 1.5.1
-- Searching for dependencies of ignition-utils1-cli
-- Looking for ignition-msgs8 -- found version 8.7.0
-- Searching for dependencies of ignition-msgs8
-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found suitable version "3.12.4", minimum required is "3") 
-- Looking for ignition-math6 -- found version 6.15.1
-- Searching for <ignition-transport11> component [log]
-- Looking for ignition-transport11-log -- found version 11.4.1
-- Searching for dependencies of ignition-transport11-log
-- Searching for <ignition-transport11> component [parameters]
-- Looking for ignition-transport11-parameters -- found version 11.4.1
-- Searching for dependencies of ignition-transport11-parameters
-- Looking for ignition-msgs8 -- found version 8.7.0
-- Looking for ignition-common4 -- found version 4.7.0
-- Searching for dependencies of ignition-common4
-- Looking for dlfcn.h - found
-- Looking for libdl - found
-- Found DL: TRUE  
-- Searching for <ignition-common4> component [profiler]
-- Looking for ignition-common4-profiler -- found version 4.7.0
-- Searching for dependencies of ignition-common4-profiler
-- Searching for <ignition-common4> component [events]
-- Looking for ignition-common4-events -- found version 4.7.0
-- Searching for dependencies of ignition-common4-events
-- Looking for ignition-math6 -- found version 6.15.1
-- Searching for <ignition-common4> component [av]
-- Looking for ignition-common4-av -- found version 4.7.0
-- Searching for dependencies of ignition-common4-av
-- Checking for module 'libswscale'
--   Found libswscale, version 5.9.100
-- Found SWSCALE: TRUE  
-- Checking for module 'libavdevice >= 56.4.100'
--   Found libavdevice , version 58.13.100
-- Found AVDEVICE: TRUE (Required is at least version "56.4.100") 
-- Checking for module 'libavformat'
--   Found libavformat, version 58.76.100
-- Found AVFORMAT: TRUE  
-- Checking for module 'libavcodec'
--   Found libavcodec, version 58.134.100
-- Found AVCODEC: TRUE  
-- Checking for module 'libavutil'
--   Found libavutil, version 56.70.100
-- Found AVUTIL: TRUE  
-- Looking for ignition-fuel_tools7 -- found version 7.3.1
-- Searching for dependencies of ignition-fuel_tools7
-- Found CURL: /usr/lib/x86_64-linux-gnu/libcurl.so (found version "7.81.0")  
-- Checking for module 'jsoncpp'
--   Found jsoncpp, version 1.9.5
-- Found JSONCPP: TRUE  
-- Checking for module 'libzip'
--   Found libzip, version 1.7.3
-- Found ZIP: TRUE  
-- Looking for ignition-common4 -- found version 4.7.0
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-msgs8 -- found version 8.7.0
-- Looking for ignition-gui6 -- found version 6.8.0
-- Searching for dependencies of ignition-gui6
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-common4 -- found version 4.7.0
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Looking for ignition-transport11 -- found version 11.4.1
-- Looking for ignition-rendering6 -- found version 6.6.3
-- Searching for dependencies of ignition-rendering6
-- Looking for ignition-math6 -- found version 6.15.1
-- Searching for dependencies of ignition-math6
-- Searching for <ignition-math6> component [eigen3]
-- Looking for ignition-math6-eigen3 -- found version 6.15.1
-- Searching for dependencies of ignition-math6-eigen3
-- Looking for ignition-common4 -- found version 4.7.0
-- Searching for dependencies of ignition-common4
-- Looking for dlfcn.h - found
-- Looking for libdl - found
-- Searching for <ignition-common4> component [graphics]
-- Looking for ignition-common4-graphics -- found version 4.7.0
-- Searching for dependencies of ignition-common4-graphics
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-utils1 -- found version 1.5.1
-- Searching for <ignition-common4> component [events]
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Searching for dependencies of ignition-plugin1
-- Searching for <ignition-plugin1> component [all]
-- Looking for all libraries of ignition-plugin1 -- found version 1.4.0
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Looking for ignition-msgs8 -- found version 8.7.0
-- Looking for ignition-physics5 -- found version 5.3.2
-- Searching for dependencies of ignition-physics5
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Looking for ignition-utils1 -- found version 1.5.1
-- Searching for <ignition-physics5> component [heightmap]
-- Looking for ignition-physics5-heightmap -- found version 5.3.2
-- Searching for dependencies of ignition-physics5-heightmap
-- Looking for ignition-common4 -- found version 4.7.0
-- Searching for <ignition-physics5> component [mesh]
-- Looking for ignition-physics5-mesh -- found version 5.3.2
-- Searching for dependencies of ignition-physics5-mesh
-- Looking for ignition-common4 -- found version 4.7.0
-- Searching for <ignition-physics5> component [sdf]
-- Looking for ignition-physics5-sdf -- found version 5.3.2
-- Searching for dependencies of ignition-physics5-sdf
-- Looking for sdformat12 -- found version 12.8.0
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6
-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") 
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-common4 -- found version 4.7.0
-- Looking for ignition-transport11 -- found version 11.4.1
-- Looking for ignition-rendering6 -- found version 6.6.3
-- Looking for ignition-msgs8 -- found version 8.7.0
-- Looking for sdformat12 -- found version 12.8.0
-- Searching for <ignition-sensors6> component [air_pressure]
-- Looking for ignition-sensors6-air_pressure -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-air_pressure
-- Searching for <ignition-sensors6> component [altimeter]
-- Looking for ignition-sensors6-altimeter -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-altimeter
-- Searching for <ignition-sensors6> component [imu]
-- Looking for ignition-sensors6-imu -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-imu
-- Searching for <ignition-sensors6> component [force_torque]
-- Looking for ignition-sensors6-force_torque -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-force_torque
-- Searching for <ignition-sensors6> component [logical_camera]
-- Looking for ignition-sensors6-logical_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-logical_camera
-- Searching for <ignition-sensors6> component [magnetometer]
-- Looking for ignition-sensors6-magnetometer -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-magnetometer
-- Searching for <ignition-sensors6> component [navsat]
-- Looking for ignition-sensors6-navsat -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-navsat
-- Searching for <ignition-sensors6> component [rendering]
-- Looking for ignition-sensors6-rendering -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-rendering
-- Searching for <ignition-sensors6> component [lidar]
-- Looking for ignition-sensors6-lidar -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-lidar
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for <ignition-sensors6> component [gpu_lidar]
-- Looking for ignition-sensors6-gpu_lidar -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-gpu_lidar
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for <ignition-sensors6> component [camera]
-- Looking for ignition-sensors6-camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-camera
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for <ignition-sensors6> component [boundingbox_camera]
-- Looking for ignition-sensors6-boundingbox_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-boundingbox_camera
-- Searching for <ignition-sensors6> component [segmentation_camera]
-- Looking for ignition-sensors6-segmentation_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-segmentation_camera
-- Searching for <ignition-sensors6> component [depth_camera]
-- Looking for ignition-sensors6-depth_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-depth_camera
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for <ignition-sensors6> component [rgbd_camera]
-- Looking for ignition-sensors6-rgbd_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-rgbd_camera
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Searching for <ignition-sensors6> component [thermal_camera]
-- Looking for ignition-sensors6-thermal_camera -- found version 6.8.1
-- Searching for dependencies of ignition-sensors6-thermal_camera
-- Looking for ignition-sensors6 -- found version 6.8.1
-- Looking for ignition-rendering6 -- found version 6.6.3
-- Looking for ignition-math6 -- found version 6.15.1
-- Looking for ignition-utils1 -- found version 1.5.1
-- Looking for ignition-plugin1 -- found version 1.4.0
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/gz_quadruped_hardware
[ 25%] [32mBuilding CXX object CMakeFiles/gz_quadruped_plugins.dir/src/gz_system.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/gz_quadruped_hardware-system.dir/src/gz_quadruped_plugin.cpp.o[0m
[ 75%] [32m[1mLinking CXX shared library libgz_quadruped_plugins.so[0m
[100%] [32m[1mLinking CXX shared library libgz_quadruped_hardware-system.so[0m
[100%] Built target gz_quadruped_plugins
[100%] Built target gz_quadruped_hardware-system
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_plugins.so
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_quadruped_plugin.hpp
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system.hpp
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system_interface.hpp
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//xacro/foot_force_sensor.xacro
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.sh
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_hardware-system.so
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//gz_quadruped_hardware.xml
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_hardware
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_hardware
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.sh
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.bash
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.sh
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.zsh
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/packages/gz_quadruped_hardware
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/gz_quadruped_hardware__pluginlib__plugin/gz_quadruped_hardware
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_include_directories-extras.cmake
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_libraries-extras.cmake
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig.cmake
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig-version.cmake
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.xml
