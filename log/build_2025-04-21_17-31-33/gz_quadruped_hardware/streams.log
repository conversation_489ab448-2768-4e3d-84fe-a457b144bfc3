[0.005s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/gz_quadruped_hardware
[0.012s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.101s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.104s] -- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[0.104s] -- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
[0.119s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.121s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.123s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.128s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.133s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.210s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.211s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.220s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.226s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.238s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.242s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.248s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.288s] -- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
[0.294s] -- Compiling against Gazebo fortress
[0.295s] -- Looking for ignition-gazebo6 -- found version 6.17.0
[0.298s] -- Searching for dependencies of ignition-gazebo6
[0.299s] -- Looking for sdformat12 -- found version 12.8.0
[0.299s] -- Searching for dependencies of sdformat12
[0.303s] -- Checking for module 'tinyxml2'
[0.321s] --   Found tinyxml2, version 9.0.0
[0.375s] -- Looking for ignition-math6 -- found version 6.15.1
[0.376s] -- Searching for dependencies of ignition-math6
[0.382s] -- Looking for ignition-utils1 -- found version 1.5.1
[0.383s] -- Searching for dependencies of ignition-utils1
[0.388s] -- Looking for ignition-plugin1 -- found version 1.4.0
[0.389s] -- Searching for dependencies of ignition-plugin1
[0.390s] -- Searching for <ignition-plugin1> component [loader]
[0.392s] -- Looking for ignition-plugin1-loader -- found version 1.4.0
[0.392s] -- Searching for dependencies of ignition-plugin1-loader
[0.392s] -- Searching for <ignition-plugin1> component [register]
[0.394s] -- Looking for ignition-plugin1-register -- found version 1.4.0
[0.394s] -- Searching for dependencies of ignition-plugin1-register
[0.395s] -- Looking for ignition-transport11 -- found version 11.4.1
[0.395s] -- Searching for dependencies of ignition-transport11
[0.410s] -- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") 
[0.415s] -- Config-file not installed for ZeroMQ -- checking for pkg-config
[0.419s] -- Checking for module 'libzmq >= 4'
[0.433s] --   Found libzmq , version 4.3.4
[0.527s] -- Found ZeroMQ: TRUE (Required is at least version "4") 
[0.536s] -- Checking for module 'uuid'
[0.572s] --   Found uuid, version 2.37.2
[0.675s] -- Found UUID: TRUE  
[0.677s] -- Looking for ignition-utils1 -- found version 1.5.1
[0.678s] -- Searching for dependencies of ignition-utils1
[0.678s] -- Searching for <ignition-utils1> component [cli]
[0.685s] -- Looking for ignition-utils1-cli -- found version 1.5.1
[0.686s] -- Searching for dependencies of ignition-utils1-cli
[0.690s] -- Looking for ignition-msgs8 -- found version 8.7.0
[0.690s] -- Searching for dependencies of ignition-msgs8
[0.702s] -- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found suitable version "3.12.4", minimum required is "3") 
[0.702s] -- Looking for ignition-math6 -- found version 6.15.1
[0.707s] -- Searching for <ignition-transport11> component [log]
[0.708s] -- Looking for ignition-transport11-log -- found version 11.4.1
[0.709s] -- Searching for dependencies of ignition-transport11-log
[0.709s] -- Searching for <ignition-transport11> component [parameters]
[0.710s] -- Looking for ignition-transport11-parameters -- found version 11.4.1
[0.710s] -- Searching for dependencies of ignition-transport11-parameters
[0.711s] -- Looking for ignition-msgs8 -- found version 8.7.0
[0.712s] -- Looking for ignition-common4 -- found version 4.7.0
[0.712s] -- Searching for dependencies of ignition-common4
[0.712s] -- Looking for dlfcn.h - found
[0.713s] -- Looking for libdl - found
[0.714s] -- Found DL: TRUE  
[0.720s] -- Searching for <ignition-common4> component [profiler]
[0.721s] -- Looking for ignition-common4-profiler -- found version 4.7.0
[0.721s] -- Searching for dependencies of ignition-common4-profiler
[0.721s] -- Searching for <ignition-common4> component [events]
[0.723s] -- Looking for ignition-common4-events -- found version 4.7.0
[0.723s] -- Searching for dependencies of ignition-common4-events
[0.723s] -- Looking for ignition-math6 -- found version 6.15.1
[0.723s] -- Searching for <ignition-common4> component [av]
[0.724s] -- Looking for ignition-common4-av -- found version 4.7.0
[0.724s] -- Searching for dependencies of ignition-common4-av
[0.728s] -- Checking for module 'libswscale'
[0.737s] --   Found libswscale, version 5.9.100
[0.805s] -- Found SWSCALE: TRUE  
[0.820s] -- Checking for module 'libavdevice >= 56.4.100'
[0.859s] --   Found libavdevice , version 58.13.100
[0.972s] -- Found AVDEVICE: TRUE (Required is at least version "56.4.100") 
[0.987s] -- Checking for module 'libavformat'
[1.025s] --   Found libavformat, version 58.76.100
[1.136s] -- Found AVFORMAT: TRUE  
[1.151s] -- Checking for module 'libavcodec'
[1.182s] --   Found libavcodec, version 58.134.100
[1.289s] -- Found AVCODEC: TRUE  
[1.306s] -- Checking for module 'libavutil'
[1.340s] --   Found libavutil, version 56.70.100
[1.444s] -- Found AVUTIL: TRUE  
[1.452s] -- Looking for ignition-fuel_tools7 -- found version 7.3.1
[1.453s] -- Searching for dependencies of ignition-fuel_tools7
[1.567s] -- Found CURL: /usr/lib/x86_64-linux-gnu/libcurl.so (found version "7.81.0")  
[1.584s] -- Checking for module 'jsoncpp'
[1.623s] --   Found jsoncpp, version 1.9.5
[1.729s] -- Found JSONCPP: TRUE  
[1.745s] -- Checking for module 'libzip'
[1.781s] --   Found libzip, version 1.7.3
[1.882s] -- Found ZIP: TRUE  
[1.883s] -- Looking for ignition-common4 -- found version 4.7.0
[1.884s] -- Looking for ignition-math6 -- found version 6.15.1
[1.885s] -- Looking for ignition-msgs8 -- found version 8.7.0
[1.892s] -- Looking for ignition-gui6 -- found version 6.8.0
[1.892s] -- Searching for dependencies of ignition-gui6
[1.913s] -- Looking for ignition-math6 -- found version 6.15.1
[1.913s] -- Looking for ignition-common4 -- found version 4.7.0
[1.914s] -- Looking for ignition-plugin1 -- found version 1.4.0
[1.914s] -- Looking for ignition-transport11 -- found version 11.4.1
[1.916s] -- Looking for ignition-rendering6 -- found version 6.6.3
[1.916s] -- Searching for dependencies of ignition-rendering6
[1.916s] -- Looking for ignition-math6 -- found version 6.15.1
[1.916s] -- Searching for dependencies of ignition-math6
[1.916s] -- Searching for <ignition-math6> component [eigen3]
[1.918s] -- Looking for ignition-math6-eigen3 -- found version 6.15.1
[1.918s] -- Searching for dependencies of ignition-math6-eigen3
[1.920s] -- Looking for ignition-common4 -- found version 4.7.0
[1.920s] -- Searching for dependencies of ignition-common4
[1.920s] -- Looking for dlfcn.h - found
[1.920s] -- Looking for libdl - found
[1.928s] -- Searching for <ignition-common4> component [graphics]
[1.931s] -- Looking for ignition-common4-graphics -- found version 4.7.0
[1.931s] -- Searching for dependencies of ignition-common4-graphics
[1.931s] -- Looking for ignition-math6 -- found version 6.15.1
[1.932s] -- Looking for ignition-utils1 -- found version 1.5.1
[1.932s] -- Searching for <ignition-common4> component [events]
[1.934s] -- Looking for ignition-plugin1 -- found version 1.4.0
[1.934s] -- Searching for dependencies of ignition-plugin1
[1.934s] -- Searching for <ignition-plugin1> component [all]
[1.937s] -- Looking for all libraries of ignition-plugin1 -- found version 1.4.0
[1.938s] -- Looking for ignition-plugin1 -- found version 1.4.0
[1.942s] -- Looking for ignition-msgs8 -- found version 8.7.0
[1.956s] -- Looking for ignition-physics5 -- found version 5.3.2
[1.956s] -- Searching for dependencies of ignition-physics5
[1.956s] -- Looking for ignition-math6 -- found version 6.15.1
[1.956s] -- Looking for ignition-plugin1 -- found version 1.4.0
[1.957s] -- Looking for ignition-utils1 -- found version 1.5.1
[1.957s] -- Searching for <ignition-physics5> component [heightmap]
[1.959s] -- Looking for ignition-physics5-heightmap -- found version 5.3.2
[1.959s] -- Searching for dependencies of ignition-physics5-heightmap
[1.959s] -- Looking for ignition-common4 -- found version 4.7.0
[1.960s] -- Searching for <ignition-physics5> component [mesh]
[1.962s] -- Looking for ignition-physics5-mesh -- found version 5.3.2
[1.962s] -- Searching for dependencies of ignition-physics5-mesh
[1.962s] -- Looking for ignition-common4 -- found version 4.7.0
[1.963s] -- Searching for <ignition-physics5> component [sdf]
[1.964s] -- Looking for ignition-physics5-sdf -- found version 5.3.2
[1.964s] -- Searching for dependencies of ignition-physics5-sdf
[1.965s] -- Looking for sdformat12 -- found version 12.8.0
[1.966s] -- Looking for ignition-sensors6 -- found version 6.8.1
[1.967s] -- Searching for dependencies of ignition-sensors6
[1.976s] -- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") 
[1.977s] -- Looking for ignition-math6 -- found version 6.15.1
[1.977s] -- Looking for ignition-common4 -- found version 4.7.0
[1.977s] -- Looking for ignition-transport11 -- found version 11.4.1
[1.978s] -- Looking for ignition-rendering6 -- found version 6.6.3
[1.978s] -- Looking for ignition-msgs8 -- found version 8.7.0
[1.979s] -- Looking for sdformat12 -- found version 12.8.0
[1.979s] -- Searching for <ignition-sensors6> component [air_pressure]
[1.980s] -- Looking for ignition-sensors6-air_pressure -- found version 6.8.1
[1.980s] -- Searching for dependencies of ignition-sensors6-air_pressure
[1.980s] -- Searching for <ignition-sensors6> component [altimeter]
[1.981s] -- Looking for ignition-sensors6-altimeter -- found version 6.8.1
[1.982s] -- Searching for dependencies of ignition-sensors6-altimeter
[1.982s] -- Searching for <ignition-sensors6> component [imu]
[1.983s] -- Looking for ignition-sensors6-imu -- found version 6.8.1
[1.983s] -- Searching for dependencies of ignition-sensors6-imu
[1.983s] -- Searching for <ignition-sensors6> component [force_torque]
[1.985s] -- Looking for ignition-sensors6-force_torque -- found version 6.8.1
[1.985s] -- Searching for dependencies of ignition-sensors6-force_torque
[1.985s] -- Searching for <ignition-sensors6> component [logical_camera]
[1.987s] -- Looking for ignition-sensors6-logical_camera -- found version 6.8.1
[1.987s] -- Searching for dependencies of ignition-sensors6-logical_camera
[1.987s] -- Searching for <ignition-sensors6> component [magnetometer]
[1.989s] -- Looking for ignition-sensors6-magnetometer -- found version 6.8.1
[1.989s] -- Searching for dependencies of ignition-sensors6-magnetometer
[1.989s] -- Searching for <ignition-sensors6> component [navsat]
[1.990s] -- Looking for ignition-sensors6-navsat -- found version 6.8.1
[1.990s] -- Searching for dependencies of ignition-sensors6-navsat
[1.991s] -- Searching for <ignition-sensors6> component [rendering]
[1.992s] -- Looking for ignition-sensors6-rendering -- found version 6.8.1
[1.992s] -- Searching for dependencies of ignition-sensors6-rendering
[1.992s] -- Searching for <ignition-sensors6> component [lidar]
[1.993s] -- Looking for ignition-sensors6-lidar -- found version 6.8.1
[1.993s] -- Searching for dependencies of ignition-sensors6-lidar
[1.993s] -- Looking for ignition-sensors6 -- found version 6.8.1
[1.994s] -- Searching for <ignition-sensors6> component [gpu_lidar]
[1.995s] -- Looking for ignition-sensors6-gpu_lidar -- found version 6.8.1
[1.995s] -- Searching for dependencies of ignition-sensors6-gpu_lidar
[1.995s] -- Looking for ignition-sensors6 -- found version 6.8.1
[1.995s] -- Searching for <ignition-sensors6> component [camera]
[1.996s] -- Looking for ignition-sensors6-camera -- found version 6.8.1
[1.997s] -- Searching for dependencies of ignition-sensors6-camera
[1.997s] -- Looking for ignition-sensors6 -- found version 6.8.1
[1.997s] -- Searching for <ignition-sensors6> component [boundingbox_camera]
[1.998s] -- Looking for ignition-sensors6-boundingbox_camera -- found version 6.8.1
[1.998s] -- Searching for dependencies of ignition-sensors6-boundingbox_camera
[1.998s] -- Searching for <ignition-sensors6> component [segmentation_camera]
[2.000s] -- Looking for ignition-sensors6-segmentation_camera -- found version 6.8.1
[2.000s] -- Searching for dependencies of ignition-sensors6-segmentation_camera
[2.000s] -- Searching for <ignition-sensors6> component [depth_camera]
[2.001s] -- Looking for ignition-sensors6-depth_camera -- found version 6.8.1
[2.001s] -- Searching for dependencies of ignition-sensors6-depth_camera
[2.001s] -- Looking for ignition-sensors6 -- found version 6.8.1
[2.002s] -- Searching for <ignition-sensors6> component [rgbd_camera]
[2.003s] -- Looking for ignition-sensors6-rgbd_camera -- found version 6.8.1
[2.003s] -- Searching for dependencies of ignition-sensors6-rgbd_camera
[2.003s] -- Looking for ignition-sensors6 -- found version 6.8.1
[2.003s] -- Searching for <ignition-sensors6> component [thermal_camera]
[2.005s] -- Looking for ignition-sensors6-thermal_camera -- found version 6.8.1
[2.005s] -- Searching for dependencies of ignition-sensors6-thermal_camera
[2.005s] -- Looking for ignition-sensors6 -- found version 6.8.1
[2.005s] -- Looking for ignition-rendering6 -- found version 6.6.3
[2.006s] -- Looking for ignition-math6 -- found version 6.15.1
[2.007s] -- Looking for ignition-utils1 -- found version 1.5.1
[2.018s] -- Looking for ignition-plugin1 -- found version 1.4.0
[2.030s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.077s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.077s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.077s] -- Configured cppcheck include dirs: /home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware/include
[2.078s] -- Configured cppcheck exclude dirs and/or files: 
[2.078s] -- Added test 'cpplint' to check C / C++ code against the Google style
[2.078s] -- Configured cpplint exclude dirs and/or files: 
[2.078s] -- Added test 'lint_cmake' to check CMake code style
[2.079s] -- Added test 'uncrustify' to check C / C++ code style
[2.079s] -- Configured uncrustify additional arguments: 
[2.079s] -- Added test 'xmllint' to check XML markup files
[2.080s] -- Configuring done
[2.100s] -- Generating done
[2.104s] -- Build files have been written to: /home/<USER>/ros2_ws/build/gz_quadruped_hardware
[2.110s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/gz_quadruped_hardware
[2.112s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[2.152s] [ 25%] [32mBuilding CXX object CMakeFiles/gz_quadruped_plugins.dir/src/gz_system.cpp.o[0m
[2.154s] [ 50%] [32mBuilding CXX object CMakeFiles/gz_quadruped_hardware-system.dir/src/gz_quadruped_plugin.cpp.o[0m
[8.123s] [ 75%] [32m[1mLinking CXX shared library libgz_quadruped_plugins.so[0m
[8.146s] [100%] [32m[1mLinking CXX shared library libgz_quadruped_hardware-system.so[0m
[8.465s] [100%] Built target gz_quadruped_plugins
[8.537s] [100%] Built target gz_quadruped_hardware-system
[8.544s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[8.549s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/gz_quadruped_hardware
[8.553s] -- Install configuration: ""
[8.554s] -- Execute custom install script
[8.554s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_plugins.so
[8.561s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_quadruped_plugin.hpp
[8.569s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system.hpp
[8.579s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system_interface.hpp
[8.588s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//xacro/foot_force_sensor.xacro
[8.605s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.sh
[8.621s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.dsv
[8.643s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_hardware-system.so
[8.655s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//gz_quadruped_hardware.xml
[8.666s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_hardware
[8.684s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_hardware
[8.704s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.sh
[8.723s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.dsv
[8.741s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.sh
[8.756s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.dsv
[8.772s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.bash
[8.786s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.sh
[8.801s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.zsh
[8.809s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.dsv
[8.817s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.dsv
[8.824s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/packages/gz_quadruped_hardware
[8.830s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/gz_quadruped_hardware__pluginlib__plugin/gz_quadruped_hardware
[8.836s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_include_directories-extras.cmake
[8.844s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_libraries-extras.cmake
[8.856s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig.cmake
[8.868s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig-version.cmake
[8.879s] -- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.xml
[8.898s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/gz_quadruped_hardware
