[0.000000] (-) TimerEvent: {}
[0.000314] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000358] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000458] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000653] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000731] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000779] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000799] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000821] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000871] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000885] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000910] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000950] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000968] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000983] (-) JobUnselected: {'identifier': 'elevation_mapping'}
[0.001015] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001038] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001052] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001067] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001080] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001095] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001110] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001124] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001160] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001178] (-) JobUnselected: {'identifier': 'kindr_msgs'}
[0.001192] (-) JobUnselected: {'identifier': 'kindr_ros'}
[0.001309] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001355] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001386] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001408] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001423] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001436] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001450] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001464] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001477] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001491] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001505] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001519] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001532] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001545] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001566] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001585] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001616] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001635] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001648] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001679] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001692] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001714] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001729] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001743] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001757] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001796] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001817] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001902] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001917] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001930] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001948] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001962] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002052] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002130] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002271] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002425] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002445] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002463] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002479] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002492] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002506] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002527] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002550] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002626] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002639] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002651] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002664] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002676] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002688] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002699] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002719] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002733] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002752] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.002768] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002785] (gz_quadruped_hardware) JobQueued: {'identifier': 'gz_quadruped_hardware', 'dependencies': OrderedDict()}
[0.002807] (gz_quadruped_hardware) JobStarted: {'identifier': 'gz_quadruped_hardware'}
[0.005866] (gz_quadruped_hardware) JobProgress: {'identifier': 'gz_quadruped_hardware', 'progress': 'cmake'}
[0.006190] (gz_quadruped_hardware) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/gz_quadruped_hardware'], 'cwd': '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursor2fSVHQ'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('TERM_PROGRAM_VERSION', '0.48.9'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursor2fSVHQ/usr/share/perl5/:/tmp/.mount_cursor2fSVHQ/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/cursor'), ('MANAGERPID', '2805'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '3310'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '416570'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:23731'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('INVOCATION_ID', '2b31a3de0ac24eeb9f20788ec77a5d30'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '3178a19846384132a19500b753491490'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursor2fSVHQ/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('XDG_DATA_DIRS', '/tmp/.mount_cursor2fSVHQ/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursor2fSVHQ/usr/lib/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.014964] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.100078] (-) TimerEvent: {}
[0.103652] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.106587] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)\n'}
[0.106915] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)\n'}
[0.122234] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.123390] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.126082] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.130462] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.135935] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.200189] (-) TimerEvent: {}
[0.212925] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.213504] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.222349] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.228602] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.240556] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.245003] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.250689] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.291065] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)\n'}
[0.296420] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Compiling against Gazebo fortress\n'}
[0.297740] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-gazebo6 -- found version 6.17.0\n'}
[0.300294] (-) TimerEvent: {}
[0.300695] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-gazebo6\n'}
[0.301798] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for sdformat12 -- found version 12.8.0\n'}
[0.301997] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of sdformat12\n'}
[0.305372] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'tinyxml2'\n"}
[0.323745] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found tinyxml2, version 9.0.0\n'}
[0.377590] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[0.378684] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-math6\n'}
[0.385088] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1 -- found version 1.5.1\n'}
[0.385807] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-utils1\n'}
[0.391000] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[0.391702] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-plugin1\n'}
[0.392295] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-plugin1> component [loader]\n'}
[0.394309] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1-loader -- found version 1.4.0\n'}
[0.394580] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-plugin1-loader\n'}
[0.394931] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-plugin1> component [register]\n'}
[0.396434] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1-register -- found version 1.4.0\n'}
[0.396568] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-plugin1-register\n'}
[0.398030] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-transport11 -- found version 11.4.1\n'}
[0.398168] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-transport11\n'}
[0.400469] (-) TimerEvent: {}
[0.412342] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") \n'}
[0.418135] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Config-file not installed for ZeroMQ -- checking for pkg-config\n'}
[0.421718] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libzmq >= 4'\n"}
[0.435819] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libzmq , version 4.3.4\n'}
[0.500709] (-) TimerEvent: {}
[0.529380] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ZeroMQ: TRUE (Required is at least version "4") \n'}
[0.539185] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'uuid'\n"}
[0.574700] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found uuid, version 2.37.2\n'}
[0.600852] (-) TimerEvent: {}
[0.677854] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found UUID: TRUE  \n'}
[0.679284] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1 -- found version 1.5.1\n'}
[0.680726] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-utils1\n'}
[0.681124] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-utils1> component [cli]\n'}
[0.687487] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1-cli -- found version 1.5.1\n'}
[0.688430] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-utils1-cli\n'}
[0.692569] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-msgs8 -- found version 8.7.0\n'}
[0.693019] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-msgs8\n'}
[0.701097] (-) TimerEvent: {}
[0.704698] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found suitable version "3.12.4", minimum required is "3") \n'}
[0.704988] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[0.710111] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-transport11> component [log]\n'}
[0.711187] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-transport11-log -- found version 11.4.1\n'}
[0.711444] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-transport11-log\n'}
[0.711641] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-transport11> component [parameters]\n'}
[0.712793] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-transport11-parameters -- found version 11.4.1\n'}
[0.712990] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-transport11-parameters\n'}
[0.713517] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-msgs8 -- found version 8.7.0\n'}
[0.714586] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[0.714819] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4\n'}
[0.714906] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for dlfcn.h - found\n'}
[0.715582] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for libdl - found\n'}
[0.716335] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found DL: TRUE  \n'}
[0.722439] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-common4> component [profiler]\n'}
[0.723733] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4-profiler -- found version 4.7.0\n'}
[0.723952] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4-profiler\n'}
[0.724262] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-common4> component [events]\n'}
[0.725390] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4-events -- found version 4.7.0\n'}
[0.725507] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4-events\n'}
[0.725609] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[0.725952] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-common4> component [av]\n'}
[0.727069] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4-av -- found version 4.7.0\n'}
[0.727260] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4-av\n'}
[0.730545] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libswscale'\n"}
[0.739519] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libswscale, version 5.9.100\n'}
[0.801337] (-) TimerEvent: {}
[0.807295] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found SWSCALE: TRUE  \n'}
[0.822903] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libavdevice >= 56.4.100'\n"}
[0.861956] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libavdevice , version 58.13.100\n'}
[0.901537] (-) TimerEvent: {}
[0.973740] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found AVDEVICE: TRUE (Required is at least version "56.4.100") \n'}
[0.989912] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libavformat'\n"}
[1.001743] (-) TimerEvent: {}
[1.027321] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libavformat, version 58.76.100\n'}
[1.102001] (-) TimerEvent: {}
[1.138328] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found AVFORMAT: TRUE  \n'}
[1.153419] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libavcodec'\n"}
[1.185014] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libavcodec, version 58.134.100\n'}
[1.202140] (-) TimerEvent: {}
[1.291921] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found AVCODEC: TRUE  \n'}
[1.302287] (-) TimerEvent: {}
[1.308538] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libavutil'\n"}
[1.342199] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libavutil, version 56.70.100\n'}
[1.402422] (-) TimerEvent: {}
[1.446193] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found AVUTIL: TRUE  \n'}
[1.454570] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-fuel_tools7 -- found version 7.3.1\n'}
[1.455538] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-fuel_tools7\n'}
[1.502529] (-) TimerEvent: {}
[1.569598] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found CURL: /usr/lib/x86_64-linux-gnu/libcurl.so (found version "7.81.0")  \n'}
[1.587076] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'jsoncpp'\n"}
[1.602735] (-) TimerEvent: {}
[1.625940] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found jsoncpp, version 1.9.5\n'}
[1.702892] (-) TimerEvent: {}
[1.731837] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found JSONCPP: TRUE  \n'}
[1.747364] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Checking for module 'libzip'\n"}
[1.783846] (gz_quadruped_hardware) StdoutLine: {'line': b'--   Found libzip, version 1.7.3\n'}
[1.803008] (-) TimerEvent: {}
[1.884143] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ZIP: TRUE  \n'}
[1.885661] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.886774] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.888041] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-msgs8 -- found version 8.7.0\n'}
[1.894236] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-gui6 -- found version 6.8.0\n'}
[1.894836] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-gui6\n'}
[1.903058] (-) TimerEvent: {}
[1.915650] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.915950] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.916473] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[1.916604] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-transport11 -- found version 11.4.1\n'}
[1.918445] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-rendering6 -- found version 6.6.3\n'}
[1.918640] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-rendering6\n'}
[1.918899] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.919187] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-math6\n'}
[1.919263] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-math6> component [eigen3]\n'}
[1.920579] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6-eigen3 -- found version 6.15.1\n'}
[1.920757] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-math6-eigen3\n'}
[1.922459] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.922687] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4\n'}
[1.922723] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for dlfcn.h - found\n'}
[1.922744] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for libdl - found\n'}
[1.930593] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-common4> component [graphics]\n'}
[1.933354] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4-graphics -- found version 4.7.0\n'}
[1.933891] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-common4-graphics\n'}
[1.934198] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.934639] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1 -- found version 1.5.1\n'}
[1.935176] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-common4> component [events]\n'}
[1.936330] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[1.936864] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-plugin1\n'}
[1.937050] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-plugin1> component [all]\n'}
[1.940193] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for all libraries of ignition-plugin1 -- found version 1.4.0\n'}
[1.941089] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[1.944490] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-msgs8 -- found version 8.7.0\n'}
[1.958544] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-physics5 -- found version 5.3.2\n'}
[1.958653] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-physics5\n'}
[1.958783] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.959112] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[1.959326] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1 -- found version 1.5.1\n'}
[1.960053] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-physics5> component [heightmap]\n'}
[1.961567] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-physics5-heightmap -- found version 5.3.2\n'}
[1.961908] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-physics5-heightmap\n'}
[1.962193] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.962582] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-physics5> component [mesh]\n'}
[1.964471] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-physics5-mesh -- found version 5.3.2\n'}
[1.964733] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-physics5-mesh\n'}
[1.965052] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.965344] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-physics5> component [sdf]\n'}
[1.966912] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-physics5-sdf -- found version 5.3.2\n'}
[1.967173] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-physics5-sdf\n'}
[1.967384] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for sdformat12 -- found version 12.8.0\n'}
[1.969037] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[1.969258] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6\n'}
[1.979030] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found Protobuf: /usr/lib/x86_64-linux-gnu/libprotobuf.so (found version "3.12.4") \n'}
[1.979669] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[1.979957] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-common4 -- found version 4.7.0\n'}
[1.980153] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-transport11 -- found version 11.4.1\n'}
[1.980512] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-rendering6 -- found version 6.6.3\n'}
[1.980653] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-msgs8 -- found version 8.7.0\n'}
[1.981351] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for sdformat12 -- found version 12.8.0\n'}
[1.981452] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [air_pressure]\n'}
[1.982631] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-air_pressure -- found version 6.8.1\n'}
[1.982751] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-air_pressure\n'}
[1.983094] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [altimeter]\n'}
[1.984086] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-altimeter -- found version 6.8.1\n'}
[1.984316] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-altimeter\n'}
[1.984535] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [imu]\n'}
[1.985729] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-imu -- found version 6.8.1\n'}
[1.985944] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-imu\n'}
[1.986164] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [force_torque]\n'}
[1.987518] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-force_torque -- found version 6.8.1\n'}
[1.987682] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-force_torque\n'}
[1.988094] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [logical_camera]\n'}
[1.989510] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-logical_camera -- found version 6.8.1\n'}
[1.989677] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-logical_camera\n'}
[1.989996] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [magnetometer]\n'}
[1.991362] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-magnetometer -- found version 6.8.1\n'}
[1.991495] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-magnetometer\n'}
[1.991746] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [navsat]\n'}
[1.992885] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-navsat -- found version 6.8.1\n'}
[1.993081] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-navsat\n'}
[1.993275] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [rendering]\n'}
[1.994423] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-rendering -- found version 6.8.1\n'}
[1.994608] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-rendering\n'}
[1.994777] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [lidar]\n'}
[1.995928] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-lidar -- found version 6.8.1\n'}
[1.996014] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-lidar\n'}
[1.996189] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[1.996427] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [gpu_lidar]\n'}
[1.997578] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-gpu_lidar -- found version 6.8.1\n'}
[1.997667] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-gpu_lidar\n'}
[1.997813] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[1.998054] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [camera]\n'}
[1.999119] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-camera -- found version 6.8.1\n'}
[1.999333] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-camera\n'}
[1.999509] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[1.999759] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [boundingbox_camera]\n'}
[2.000873] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-boundingbox_camera -- found version 6.8.1\n'}
[2.001009] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-boundingbox_camera\n'}
[2.001236] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [segmentation_camera]\n'}
[2.002347] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-segmentation_camera -- found version 6.8.1\n'}
[2.002516] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-segmentation_camera\n'}
[2.002743] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [depth_camera]\n'}
[2.003117] (-) TimerEvent: {}
[2.003889] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-depth_camera -- found version 6.8.1\n'}
[2.004059] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-depth_camera\n'}
[2.004245] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[2.004508] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [rgbd_camera]\n'}
[2.005662] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-rgbd_camera -- found version 6.8.1\n'}
[2.005829] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-rgbd_camera\n'}
[2.006008] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[2.006269] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for <ignition-sensors6> component [thermal_camera]\n'}
[2.007379] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6-thermal_camera -- found version 6.8.1\n'}
[2.007533] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Searching for dependencies of ignition-sensors6-thermal_camera\n'}
[2.007734] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-sensors6 -- found version 6.8.1\n'}
[2.008266] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-rendering6 -- found version 6.6.3\n'}
[2.008413] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-math6 -- found version 6.15.1\n'}
[2.009670] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-utils1 -- found version 1.5.1\n'}
[2.021130] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Looking for ignition-plugin1 -- found version 1.4.0\n'}
[2.032809] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[2.079612] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[2.080229] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[2.080290] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/ros2_ws/src/quadruped_ros2_control/hardwares/gz_quadruped_hardware/include\n'}
[2.080317] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[2.080765] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[2.080828] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[2.081010] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[2.081323] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[2.081383] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[2.081470] (gz_quadruped_hardware) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[2.082653] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Configuring done\n'}
[2.102948] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Generating done\n'}
[2.103176] (-) TimerEvent: {}
[2.106840] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros2_ws/build/gz_quadruped_hardware\n'}
[2.113037] (gz_quadruped_hardware) CommandEnded: {'returncode': 0}
[2.114366] (gz_quadruped_hardware) JobProgress: {'identifier': 'gz_quadruped_hardware', 'progress': 'build'}
[2.114395] (gz_quadruped_hardware) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursor2fSVHQ'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('TERM_PROGRAM_VERSION', '0.48.9'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursor2fSVHQ/usr/share/perl5/:/tmp/.mount_cursor2fSVHQ/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/cursor'), ('MANAGERPID', '2805'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '3310'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '416570'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:23731'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('INVOCATION_ID', '2b31a3de0ac24eeb9f20788ec77a5d30'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '3178a19846384132a19500b753491490'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursor2fSVHQ/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('XDG_DATA_DIRS', '/tmp/.mount_cursor2fSVHQ/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursor2fSVHQ/usr/lib/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[2.154258] (gz_quadruped_hardware) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/gz_quadruped_plugins.dir/src/gz_system.cpp.o\x1b[0m\n'}
[2.156572] (gz_quadruped_hardware) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/gz_quadruped_hardware-system.dir/src/gz_quadruped_plugin.cpp.o\x1b[0m\n'}
[2.203370] (-) TimerEvent: {}
[2.303693] (-) TimerEvent: {}
[2.404031] (-) TimerEvent: {}
[2.504334] (-) TimerEvent: {}
[2.604628] (-) TimerEvent: {}
[2.704830] (-) TimerEvent: {}
[2.805040] (-) TimerEvent: {}
[2.905245] (-) TimerEvent: {}
[3.005450] (-) TimerEvent: {}
[3.105794] (-) TimerEvent: {}
[3.205993] (-) TimerEvent: {}
[3.306231] (-) TimerEvent: {}
[3.406521] (-) TimerEvent: {}
[3.506827] (-) TimerEvent: {}
[3.607033] (-) TimerEvent: {}
[3.707327] (-) TimerEvent: {}
[3.807614] (-) TimerEvent: {}
[3.907938] (-) TimerEvent: {}
[4.008147] (-) TimerEvent: {}
[4.108398] (-) TimerEvent: {}
[4.208715] (-) TimerEvent: {}
[4.308943] (-) TimerEvent: {}
[4.409152] (-) TimerEvent: {}
[4.509392] (-) TimerEvent: {}
[4.609695] (-) TimerEvent: {}
[4.709937] (-) TimerEvent: {}
[4.810147] (-) TimerEvent: {}
[4.910439] (-) TimerEvent: {}
[5.010757] (-) TimerEvent: {}
[5.110974] (-) TimerEvent: {}
[5.211193] (-) TimerEvent: {}
[5.311401] (-) TimerEvent: {}
[5.411681] (-) TimerEvent: {}
[5.511896] (-) TimerEvent: {}
[5.612092] (-) TimerEvent: {}
[5.712331] (-) TimerEvent: {}
[5.812662] (-) TimerEvent: {}
[5.912882] (-) TimerEvent: {}
[6.013086] (-) TimerEvent: {}
[6.113317] (-) TimerEvent: {}
[6.213512] (-) TimerEvent: {}
[6.313823] (-) TimerEvent: {}
[6.414033] (-) TimerEvent: {}
[6.514278] (-) TimerEvent: {}
[6.614574] (-) TimerEvent: {}
[6.714892] (-) TimerEvent: {}
[6.815095] (-) TimerEvent: {}
[6.915325] (-) TimerEvent: {}
[7.015618] (-) TimerEvent: {}
[7.115853] (-) TimerEvent: {}
[7.216070] (-) TimerEvent: {}
[7.316311] (-) TimerEvent: {}
[7.416589] (-) TimerEvent: {}
[7.516869] (-) TimerEvent: {}
[7.617070] (-) TimerEvent: {}
[7.717320] (-) TimerEvent: {}
[7.817603] (-) TimerEvent: {}
[7.917951] (-) TimerEvent: {}
[8.018238] (-) TimerEvent: {}
[8.118579] (-) TimerEvent: {}
[8.126010] (gz_quadruped_hardware) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX shared library libgz_quadruped_plugins.so\x1b[0m\n'}
[8.148234] (gz_quadruped_hardware) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX shared library libgz_quadruped_hardware-system.so\x1b[0m\n'}
[8.218759] (-) TimerEvent: {}
[8.319017] (-) TimerEvent: {}
[8.419303] (-) TimerEvent: {}
[8.467970] (gz_quadruped_hardware) StdoutLine: {'line': b'[100%] Built target gz_quadruped_plugins\n'}
[8.519467] (-) TimerEvent: {}
[8.539553] (gz_quadruped_hardware) StdoutLine: {'line': b'[100%] Built target gz_quadruped_hardware-system\n'}
[8.546234] (gz_quadruped_hardware) CommandEnded: {'returncode': 0}
[8.546593] (gz_quadruped_hardware) JobProgress: {'identifier': 'gz_quadruped_hardware', 'progress': 'install'}
[8.551329] (gz_quadruped_hardware) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware'], 'cwd': '/home/<USER>/ros2_ws/build/gz_quadruped_hardware', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursor2fSVHQ'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('TERM_PROGRAM_VERSION', '0.48.9'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursor2fSVHQ/usr/share/perl5/:/tmp/.mount_cursor2fSVHQ/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursor2fSVHQ/usr/share/cursor/cursor'), ('MANAGERPID', '2805'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '3310'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '416570'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:23731'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('INVOCATION_ID', '2b31a3de0ac24eeb9f20788ec77a5d30'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '3178a19846384132a19500b753491490'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursor2fSVHQ/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/gz_quadruped_hardware'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('XDG_DATA_DIRS', '/tmp/.mount_cursor2fSVHQ/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursor2fSVHQ/usr/lib/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt4/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib32/qt5/plugins/:/tmp/.mount_cursor2fSVHQ/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[8.556032] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.556390] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Execute custom install script\n'}
[8.556481] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_plugins.so\n'}
[8.563783] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_quadruped_plugin.hpp\n'}
[8.571498] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system.hpp\n'}
[8.582085] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system_interface.hpp\n'}
[8.590988] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//xacro/foot_force_sensor.xacro\n'}
[8.607910] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.sh\n'}
[8.619675] (-) TimerEvent: {}
[8.623870] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.dsv\n'}
[8.645303] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_hardware-system.so\n'}
[8.657444] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//gz_quadruped_hardware.xml\n'}
[8.668277] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_hardware\n'}
[8.686780] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_hardware\n'}
[8.706642] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.sh\n'}
[8.719904] (-) TimerEvent: {}
[8.725529] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.dsv\n'}
[8.743683] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.sh\n'}
[8.758233] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.dsv\n'}
[8.775094] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.bash\n'}
[8.788977] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.sh\n'}
[8.803450] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.zsh\n'}
[8.812184] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.dsv\n'}
[8.819969] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.dsv\n'}
[8.820031] (-) TimerEvent: {}
[8.826407] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/packages/gz_quadruped_hardware\n'}
[8.832704] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/gz_quadruped_hardware__pluginlib__plugin/gz_quadruped_hardware\n'}
[8.839037] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[8.846687] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[8.858492] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig.cmake\n'}
[8.870896] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig-version.cmake\n'}
[8.882111] (gz_quadruped_hardware) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.xml\n'}
[8.900360] (gz_quadruped_hardware) CommandEnded: {'returncode': 0}
[8.916602] (gz_quadruped_hardware) JobEnded: {'identifier': 'gz_quadruped_hardware', 'rc': 0}
[8.916954] (-) EventReactorShutdown: {}
