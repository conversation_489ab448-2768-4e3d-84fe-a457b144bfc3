[0.121s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-up-to', 'unitree_guide_controller', 'go2_description', 'keyboard_input', '--symlink-install']
[0.121s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=['unitree_guide_controller', 'go2_description', 'keyboard_input'], packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x790a34775ea0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x790a34775a20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x790a34775a20>>)
[0.326s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.327s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.327s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.327s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.327s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.327s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.327s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ros'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python_setup_py'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ros'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['cmake', 'python']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'cmake'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['python_setup_py']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python_setup_py'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ros'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['cmake', 'python']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'cmake'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['python_setup_py']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python_setup_py'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ros'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['cmake', 'python']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'cmake'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['python_setup_py']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python_setup_py'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_meta'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ros']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ros'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['cmake', 'python']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'cmake'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['python_setup_py']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python_setup_py'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ignore', 'ignore_ament_install']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore_ament_install'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_pkg']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_pkg'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_meta']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ros'
[0.347s] DEBUG:colcon.colcon_core.package_identification:Package 'src/elevation_map_converter' with type 'ros.ament_cmake' and name 'elevation_map_converter'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore_ament_install'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_pkg']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_pkg'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_meta']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_meta'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ros']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ros'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['cmake', 'python']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'cmake'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['python_setup_py']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python_setup_py'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ignore', 'ignore_ament_install']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore_ament_install'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_pkg']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_pkg'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_meta']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_meta'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ros']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ros'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['cmake', 'python']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'cmake'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['python_setup_py']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python_setup_py'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore_ament_install'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_pkg']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_pkg'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_meta']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_meta'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ros']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ros'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['cmake', 'python']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'cmake'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['python_setup_py']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python_setup_py'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore_ament_install'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_pkg']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_pkg'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_meta']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_meta'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ros']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ros'
[0.350s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_ballbot_mpcnet'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore_ament_install'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_pkg']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_pkg'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_meta']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_meta'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ros']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ros'
[0.352s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_mpcnet'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ignore', 'ignore_ament_install']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ros'
[0.353s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core' with type 'ros.ament_cmake' and name 'ocs2_mpcnet_core'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore_ament_install'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_pkg']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_pkg'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_meta']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_meta'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ros']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ros'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['cmake', 'python']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'cmake'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['python_setup_py']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python_setup_py'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ignore', 'ignore_ament_install']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore_ament_install'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_pkg']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_pkg'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_meta']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ros']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ros'
[0.356s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands' with type 'ros.ament_cmake' and name 'ocs2_anymal_commands'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore_ament_install'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_pkg']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_pkg'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_meta']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_meta'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ros']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ros'
[0.357s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_loopshaping_mpc'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ignore', 'ignore_ament_install']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore_ament_install'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_pkg']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_pkg'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_meta']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_meta'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ros']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ros'
[0.359s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models' with type 'ros.ament_cmake' and name 'ocs2_anymal_models'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore_ament_install'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_pkg']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_pkg'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_meta']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_meta'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ros']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ros'
[0.361s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_mpc'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ignore', 'ignore_ament_install']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore_ament_install'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_pkg']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_pkg'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_meta']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_meta'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ros']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ros'
[0.362s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_interface'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ignore', 'ignore_ament_install']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore_ament_install'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_pkg']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_pkg'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_meta']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_meta'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ros']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ros'
[0.363s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_loopshaping_interface'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ignore', 'ignore_ament_install']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore_ament_install'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_meta']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_meta'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ros']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ros'
[0.364s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface' with type 'ros.ament_cmake' and name 'ocs2_switched_model_interface'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore_ament_install'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_pkg']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_pkg'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_meta']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_meta'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ros']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ros'
[0.365s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs' with type 'ros.ament_cmake' and name 'ocs2_switched_model_msgs'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ignore', 'ignore_ament_install']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore_ament_install'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_pkg']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_pkg'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_meta']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_meta'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ros']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ros'
[0.367s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model' with type 'ros.ament_cmake' and name 'segmented_planes_terrain_model'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ros'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['cmake', 'python']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'cmake'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['python_setup_py']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python_setup_py'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ros'
[0.386s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_raisim'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ignore', 'ignore_ament_install']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore_ament_install'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_pkg']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_pkg'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_meta']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_meta'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ros']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ros'
[0.390s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core' with type 'ros.ament_cmake' and name 'ocs2_raisim_core'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ignore', 'ignore_ament_install']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore_ament_install'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_pkg']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_pkg'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_meta']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_meta'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ros']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ros'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['cmake', 'python']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'cmake'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['python_setup_py']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python_setup_py'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ignore', 'ignore_ament_install']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore_ament_install'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_pkg']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_pkg'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_meta']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_meta'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ros']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ros'
[0.394s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot' with type 'ros.ament_cmake' and name 'ocs2_ballbot'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore_ament_install'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_pkg']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_pkg'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_meta']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_meta'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ros']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ros'
[0.397s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros' with type 'ros.ament_cmake' and name 'ocs2_ballbot_ros'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ignore', 'ignore_ament_install']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore_ament_install'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_pkg']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_pkg'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_meta']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_meta'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ros']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ros'
[0.399s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole' with type 'ros.ament_cmake' and name 'ocs2_cartpole'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ignore', 'ignore_ament_install']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore_ament_install'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_pkg']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_pkg'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_meta']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_meta'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ros']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ros'
[0.400s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros' with type 'ros.ament_cmake' and name 'ocs2_cartpole_ros'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ignore', 'ignore_ament_install']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore_ament_install'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_pkg']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_pkg'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_meta']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_meta'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ros']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ros'
[0.401s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator' with type 'ros.ament_cmake' and name 'ocs2_double_integrator'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore_ament_install'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_pkg']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_pkg'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_meta']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_meta'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ros']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ros'
[0.402s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros' with type 'ros.ament_cmake' and name 'ocs2_double_integrator_ros'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ignore', 'ignore_ament_install']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore_ament_install'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_pkg']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_pkg'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_meta']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_meta'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ros']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ros'
[0.404s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot' with type 'ros.ament_cmake' and name 'ocs2_legged_robot'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore_ament_install'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_pkg']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_pkg'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_meta']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_meta'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ros']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ros'
[0.405s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_ros'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ignore', 'ignore_ament_install']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore_ament_install'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_pkg']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_pkg'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_meta']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_meta'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ros']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ros'
[0.406s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore_ament_install'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_pkg']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_pkg'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_meta']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_meta'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ros']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ros'
[0.407s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator_ros'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ignore', 'ignore_ament_install']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore_ament_install'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_pkg']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_pkg'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_meta']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_meta'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ros']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ros'
[0.409s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor' with type 'ros.ament_cmake' and name 'ocs2_quadrotor'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ignore', 'ignore_ament_install']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore_ament_install'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_pkg']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_pkg'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_meta']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_meta'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ros']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ros'
[0.410s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros' with type 'ros.ament_cmake' and name 'ocs2_quadrotor_ros'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ignore', 'ignore_ament_install']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore_ament_install'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_pkg']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_pkg'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_meta']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_meta'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ros']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ros'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['cmake', 'python']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'cmake'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['python_setup_py']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python_setup_py'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ignore', 'ignore_ament_install']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore_ament_install'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_pkg']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_pkg'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_meta']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_meta'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ros']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ros'
[0.412s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_core' with type 'ros.ament_cmake' and name 'ocs2_core'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ignore', 'ignore_ament_install']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore_ament_install'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_pkg']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_pkg'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_meta']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_meta'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ros']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ros'
[0.413s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_oc' with type 'ros.ament_cmake' and name 'ocs2_oc'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ignore', 'ignore_ament_install']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore_ament_install'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_pkg']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_pkg'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_meta']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_meta'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ros']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ros'
[0.414s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_thirdparty' with type 'ros.ament_cmake' and name 'ocs2_thirdparty'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ignore', 'ignore_ament_install']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore_ament_install'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_pkg']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_pkg'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_meta']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_meta'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ros']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ros'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['cmake', 'python']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'cmake'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['python_setup_py']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python_setup_py'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ignore', 'ignore_ament_install']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore_ament_install'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_pkg']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_pkg'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_meta']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_meta'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ros']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ros'
[0.416s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ddp' with type 'ros.ament_cmake' and name 'ocs2_ddp'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ignore', 'ignore_ament_install']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore_ament_install'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_pkg']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_pkg'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_meta']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_meta'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ros']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ros'
[0.418s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ipm' with type 'ros.ament_cmake' and name 'ocs2_ipm'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore_ament_install'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_pkg']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_pkg'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_meta']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_meta'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ros']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ros'
[0.420s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_mpc' with type 'ros.ament_cmake' and name 'ocs2_mpc'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ignore', 'ignore_ament_install']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore_ament_install'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_pkg']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_pkg'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_meta']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_meta'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ros']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ros'
[0.422s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_qp_solver' with type 'ros.ament_cmake' and name 'ocs2_qp_solver'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ignore', 'ignore_ament_install']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore_ament_install'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_pkg']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_pkg'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_meta']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_meta'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ros']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ros'
[0.424s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_slp' with type 'ros.ament_cmake' and name 'ocs2_slp'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore_ament_install'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_pkg']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_pkg'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_meta']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_meta'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ros']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ros'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['cmake', 'python']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'cmake'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['python_setup_py']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python_setup_py'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore_ament_install'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_pkg']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_pkg'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_meta']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_meta'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ros']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ros'
[0.426s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon' with type 'ros.ament_cmake' and name 'blasfeo_colcon'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore_ament_install'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_pkg']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_pkg'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_meta']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_meta'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ros']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ros'
[0.428s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon' with type 'ros.ament_cmake' and name 'hpipm_colcon'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore_ament_install'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_pkg']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_pkg'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_meta']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_meta'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ros']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ros'
[0.430s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp' with type 'ros.ament_cmake' and name 'ocs2_sqp'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ignore', 'ignore_ament_install']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore_ament_install'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_pkg']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_pkg'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_meta']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_meta'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ros']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ros'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['cmake', 'python']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'cmake'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['python_setup_py']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python_setup_py'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore_ament_install'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_pkg']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_pkg'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_meta']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_meta'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ros']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ros'
[0.432s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_msgs' with type 'ros.ament_cmake' and name 'ocs2_msgs'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ignore', 'ignore_ament_install']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore_ament_install'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_pkg']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_pkg'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_meta']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_meta'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ros']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ros'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['cmake', 'python']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'cmake'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['python_setup_py']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python_setup_py'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ignore', 'ignore_ament_install']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore_ament_install'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_pkg']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_pkg'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_meta']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_meta'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ros']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ros'
[0.434s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model' with type 'ros.ament_cmake' and name 'ocs2_centroidal_model'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ignore', 'ignore_ament_install']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore_ament_install'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_pkg']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_pkg'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_meta']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_meta'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ros']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ros'
[0.435s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface' with type 'ros.ament_cmake' and name 'ocs2_pinocchio_interface'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ignore', 'ignore_ament_install']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore_ament_install'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_pkg']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_pkg'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_meta']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_meta'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ros']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ros'
[0.436s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision' with type 'ros.ament_cmake' and name 'ocs2_self_collision'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ignore', 'ignore_ament_install']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore_ament_install'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_pkg']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_pkg'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_meta']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_meta'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ros']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ros'
[0.437s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization' with type 'ros.ament_cmake' and name 'ocs2_self_collision_visualization'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ignore', 'ignore_ament_install']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore_ament_install'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_pkg']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_pkg'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_meta']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_meta'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ros']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ros'
[0.438s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation' with type 'ros.ament_cmake' and name 'ocs2_sphere_approximation'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore_ament_install'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_pkg']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_pkg'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_meta']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_meta'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ros']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ros'
[0.440s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_python_interface' with type 'ros.ament_cmake' and name 'ocs2_python_interface'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ignore', 'ignore_ament_install']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore_ament_install'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_pkg']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_pkg'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_meta']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_meta'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ros']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ros'
[0.442s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_robotic_tools' with type 'ros.ament_cmake' and name 'ocs2_robotic_tools'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore_ament_install'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_pkg']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_pkg'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_meta']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_meta'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ros']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ros'
[0.445s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_ros_interfaces' with type 'ros.ament_cmake' and name 'ocs2_ros_interfaces'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ignore', 'ignore_ament_install']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore_ament_install'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_pkg']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_pkg'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_meta']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_meta'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ros']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ros'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['cmake', 'python']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'cmake'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['python_setup_py']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python_setup_py'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ignore', 'ignore_ament_install']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore_ament_install'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_pkg']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_pkg'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_meta']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_meta'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ros']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ros'
[0.448s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/grid_map_sdf' with type 'ros.ament_cmake' and name 'grid_map_sdf'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ignore', 'ignore_ament_install']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore_ament_install'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_pkg']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_pkg'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_meta']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_meta'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ros']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ros'
[0.449s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/ocs2_robotic_assets' with type 'ros.ament_cmake' and name 'ocs2_robotic_assets'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore_ament_install'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_pkg']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_pkg'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_meta']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_meta'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ros']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ros'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['cmake', 'python']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'cmake'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['python_setup_py']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python_setup_py'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore_ament_install'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_pkg']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_pkg'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_meta']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_meta'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ros']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ros'
[0.451s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon' with type 'ros.ament_cmake' and name 'cgal5_colcon'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ignore', 'ignore_ament_install']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore_ament_install'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_pkg']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_pkg'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_meta']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_meta'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ros']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ros'
[0.452s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition' with type 'ros.ament_cmake' and name 'convex_plane_decomposition'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore_ament_install'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_pkg']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_pkg'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_meta']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_meta'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ros']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ros'
[0.453s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_msgs'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ignore', 'ignore_ament_install']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore_ament_install'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_pkg']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_pkg'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_meta']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_meta'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ros']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ros'
[0.455s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_ros'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ignore', 'ignore_ament_install']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore_ament_install'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_pkg']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_pkg'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_meta']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_meta'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ros']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ros'
[0.456s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl' with type 'ros.ament_cmake' and name 'grid_map_filters_rsl'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ignore', 'ignore_ament_install']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore_ament_install'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_pkg']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_pkg'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_meta']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_meta'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ros']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ros'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['cmake', 'python']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'cmake'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'python'
[0.459s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qpoases_vendor' with type 'cmake' and name 'qpOASES'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ignore', 'ignore_ament_install']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore_ament_install'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_pkg']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_pkg'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_meta']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_meta'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ros']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ros'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['cmake', 'python']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'cmake'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['python_setup_py']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python_setup_py'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ignore', 'ignore_ament_install']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore_ament_install'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_pkg']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_pkg'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_meta']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_meta'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ros']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ros'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['cmake', 'python']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'cmake'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['python_setup_py']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python_setup_py'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ignore', 'ignore_ament_install']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore_ament_install'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_pkg']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_pkg'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_meta']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_meta'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ros']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ros'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['cmake', 'python']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'cmake'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['python_setup_py']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python_setup_py'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ignore', 'ignore_ament_install']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore_ament_install'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_pkg']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_pkg'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_meta']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_meta'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ros']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ros'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['cmake', 'python']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'cmake'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['python_setup_py']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python_setup_py'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ignore', 'ignore_ament_install']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore_ament_install'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_pkg']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_pkg'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_meta']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_meta'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ros']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ros'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['cmake', 'python']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'cmake'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['python_setup_py']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python_setup_py'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore_ament_install'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_pkg']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_pkg'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_meta']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_meta'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ros']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ros'
[0.463s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/control_input_msgs' with type 'ros.ament_cmake' and name 'control_input_msgs'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore_ament_install'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_pkg']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_pkg'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_meta']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_meta'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ros']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ros'
[0.464s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/joystick_input' with type 'ros.ament_cmake' and name 'joystick_input'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ignore', 'ignore_ament_install']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore_ament_install'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_pkg']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_pkg'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_meta']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_meta'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ros']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ros'
[0.466s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/keyboard_input' with type 'ros.ament_cmake' and name 'keyboard_input'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore_ament_install'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_pkg']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_pkg'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_meta']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_meta'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ros']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ros'
[0.468s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/unitree_joystick_input' with type 'ros.ament_cmake' and name 'unitree_joystick_input'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ignore', 'ignore_ament_install']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore_ament_install'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_pkg']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_pkg'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_meta']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_meta'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ros']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ros'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['cmake', 'python']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'cmake'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['python_setup_py']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python_setup_py'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ignore', 'ignore_ament_install']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore_ament_install'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_pkg']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_pkg'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_meta']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_meta'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ros']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ros'
[0.471s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/leg_pd_controller' with type 'ros.ament_cmake' and name 'leg_pd_controller'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore_ament_install'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_pkg']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_pkg'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_meta']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_meta'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ros']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ros'
[0.474s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller' with type 'ros.ament_cmake' and name 'ocs2_quadruped_controller'
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore'
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore_ament_install'
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_pkg']
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_pkg'
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_meta']
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_meta'
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ros']
[0.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ros'
[0.476s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/rl_quadruped_controller' with type 'ros.ament_cmake' and name 'rl_quadruped_controller'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ignore', 'ignore_ament_install']
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore'
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore_ament_install'
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_pkg']
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_pkg'
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_meta']
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_meta'
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ros']
[0.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ros'
[0.478s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/unitree_guide_controller' with type 'ros.ament_cmake' and name 'unitree_guide_controller'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ignore', 'ignore_ament_install']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore_ament_install'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_pkg']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_pkg'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_meta']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_meta'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ros']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ros'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['cmake', 'python']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'cmake'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['python_setup_py']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python_setup_py'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ignore', 'ignore_ament_install']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore_ament_install'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_pkg']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_pkg'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_meta']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_meta'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ros']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ros'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['cmake', 'python']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'cmake'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['python_setup_py']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python_setup_py'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ignore', 'ignore_ament_install']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore_ament_install'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_pkg']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_pkg'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_meta']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_meta'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ros']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ros'
[0.482s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description' with type 'ros.ament_cmake' and name 'anymal_c_description'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ignore', 'ignore_ament_install']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore_ament_install'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_pkg']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_pkg'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_meta']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_meta'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ros']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ros'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['cmake', 'python']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'cmake'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['python_setup_py']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python_setup_py'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ignore', 'ignore_ament_install']
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore'
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore_ament_install'
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_pkg']
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_pkg'
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_meta']
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_meta'
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ros']
[0.484s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ros'
[0.485s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description' with type 'ros.ament_cmake' and name 'lite3_description'
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ignore', 'ignore_ament_install']
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore'
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore_ament_install'
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_pkg']
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_pkg'
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_meta']
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_meta'
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ros']
[0.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ros'
[0.486s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description' with type 'ros.ament_cmake' and name 'x30_description'
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ignore', 'ignore_ament_install']
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore'
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore_ament_install'
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_pkg']
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_pkg'
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_meta']
[0.486s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_meta'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ros']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ros'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['cmake', 'python']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'cmake'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['python_setup_py']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python_setup_py'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ignore', 'ignore_ament_install']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore_ament_install'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_pkg']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_pkg'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_meta']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_meta'
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ros']
[0.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ros'
[0.488s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/a1_description' with type 'ros.ament_cmake' and name 'a1_description'
[0.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ignore', 'ignore_ament_install']
[0.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore'
[0.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore_ament_install'
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_pkg']
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_pkg'
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_meta']
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_meta'
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ros']
[0.489s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ros'
[0.490s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description' with type 'ros.ament_cmake' and name 'aliengo_description'
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ignore', 'ignore_ament_install']
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore'
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore_ament_install'
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_pkg']
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_pkg'
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_meta']
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_meta'
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ros']
[0.490s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ros'
[0.491s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/b2_description' with type 'ros.ament_cmake' and name 'b2_description'
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ignore', 'ignore_ament_install']
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore'
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore_ament_install'
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_pkg']
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_pkg'
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_meta']
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_meta'
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ros']
[0.492s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ros'
[0.493s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go1_description' with type 'ros.ament_cmake' and name 'go1_description'
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ignore', 'ignore_ament_install']
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore'
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore_ament_install'
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_pkg']
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_pkg'
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_meta']
[0.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_meta'
[0.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ros']
[0.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ros'
[0.494s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go2_description' with type 'ros.ament_cmake' and name 'go2_description'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ignore', 'ignore_ament_install']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore_ament_install'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_pkg']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_pkg'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_meta']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_meta'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ros']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ros'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['cmake', 'python']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'cmake'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['python_setup_py']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python_setup_py'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ignore', 'ignore_ament_install']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore_ament_install'
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_pkg']
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_pkg'
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_meta']
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_meta'
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ros']
[0.496s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ros'
[0.496s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description' with type 'ros.ament_cmake' and name 'cyberdog_description'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ignore', 'ignore_ament_install']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore_ament_install'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_pkg']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_pkg'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_meta']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_meta'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ros']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ros'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['cmake', 'python']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'cmake'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['python_setup_py']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python_setup_py'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ignore', 'ignore_ament_install']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore_ament_install'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_pkg']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_pkg'
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_meta']
[0.497s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_meta'
[0.498s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ros']
[0.498s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ros'
[0.499s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware' with type 'ros.ament_cmake' and name 'gz_quadruped_hardware'
[0.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ignore', 'ignore_ament_install']
[0.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore'
[0.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore_ament_install'
[0.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_pkg']
[0.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_pkg'
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_meta']
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_meta'
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ros']
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ros'
[0.500s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco' with type 'ros.ament_cmake' and name 'hardware_unitree_mujoco'
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ignore', 'ignore_ament_install']
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore'
[0.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore_ament_install'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_pkg']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_pkg'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_meta']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_meta'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ros']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ros'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['cmake', 'python']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'cmake'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['python_setup_py']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python_setup_py'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ignore', 'ignore_ament_install']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore_ament_install'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_pkg']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_pkg'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_meta']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_meta'
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ros']
[0.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ros'
[0.502s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/controller_common' with type 'ros.ament_cmake' and name 'controller_common'
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ignore', 'ignore_ament_install']
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore'
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore_ament_install'
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_pkg']
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_pkg'
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_meta']
[0.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_meta'
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ros']
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ros'
[0.503s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/gz_quadruped_playground' with type 'ros.ament_cmake' and name 'gz_quadruped_playground'
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore'
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore_ament_install'
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_pkg']
[0.503s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_pkg'
[0.504s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_meta']
[0.504s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_meta'
[0.504s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ros']
[0.504s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ros'
[0.504s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/qpoases_colcon' with type 'ros.ament_cmake' and name 'qpoases_colcon'
[0.504s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.504s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.504s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.504s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.504s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.551s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet'
[0.551s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_loopshaping_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc'
[0.551s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_loopshaping_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface'
[0.551s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_controller' in 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller'
[0.551s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_raisim' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadrotor_ros' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mobile_manipulator_ros' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_ros' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_double_integrator_ros' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot_ros' in 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'segmented_planes_terrain_model' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_self_collision_visualization' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadrotor' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mpcnet_core' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mobile_manipulator' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_double_integrator' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_cartpole_ros' in 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot' in 'src/ocs2_ros2/basic examples/ocs2_ballbot'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_models' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_commands' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_switched_model_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_sqp' in 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_sphere_approximation' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_self_collision' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_python_interface' in 'src/ocs2_ros2/robotics/ocs2_python_interface'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ipm' in 'src/ocs2_ros2/mpc/ocs2_ipm'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_centroidal_model' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_cartpole' in 'src/ocs2_ros2/basic examples/ocs2_cartpole'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_slp' in 'src/ocs2_ros2/mpc/ocs2_slp'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ros_interfaces' in 'src/ocs2_ros2/robotics/ocs2_ros_interfaces'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_pinocchio_interface' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ddp' in 'src/ocs2_ros2/mpc/ocs2_ddp'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'hpipm_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_robotic_tools' in 'src/ocs2_ros2/robotics/ocs2_robotic_tools'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_raisim_core' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_qp_solver' in 'src/ocs2_ros2/mpc/ocs2_qp_solver'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mpc' in 'src/ocs2_ros2/mpc/ocs2_mpc'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'rl_quadruped_controller' in 'src/quadruped_ros2_control/controllers/rl_quadruped_controller'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_oc' in 'src/ocs2_ros2/core/ocs2_oc'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition_ros' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'unitree_joystick_input' in 'src/quadruped_ros2_control/commands/unitree_joystick_input'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_switched_model_msgs' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_core' in 'src/ocs2_ros2/core/ocs2_core'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'joystick_input' in 'src/quadruped_ros2_control/commands/joystick_input'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'gz_quadruped_playground' in 'src/quadruped_ros2_control/libraries/gz_quadruped_playground'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'x30_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'qpoases_colcon' in 'src/quadruped_ros2_control/libraries/qpoases_colcon'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'qpOASES' in 'src/qpoases_vendor'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_thirdparty' in 'src/ocs2_ros2/core/ocs2_thirdparty'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_robotic_assets' in 'src/ocs2_ros2/submodules/ocs2_robotic_assets'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_msgs' in 'src/ocs2_ros2/robotics/ocs2_msgs'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'lite3_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'leg_pd_controller' in 'src/quadruped_ros2_control/controllers/leg_pd_controller'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'hardware_unitree_mujoco' in 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'gz_quadruped_hardware' in 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'grid_map_sdf' in 'src/ocs2_ros2/submodules/grid_map_sdf'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'grid_map_filters_rsl' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'go1_description' in 'src/quadruped_ros2_control/descriptions/unitree/go1_description'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'elevation_map_converter' in 'src/elevation_map_converter'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'cyberdog_description' in 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition_msgs' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'cgal5_colcon' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon'
[0.552s] INFO:colcon.colcon_core.package_selection:Skipping package 'blasfeo_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon'
[0.553s] INFO:colcon.colcon_core.package_selection:Skipping package 'b2_description' in 'src/quadruped_ros2_control/descriptions/unitree/b2_description'
[0.553s] INFO:colcon.colcon_core.package_selection:Skipping package 'anymal_c_description' in 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description'
[0.553s] INFO:colcon.colcon_core.package_selection:Skipping package 'aliengo_description' in 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description'
[0.553s] INFO:colcon.colcon_core.package_selection:Skipping package 'a1_description' in 'src/quadruped_ros2_control/descriptions/unitree/a1_description'
[0.553s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.553s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.556s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 412 installed packages in /opt/ros/humble
[0.558s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_args' from command line to 'None'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_target' from command line to 'None'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.605s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.605s] DEBUG:colcon.colcon_core.verb:Building package 'control_input_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/control_input_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/control_input_msgs', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs', 'symlink_install': True, 'test_result_base': None}
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_target' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.606s] DEBUG:colcon.colcon_core.verb:Building package 'go2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/go2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/go2_description', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description', 'symlink_install': True, 'test_result_base': None}
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_target' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_clean_cache' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_clean_first' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_force_configure' from command line to 'False'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'ament_cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'catkin_cmake_args' from command line to 'None'
[0.606s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.606s] DEBUG:colcon.colcon_core.verb:Building package 'controller_common' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/controller_common', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/controller_common', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common', 'symlink_install': True, 'test_result_base': None}
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_target' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_clean_cache' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_clean_first' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_force_configure' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'ament_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'catkin_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.607s] DEBUG:colcon.colcon_core.verb:Building package 'keyboard_input' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/keyboard_input', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/keyboard_input', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input', 'symlink_install': True, 'test_result_base': None}
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_target' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_clean_cache' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_clean_first' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_force_configure' from command line to 'False'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'ament_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'catkin_cmake_args' from command line to 'None'
[0.607s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.607s] DEBUG:colcon.colcon_core.verb:Building package 'unitree_guide_controller' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/unitree_guide_controller', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller', 'symlink_install': True, 'test_result_base': None}
[0.608s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.609s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.609s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs' with build type 'ament_cmake'
[0.609s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs'
[0.614s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.614s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.614s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.617s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description' with build type 'ament_cmake'
[0.618s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description'
[0.618s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.618s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.630s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/control_input_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/control_input_msgs -- -j32 -l32
[0.635s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/go2_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go2_description -- -j32 -l32
[0.680s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/go2_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go2_description -- -j32 -l32
[0.692s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/go2_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go2_description
[0.720s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(go2_description)
[0.721s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/go2_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go2_description
[0.725s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake module files
[0.726s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake config files
[0.726s] Level 1:colcon.colcon_core.shell:create_environment_hook('go2_description', 'cmake_prefix_path')
[0.726s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.ps1'
[0.727s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.dsv'
[0.728s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.sh'
[0.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/pkgconfig/go2_description.pc'
[0.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/python3.10/site-packages'
[0.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.730s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.ps1'
[0.730s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv'
[0.732s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.sh'
[0.732s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.bash'
[0.733s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.zsh'
[0.734s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/go2_description/share/colcon-core/packages/go2_description)
[0.735s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(go2_description)
[0.735s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake module files
[0.735s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake config files
[0.736s] Level 1:colcon.colcon_core.shell:create_environment_hook('go2_description', 'cmake_prefix_path')
[0.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.ps1'
[0.736s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.dsv'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.sh'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/pkgconfig/go2_description.pc'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/python3.10/site-packages'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.ps1'
[0.739s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.sh'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.bash'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.zsh'
[0.741s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/go2_description/share/colcon-core/packages/go2_description)
[1.077s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/control_input_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/control_input_msgs -- -j32 -l32
[1.080s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/control_input_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/control_input_msgs
[1.166s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(control_input_msgs)
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake module files
[1.167s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/control_input_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/control_input_msgs
[1.168s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake config files
[1.169s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'cmake_prefix_path')
[1.169s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.ps1'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.dsv'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.sh'
[1.170s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib'
[1.171s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'ld_library_path_lib')
[1.171s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.ps1'
[1.171s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.dsv'
[1.172s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.sh'
[1.172s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[1.172s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/pkgconfig/control_input_msgs.pc'
[1.172s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/python3.10/site-packages'
[1.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[1.173s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.ps1'
[1.174s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv'
[1.175s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.sh'
[1.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.bash'
[1.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.zsh'
[1.177s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/control_input_msgs/share/colcon-core/packages/control_input_msgs)
[1.178s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(control_input_msgs)
[1.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake module files
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake config files
[1.180s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'cmake_prefix_path')
[1.180s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.ps1'
[1.181s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.dsv'
[1.181s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.sh'
[1.182s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib'
[1.182s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'ld_library_path_lib')
[1.183s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.ps1'
[1.183s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.dsv'
[1.184s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.sh'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/pkgconfig/control_input_msgs.pc'
[1.185s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/python3.10/site-packages'
[1.185s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[1.185s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.ps1'
[1.186s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv'
[1.187s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.sh'
[1.188s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.bash'
[1.188s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.zsh'
[1.189s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/control_input_msgs/share/colcon-core/packages/control_input_msgs)
[1.190s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common' with build type 'ament_cmake'
[1.190s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common'
[1.190s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.191s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.194s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input' with build type 'ament_cmake'
[1.194s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input'
[1.194s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.194s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.202s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/controller_common': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/controller_common -- -j32 -l32
[1.205s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[1.277s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/controller_common' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/controller_common -- -j32 -l32
[1.279s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/controller_common': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/controller_common
[1.281s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[1.283s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[1.311s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(controller_common)
[1.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake module files
[1.312s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/controller_common' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/controller_common
[1.312s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake config files
[1.313s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'cmake_prefix_path')
[1.313s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.ps1'
[1.313s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.dsv'
[1.314s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.sh'
[1.315s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib'
[1.315s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'ld_library_path_lib')
[1.315s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.ps1'
[1.316s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.dsv'
[1.316s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.sh'
[1.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[1.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/pkgconfig/controller_common.pc'
[1.317s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/python3.10/site-packages'
[1.317s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[1.317s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.ps1'
[1.318s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv'
[1.318s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.sh'
[1.318s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.bash'
[1.319s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.zsh'
[1.319s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/controller_common/share/colcon-core/packages/controller_common)
[1.320s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(controller_common)
[1.320s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake module files
[1.320s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake config files
[1.321s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'cmake_prefix_path')
[1.321s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.ps1'
[1.321s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.dsv'
[1.321s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.sh'
[1.322s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib'
[1.322s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'ld_library_path_lib')
[1.322s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.ps1'
[1.323s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.dsv'
[1.323s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.sh'
[1.323s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[1.323s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/pkgconfig/controller_common.pc'
[1.323s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/python3.10/site-packages'
[1.324s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[1.324s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.ps1'
[1.325s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv'
[1.326s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.sh'
[1.326s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.bash'
[1.326s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.zsh'
[1.327s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/controller_common/share/colcon-core/packages/controller_common)
[1.327s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller' with build type 'ament_cmake'
[1.327s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller'
[1.328s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.328s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.332s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyboard_input)
[1.332s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake module files
[1.332s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[1.333s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake config files
[1.334s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyboard_input', 'cmake_prefix_path')
[1.334s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.ps1'
[1.335s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.dsv'
[1.335s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.sh'
[1.336s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib'
[1.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[1.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/pkgconfig/keyboard_input.pc'
[1.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/python3.10/site-packages'
[1.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[1.338s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.ps1'
[1.339s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv'
[1.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.sh'
[1.340s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.bash'
[1.341s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.zsh'
[1.342s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/keyboard_input/share/colcon-core/packages/keyboard_input)
[1.343s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyboard_input)
[1.343s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake module files
[1.344s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake config files
[1.344s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyboard_input', 'cmake_prefix_path')
[1.345s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.ps1'
[1.345s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.dsv'
[1.346s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.sh'
[1.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib'
[1.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[1.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/pkgconfig/keyboard_input.pc'
[1.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/python3.10/site-packages'
[1.348s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[1.348s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.ps1'
[1.349s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv'
[1.350s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.sh'
[1.350s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.bash'
[1.351s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.zsh'
[1.351s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/keyboard_input/share/colcon-core/packages/keyboard_input)
[1.357s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[1.470s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[1.472s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[1.498s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(unitree_guide_controller)
[1.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake module files
[1.499s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[1.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake config files
[1.500s] Level 1:colcon.colcon_core.shell:create_environment_hook('unitree_guide_controller', 'cmake_prefix_path')
[1.501s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.ps1'
[1.501s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.dsv'
[1.502s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.sh'
[1.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib'
[1.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[1.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/pkgconfig/unitree_guide_controller.pc'
[1.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/python3.10/site-packages'
[1.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[1.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.ps1'
[1.504s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv'
[1.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.sh'
[1.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.bash'
[1.505s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.zsh'
[1.505s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/unitree_guide_controller/share/colcon-core/packages/unitree_guide_controller)
[1.506s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(unitree_guide_controller)
[1.506s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake module files
[1.506s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake config files
[1.507s] Level 1:colcon.colcon_core.shell:create_environment_hook('unitree_guide_controller', 'cmake_prefix_path')
[1.507s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.ps1'
[1.507s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.dsv'
[1.508s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.sh'
[1.508s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib'
[1.508s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[1.508s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/pkgconfig/unitree_guide_controller.pc'
[1.509s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/python3.10/site-packages'
[1.509s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[1.509s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.ps1'
[1.510s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv'
[1.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.sh'
[1.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.bash'
[1.512s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.zsh'
[1.513s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/unitree_guide_controller/share/colcon-core/packages/unitree_guide_controller)
[1.513s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.514s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.514s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.514s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.522s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.522s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.522s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.536s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.536s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.ps1'
[1.537s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_ps1.py'
[1.539s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.ps1'
[1.540s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.sh'
[1.540s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_sh.py'
[1.541s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.sh'
[1.542s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.bash'
[1.542s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.bash'
[1.543s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.zsh'
[1.544s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.zsh'
