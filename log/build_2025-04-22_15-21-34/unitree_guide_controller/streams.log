[0.028s] Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[0.128s] [100%] Built target unitree_guide_controller
[0.142s] Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[0.143s] Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[0.152s] -- Install configuration: "Release"
[0.153s] -- Execute custom install script
[0.153s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//unitree_guide_controller.xml
[0.153s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateBalanceTest.h
[0.153s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFixedStand.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFreeStand.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateSwingTest.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateTrotting.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/UnitreeGuideController.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTools.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTypes.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/BalanceCtrl.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/CtrlComponent.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/Estimator.h
[0.154s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/LowPassFilter.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/FeetEndCalc.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/GaitGenerator.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/WaveGenerator.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/QuadrupedRobot.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/RobotLeg.h
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo.launch.py
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo_classic.launch.py
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/mujoco.launch.py
[0.155s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.sh
[0.156s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.dsv
[0.156s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/package_run_dependencies/unitree_guide_controller
[0.156s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/parent_prefix_path/unitree_guide_controller
[0.156s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.sh
[0.156s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.dsv
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.sh
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.dsv
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.bash
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.sh
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.zsh
[0.157s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.dsv
[0.157s] -- Symlinking: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/packages/unitree_guide_controller
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/unitree_guide_controller
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_targets-extras.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig-version.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.xml
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport.cmake
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport-release.cmake
[0.171s] Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
