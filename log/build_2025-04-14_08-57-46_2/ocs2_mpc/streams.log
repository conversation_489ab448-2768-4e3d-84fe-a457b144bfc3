[0.026s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_mpc': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_mpc -- -j32 -l32
[0.080s] [100%] Built target ocs2_mpc
[0.089s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_mpc' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_mpc -- -j32 -l32
[0.090s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_mpc': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_mpc
[0.099s] -- Install configuration: "Release"
[0.099s] -- Execute custom install script
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/CommandData.h
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/LoopshapingSystemObservation.h
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_BASE.h
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_MRT_Interface.h
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_Settings.h
[0.100s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MRT_BASE.h
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MrtObserver.h
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/SystemObservation.h
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.sh
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.dsv
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_mpc
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_mpc
[0.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.sh
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.dsv
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.sh
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.dsv
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.bash
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.sh
[0.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.zsh
[0.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.dsv
[0.103s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.dsv
[0.110s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/packages/ocs2_mpc
[0.110s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_targets-extras.cmake
[0.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
[0.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig.cmake
[0.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig-version.cmake
[0.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.xml
[0.111s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/lib/libocs2_mpc.a
[0.111s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport.cmake
[0.111s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport-release.cmake
[0.115s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_mpc' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_mpc
