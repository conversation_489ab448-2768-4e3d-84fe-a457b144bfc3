[0.016s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces
[0.025s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.126s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.131s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.154s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.158s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.160s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.167s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.176s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.199s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.201s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.295s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.321s] -- Found rclcpp_lifecycle: 16.0.12 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.330s] -- Found ocs2_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake)
[0.334s] -- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
[0.347s] -- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[0.372s] -- Found interactive_markers: 2.3.2 (/opt/ros/humble/share/interactive_markers/cmake)
[0.378s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.412s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.413s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.413s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/include>
[0.413s] -- Configured cppcheck exclude dirs and/or files: 
[0.414s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.414s] -- Configured cpplint exclude dirs and/or files: 
[0.414s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.414s] -- Added test 'lint_cmake' to check CMake code style
[0.414s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.415s] -- Added test 'uncrustify' to check C / C++ code style
[0.415s] -- Configured uncrustify additional arguments: 
[0.416s] -- Added test 'xmllint' to check XML markup files
[0.416s] -- Configuring done
[0.426s] -- Generating done
[0.430s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
[0.435s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces
[0.435s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ros_interfaces -- -j32 -l32
[0.473s] [35m[1mConsolidate compiler generated dependencies of target ocs2_ros_interfaces[0m
[0.502s] [ 77%] Built target ocs2_ros_interfaces
[0.509s] [35m[1mConsolidate compiler generated dependencies of target test_custom_callback_queue[0m
[0.509s] [35m[1mConsolidate compiler generated dependencies of target multiplot_remap[0m
[0.521s] [ 88%] Built target test_custom_callback_queue
[0.521s] [100%] Built target multiplot_remap
[0.528s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ros_interfaces -- -j32 -l32
[0.530s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
[0.534s] -- Install configuration: "Release"
[0.534s] -- Execute custom install script
[0.534s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesInteractiveMarker.h
[0.534s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesKeyboardPublisher.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesRosPublisher.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgConversions.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgHelpers.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mpc/MPC_ROS_Interface.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/DummyObserver.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/LoopshapingDummyObserver.h
[0.535s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Dummy_Loop.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Interface.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/RosReferenceManager.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/SolverObserverRosCallbacks.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationColors.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationHelpers.h
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch.py
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/multiplot/performance_indices.xml
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/multiplot_remap
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.sh
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.dsv
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/package_run_dependencies/ocs2_ros_interfaces
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/parent_prefix_path/ocs2_ros_interfaces
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.sh
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.dsv
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.sh
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.dsv
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.bash
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.sh
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.zsh
[0.536s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.dsv
[0.536s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.dsv
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/packages/ocs2_ros_interfaces
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_targets-extras.cmake
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig.cmake
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig-version.cmake
[0.541s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.xml
[0.541s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/libocs2_ros_interfaces.a
[0.541s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport.cmake
[0.541s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport-release.cmake
[0.542s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
