[0.022s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_commands -- -j32 -l32
[0.073s] [  8%] Built target gtest
[0.073s] [ 17%] Built target gtest_main
[0.097s] [ 56%] Built target ocs2_anymal_commands
[0.125s] [ 65%] Built target motion_command_node
[0.128s] [ 73%] Built target target_command_node
[0.131s] [ 91%] Built target test_ocs2_anymal_commands
[0.131s] [100%] Built target gait_command_node
[0.140s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_commands -- -j32 -l32
[0.142s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_commands
[0.148s] -- Install configuration: "Release"
[0.149s] -- Execute custom install script
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/LoadMotions.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ModeSequenceKeyboard.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandController.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandDummy.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandInterface.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/PoseCommandToCostDesiredRos.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ReferenceExtrapolation.h
[0.149s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/TerrainAdaptation.h
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/target_command_node
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/gait_command_node
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/motion_command_node
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/gait.info
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions.info
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/demo_motion.txt
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/walking.txt
[0.150s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.sh
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.dsv
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_commands
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_commands
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.sh
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.dsv
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.sh
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.dsv
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.bash
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.sh
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.zsh
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.dsv
[0.151s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.dsv
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/packages/ocs2_anymal_commands
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_dependencies-extras.cmake
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_targets-extras.cmake
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig.cmake
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig-version.cmake
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.xml
[0.160s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/libocs2_anymal_commands.a
[0.161s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport.cmake
[0.162s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport-release.cmake
[0.164s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_commands' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_commands
