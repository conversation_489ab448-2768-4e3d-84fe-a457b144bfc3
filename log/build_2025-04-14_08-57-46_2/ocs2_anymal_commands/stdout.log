[  8%] Built target gtest
[ 17%] Built target gtest_main
[ 56%] Built target ocs2_anymal_commands
[ 65%] Built target motion_command_node
[ 73%] Built target target_command_node
[ 91%] Built target test_ocs2_anymal_commands
[100%] Built target gait_command_node
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/LoadMotions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ModeSequenceKeyboard.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandDummy.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/MotionCommandInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/PoseCommandToCostDesiredRos.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/ReferenceExtrapolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/include/ocs2_anymal_commands/ocs2_anymal_commands/TerrainAdaptation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/target_command_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/gait_command_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/ocs2_anymal_commands/motion_command_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/gait.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/demo_motion.txt
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands//config/motions/walking.txt
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_commands
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_commands
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ament_index/resource_index/packages/ocs2_anymal_commands
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/ocs2_anymal_commandsConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib/libocs2_anymal_commands.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_commands/share/ocs2_anymal_commands/cmake/export_ocs2_anymal_commandsExport-release.cmake
