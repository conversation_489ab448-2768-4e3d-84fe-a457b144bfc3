[ 16%] Built target gtest
[ 33%] Built target gtest_main
[ 50%] Built target ocs2_anymal_mpc
[ 66%] Built target ocs2_anymal_mpc_test
[100%] Built target ocs2_anymal_mpc_dummy_mrt_node
[100%] Built target ocs2_anymal_mpc_mpc_node
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/include/ocs2_anymal_mpc/ocs2_anymal_mpc/AnymalInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//config/c_series/frame_declaration.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//config/c_series/multiple_shooting.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//config/c_series/targetCommand.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//config/c_series/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//launch/anymal_c.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc//launch/mpc.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib/ocs2_anymal_mpc/ocs2_anymal_mpc_mpc_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib/ocs2_anymal_mpc/ocs2_anymal_mpc_dummy_mrt_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ament_index/resource_index/packages/ocs2_anymal_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/ocs2_anymal_mpcConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/ocs2_anymal_mpcConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib/libocs2_anymal_mpc.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/export_ocs2_anymal_mpcExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake/export_ocs2_anymal_mpcExport-release.cmake
