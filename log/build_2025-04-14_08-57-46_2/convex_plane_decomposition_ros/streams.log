[0.012s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros -- -j32 -l32
[0.084s] [ 33%] Built target convex_plane_decomposition_ros
[0.110s] [ 46%] Built target convex_plane_decomposition_ros_save_elevationmap
[0.115s] [ 60%] Built target convex_plane_decomposition_ros_add_noise
[0.116s] [ 73%] Built target convex_plane_decomposition_ros_node
[0.124s] [ 86%] Built target convex_plane_decomposition_ros_TestShapeGrowing
[0.125s] [100%] Built target convex_plane_decomposition_ros_approximation_demo_node
[0.134s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros -- -j32 -l32
[0.136s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros
[0.140s] -- Install configuration: "Release"
[0.141s] -- Execute custom install script
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/ConvexPlaneDecompositionRos.h
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/MessageConversion.h
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/ParameterLoading.h
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/RosVisualizations.h
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/demo_node.yaml
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/node.yaml
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/parameters.yaml
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/holes.png
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/real_stairs_125cm.png
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/slope_1m_1m_20cm.png
[0.142s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/straight_stairs_1m_1m_60cm.png
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/terrain.png
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/convex_plane_decomposition.launch.py
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/demo.launch.py
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/save_elevation_map.launch.py
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/rviz/config_demo.rviz
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/library_path.sh
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/library_path.dsv
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_node
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_add_noise
[0.143s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_save_elevationmap
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_approximation_demo_node
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_TestShapeGrowing
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/package_run_dependencies/convex_plane_decomposition_ros
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/parent_prefix_path/convex_plane_decomposition_ros
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/ament_prefix_path.sh
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/ament_prefix_path.dsv
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/path.sh
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/path.dsv
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.bash
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.sh
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.zsh
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.dsv
[0.145s] -- Symlinking: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/package.dsv
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/packages/convex_plane_decomposition_ros
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/ament_cmake_export_dependencies-extras.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/ament_cmake_export_targets-extras.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/convex_plane_decomposition_rosConfig.cmake
[0.153s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/convex_plane_decomposition_rosConfig-version.cmake
[0.153s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/package.xml
[0.153s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/libconvex_plane_decomposition_ros.a
[0.153s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/export_convex_plane_decomposition_rosExport.cmake
[0.153s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/export_convex_plane_decomposition_rosExport-release.cmake
[0.164s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros
