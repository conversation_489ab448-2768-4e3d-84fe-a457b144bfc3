[0.018s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_switched_model_interface -- -j32 -l32
[0.069s] [  5%] Built target gtest
[0.069s] [  5%] Built target gtest_main
[0.127s] [ 67%] Built target ocs2_switched_model_interface
[0.156s] [ 79%] Built target test_ocs2_switched_model_interface_footplanner
[0.156s] [ 79%] Built target test_ocs2_switched_model_interface_convexTerrain
[0.156s] [ 79%] Built target test_ocs2_switched_model_interface_terrain
[0.156s] [ 80%] Built target test_constraints
[0.159s] [ 83%] Built target test_ocs2_switched_model_interface_core
[0.165s] [ 94%] Built target test_ocs2_switched_model_interface_logic
[0.165s] [100%] Built target test_ocs2_switched_model_interface_cost
[0.179s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_switched_model_interface -- -j32 -l32
[0.180s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
[0.187s] -- Install configuration: "Release"
[0.187s] -- Execute custom install script
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/AnalyticalInverseKinematics.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/LegInverseKinematicParameters.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/EndEffectorVelocityConstraint.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FootNormalConstraint.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FrictionConeConstraint.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/ZeroForceConstraint.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ComModelBase.h
[0.188s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/InverseKinematicsModelBase.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/KinematicsModelBase.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ModelSettings.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/MotionPhaseDefinition.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/Rotations.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModel.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModelPrecomputation.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/TorqueApproximation.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CollisionAvoidanceCost.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CostElements.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FootPlacementCost.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FrictionConeCost.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/LinearStateInequalitySoftconstraint.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingCost.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingTerminalCost.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/TorqueLimitsSoftConstraint.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoDynamicsParameters.h
[0.189s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoSystemDynamicsAd.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/CubicSpline.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/FootPhase.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/KinematicFootPlacementPenalty.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/QuinticSplineSwing.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SplineCpg.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingSpline3d.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingTrajectoryPlanner.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/initialization/ComKinoInitializer.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/DynamicsParametersSynchronizedModule.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/Gait.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitAdaptation.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitReceiver.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSchedule.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSwitching.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/ModeSequenceTemplate.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SingleLegLogic.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SwitchedModelModeScheduleManager.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/ros_msg_conversions/RosMsgConversions.h
[0.190s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/ConvexTerrain.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarSignedDistanceField.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarTerrainModel.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlaneFitting.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/SignedDistanceField.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainModel.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainPlane.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/test/TestEvaluateConstraints.h
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.sh
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.dsv
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_switched_model_interface
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_switched_model_interface
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.sh
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.dsv
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.sh
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.dsv
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.bash
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.sh
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.zsh
[0.191s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.dsv
[0.191s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.dsv
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/packages/ocs2_switched_model_interface
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_dependencies-extras.cmake
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_targets-extras.cmake
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig.cmake
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig-version.cmake
[0.200s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.xml
[0.200s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib/libocs2_switched_model_interface.a
[0.200s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport.cmake
[0.200s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport-release.cmake
[0.202s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_switched_model_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_switched_model_interface
