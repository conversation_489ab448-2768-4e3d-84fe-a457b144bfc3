[ 22%] Built target gtest_main
[ 44%] Built target gtest
[ 66%] Built target grid_map_sdf
[100%] Built target grid_map_sdf-test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/lib/libgrid_map_sdf.a
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/package_run_dependencies/grid_map_sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/parent_prefix_path/grid_map_sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/packages/grid_map_sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_libraries-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/grid_map_sdfConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/grid_map_sdfConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/pnmfile.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/dt.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/image.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/imutil.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/imconv.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/misc.hpp
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/SignedDistanceField.hpp
