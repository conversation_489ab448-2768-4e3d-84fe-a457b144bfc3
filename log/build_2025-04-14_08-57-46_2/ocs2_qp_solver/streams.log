[0.025s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_qp_solver': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_qp_solver -- -j32 -l32
[0.071s] [ 15%] Built target gtest_main
[0.072s] [ 30%] Built target gtest
[0.077s] [ 69%] Built target ocs2_qp_solver
[0.098s] [100%] Built target test_ocs2_qp_solver
[0.126s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_qp_solver' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_qp_solver -- -j32 -l32
[0.128s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_qp_solver': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_qp_solver
[0.135s] -- Install configuration: "Release"
[0.136s] -- Execute custom install script
[0.136s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/Ocs2QpSolver.h
[0.136s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/QpDiscreteTranscription.h
[0.136s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/QpSolver.h
[0.136s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/QpSolverTypes.h
[0.137s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/QpTrajectories.h
[0.137s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/include/ocs2_qp_solver/ocs2_qp_solver/test/testProblemsGeneration.h
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/library_path.sh
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/library_path.dsv
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ament_index/resource_index/package_run_dependencies/ocs2_qp_solver
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ament_index/resource_index/parent_prefix_path/ocs2_qp_solver
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/ament_prefix_path.sh
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/ament_prefix_path.dsv
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/path.sh
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/environment/path.dsv
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/local_setup.bash
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/local_setup.sh
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/local_setup.zsh
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/local_setup.dsv
[0.141s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/package.dsv
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ament_index/resource_index/packages/ocs2_qp_solver
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/ament_cmake_export_dependencies-extras.cmake
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/ament_cmake_export_targets-extras.cmake
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/ocs2_qp_solverConfig.cmake
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/ocs2_qp_solverConfig-version.cmake
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/package.xml
[0.147s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_qp_solver/lib/libocs2_qp_solver.a
[0.147s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/export_ocs2_qp_solverExport.cmake
[0.147s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake/export_ocs2_qp_solverExport-release.cmake
[0.166s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_qp_solver' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_qp_solver
