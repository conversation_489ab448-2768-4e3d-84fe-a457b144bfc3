[ 10%] Built target gtest
[ 20%] Built target gtest_main
[ 55%] Built target ocs2_anymal_models
[ 65%] Built target ocs2_anymal_models_switched_model_test
[100%] Built target TestQuadrupedPinocchio
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/AnymalModels.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/DynamicsHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/FrameDeclaration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedCom.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedInverseKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedPinocchioMapping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h.in
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//config/visualize_urdf.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//launch/visualize.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/anymal_camel_rsl.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/frame_declaration_anymal_c.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/packages/ocs2_anymal_models
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/lib/libocs2_anymal_models.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport-release.cmake
