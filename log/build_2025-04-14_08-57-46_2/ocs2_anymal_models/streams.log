[0.023s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_models -- -j32 -l32
[0.070s] [ 10%] Built target gtest
[0.070s] [ 20%] Built target gtest_main
[0.099s] [ 55%] Built target ocs2_anymal_models
[0.126s] [ 65%] Built target ocs2_anymal_models_switched_model_test
[0.144s] [100%] Built target TestQuadrupedPinocchio
[0.157s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_models -- -j32 -l32
[0.158s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_models
[0.163s] -- Install configuration: "Release"
[0.163s] -- Execute custom install script
[0.163s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/.gitignore
[0.163s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/AnymalModels.h
[0.163s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/DynamicsHelpers.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/FrameDeclaration.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedCom.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedInverseKinematics.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedKinematics.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/QuadrupedPinocchioMapping.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/include/ocs2_anymal_models/ocs2_anymal_models/package_path.h.in
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//config/visualize_urdf.rviz
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//launch/visualize.launch.py
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/anymal_camel_rsl.urdf
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models//urdf/frame_declaration_anymal_c.info
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.sh
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/library_path.dsv
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_models
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_models
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.sh
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/ament_prefix_path.dsv
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.sh
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/environment/path.dsv
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.bash
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.sh
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.zsh
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/local_setup.dsv
[0.165s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.dsv
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ament_index/resource_index/packages/ocs2_anymal_models
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_dependencies-extras.cmake
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ament_cmake_export_targets-extras.cmake
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig.cmake
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/ocs2_anymal_modelsConfig-version.cmake
[0.175s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/package.xml
[0.175s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/lib/libocs2_anymal_models.a
[0.175s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport.cmake
[0.176s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_models/share/ocs2_anymal_models/cmake/export_ocs2_anymal_modelsExport-release.cmake
[0.177s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_models' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_models
