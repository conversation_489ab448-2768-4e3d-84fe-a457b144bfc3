-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_anymal_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/share/ocs2_anymal_mpc/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found Boost: /usr/local/include (found version "1.74.0") found components: system filesystem 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found ocs2_quadruped_loopshaping_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/share/ocs2_quadruped_loopshaping_interface/cmake)
-- Found std_srvs: 4.8.0 (/opt/ros/humble/share/std_srvs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc[0m
[ 12%] Built target gtest
[ 25%] Built target gtest_main
[ 31%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc.dir/src/AnymalLoopshapingInterface.cpp.o[0m
[ 37%] [32m[1mLinking CXX static library libocs2_anymal_loopshaping_mpc.a[0m
[ 37%] Built target ocs2_anymal_loopshaping_mpc
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_dummy_mrt_node[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_mpc_node[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_perceptive_demo[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_test[0m
[ 43%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_dummy_mrt_node.dir/src/AnymalLoopshapingDummyMrt.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_mpc_node.dir/src/AnymalLoopshapingMpcNode.cpp.o[0m
[ 56%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_test.dir/test/testMotionTracking.cpp.o[0m
[ 62%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_test.dir/test/testProblemFormulation.cpp.o[0m
[ 68%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_test.dir/test/testSensitivity.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_demo.dir/src/PerceptiveMpcDemo.cpp.o[0m
[ 81%] [32m[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_dummy_mrt_node[0m
[ 87%] [32m[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_mpc_node[0m
[ 93%] [32m[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_test[0m
[ 93%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node
[ 93%] Built target ocs2_anymal_loopshaping_mpc_test
[ 93%] Built target ocs2_anymal_loopshaping_mpc_mpc_node
[100%] [32m[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_perceptive_demo[0m
[100%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/include/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc/AnymalLoopshapingInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_mpc_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_dummy_mrt_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_demo
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/frame_declaration.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/loopshaping.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/multiple_shooting.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/rviz/demo_config.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/anymal_c.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/mpc.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_demo.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/packages/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/libocs2_anymal_loopshaping_mpc.a
-- Old export file "/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake].
-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake
-- Installing: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake
