[0.000000] (-) TimerEvent: {}
[0.000488] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000516] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000562] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000599] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000626] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000643] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000659] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000676] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000691] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000735] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000872] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000913] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000949] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000974] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000990] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001004] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001019] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001033] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001067] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001092] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001100] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001107] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001114] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001119] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001125] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001131] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001137] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001143] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001150] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001156] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001162] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001168] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001174] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001180] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001186] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001205] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001211] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001227] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001233] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001241] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001247] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001253] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001260] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001266] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001273] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001279] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001285] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001291] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001297] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001303] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001309] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001315] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001321] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001327] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001333] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001339] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001345] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001351] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.001357] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.001364] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.001370] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.001381] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.001387] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.001394] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.001400] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.001406] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.001413] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.001419] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.001424] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001430] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001437] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001443] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001449] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001456] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.001463] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.001469] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001478] (elevation_map_converter) JobQueued: {'identifier': 'elevation_map_converter', 'dependencies': OrderedDict()}
[0.001492] (elevation_map_converter) JobStarted: {'identifier': 'elevation_map_converter'}
[0.005294] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'cmake'}
[0.005531] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'build'}
[0.005826] (elevation_map_converter) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/elevation_map_converter', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/elevation_map_converter', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3132'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=a2671b26ca2b98e1755544da6806ebf4'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3132,unix/cg215:/tmp/.ICE-unix/3132'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/12d7a0fe_2fbe_4614_ba5d_e9e82d49fc37'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.130'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/elevation_map_converter'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=a2671b26ca2b98e1755544da6806ebf4'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.050261] (elevation_map_converter) StdoutLine: {'line': b'[100%] Built target elevation_map_to_image_node\n'}
[0.056956] (elevation_map_converter) CommandEnded: {'returncode': 0}
[0.057167] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'install'}
[0.061872] (elevation_map_converter) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/elevation_map_converter'], 'cwd': '/home/<USER>/ros2_ws/build/elevation_map_converter', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3132'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=a2671b26ca2b98e1755544da6806ebf4'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3132,unix/cg215:/tmp/.ICE-unix/3132'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/12d7a0fe_2fbe_4614_ba5d_e9e82d49fc37'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.130'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/elevation_map_converter'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=a2671b26ca2b98e1755544da6806ebf4'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.066842] (elevation_map_converter) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.066979] (elevation_map_converter) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.067030] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/elevation_map_to_image_node\n'}
[0.067075] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/fix_terrain_orientation_node.py\n'}
[0.067379] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/elevation_map_converter.launch.py\n'}
[0.067461] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/fix_terrain_orientation.launch.py\n'}
[0.067490] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/package_run_dependencies/elevation_map_converter\n'}
[0.067515] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/parent_prefix_path/elevation_map_converter\n'}
[0.067561] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.sh\n'}
[0.067586] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.dsv\n'}
[0.067627] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.sh\n'}
[0.067650] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.dsv\n'}
[0.067699] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.bash\n'}
[0.067721] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.sh\n'}
[0.067779] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.zsh\n'}
[0.067801] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.dsv\n'}
[0.067874] (elevation_map_converter) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.dsv\n'}
[0.073432] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/packages/elevation_map_converter\n'}
[0.073623] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig.cmake\n'}
[0.073647] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig-version.cmake\n'}
[0.073731] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.xml\n'}
[0.074562] (elevation_map_converter) CommandEnded: {'returncode': 0}
[0.087213] (elevation_map_converter) JobEnded: {'identifier': 'elevation_map_converter', 'rc': 0}
[0.087531] (-) EventReactorShutdown: {}
