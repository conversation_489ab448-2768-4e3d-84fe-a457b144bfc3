[0.006s] Invoking command in '/home/<USER>/ros2_ws/build/elevation_map_converter': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/elevation_map_converter -- -j32 -l32
[0.051s] [100%] Built target elevation_map_to_image_node
[0.058s] Invoked command in '/home/<USER>/ros2_ws/build/elevation_map_converter' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/elevation_map_converter -- -j32 -l32
[0.064s] Invoking command in '/home/<USER>/ros2_ws/build/elevation_map_converter': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/elevation_map_converter
[0.069s] -- Install configuration: ""
[0.070s] -- Execute custom install script
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/elevation_map_to_image_node
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/fix_terrain_orientation_node.py
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/elevation_map_converter.launch.py
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/fix_terrain_orientation.launch.py
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/package_run_dependencies/elevation_map_converter
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/parent_prefix_path/elevation_map_converter
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.sh
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.dsv
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.sh
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.dsv
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.bash
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.sh
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.zsh
[0.070s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.dsv
[0.070s] -- Symlinking: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.dsv
[0.077s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/packages/elevation_map_converter
[0.077s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig.cmake
[0.077s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig-version.cmake
[0.077s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.xml
[0.078s] Invoked command in '/home/<USER>/ros2_ws/build/elevation_map_converter' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/elevation_map_converter
