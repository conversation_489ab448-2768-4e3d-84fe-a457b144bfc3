[0.000000] (-) TimerEvent: {}
[0.000205] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000276] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000295] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000313] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000329] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000352] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000374] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000413] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000432] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000447] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000463] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000499] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000522] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000538] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000554] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000570] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000586] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000626] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000641] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000661] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000676] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000817] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000857] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000878] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000911] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000935] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000950] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000968] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000984] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001006] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001042] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001056] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001072] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001091] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001122] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001145] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001161] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001190] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001216] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001225] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001232] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001239] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001244] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001250] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001257] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001263] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001269] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001275] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001281] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001287] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001293] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001299] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001305] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001311] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001327] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001332] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001340] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001347] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.001354] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.001360] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.001367] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.001377] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.001384] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.001390] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.001397] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.001403] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.001409] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.001416] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.001423] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001429] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001436] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001442] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001449] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001455] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.001461] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001470] (elevation_map_converter) JobQueued: {'identifier': 'elevation_map_converter', 'dependencies': OrderedDict()}
[0.001480] (elevation_map_converter) JobStarted: {'identifier': 'elevation_map_converter'}
[0.005333] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'cmake'}
[0.005587] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'build'}
[0.005948] (elevation_map_converter) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/elevation_map_converter', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/elevation_map_converter', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursoraCGAHM'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursoraCGAHM/usr/share/perl5/:/tmp/.mount_cursoraCGAHM/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '1260843'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '8fae1f1a78b24eea9b62c9a8fef88a1a'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursoraCGAHM/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/elevation_map_converter'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursoraCGAHM/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursoraCGAHM/usr/lib/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib32/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib64/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib32/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.052733] (elevation_map_converter) StdoutLine: {'line': b'[100%] Built target elevation_map_to_image_node\n'}
[0.059170] (elevation_map_converter) CommandEnded: {'returncode': 0}
[0.059970] (elevation_map_converter) JobProgress: {'identifier': 'elevation_map_converter', 'progress': 'install'}
[0.065649] (elevation_map_converter) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/elevation_map_converter'], 'cwd': '/home/<USER>/ros2_ws/build/elevation_map_converter', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursoraCGAHM'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursoraCGAHM/usr/share/perl5/:/tmp/.mount_cursoraCGAHM/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursoraCGAHM/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '1260843'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '8fae1f1a78b24eea9b62c9a8fef88a1a'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursoraCGAHM/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/elevation_map_converter'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursoraCGAHM/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursoraCGAHM/usr/lib/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib32/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib64/qt4/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib32/qt5/plugins/:/tmp/.mount_cursoraCGAHM/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.070875] (elevation_map_converter) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.071045] (elevation_map_converter) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.071103] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/elevation_map_to_image_node\n'}
[0.071132] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/lib/elevation_map_converter/fix_terrain_orientation_node.py\n'}
[0.071286] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/elevation_map_converter.launch.py\n'}
[0.071348] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter//launch/fix_terrain_orientation.launch.py\n'}
[0.071401] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/package_run_dependencies/elevation_map_converter\n'}
[0.071546] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/parent_prefix_path/elevation_map_converter\n'}
[0.071635] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.sh\n'}
[0.071667] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/ament_prefix_path.dsv\n'}
[0.071693] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.sh\n'}
[0.071742] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/environment/path.dsv\n'}
[0.071778] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.bash\n'}
[0.071823] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.sh\n'}
[0.071849] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.zsh\n'}
[0.071885] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/local_setup.dsv\n'}
[0.071923] (elevation_map_converter) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.dsv\n'}
[0.078279] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/ament_index/resource_index/packages/elevation_map_converter\n'}
[0.078344] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig.cmake\n'}
[0.078375] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/cmake/elevation_map_converterConfig-version.cmake\n'}
[0.078412] (elevation_map_converter) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/elevation_map_converter/share/elevation_map_converter/package.xml\n'}
[0.079479] (elevation_map_converter) CommandEnded: {'returncode': 0}
[0.092289] (elevation_map_converter) JobEnded: {'identifier': 'elevation_map_converter', 'rc': 0}
[0.092759] (-) EventReactorShutdown: {}
