[0.012s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ros_interfaces -- -j32 -l32
[0.101s] [ 70%] Built target ocs2_ros_interfaces
[0.124s] [ 80%] Built target test_custom_callback_queue
[0.124s] [ 90%] Built target multiplot_remap
[0.126s] [100%] Built target perceptive_mpc_keyboard_control
[0.134s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ros_interfaces -- -j32 -l32
[0.140s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
[0.144s] -- Install configuration: "Release"
[0.144s] -- Execute custom install script
[0.144s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/perceptive_mpc_keyboard_control
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesInteractiveMarker.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesKeyboardPublisher.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/command/TargetTrajectoriesRosPublisher.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgConversions.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/common/RosMsgHelpers.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mpc/MPC_ROS_Interface.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/DummyObserver.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/LoopshapingDummyObserver.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Dummy_Loop.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/mrt/MRT_ROS_Interface.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/RosReferenceManager.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/synchronized_module/SolverObserverRosCallbacks.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationColors.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/include/ocs2_ros_interfaces/ocs2_ros_interfaces/visualization/VisualizationHelpers.h
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/launch/performance_indices.launch.py
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/multiplot/performance_indices.xml
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/ocs2_ros_interfaces/multiplot_remap
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.sh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/library_path.dsv
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/package_run_dependencies/ocs2_ros_interfaces
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/parent_prefix_path/ocs2_ros_interfaces
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.sh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/ament_prefix_path.dsv
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.sh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/environment/path.dsv
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.bash
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.sh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.zsh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/local_setup.dsv
[0.146s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.dsv
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ament_index/resource_index/packages/ocs2_ros_interfaces
[0.151s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ament_cmake_export_targets-extras.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/ocs2_ros_interfacesConfig-version.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/package.xml
[0.152s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib/libocs2_ros_interfaces.a
[0.152s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport.cmake
[0.152s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake/export_ocs2_ros_interfacesExport-release.cmake
[0.153s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ros_interfaces' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
