[  3%] Built target gtest_main
[  7%] Built target gtest
[ 68%] Built target ocs2_oc
[ 72%] Built target test_trajectory_spreading
[ 75%] Built target test_change_of_variables
[ 79%] Built target test_precondition
[ 83%] Built target test_ocs2_oc_data
[ 87%] Built target test_ocp_to_kkt
[ 92%] Built target test_ocs2_oc_rollout
[100%] Built target test_ocs2_oc_multiple_shooting
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/ChangeOfInputVariables.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/LinearQuadraticApproximator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Helpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Initialization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/LagrangianEvaluation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/MetricsComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/PerformanceIndexComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/ProjectionMultiplierCoefficients.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Transcription.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/DualSolution.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/LoopshapingPrimalSolution.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PerformanceIndex.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PrimalSolution.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/ProblemMetrics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/TimeDiscretization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/LoopshapingOptimalControlProblem.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpSize.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpToKkt.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblem.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblemHelperFunction.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_solver/SolverBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/precondition/Ruzi.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/InitializerRollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/PerformanceIndicesRollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinder.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinderType.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/StateTriggeredRollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/TimeTriggeredRollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/search_strategy/FilterLinesearch.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingSynchronizedModule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerDecorator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverObserver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverSynchronizedModule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreading.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreadingHelperFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/DoubleIntegratorReachingTask.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP0.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP1.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/ball_dynamics_staterollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/circular_kinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/dynamics_hybrid_slq_test.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/pendulum_dynamics_staterollout.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/testProblemsGeneration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/package_run_dependencies/ocs2_oc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/parent_prefix_path/ocs2_oc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/packages/ocs2_oc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/lib/libocs2_oc.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport-release.cmake
