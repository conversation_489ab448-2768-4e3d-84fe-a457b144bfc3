[  2%] Built target gtest
[  5%] Built target gtest_main
[ 67%] Built target ocs2_switched_model_interface
[ 70%] Built target test_ocs2_switched_model_interface_footplanner
[ 73%] Built target test_ocs2_switched_model_interface_terrain
[ 82%] Built target test_ocs2_switched_model_interface_cost
[ 82%] Built target test_ocs2_switched_model_interface_convexTerrain
[ 85%] Built target test_ocs2_switched_model_interface_core
[ 89%] Built target test_constraints
[100%] Built target test_ocs2_switched_model_interface_logic
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/AnalyticalInverseKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/analytical_inverse_kinematics/LegInverseKinematicParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/EndEffectorVelocityConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FootNormalConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/FrictionConeConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/constraint/ZeroForceConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ComModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/InverseKinematicsModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/KinematicsModelBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/ModelSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/MotionPhaseDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/Rotations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/SwitchedModelPrecomputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/core/TorqueApproximation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CollisionAvoidanceCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/CostElements.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FootPlacementCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/FrictionConeCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/LinearStateInequalitySoftconstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/MotionTrackingTerminalCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/cost/TorqueLimitsSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoDynamicsParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/dynamics/ComKinoSystemDynamicsAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/CubicSpline.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/FootPhase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/KinematicFootPlacementPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/QuinticSplineSwing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SplineCpg.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingSpline3d.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/foot_planner/SwingTrajectoryPlanner.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/initialization/ComKinoInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/DynamicsParametersSynchronizedModule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/Gait.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitAdaptation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/GaitSwitching.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/ModeSequenceTemplate.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SingleLegLogic.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/logic/SwitchedModelModeScheduleManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/ros_msg_conversions/RosMsgConversions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/ConvexTerrain.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarSignedDistanceField.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlanarTerrainModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/PlaneFitting.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/SignedDistanceField.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/terrain/TerrainPlane.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/include/ocs2_switched_model_interface/ocs2_switched_model_interface/test/TestEvaluateConstraints.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ament_index/resource_index/packages/ocs2_switched_model_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/ocs2_switched_model_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib/libocs2_switched_model_interface.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_switched_model_interface/share/ocs2_switched_model_interface/cmake/export_ocs2_switched_model_interfaceExport-release.cmake
