[100%] Built target ocs2_quadruped_interface
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedDummyNode.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedLogger.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedMpc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedMpcNode.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedPointfootInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedTfPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/QuadrupedVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/SwingPlanningVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/TerrainPlaneVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/include/ocs2_quadruped_interface/ocs2_quadruped_interface/TerrainReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface//config/config.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface//launch/visualization.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ament_index/resource_index/packages/ocs2_quadruped_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ocs2_quadruped_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/ocs2_quadruped_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib/libocs2_quadruped_interface.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/export_ocs2_quadruped_interfaceExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_interface/share/ocs2_quadruped_interface/cmake/export_ocs2_quadruped_interfaceExport-release.cmake
