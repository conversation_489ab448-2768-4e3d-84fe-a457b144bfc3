[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc[0m
[ 12%] Built target gtest
[ 25%] Built target gtest_main
[ 37%] Built target ocs2_anymal_loopshaping_mpc
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_mpc_node[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_test[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_dummy_mrt_node[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_anymal_loopshaping_mpc_perceptive_demo[0m
[ 50%] Built target ocs2_anymal_loopshaping_mpc_mpc_node
[ 62%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node
[ 87%] Built target ocs2_anymal_loopshaping_mpc_test
[100%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/include/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc/AnymalLoopshapingInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_mpc_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_dummy_mrt_node
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_demo
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/frame_declaration.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/loopshaping.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/multiple_shooting.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/rviz/demo_config.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/anymal_c.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/mpc.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_demo.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/packages/ocs2_anymal_loopshaping_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/libocs2_anymal_loopshaping_mpc.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake
