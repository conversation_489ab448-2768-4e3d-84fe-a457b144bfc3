[ 22%] Built target gtest
[ 22%] Built target gtest_main
[ 33%] Built target ocs2_anymal_loopshaping_mpc
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o[0m
[ 50%] Built target ocs2_anymal_loopshaping_mpc_mpc_node
[ 61%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node
[ 83%] Built target ocs2_anymal_loopshaping_mpc_test
[ 94%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:235:23:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass ocs2::MRT_ROS_Interface[m[K’ has no member named ‘[01m[KsetPolicy[m[K’; did you mean ‘[01m[KgetPolicy[m[K’?
  235 |     mrt_ros_interface.[01;31m[KsetPolicy[m[K(mpcMrtInterface.getPolicy());
      |                       [01;31m[K^~~~~~~~~[m[K
      |                       [32m[KgetPolicy[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
