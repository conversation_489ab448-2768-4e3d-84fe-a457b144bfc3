[0.110s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'd435i_detection_ros2']
[0.110s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['d435i_detection_ros2'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x797abad96c80>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x797abad967a0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x797abad967a0>>, mixin_verb=('build',))
[0.151s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.152s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/FAST_LIO) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/FAST_LIO' with type 'ros.ament_cmake' and name 'fast_lio'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'ros'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['cmake', 'python']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'cmake'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'python'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extensions ['python_setup_py']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i) by extension 'python_setup_py'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/build) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/build) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/build) ignored
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/install) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/install) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/install) ignored
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/log) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/log) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/log) ignored
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'ros'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['cmake', 'python']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'cmake'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'python'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extensions ['python_setup_py']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src) by extension 'python_setup_py'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/realsense_ros_gazebo-humble) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/d435i/src/realsense_ros_gazebo-humble' with type 'ros.ament_cmake' and name 'realsense_ros_gazebo'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i/src/robot_p) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/d435i/src/robot_p' with type 'ros.ament_python' and name 'robot_p'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detection_ros2) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/d435i_detection_ros2' with type 'ros.ament_python' and name 'd435i_detection_ros2'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detetion) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detetion) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/d435i_detetion) ignored
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/elevation_map_converter' with type 'ros.ament_cmake' and name 'elevation_map_converter'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/livox_ros_driver2) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/livox_ros_driver2' with type 'ros.ament_cmake' and name 'livox_ros_driver2'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ros'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['cmake', 'python']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'cmake'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['python_setup_py']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python_setup_py'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ros'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['cmake', 'python']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'cmake'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['python_setup_py']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python_setup_py'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ros'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['cmake', 'python']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'cmake'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['python_setup_py']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python_setup_py'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_ballbot_mpcnet'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_mpcnet'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core' with type 'ros.ament_cmake' and name 'ocs2_mpcnet_core'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ros'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['cmake', 'python']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'cmake'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['python_setup_py']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python_setup_py'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ros'
[0.186s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands' with type 'ros.ament_cmake' and name 'ocs2_anymal_commands'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_loopshaping_mpc'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models' with type 'ros.ament_cmake' and name 'ocs2_anymal_models'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ros'
[0.188s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_mpc'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_meta'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ros']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ros'
[0.190s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_interface'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ros'
[0.191s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_loopshaping_interface'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ros'
[0.193s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface' with type 'ros.ament_cmake' and name 'ocs2_switched_model_interface'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs' with type 'ros.ament_cmake' and name 'ocs2_switched_model_msgs'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model' with type 'ros.ament_cmake' and name 'segmented_planes_terrain_model'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['cmake', 'python']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'cmake'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['python_setup_py']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python_setup_py'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ros'
[0.197s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_raisim'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ros'
[0.198s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core' with type 'ros.ament_cmake' and name 'ocs2_raisim_core'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot' with type 'ros.ament_cmake' and name 'ocs2_ballbot'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ros'
[0.202s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros' with type 'ros.ament_cmake' and name 'ocs2_ballbot_ros'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole' with type 'ros.ament_cmake' and name 'ocs2_cartpole'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros' with type 'ros.ament_cmake' and name 'ocs2_cartpole_ros'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator' with type 'ros.ament_cmake' and name 'ocs2_double_integrator'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ros'
[0.206s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros' with type 'ros.ament_cmake' and name 'ocs2_double_integrator_ros'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ros'
[0.207s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot' with type 'ros.ament_cmake' and name 'ocs2_legged_robot'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ros'
[0.209s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ros'
[0.210s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator_ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ros'
[0.213s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor' with type 'ros.ament_cmake' and name 'ocs2_quadrotor'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros' with type 'ros.ament_cmake' and name 'ocs2_quadrotor_ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python_setup_py'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_core' with type 'ros.ament_cmake' and name 'ocs2_core'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_oc' with type 'ros.ament_cmake' and name 'ocs2_oc'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ros'
[0.217s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_thirdparty' with type 'ros.ament_cmake' and name 'ocs2_thirdparty'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ros'
[0.218s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ddp' with type 'ros.ament_cmake' and name 'ocs2_ddp'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ros'
[0.219s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ipm' with type 'ros.ament_cmake' and name 'ocs2_ipm'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ros'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_mpc' with type 'ros.ament_cmake' and name 'ocs2_mpc'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ros'
[0.221s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_qp_solver' with type 'ros.ament_cmake' and name 'ocs2_qp_solver'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_slp' with type 'ros.ament_cmake' and name 'ocs2_slp'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ros'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['cmake', 'python']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'cmake'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['python_setup_py']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon' with type 'ros.ament_cmake' and name 'blasfeo_colcon'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon' with type 'ros.ament_cmake' and name 'hpipm_colcon'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp' with type 'ros.ament_cmake' and name 'ocs2_sqp'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ros'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['cmake', 'python']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'cmake'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['python_setup_py']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python_setup_py'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ros'
[0.226s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_msgs' with type 'ros.ament_cmake' and name 'ocs2_msgs'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ros'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['cmake', 'python']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ros'
[0.227s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model' with type 'ros.ament_cmake' and name 'ocs2_centroidal_model'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ros'
[0.228s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface' with type 'ros.ament_cmake' and name 'ocs2_pinocchio_interface'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ros'
[0.229s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision' with type 'ros.ament_cmake' and name 'ocs2_self_collision'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ros'
[0.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization' with type 'ros.ament_cmake' and name 'ocs2_self_collision_visualization'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ros'
[0.231s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation' with type 'ros.ament_cmake' and name 'ocs2_sphere_approximation'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ros'
[0.231s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_python_interface' with type 'ros.ament_cmake' and name 'ocs2_python_interface'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ros'
[0.232s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_robotic_tools' with type 'ros.ament_cmake' and name 'ocs2_robotic_tools'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_ros_interfaces' with type 'ros.ament_cmake' and name 'ocs2_ros_interfaces'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ros'
[0.235s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/grid_map_sdf' with type 'ros.ament_cmake' and name 'grid_map_sdf'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/ocs2_robotic_assets' with type 'ros.ament_cmake' and name 'ocs2_robotic_assets'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['python_setup_py']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python_setup_py'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ros'
[0.237s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon' with type 'ros.ament_cmake' and name 'cgal5_colcon'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition' with type 'ros.ament_cmake' and name 'convex_plane_decomposition'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ros'
[0.239s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_msgs'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ros'
[0.240s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ignore', 'ignore_ament_install']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ros'
[0.241s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl' with type 'ros.ament_cmake' and name 'grid_map_filters_rsl'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ignore', 'ignore_ament_install']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_pkg']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_pkg'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_meta']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_meta'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ros']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ros'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['cmake', 'python']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'cmake'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'python'
[0.243s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qpoases_vendor' with type 'cmake' and name 'qpOASES'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extensions ['ignore', 'ignore_ament_install']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extension 'ignore'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extension 'ignore_ament_install'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extensions ['colcon_pkg']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extension 'colcon_pkg'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extensions ['colcon_meta']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extension 'colcon_meta'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extensions ['ros']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_integration_launch) by extension 'ros'
[0.244s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_integration_launch' with type 'ros.ament_cmake' and name 'quadruped_integration_launch'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ignore', 'ignore_ament_install']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore_ament_install'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_pkg']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_pkg'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_meta']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_meta'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ros']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ros'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['cmake', 'python']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'cmake'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['python_setup_py']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python_setup_py'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ignore', 'ignore_ament_install']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore_ament_install'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_pkg']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_pkg'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_meta']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_meta'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ros']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ros'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['cmake', 'python']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'cmake'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['python_setup_py']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python_setup_py'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ignore', 'ignore_ament_install']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore_ament_install'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_pkg']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_pkg'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_meta']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_meta'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ros']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ros'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['cmake', 'python']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'cmake'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['python_setup_py']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python_setup_py'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore_ament_install'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_pkg']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_pkg'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_meta']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_meta'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ros']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ros'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['cmake', 'python']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'cmake'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['python_setup_py']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python_setup_py'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore_ament_install'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_pkg']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_pkg'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_meta']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_meta'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ros']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ros'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['cmake', 'python']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'cmake'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['python_setup_py']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python_setup_py'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ros'
[0.247s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/control_input_msgs' with type 'ros.ament_cmake' and name 'control_input_msgs'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ros'
[0.248s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/joystick_input' with type 'ros.ament_cmake' and name 'joystick_input'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ignore', 'ignore_ament_install']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore_ament_install'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_pkg']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_pkg'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_meta']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_meta'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ros']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ros'
[0.249s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/keyboard_input' with type 'ros.ament_cmake' and name 'keyboard_input'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore_ament_install'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_pkg']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_pkg'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_meta']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_meta'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ros']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ros'
[0.249s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/unitree_joystick_input' with type 'ros.ament_cmake' and name 'unitree_joystick_input'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ignore', 'ignore_ament_install']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore_ament_install'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_pkg']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_pkg'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_meta']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_meta'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ros']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ros'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['cmake', 'python']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'cmake'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['python_setup_py']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python_setup_py'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ignore', 'ignore_ament_install']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore_ament_install'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_pkg']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_pkg'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_meta']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_meta'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ros']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ros'
[0.250s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/leg_pd_controller' with type 'ros.ament_cmake' and name 'leg_pd_controller'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore_ament_install'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_pkg']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_pkg'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_meta']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_meta'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ros']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ros'
[0.252s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller' with type 'ros.ament_cmake' and name 'ocs2_quadruped_controller'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore_ament_install'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_pkg']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_pkg'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_meta']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_meta'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ros']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ros'
[0.253s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/rl_quadruped_controller' with type 'ros.ament_cmake' and name 'rl_quadruped_controller'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ignore', 'ignore_ament_install']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore_ament_install'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_pkg']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_pkg'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_meta']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_meta'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ros']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ros'
[0.254s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/unitree_guide_controller' with type 'ros.ament_cmake' and name 'unitree_guide_controller'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ignore', 'ignore_ament_install']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore_ament_install'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_pkg']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_pkg'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_meta']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_meta'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ros']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ros'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['cmake', 'python']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'cmake'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['python_setup_py']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python_setup_py'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ignore', 'ignore_ament_install']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore_ament_install'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_pkg']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_pkg'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_meta']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_meta'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ros']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ros'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['cmake', 'python']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'cmake'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['python_setup_py']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python_setup_py'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ignore', 'ignore_ament_install']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore_ament_install'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_pkg']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_pkg'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_meta']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_meta'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ros']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ros'
[0.256s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description' with type 'ros.ament_cmake' and name 'anymal_c_description'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ignore', 'ignore_ament_install']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore_ament_install'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_pkg']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_pkg'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_meta']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_meta'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ros']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ros'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['cmake', 'python']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'cmake'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['python_setup_py']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python_setup_py'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ignore', 'ignore_ament_install']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore_ament_install'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_pkg']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_pkg'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_meta']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_meta'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ros']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ros'
[0.257s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description' with type 'ros.ament_cmake' and name 'lite3_description'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore_ament_install'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_pkg']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_pkg'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_meta']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_meta'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ros']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ros'
[0.257s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description' with type 'ros.ament_cmake' and name 'x30_description'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore_ament_install'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_pkg']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ros']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ros'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['cmake', 'python']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'cmake'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['python_setup_py']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python_setup_py'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore_ament_install'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_pkg']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ros']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ros'
[0.258s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/a1_description' with type 'ros.ament_cmake' and name 'a1_description'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore_ament_install'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_pkg']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_pkg'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_meta']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_meta'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ros']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ros'
[0.259s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description' with type 'ros.ament_cmake' and name 'aliengo_description'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ignore', 'ignore_ament_install']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore_ament_install'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_pkg']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_pkg'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_meta']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_meta'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ros']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ros'
[0.260s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/b2_description' with type 'ros.ament_cmake' and name 'b2_description'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ignore', 'ignore_ament_install']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore_ament_install'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_pkg']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_pkg'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_meta']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_meta'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ros']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ros'
[0.261s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go1_description' with type 'ros.ament_cmake' and name 'go1_description'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ignore', 'ignore_ament_install']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore_ament_install'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_pkg']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_pkg'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_meta']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_meta'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ros']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ros'
[0.261s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go2_description' with type 'ros.ament_cmake' and name 'go2_description'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ignore', 'ignore_ament_install']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ros'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['cmake', 'python']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'cmake'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['python_setup_py']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python_setup_py'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ros'
[0.262s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description' with type 'ros.ament_cmake' and name 'cyberdog_description'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ros'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['cmake', 'python']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'cmake'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['python_setup_py']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python_setup_py'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ros'
[0.264s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware' with type 'ros.ament_cmake' and name 'gz_quadruped_hardware'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore_ament_install'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_pkg']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_pkg'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_meta']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_meta'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ros']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ros'
[0.264s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco' with type 'ros.ament_cmake' and name 'hardware_unitree_mujoco'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['cmake', 'python']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'cmake'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['python_setup_py']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ros'
[0.265s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/controller_common' with type 'ros.ament_cmake' and name 'controller_common'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ros'
[0.266s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/gz_quadruped_playground' with type 'ros.ament_cmake' and name 'gz_quadruped_playground'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'ros'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller) by extension 'python_setup_py'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'colcon_meta'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['ros']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'ros'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['cmake', 'python']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'cmake'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'python'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extensions ['python_setup_py']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/ocs2_quadruped_controller/launch) by extension 'python_setup_py'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_meta'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ros']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ros'
[0.267s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/qpoases_colcon' with type 'ros.ament_cmake' and name 'qpoases_colcon'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['ignore', 'ignore_ament_install']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'ignore'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'ignore_ament_install'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['colcon_pkg']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'colcon_pkg'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['colcon_meta']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'ros'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['cmake', 'python']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'cmake'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'python'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extensions ['python_setup_py']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros) by extension 'python_setup_py'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extensions ['ignore', 'ignore_ament_install']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extension 'ignore'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extension 'ignore_ament_install'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extensions ['colcon_pkg']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extension 'colcon_pkg'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extensions ['colcon_meta']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera) by extension 'ros'
[0.272s] DEBUG:colcon.colcon_core.package_identification:Package 'src/realsense-ros/realsense2_camera' with type 'ros.ament_cmake' and name 'realsense2_camera'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extension 'ignore_ament_install'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extensions ['colcon_pkg']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extension 'colcon_pkg'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extensions ['colcon_meta']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extension 'colcon_meta'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extensions ['ros']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_camera_msgs) by extension 'ros'
[0.273s] DEBUG:colcon.colcon_core.package_identification:Package 'src/realsense-ros/realsense2_camera_msgs' with type 'ros.ament_cmake' and name 'realsense2_camera_msgs'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extensions ['ignore', 'ignore_ament_install']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extension 'ignore'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extension 'ignore_ament_install'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extensions ['colcon_pkg']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extension 'colcon_pkg'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extensions ['colcon_meta']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extension 'colcon_meta'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extensions ['ros']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/realsense2_description) by extension 'ros'
[0.274s] DEBUG:colcon.colcon_core.package_identification:Package 'src/realsense-ros/realsense2_description' with type 'ros.ament_cmake' and name 'realsense2_description'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'ignore'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'ignore_ament_install'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['colcon_pkg']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'colcon_pkg'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['colcon_meta']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'colcon_meta'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['ros']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'ros'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['cmake', 'python']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'cmake'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'python'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extensions ['python_setup_py']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/realsense-ros/scripts) by extension 'python_setup_py'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'ignore_ament_install'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['colcon_pkg']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'colcon_pkg'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['colcon_meta']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'colcon_meta'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['ros']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'ros'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['cmake', 'python']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'cmake'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'python'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extensions ['python_setup_py']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws) by extension 'python_setup_py'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/build) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/build) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/build) ignored
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/install) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/install) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/install) ignored
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/log) by extensions ['ignore', 'ignore_ament_install']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/log) by extension 'ignore'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/log) ignored
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['ignore', 'ignore_ament_install']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'ignore'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'ignore_ament_install'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['colcon_pkg']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'colcon_pkg'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['colcon_meta']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'colcon_meta'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['ros']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'ros'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['cmake', 'python']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'cmake'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'python'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extensions ['python_setup_py']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src) by extension 'python_setup_py'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'ignore'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'ignore_ament_install'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['colcon_pkg']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'colcon_pkg'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['colcon_meta']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'colcon_meta'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['ros']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'ros'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['cmake', 'python']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'cmake'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'python'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extensions ['python_setup_py']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation) by extension 'python_setup_py'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extensions ['ignore', 'ignore_ament_install']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extension 'ignore'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extension 'ignore_ament_install'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extensions ['colcon_pkg']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extension 'colcon_pkg'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extensions ['colcon_meta']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extension 'colcon_meta'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extensions ['ros']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2) by extension 'ros'
[0.277s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2' with type 'ros.ament_cmake' and name 'ros2_livox_simulation'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extension 'ignore'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extension 'ignore_ament_install'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extensions ['colcon_pkg']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extension 'colcon_pkg'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extensions ['colcon_meta']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extension 'colcon_meta'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extensions ['ros']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_ws/src/rm_simulation/pb_rm_simulation) by extension 'ros'
[0.278s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_ws/src/rm_simulation/pb_rm_simulation' with type 'ros.ament_cmake' and name 'pb_rm_simulation'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'ignore'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'ignore_ament_install'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['colcon_pkg']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'colcon_pkg'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['colcon_meta']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'colcon_meta'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['ros']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'ros'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['cmake', 'python']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'cmake'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'python'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extensions ['python_setup_py']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation) by extension 'python_setup_py'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extension 'ignore'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extension 'ignore_ament_install'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extensions ['colcon_pkg']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extension 'colcon_pkg'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extensions ['colcon_meta']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extension 'colcon_meta'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extensions ['ros']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/camera_simulation) by extension 'ros'
[0.279s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_simulation/camera_simulation' with type 'ros.ament_cmake' and name 'camera_simulation'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extension 'ignore'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extension 'ignore_ament_install'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extensions ['colcon_pkg']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extension 'colcon_pkg'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extensions ['colcon_meta']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extension 'colcon_meta'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extensions ['ros']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_simulation/lidar_simulation) by extension 'ros'
[0.280s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_simulation/lidar_simulation' with type 'ros.ament_cmake' and name 'lidar_simulation'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['ignore', 'ignore_ament_install']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'ignore'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'ignore_ament_install'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['colcon_pkg']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'colcon_pkg'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['colcon_meta']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'colcon_meta'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['ros']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'ros'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['cmake', 'python']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'cmake'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'python'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extensions ['python_setup_py']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map) by extension 'python_setup_py'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/build) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/build) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/build) ignored
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/install) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/install) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/install) ignored
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/log) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/log) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/log) ignored
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'ignore_ament_install'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['colcon_pkg']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'colcon_pkg'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['colcon_meta']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'colcon_meta'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['ros']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'ros'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['cmake', 'python']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'cmake'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'python'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extensions ['python_setup_py']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src) by extension 'python_setup_py'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extension 'ignore_ament_install'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extensions ['colcon_pkg']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extension 'colcon_pkg'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extensions ['colcon_meta']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extension 'colcon_meta'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extensions ['ros']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/elevation_mapping) by extension 'ros'
[0.283s] DEBUG:colcon.colcon_core.package_identification:Package 'src/try_map/src/elevation_mapping' with type 'ros.ament_cmake' and name 'elevation_mapping'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['ignore', 'ignore_ament_install']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'ignore'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'ignore_ament_install'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['colcon_pkg']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'colcon_pkg'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['colcon_meta']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'colcon_meta'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['ros']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'ros'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['cmake', 'python']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'cmake'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'python'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extensions ['python_setup_py']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros) by extension 'python_setup_py'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extension 'ignore'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extension 'ignore_ament_install'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extensions ['colcon_pkg']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extension 'colcon_pkg'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extensions ['colcon_meta']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extension 'colcon_meta'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extensions ['ros']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_msgs) by extension 'ros'
[0.284s] DEBUG:colcon.colcon_core.package_identification:Package 'src/try_map/src/kindr_ros/kindr_msgs' with type 'ros.ament_cmake' and name 'kindr_msgs'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extensions ['ignore', 'ignore_ament_install']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extension 'ignore'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extension 'ignore_ament_install'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extensions ['colcon_pkg']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extension 'colcon_pkg'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extensions ['colcon_meta']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extension 'colcon_meta'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extensions ['ros']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_ros) by extension 'ros'
[0.285s] DEBUG:colcon.colcon_core.package_identification:Package 'src/try_map/src/kindr_ros/kindr_ros' with type 'ros.ament_cmake' and name 'kindr_ros'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_rviz_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_rviz_plugins) by extension 'ignore'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/kindr_rviz_plugins) ignored
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/multi_dof_joint_trajectory_rviz_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/multi_dof_joint_trajectory_rviz_plugins) by extension 'ignore'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/kindr_ros/multi_dof_joint_trajectory_rviz_plugins) ignored
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/log) by extensions ['ignore', 'ignore_ament_install']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/log) by extension 'ignore'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/try_map/src/log) ignored
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['ignore', 'ignore_ament_install']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'ignore'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'ignore_ament_install'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['colcon_pkg']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'colcon_pkg'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['colcon_meta']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'colcon_meta'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['ros']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'ros'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['cmake', 'python']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'cmake'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'python'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extensions ['python_setup_py']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolov7-segmentation) by extension 'python_setup_py'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['ignore', 'ignore_ament_install']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'ignore'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'ignore_ament_install'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['colcon_pkg']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'colcon_pkg'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['colcon_meta']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'colcon_meta'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['ros']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'ros'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['cmake', 'python']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'cmake'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'python'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extensions ['python_setup_py']
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~) by extension 'python_setup_py'
[0.287s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['ignore', 'ignore_ament_install']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'ignore'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'ignore_ament_install'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['colcon_pkg']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'colcon_pkg'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['colcon_meta']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'colcon_meta'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['ros']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'ros'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['cmake', 'python']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'cmake'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'python'
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extensions ['python_setup_py']
[0.288s] Level 1:colcon.colcon_core.package_identification:_identify(~/ros2_ws) by extension 'python_setup_py'
[0.288s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.288s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.288s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.288s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.288s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.367s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'a1_description' in 'src/quadruped_ros2_control/descriptions/unitree/a1_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'aliengo_description' in 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'anymal_c_description' in 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'b2_description' in 'src/quadruped_ros2_control/descriptions/unitree/b2_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'blasfeo_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'camera_simulation' in 'src/sensor_simulation/camera_simulation'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'cgal5_colcon' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'control_input_msgs' in 'src/quadruped_ros2_control/commands/control_input_msgs'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition_msgs' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'cyberdog_description' in 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'elevation_map_converter' in 'src/elevation_map_converter'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'go1_description' in 'src/quadruped_ros2_control/descriptions/unitree/go1_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'go2_description' in 'src/quadruped_ros2_control/descriptions/unitree/go2_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'grid_map_filters_rsl' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'grid_map_sdf' in 'src/ocs2_ros2/submodules/grid_map_sdf'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gz_quadruped_hardware' in 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hardware_unitree_mujoco' in 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'kindr_msgs' in 'src/try_map/src/kindr_ros/kindr_msgs'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'kindr_ros' in 'src/try_map/src/kindr_ros/kindr_ros'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leg_pd_controller' in 'src/quadruped_ros2_control/controllers/leg_pd_controller'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'lidar_simulation' in 'src/sensor_simulation/lidar_simulation'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'lite3_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'livox_ros_driver2' in 'src/livox_ros_driver2'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_msgs' in 'src/ocs2_ros2/robotics/ocs2_msgs'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_robotic_assets' in 'src/ocs2_ros2/submodules/ocs2_robotic_assets'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_thirdparty' in 'src/ocs2_ros2/core/ocs2_thirdparty'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pb_rm_simulation' in 'src/rm_ws/src/rm_simulation/pb_rm_simulation'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qpOASES' in 'src/qpoases_vendor'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qpoases_colcon' in 'src/quadruped_ros2_control/libraries/qpoases_colcon'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'realsense2_camera_msgs' in 'src/realsense-ros/realsense2_camera_msgs'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'realsense_ros_gazebo' in 'src/d435i/src/realsense_ros_gazebo-humble'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'robot_p' in 'src/d435i/src/robot_p'
[0.368s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'x30_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'controller_common' in 'src/quadruped_ros2_control/libraries/controller_common'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'elevation_mapping' in 'src/try_map/src/elevation_mapping'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'fast_lio' in 'src/FAST_LIO'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gz_quadruped_playground' in 'src/quadruped_ros2_control/libraries/gz_quadruped_playground'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'joystick_input' in 'src/quadruped_ros2_control/commands/joystick_input'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'keyboard_input' in 'src/quadruped_ros2_control/commands/keyboard_input'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_core' in 'src/ocs2_ros2/core/ocs2_core'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_switched_model_msgs' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'realsense2_camera' in 'src/realsense-ros/realsense2_camera'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'realsense2_description' in 'src/realsense-ros/realsense2_description'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ros2_livox_simulation' in 'src/rm_ws/src/rm_simulation/livox_laser_simulation_ROS2'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'unitree_joystick_input' in 'src/quadruped_ros2_control/commands/unitree_joystick_input'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition_ros' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_oc' in 'src/ocs2_ros2/core/ocs2_oc'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'quadruped_integration_launch' in 'src/quadruped_integration_launch'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rl_quadruped_controller' in 'src/quadruped_ros2_control/controllers/rl_quadruped_controller'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'unitree_guide_controller' in 'src/quadruped_ros2_control/controllers/unitree_guide_controller'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mpc' in 'src/ocs2_ros2/mpc/ocs2_mpc'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_qp_solver' in 'src/ocs2_ros2/mpc/ocs2_qp_solver'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_raisim_core' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_robotic_tools' in 'src/ocs2_ros2/robotics/ocs2_robotic_tools'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hpipm_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ddp' in 'src/ocs2_ros2/mpc/ocs2_ddp'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_pinocchio_interface' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ros_interfaces' in 'src/ocs2_ros2/robotics/ocs2_ros_interfaces'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_slp' in 'src/ocs2_ros2/mpc/ocs2_slp'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_cartpole' in 'src/ocs2_ros2/basic examples/ocs2_cartpole'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_centroidal_model' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ipm' in 'src/ocs2_ros2/mpc/ocs2_ipm'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_python_interface' in 'src/ocs2_ros2/robotics/ocs2_python_interface'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_self_collision' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_sphere_approximation' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_sqp' in 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_switched_model_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_commands' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_models' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot' in 'src/ocs2_ros2/basic examples/ocs2_ballbot'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_cartpole_ros' in 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_double_integrator' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator'
[0.369s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mobile_manipulator' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mpcnet_core' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadrotor' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_self_collision_visualization' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'segmented_planes_terrain_model' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot_ros' in 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_double_integrator_ros' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_ros' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mobile_manipulator_ros' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadrotor_ros' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_raisim' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_controller' in 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_loopshaping_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_loopshaping_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc'
[0.370s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet'
[0.371s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.371s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.378s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 81 installed packages in /home/<USER>/ros2_ws/install
[0.381s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 432 installed packages in /opt/ros/humble
[0.384s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.444s] WARNING:colcon.colcon_core.package_selection:Some selected packages are already built in one or more underlay workspaces:
	'd435i_detection_ros2' is in: /home/<USER>/ros2_ws/install/d435i_detection_ros2
If a package in a merged underlay workspace is overridden and it installs headers, then all packages in the overlay must sort their include directories by workspace order. Failure to do so may result in build failures or undefined behavior at run time.
If the overridden package is used by another package in any underlay, then the overriding package in the overlay must be API and ABI compatible or undefined behavior at run time may occur.

If you understand the risks and want to override a package anyways, add the following to the command line:
	--allow-overriding d435i_detection_ros2

This may be promoted to an error in a future release of colcon-override-check.
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_args' from command line to 'None'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_target' from command line to 'None'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.445s] Level 5:colcon.colcon_core.verb:set package 'd435i_detection_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.445s] DEBUG:colcon.colcon_core.verb:Building package 'd435i_detection_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/d435i_detection_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/d435i_detection_ros2', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/d435i_detection_ros2', 'symlink_install': False, 'test_result_base': None}
[0.446s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.447s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.448s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' with build type 'ament_python'
[0.448s] Level 1:colcon.colcon_core.shell:create_environment_hook('d435i_detection_ros2', 'ament_prefix_path')
[0.450s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/d435i_detection_ros2/share/d435i_detection_ros2/hook/ament_prefix_path.ps1'
[0.451s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/d435i_detection_ros2/share/d435i_detection_ros2/hook/ament_prefix_path.dsv'
[0.452s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/d435i_detection_ros2/share/d435i_detection_ros2/hook/ament_prefix_path.sh'
[0.452s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.453s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.693s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros2_ws/src/d435i_detection_ros2'
[0.693s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.693s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.239s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
