[0.005s] Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[0.041s] [100%] Built target keyboard_input
[0.050s] Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[0.050s] Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[0.053s] -- Install configuration: ""
[0.053s] -- Execute custom install script
[0.053s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/lib/keyboard_input/keyboard_input
[0.053s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/package_run_dependencies/keyboard_input
[0.053s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/parent_prefix_path/keyboard_input
[0.053s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.sh
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.dsv
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.sh
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.dsv
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.bash
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.sh
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.zsh
[0.054s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.dsv
[0.054s] -- Symlinking: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv
[0.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/packages/keyboard_input
[0.061s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig.cmake
[0.061s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig-version.cmake
[0.061s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.xml
[0.062s] Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
