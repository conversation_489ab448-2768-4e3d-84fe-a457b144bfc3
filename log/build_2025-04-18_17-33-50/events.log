[0.000000] (-) TimerEvent: {}
[0.000549] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000599] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000656] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000703] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000718] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000741] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000756] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000769] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000817] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000832] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000848] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000861] (-) JobUnselected: {'identifier': 'elevation_mapping'}
[0.000874] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000900] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000938] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000957] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000971] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000986] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001019] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001047] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001061] (-) JobUnselected: {'identifier': 'kindr_msgs'}
[0.001075] (-) JobUnselected: {'identifier': 'kindr_ros'}
[0.001088] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001102] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001116] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001130] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001144] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001158] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001194] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001213] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001322] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001342] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001379] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001412] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001434] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001449] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001463] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001477] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001491] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001506] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001519] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001539] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001573] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001606] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001628] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001642] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001657] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001672] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001686] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001699] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001734] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001753] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001767] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001780] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001793] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001886] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001985] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002001] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002074] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002091] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002105] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002252] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002405] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002420] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002434] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002452] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002477] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002498] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002570] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002584] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002597] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002610] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002622] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002634] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002649] (control_input_msgs) JobQueued: {'identifier': 'control_input_msgs', 'dependencies': OrderedDict()}
[0.002666] (go2_description) JobQueued: {'identifier': 'go2_description', 'dependencies': OrderedDict()}
[0.002680] (controller_common) JobQueued: {'identifier': 'controller_common', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs')])}
[0.002707] (keyboard_input) JobQueued: {'identifier': 'keyboard_input', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs')])}
[0.002730] (unitree_guide_controller) JobQueued: {'identifier': 'unitree_guide_controller', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs'), ('controller_common', '/home/<USER>/ros2_ws/install/controller_common')])}
[0.002749] (control_input_msgs) JobStarted: {'identifier': 'control_input_msgs'}
[0.004569] (go2_description) JobStarted: {'identifier': 'go2_description'}
[0.006725] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'cmake'}
[0.006790] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'build'}
[0.006817] (control_input_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/control_input_msgs', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/control_input_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/control_input_msgs'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.008427] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'cmake'}
[0.008619] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'build'}
[0.009122] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/go2_description', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.032767] (go2_description) CommandEnded: {'returncode': 0}
[0.034034] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'install'}
[0.043960] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/go2_description'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.047401] (control_input_msgs) StdoutLine: {'line': b'[  3%] Built target control_input_msgs__cpp\n'}
[0.047842] (control_input_msgs) StdoutLine: {'line': b'[ 12%] Built target control_input_msgs__rosidl_generator_c\n'}
[0.051664] (go2_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.052308] (go2_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.052836] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf.dae\n'}
[0.052978] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf_mirror.dae\n'}
[0.053520] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/foot.dae\n'}
[0.053610] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/hip.dae\n'}
[0.053677] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh.dae\n'}
[0.053739] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh_mirror.dae\n'}
[0.053799] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/trunk.dae\n'}
[0.053848] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/const.xacro\n'}
[0.053909] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo.xacro\n'}
[0.054110] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo_classic.xacro\n'}
[0.054146] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/leg.xacro\n'}
[0.054171] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/materials.xacro\n'}
[0.054193] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/robot.xacro\n'}
[0.054231] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/ros2_control.xacro\n'}
[0.054252] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/transmission.xacro\n'}
[0.054353] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/gazebo_rl_control.launch.py\n'}
[0.054386] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/visualize.launch.py\n'}
[0.054582] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/gazebo.yaml\n'}
[0.054656] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config.yaml\n'}
[0.054729] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config_himloco.yaml\n'}
[0.054806] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/himloco.pt\n'}
[0.054949] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/policy.pt\n'}
[0.054990] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/gait.info\n'}
[0.055095] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/reference.info\n'}
[0.055130] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/task.info\n'}
[0.055224] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/robot_control.yaml\n'}
[0.055265] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/visualize_urdf.rviz\n'}
[0.055380] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//urdf/robot.urdf\n'}
[0.055464] (control_input_msgs) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_symlink_control_input_msgs\n'}
[0.055502] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/package_run_dependencies/go2_description\n'}
[0.055689] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/parent_prefix_path/go2_description\n'}
[0.055805] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.sh\n'}
[0.055877] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.dsv\n'}
[0.055952] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.sh\n'}
[0.056149] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.dsv\n'}
[0.056224] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.bash\n'}
[0.056307] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.sh\n'}
[0.056373] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.zsh\n'}
[0.056448] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.dsv\n'}
[0.056553] (go2_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv\n'}
[0.062629] (control_input_msgs) StdoutLine: {'line': b'[ 22%] Built target control_input_msgs__rosidl_typesupport_cpp\n'}
[0.063689] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/packages/go2_description\n'}
[0.063834] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig.cmake\n'}
[0.063939] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig-version.cmake\n'}
[0.064172] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.xml\n'}
[0.064662] (control_input_msgs) StdoutLine: {'line': b'[ 32%] Built target control_input_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.065172] (control_input_msgs) StdoutLine: {'line': b'[ 41%] Built target control_input_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.065904] (go2_description) CommandEnded: {'returncode': 0}
[0.072671] (control_input_msgs) StdoutLine: {'line': b'[ 61%] Built target control_input_msgs__rosidl_typesupport_c\n'}
[0.072846] (control_input_msgs) StdoutLine: {'line': b'[ 61%] Built target control_input_msgs__rosidl_typesupport_introspection_c\n'}
[0.073385] (control_input_msgs) StdoutLine: {'line': b'[ 70%] Built target control_input_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.073747] (go2_description) JobEnded: {'identifier': 'go2_description', 'rc': 0}
[0.087295] (control_input_msgs) StdoutLine: {'line': b'[ 70%] Built target control_input_msgs\n'}
[0.100003] (-) TimerEvent: {}
[0.102395] (control_input_msgs) StdoutLine: {'line': b'[ 74%] Built target control_input_msgs__py\n'}
[0.117748] (control_input_msgs) StdoutLine: {'line': b'[ 80%] Built target control_input_msgs__rosidl_generator_py\n'}
[0.132381] (control_input_msgs) StdoutLine: {'line': b'[ 87%] Built target control_input_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[0.133919] (control_input_msgs) StdoutLine: {'line': b'[ 93%] Built target control_input_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.134338] (control_input_msgs) StdoutLine: {'line': b'[100%] Built target control_input_msgs__rosidl_typesupport_c__pyext\n'}
[0.183546] (control_input_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.183800] (control_input_msgs) StdoutLine: {'line': b'writing control_input_msgs.egg-info/PKG-INFO\n'}
[0.183995] (control_input_msgs) StdoutLine: {'line': b'writing dependency_links to control_input_msgs.egg-info/dependency_links.txt\n'}
[0.184216] (control_input_msgs) StdoutLine: {'line': b'writing top-level names to control_input_msgs.egg-info/top_level.txt\n'}
[0.186288] (control_input_msgs) StdoutLine: {'line': b"reading manifest file 'control_input_msgs.egg-info/SOURCES.txt'\n"}
[0.186514] (control_input_msgs) StdoutLine: {'line': b"writing manifest file 'control_input_msgs.egg-info/SOURCES.txt'\n"}
[0.199800] (control_input_msgs) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_control_input_msgs_egg\n'}
[0.200044] (-) TimerEvent: {}
[0.206568] (control_input_msgs) CommandEnded: {'returncode': 0}
[0.207063] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'install'}
[0.207358] (control_input_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/control_input_msgs'], 'cwd': '/home/<USER>/ros2_ws/build/control_input_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/control_input_msgs'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.213560] (control_input_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.214099] (control_input_msgs) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.214234] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/rosidl_interfaces/control_input_msgs\n'}
[0.214439] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__functions.h\n'}
[0.214531] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__struct.h\n'}
[0.214646] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__type_support.h\n'}
[0.214787] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/inputs.h\n'}
[0.214871] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[0.214965] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/library_path.sh\n'}
[0.215081] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/library_path.dsv\n'}
[0.215171] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_fastrtps_c.h\n'}
[0.215261] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.215395] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_introspection_c.h\n'}
[0.215485] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.215596] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__builder.hpp\n'}
[0.215721] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__struct.hpp\n'}
[0.215865] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__traits.hpp\n'}
[0.216080] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__type_support.hpp\n'}
[0.216238] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/inputs.hpp\n'}
[0.216288] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.216387] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.216435] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.216475] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.216632] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/pythonpath.sh\n'}
[0.216761] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/pythonpath.dsv\n'}
[0.216815] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.216889] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.216975] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.217016] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.217217] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/__init__.py\n'}
[0.217293] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_c.c\n'}
[0.217369] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.217440] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.217511] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.217587] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.217649] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.217713] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/libcontrol_input_msgs__rosidl_generator_py.so\n'}
[0.217798] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/__init__.py\n'}
[0.217842] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/_inputs.py\n'}
[0.217899] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/_inputs_s.c\n'}
[0.218058] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.218228] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.218402] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.218519] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/msg/Inputs.idl\n'}
[0.218619] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/msg/Inputs.msg\n'}
[0.218724] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/package_run_dependencies/control_input_msgs\n'}
[0.218843] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/parent_prefix_path/control_input_msgs\n'}
[0.218961] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/ament_prefix_path.sh\n'}
[0.219064] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/ament_prefix_path.dsv\n'}
[0.219173] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/path.sh\n'}
[0.219273] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/path.dsv\n'}
[0.219369] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.bash\n'}
[0.219466] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.sh\n'}
[0.219569] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.zsh\n'}
[0.219664] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.dsv\n'}
[0.219737] (control_input_msgs) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv\n'}
[0.228298] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/packages/control_input_msgs\n'}
[0.228369] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[0.228411] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.228448] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.228487] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.228519] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.228557] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.228605] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.228657] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgsConfig.cmake\n'}
[0.228692] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgsConfig-version.cmake\n'}
[0.228742] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.xml\n'}
[0.228874] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_generator_c.so\n'}
[0.228997] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[0.229086] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_introspection_c.so\n'}
[0.229182] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_c.so\n'}
[0.229264] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.229347] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[0.229425] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_cpp.so\n'}
[0.246086] (control_input_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs'...\n"}
[0.246140] (control_input_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg'...\n"}
[0.248090] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_generator_py.so\n'}
[0.248121] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cExport.cmake\n'}
[0.248154] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[0.248233] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.248256] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.248276] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.248296] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.248316] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cExport.cmake\n'}
[0.248336] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.248354] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cppExport.cmake\n'}
[0.248394] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.248415] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.248434] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.248454] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.248513] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cppExport.cmake\n'}
[0.248535] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.248555] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_pyExport.cmake\n'}
[0.248577] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.249593] (control_input_msgs) CommandEnded: {'returncode': 0}
[0.256154] (control_input_msgs) JobEnded: {'identifier': 'control_input_msgs', 'rc': 0}
[0.256501] (controller_common) JobStarted: {'identifier': 'controller_common'}
[0.258022] (keyboard_input) JobStarted: {'identifier': 'keyboard_input'}
[0.261162] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'cmake'}
[0.261391] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'build'}
[0.261756] (controller_common) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/controller_common', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/controller_common', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/controller_common'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.262524] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'cmake'}
[0.262822] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'build'}
[0.262957] (keyboard_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/keyboard_input', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/keyboard_input', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/keyboard_input'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.299565] (keyboard_input) StdoutLine: {'line': b'[100%] Built target keyboard_input\n'}
[0.300108] (-) TimerEvent: {}
[0.300500] (controller_common) StdoutLine: {'line': b'[100%] Built target controller_common\n'}
[0.307565] (keyboard_input) CommandEnded: {'returncode': 0}
[0.307876] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'install'}
[0.307888] (keyboard_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/keyboard_input'], 'cwd': '/home/<USER>/ros2_ws/build/keyboard_input', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/keyboard_input'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.308741] (controller_common) CommandEnded: {'returncode': 0}
[0.309056] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'install'}
[0.309375] (controller_common) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/controller_common'], 'cwd': '/home/<USER>/ros2_ws/build/controller_common', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/controller_common'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.311299] (keyboard_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.311489] (keyboard_input) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.311535] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/lib/keyboard_input/keyboard_input\n'}
[0.311571] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/package_run_dependencies/keyboard_input\n'}
[0.311618] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/parent_prefix_path/keyboard_input\n'}
[0.311678] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.sh\n'}
[0.311725] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.dsv\n'}
[0.311777] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.sh\n'}
[0.311824] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.dsv\n'}
[0.311871] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.bash\n'}
[0.311920] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.sh\n'}
[0.311963] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.zsh\n'}
[0.312007] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.dsv\n'}
[0.312041] (keyboard_input) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv\n'}
[0.313754] (controller_common) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.313885] (controller_common) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.314189] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/CtrlInterfaces.h\n'}
[0.314216] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/BaseFixedStand.h\n'}
[0.314320] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/FSMState.h\n'}
[0.314346] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/StateFixedDown.h\n'}
[0.314372] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/StatePassive.h\n'}
[0.314414] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/enumClass.h\n'}
[0.314466] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/mathTools.h\n'}
[0.314491] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/mathTypes.h\n'}
[0.314513] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/library_path.sh\n'}
[0.314548] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/library_path.dsv\n'}
[0.314594] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/package_run_dependencies/controller_common\n'}
[0.314655] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/parent_prefix_path/controller_common\n'}
[0.314710] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/ament_prefix_path.sh\n'}
[0.314761] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/ament_prefix_path.dsv\n'}
[0.314813] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/path.sh\n'}
[0.314863] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/path.dsv\n'}
[0.314922] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.bash\n'}
[0.314971] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.sh\n'}
[0.315013] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.zsh\n'}
[0.315061] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.dsv\n'}
[0.315097] (controller_common) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv\n'}
[0.318596] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/packages/keyboard_input\n'}
[0.318683] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig.cmake\n'}
[0.318724] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig-version.cmake\n'}
[0.318746] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.xml\n'}
[0.319675] (keyboard_input) CommandEnded: {'returncode': 0}
[0.321050] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/packages/controller_common\n'}
[0.321120] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.321148] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/controller_commonConfig.cmake\n'}
[0.321194] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/controller_commonConfig-version.cmake\n'}
[0.321222] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.xml\n'}
[0.321445] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/lib/libcontroller_common.so\n'}
[0.321472] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/export_controller_commonExport.cmake\n'}
[0.321495] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/export_controller_commonExport-release.cmake\n'}
[0.324944] (keyboard_input) JobEnded: {'identifier': 'keyboard_input', 'rc': 0}
[0.325752] (controller_common) CommandEnded: {'returncode': 0}
[0.331791] (controller_common) JobEnded: {'identifier': 'controller_common', 'rc': 0}
[0.332453] (unitree_guide_controller) JobStarted: {'identifier': 'unitree_guide_controller'}
[0.339531] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'cmake'}
[0.339559] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'build'}
[0.339570] (unitree_guide_controller) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/unitree_guide_controller', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/unitree_guide_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.400209] (-) TimerEvent: {}
[0.433082] (unitree_guide_controller) StdoutLine: {'line': b'[100%] Built target unitree_guide_controller\n'}
[0.441802] (unitree_guide_controller) CommandEnded: {'returncode': 0}
[0.442443] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'install'}
[0.442897] (unitree_guide_controller) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/unitree_guide_controller'], 'cwd': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/unitree_guide_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.448052] (unitree_guide_controller) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.448246] (unitree_guide_controller) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.448383] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//unitree_guide_controller.xml\n'}
[0.448524] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateBalanceTest.h\n'}
[0.448675] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFixedStand.h\n'}
[0.448704] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFreeStand.h\n'}
[0.448727] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateSwingTest.h\n'}
[0.448775] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateTrotting.h\n'}
[0.448827] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/UnitreeGuideController.h\n'}
[0.448849] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTools.h\n'}
[0.448870] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTypes.h\n'}
[0.448892] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/BalanceCtrl.h\n'}
[0.448917] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/CtrlComponent.h\n'}
[0.448951] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/Estimator.h\n'}
[0.448973] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/LowPassFilter.h\n'}
[0.448994] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/FeetEndCalc.h\n'}
[0.449015] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/GaitGenerator.h\n'}
[0.449036] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/WaveGenerator.h\n'}
[0.449086] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/QuadrupedRobot.h\n'}
[0.449107] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/RobotLeg.h\n'}
[0.449128] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo.launch.py\n'}
[0.449159] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo_classic.launch.py\n'}
[0.449182] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/mujoco.launch.py\n'}
[0.449219] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.sh\n'}
[0.449254] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.dsv\n'}
[0.449294] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/package_run_dependencies/unitree_guide_controller\n'}
[0.449348] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/parent_prefix_path/unitree_guide_controller\n'}
[0.449400] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.sh\n'}
[0.449448] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.dsv\n'}
[0.449498] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.sh\n'}
[0.449545] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.dsv\n'}
[0.449596] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.bash\n'}
[0.449642] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.sh\n'}
[0.449690] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.zsh\n'}
[0.449731] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.dsv\n'}
[0.449775] (unitree_guide_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv\n'}
[0.456023] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/packages/unitree_guide_controller\n'}
[0.456097] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/unitree_guide_controller\n'}
[0.456125] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.456148] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.456172] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig.cmake\n'}
[0.456194] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig-version.cmake\n'}
[0.456222] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.xml\n'}
[0.456263] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so\n'}
[0.456314] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport.cmake\n'}
[0.456343] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport-release.cmake\n'}
[0.457534] (unitree_guide_controller) CommandEnded: {'returncode': 0}
[0.464004] (unitree_guide_controller) JobEnded: {'identifier': 'unitree_guide_controller', 'rc': 0}
[0.464397] (-) EventReactorShutdown: {}
