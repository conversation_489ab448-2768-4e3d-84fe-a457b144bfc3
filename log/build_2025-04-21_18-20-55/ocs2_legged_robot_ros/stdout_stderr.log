-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found ocs2_legged_robot: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
  /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig.cmake:41 (include)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake:41 (include)
  CMakeLists.txt:17 (find_package)

[0m
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found robot_state_publisher: 3.0.3 (/opt/ros/humble/share/robot_state_publisher/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found ocs2_ros_interfaces: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/share/ocs2_ros_interfaces/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_legged_robot_ros
[35m[1mConsolidate compiler generated dependencies of target ocs2_legged_robot_ros[0m
[  6%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_ros.dir/src/gait/GaitKeyboardPublisher.cpp.o[0m
[ 12%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_ros.dir/src/gait/GaitReceiver.cpp.o[0m
[ 18%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_ros.dir/src/visualization/LeggedRobotVisualizer.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/basic examples/ocs2_legged_robot_ros/src/visualization/LeggedRobotVisualizer.cpp:34[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
[ 25%] [32m[1mLinking CXX static library libocs2_legged_robot_ros.a[0m
[ 25%] Built target ocs2_legged_robot_ros
[35m[1mConsolidate compiler generated dependencies of target legged_robot_ipm_mpc[0m
[35m[1mConsolidate compiler generated dependencies of target legged_robot_sqp_mpc[0m
[35m[1mConsolidate compiler generated dependencies of target legged_robot_gait_command[0m
[35m[1mConsolidate compiler generated dependencies of target legged_robot_target[0m
[35m[1mConsolidate compiler generated dependencies of target legged_robot_ddp_mpc[0m
[35m[1mConsolidate compiler generated dependencies of target legged_robot_dummy[0m
[ 37%] [32mBuilding CXX object CMakeFiles/legged_robot_gait_command.dir/src/LeggedRobotGaitCommandNode.cpp.o[0m
[ 37%] [32mBuilding CXX object CMakeFiles/legged_robot_target.dir/src/LeggedRobotPoseCommandNode.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/legged_robot_sqp_mpc.dir/src/LeggedRobotSqpMpcNode.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/legged_robot_dummy.dir/src/LeggedRobotDummyNode.cpp.o[0m
[ 62%] [32mBuilding CXX object CMakeFiles/legged_robot_ipm_mpc.dir/src/LeggedRobotIpmMpcNode.cpp.o[0m
[ 62%] [32mBuilding CXX object CMakeFiles/legged_robot_ddp_mpc.dir/src/LeggedRobotDdpMpcNode.cpp.o[0m
[ 68%] [32m[1mLinking CXX executable legged_robot_gait_command[0m
[ 68%] Built target legged_robot_gait_command
[ 75%] [32m[1mLinking CXX executable legged_robot_target[0m
[ 75%] Built target legged_robot_target
[ 81%] [32m[1mLinking CXX executable legged_robot_sqp_mpc[0m
[ 87%] [32m[1mLinking CXX executable legged_robot_ipm_mpc[0m
[ 93%] [32m[1mLinking CXX executable legged_robot_ddp_mpc[0m
[100%] [32m[1mLinking CXX executable legged_robot_dummy[0m
[100%] Built target legged_robot_sqp_mpc
[100%] Built target legged_robot_ipm_mpc
[100%] Built target legged_robot_ddp_mpc
[100%] Built target legged_robot_dummy
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/GaitKeyboardPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/GaitReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/ModeSequenceTemplateRos.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/visualization/LeggedRobotVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_ddp_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_sqp_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_ipm_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_dummy
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_target
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_gait_command
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/basic.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/dummy.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/gait_command.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_ddp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_ipm.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_sqp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/mpc_ddp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/multiplot.launch
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/multiplot.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/robot_target.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//rviz/legged_robot.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/package_run_dependencies/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/parent_prefix_path/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/packages/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/libocs2_legged_robot_ros.a
-- Old export file "/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport-release.cmake].
-- Installing: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport.cmake
-- Installing: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport-release.cmake
