[0.020s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros -- -j32 -l32
[0.039s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.110s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.113s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.130s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.131s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.134s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.140s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.148s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.166s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.168s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.242s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.277s] -- Found grid_map_ros: 2.0.1 (/opt/ros/humble/share/grid_map_ros/cmake)
[0.391s] -- Found convex_plane_decomposition: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake)
[0.398s] -- Found convex_plane_decomposition_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/share/convex_plane_decomposition_msgs/cmake)
[0.407s] -- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)
[0.656s] -- Configuring done
[0.745s] -- Generating done
[0.759s] -- Build files have been written to: /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros
[0.788s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros[0m
[0.828s] [  6%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros.dir/src/ConvexPlaneDecompositionRos.cpp.o[0m
[0.828s] [ 13%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros.dir/src/MessageConversion.cpp.o[0m
[0.829s] [ 20%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros.dir/src/ParameterLoading.cpp.o[0m
[0.830s] [ 26%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros.dir/src/RosVisualizations.cpp.o[0m
[12.046s] [ 33%] [32m[1mLinking CXX static library libconvex_plane_decomposition_ros.a[0m
[12.091s] [ 33%] Built target convex_plane_decomposition_ros
[12.101s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros_node[0m
[12.103s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros_approximation_demo_node[0m
[12.104s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros_add_noise[0m
[12.105s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros_save_elevationmap[0m
[12.106s] [35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition_ros_TestShapeGrowing[0m
[12.117s] [ 40%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros_node.dir/src/ConvexPlaneDecompositionNode.cpp.o[0m
[12.123s] [ 46%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros_add_noise.dir/src/noiseNode.cpp.o[0m
[12.125s] [ 53%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros_save_elevationmap.dir/src/SaveElevationMapAsImageNode.cpp.o[0m
[12.131s] [ 60%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros_approximation_demo_node.dir/src/ConvexApproximationDemoNode.cpp.o[0m
[12.134s] [ 66%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition_ros_TestShapeGrowing.dir/test/TestShapeGrowing.cpp.o[0m
[15.679s] [ 73%] [32m[1mLinking CXX executable convex_plane_decomposition_ros_node[0m
[18.837s] [ 73%] Built target convex_plane_decomposition_ros_node
[25.240s] [ 80%] [32m[1mLinking CXX executable convex_plane_decomposition_ros_TestShapeGrowing[0m
[27.156s] [ 80%] Built target convex_plane_decomposition_ros_TestShapeGrowing
[29.349s] [ 86%] [32m[1mLinking CXX executable convex_plane_decomposition_ros_save_elevationmap[0m
[29.821s] [ 86%] Built target convex_plane_decomposition_ros_save_elevationmap
[31.107s] [ 93%] [32m[1mLinking CXX executable convex_plane_decomposition_ros_add_noise[0m
[31.720s] [ 93%] Built target convex_plane_decomposition_ros_add_noise
[31.781s] [100%] [32m[1mLinking CXX executable convex_plane_decomposition_ros_approximation_demo_node[0m
[32.287s] [100%] Built target convex_plane_decomposition_ros_approximation_demo_node
[32.299s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros -- -j32 -l32
[32.300s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros
[32.307s] -- Install configuration: "Release"
[32.308s] -- Execute custom install script
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/ConvexPlaneDecompositionRos.h
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/MessageConversion.h
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/ParameterLoading.h
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/include/convex_plane_decomposition_ros/convex_plane_decomposition_ros/RosVisualizations.h
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/demo_node.yaml
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/node.yaml
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/config/parameters.yaml
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/holes.png
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/real_stairs_125cm.png
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/slope_1m_1m_20cm.png
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/straight_stairs_1m_1m_60cm.png
[32.308s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/data/terrain.png
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/convex_plane_decomposition.launch.py
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/demo.launch.py
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/launch/save_elevation_map.launch.py
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/rviz/config_demo.rviz
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/library_path.sh
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/library_path.dsv
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_node
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_add_noise
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_save_elevationmap
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_approximation_demo_node
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/convex_plane_decomposition_ros/convex_plane_decomposition_ros_TestShapeGrowing
[32.309s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/package_run_dependencies/convex_plane_decomposition_ros
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/parent_prefix_path/convex_plane_decomposition_ros
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/ament_prefix_path.sh
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/ament_prefix_path.dsv
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/path.sh
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/environment/path.dsv
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.bash
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.sh
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.zsh
[32.310s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/local_setup.dsv
[32.310s] -- Symlinking: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/package.dsv
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/ament_index/resource_index/packages/convex_plane_decomposition_ros
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/ament_cmake_export_dependencies-extras.cmake
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/ament_cmake_export_targets-extras.cmake
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/convex_plane_decomposition_rosConfig.cmake
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/convex_plane_decomposition_rosConfig-version.cmake
[32.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/package.xml
[32.322s] -- Installing: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib/libconvex_plane_decomposition_ros.a
[32.324s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/export_convex_plane_decomposition_rosExport.cmake
[32.324s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake/export_convex_plane_decomposition_rosExport-release.cmake
[32.326s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition_ros' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition_ros
