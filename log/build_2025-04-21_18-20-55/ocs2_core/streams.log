[0.040s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_core': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_core -- -j32 -l32
[0.074s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.203s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.209s] -- Found ocs2_thirdparty: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_thirdparty/share/ocs2_thirdparty/cmake)
[0.229s] -- OCS2_CXX_FLAGS: -pthread-Wfatal-errors-Wl,--no-as-needed-fPIC-DBOOST_ALL_DYN_LINK-fopenmp
[0.234s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.311s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.403s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.412s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.413s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_core/include>;$<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_core/test/include>
[0.413s] -- Configured cppcheck exclude dirs and/or files: 
[0.421s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.421s] -- Configured cpplint exclude dirs and/or files: 
[0.425s] -- Added test 'lint_cmake' to check CMake code style
[0.434s] -- Added test 'uncrustify' to check C / C++ code style
[0.434s] -- Configured uncrustify additional arguments: 
[0.436s] -- Added test 'xmllint' to check XML markup files
[0.437s] -- Configuring done
[0.489s] -- Generating done
[0.502s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_core
[0.547s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.547s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.573s] [  1%] Built target gtest
[0.573s] [  2%] Built target gtest_main
[0.594s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core[0m
[0.746s] [  3%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateAugmentedLagrangianCollection.cpp.o[0m
[0.749s] [  3%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/Types.cpp.o[0m
[0.749s] [  4%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/AugmentedLagrangian.cpp.o[0m
[0.749s] [  5%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateAugmentedLagrangian.cpp.o[0m
[0.750s] [  5%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateInputAugmentedLagrangian.cpp.o[0m
[0.750s] [  6%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateInputAugmentedLagrangianCollection.cpp.o[0m
[0.754s] [  7%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/CppAdSparsity.cpp.o[0m
[0.758s] [  7%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/CppAdInterface.cpp.o[0m
[0.759s] [  8%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/FiniteDifferenceMethods.cpp.o[0m
[0.760s] [  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateInputConstraintCppAd.cpp.o[0m
[0.761s] [  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateConstraintCppAd.cpp.o[0m
[0.764s] [  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateInputConstraintCollection.cpp.o[0m
[0.765s] [ 10%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateConstraintCollection.cpp.o[0m
[0.766s] [ 11%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/LinearStateConstraint.cpp.o[0m
[0.766s] [ 12%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/LinearStateInputConstraint.cpp.o[0m
[0.767s] [ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/StateBasedLinearController.cpp.o[0m
[0.767s] [ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/FeedforwardController.cpp.o[0m
[0.768s] [ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/QuadraticStateCost.cpp.o[0m
[0.768s] [ 14%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/LinearController.cpp.o[0m
[0.769s] [ 15%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateCostCollection.cpp.o[0m
[0.775s] [ 15%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateCostCppAd.cpp.o[0m
[0.775s] [ 16%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/QuadraticStateInputCost.cpp.o[0m
[0.779s] [ 17%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputCostCollection.cpp.o[0m
[0.827s] [ 18%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputCostCppAd.cpp.o[0m
[0.832s] [ 19%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/ControlledSystemBase.cpp.o[0m
[0.834s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/LinearSystemDynamics.cpp.o[0m
[0.834s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputGaussNewtonCostAd.cpp.o[0m
[0.867s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsBase.cpp.o[0m
[0.875s] [ 21%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SensitivityIntegrator.cpp.o[0m
[0.876s] [ 22%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsBaseAD.cpp.o[0m
[0.881s] [ 23%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsLinearizer.cpp.o[0m
[0.883s] [ 23%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/TransferFunctionBase.cpp.o[0m
[3.422s] [ 24%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SensitivityIntegratorImpl.cpp.o[0m
[3.512s] [ 24%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/Integrator.cpp.o[0m
[3.525s] [ 25%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/IntegratorBase.cpp.o[0m
[3.625s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/RungeKuttaDormandPrince5.cpp.o[0m
[3.665s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/OdeBase.cpp.o[0m
[3.741s] [ 27%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/Observer.cpp.o[0m
[3.791s] [ 28%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/StateTriggeredEventHandler.cpp.o[0m
[3.826s] [ 28%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SystemEventHandler.cpp.o[0m
[3.861s] [ 29%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/reference/ModeSchedule.cpp.o[0m
[4.188s] [ 30%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/reference/TargetTrajectories.cpp.o[0m
[4.312s] [ 30%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingDefinition.cpp.o[0m
[4.473s] [ 31%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingPropertyTree.cpp.o[0m
[4.874s] [ 32%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingFilter.cpp.o[0m
[5.051s] [ 32%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingPreComputation.cpp.o[0m
[5.076s] [ 33%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCost.cpp.o[0m
[5.739s] [ 34%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingStateCost.cpp.o[0m
[5.787s] [ 34%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingStateInputCost.cpp.o[0m
[5.921s] [ 35%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCostEliminatePattern.cpp.o[0m
[6.102s] [ 36%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCostOutputPattern.cpp.o[0m
[6.191s] [ 36%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraint.cpp.o[0m
[6.294s] [ 37%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.cpp.o[0m
[6.310s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.cpp.o[0m
[6.617s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.cpp.o[0m
[7.124s] [ 39%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.cpp.o[0m
[7.125s] [ 40%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.cpp.o[0m
[7.131s] [ 40%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.cpp.o[0m
[7.245s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.cpp.o[0m
[7.258s] [ 42%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.cpp.o[0m
[7.274s] [ 42%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraint.cpp.o[0m
[7.408s] [ 43%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingStateConstraint.cpp.o[0m
[7.415s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingStateInputConstraint.cpp.o[0m
[7.432s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraintEliminatePattern.cpp.o[0m
[7.809s] [ 45%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraintOutputPattern.cpp.o[0m
[7.863s] [ 46%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamics.cpp.o[0m
[8.311s] [ 46%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.cpp.o[0m
[8.389s] [ 47%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.cpp.o[0m
[8.578s] [ 47%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingFilterDynamics.cpp.o[0m
[9.746s] [ 48%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/ModelData.cpp.o[0m
[9.753s] [ 49%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/initialization/LoopshapingInitializer.cpp.o[0m
[10.123s] [ 49%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/Metrics.cpp.o[0m
[10.252s] [ 50%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/Multiplier.cpp.o[0m
[10.350s] [ 51%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/misc/LinearAlgebra.cpp.o[0m
[10.353s] [ 51%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/misc/Log.cpp.o[0m
[10.445s] [ 52%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateSoftConstraint.cpp.o[0m
[10.687s] [ 53%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateInputSoftConstraint.cpp.o[0m
[10.739s] [ 53%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateInputSoftBoxConstraint.cpp.o[0m
[10.822s] [ 54%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/MultidimensionalPenalty.cpp.o[0m
[10.982s] [ 55%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/Penalties.cpp.o[0m
[11.138s] [ 55%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/penalties/RelaxedBarrierPenalty.cpp.o[0m
[11.192s] [ 56%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/penalties/SquaredHingePenalty.cpp.o[0m
[11.568s] [ 57%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/thread_support/ThreadPool.cpp.o[0m
[49.109s] [ 57%] [32m[1mLinking CXX static library libocs2_core.a[0m
[49.223s] [ 57%] Built target ocs2_core
[49.238s] [35m[1mConsolidate compiler generated dependencies of target test_control[0m
[49.240s] [35m[1mConsolidate compiler generated dependencies of target test_cost[0m
[49.241s] [35m[1mConsolidate compiler generated dependencies of target test_ModeSchedule[0m
[49.242s] [35m[1mConsolidate compiler generated dependencies of target initialization_unittest[0m
[49.243s] [35m[1mConsolidate compiler generated dependencies of target interpolation_unittest[0m
[49.243s] [35m[1mConsolidate compiler generated dependencies of target test_transferfunctionbase[0m
[49.244s] [35m[1mConsolidate compiler generated dependencies of target test_metrics[0m
[49.244s] [35m[1mConsolidate compiler generated dependencies of target test_integration[0m
[49.244s] [35m[1mConsolidate compiler generated dependencies of target test_dynamics[0m
[49.246s] [35m[1mConsolidate compiler generated dependencies of target test_ModelData[0m
[49.246s] [35m[1mConsolidate compiler generated dependencies of target test_multiplier[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_core[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target test_softConstraint[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core_cppadcg[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_thread_support[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target test_constraint[0m
[49.247s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_misc[0m
[49.255s] [35m[1mConsolidate compiler generated dependencies of target ocs2_core_loopshaping[0m
[49.262s] [ 57%] [32mBuilding CXX object CMakeFiles/test_control.dir/test/control/testLinearController.cpp.o[0m
[49.265s] [ 59%] [32mBuilding CXX object CMakeFiles/test_control.dir/test/control/testFeedforwardController.cpp.o[0m
[49.266s] [ 59%] [32mBuilding CXX object CMakeFiles/test_ModeSchedule.dir/test/reference/testModeSchedule.cpp.o[0m
[49.275s] [ 60%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testCostCollection.cpp.o[0m
[49.275s] [ 60%] [32mBuilding CXX object CMakeFiles/test_metrics.dir/test/model_data/testMetrics.cpp.o[0m
[49.275s] [ 60%] [32mBuilding CXX object CMakeFiles/interpolation_unittest.dir/test/misc/testInterpolation.cpp.o[0m
[49.275s] [ 61%] [32mBuilding CXX object CMakeFiles/test_transferfunctionbase.dir/test/dynamics/testTransferfunctionBase.cpp.o[0m
[49.275s] [ 62%] [32mBuilding CXX object CMakeFiles/test_multiplier.dir/test/model_data/testMultiplier.cpp.o[0m
[49.275s] [ 63%] [32mBuilding CXX object CMakeFiles/initialization_unittest.dir/test/initialization/InitializationTest.cpp.o[0m
[49.276s] [ 63%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testBufferedValue.cpp.o[0m
[49.278s] [ 64%] [32mBuilding CXX object CMakeFiles/test_softConstraint.dir/test/soft_constraint/testDoubleSidedPenalty.cpp.o[0m
[49.278s] [ 64%] [32mBuilding CXX object CMakeFiles/test_ModelData.dir/test/model_data/testModelData.cpp.o[0m
[49.278s] [ 65%] [32mBuilding CXX object CMakeFiles/test_softConstraint.dir/test/soft_constraint/testSoftConstraint.cpp.o[0m
[49.278s] [ 66%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testCostCppAd.cpp.o[0m
[49.278s] [ 66%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_core.dir/test/testPrecomputation.cpp.o[0m
[49.279s] [ 67%] [32mBuilding CXX object CMakeFiles/test_dynamics.dir/test/dynamics/testSystemDynamicsLinearizer.cpp.o[0m
[49.279s] [ 68%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testThreadPool.cpp.o[0m
[49.280s] [ 69%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testSynchronized.cpp.o[0m
[49.284s] [ 70%] [32mBuilding CXX object CMakeFiles/test_dynamics.dir/test/dynamics/testSystemDynamicsPreComputation.cpp.o[0m
[49.285s] [ 71%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/testSensitivityIntegrator.cpp.o[0m
[49.287s] [ 72%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testQuadraticCostFunction.cpp.o[0m
[49.288s] [ 73%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_core.dir/test/testTypes.cpp.o[0m
[49.288s] [ 74%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testConstraintCollection.cpp.o[0m
[49.289s] [ 75%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/TrapezoidalIntegrationTest.cpp.o[0m
[49.291s] [ 76%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/IntegrationTest.cpp.o[0m
[49.295s] [ 76%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/testRungeKuttaDormandPrince5.cpp.o[0m
[49.302s] [ 77%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testLinearConstraint.cpp.o[0m
[49.305s] [ 77%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testConstraintCppAd.cpp.o[0m
[49.318s] [ 77%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testInterpolation.cpp.o[0m
[49.322s] [ 78%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testCppADCG_dynamics.cpp.o[0m
[49.376s] [ 79%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingConfiguration.cpp.o[0m
[49.377s] [ 80%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testSparsityHelpers.cpp.o[0m
[52.805s] [ 80%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingAugmentedLagrangian.cpp.o[0m
[53.548s] [ 81%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingConstraint.cpp.o[0m
[53.556s] [ 82%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLinearAlgebra.cpp.o[0m
[53.712s] [ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingCost.cpp.o[0m
[54.337s] [ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingSoftConstraint.cpp.o[0m
[54.426s] [ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testCppAdInterface.cpp.o[0m
[54.482s] [ 84%] [32m[1mLinking CXX executable ocs2_core_test_core[0m
[54.536s] [ 85%] [32m[1mLinking CXX executable test_control[0m
[54.734s] [ 86%] [32m[1mLinking CXX executable test_metrics[0m
[54.745s] [ 86%] Built target ocs2_core_test_core
[54.770s] [ 87%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLogging.cpp.o[0m
[54.903s] [ 87%] Built target test_control
[54.947s] [ 88%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingDefinition.cpp.o[0m
[55.288s] [ 88%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLoadData.cpp.o[0m
[55.319s] [ 88%] [32m[1mLinking CXX executable initialization_unittest[0m
[55.408s] [ 88%] Built target test_metrics
[55.453s] [ 89%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingDynamics.cpp.o[0m
[55.457s] [ 90%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLookup.cpp.o[0m
[55.635s] [ 90%] Built target initialization_unittest
[55.673s] [ 90%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingFilterDynamics.cpp.o[0m
[56.047s] [ 90%] [32m[1mLinking CXX executable test_multiplier[0m
[56.316s] [ 90%] Built target test_multiplier
[56.360s] [ 91%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingPreComputation.cpp.o[0m
[56.556s] [ 92%] [32m[1mLinking CXX executable test_transferfunctionbase[0m
[56.733s] [ 92%] Built target test_transferfunctionbase
[59.097s] [ 92%] [32m[1mLinking CXX executable test_cost[0m
[59.425s] [ 92%] Built target test_cost
[59.426s] [ 93%] [32m[1mLinking CXX executable test_ModeSchedule[0m
[59.605s] [ 93%] Built target test_ModeSchedule
[59.620s] [ 94%] [32m[1mLinking CXX executable test_integration[0m
[59.643s] [ 95%] [32m[1mLinking CXX executable test_ModelData[0m
[59.767s] [ 95%] [32m[1mLinking CXX executable test_softConstraint[0m
[59.814s] [ 95%] Built target test_ModelData
[59.907s] [ 95%] Built target test_integration
[59.990s] [ 95%] [32m[1mLinking CXX executable test_dynamics[0m
[60.110s] [ 95%] Built target test_softConstraint
[60.328s] [ 95%] Built target test_dynamics
[61.115s] [ 96%] [32m[1mLinking CXX executable interpolation_unittest[0m
[61.274s] [ 96%] Built target interpolation_unittest
[61.568s] [ 97%] [32m[1mLinking CXX executable test_constraint[0m
[61.631s] [ 97%] [32m[1mLinking CXX executable ocs2_core_test_thread_support[0m
[61.778s] [ 97%] Built target test_constraint
[61.791s] [ 97%] Built target ocs2_core_test_thread_support
[63.707s] [ 98%] [32m[1mLinking CXX executable ocs2_core_loopshaping[0m
[63.858s] [ 98%] Built target ocs2_core_loopshaping
[65.459s] [ 99%] [32m[1mLinking CXX executable ocs2_core_test_misc[0m
[65.564s] [ 99%] Built target ocs2_core_test_misc
[68.303s] [100%] [32m[1mLinking CXX executable ocs2_core_cppadcg[0m
[68.416s] [100%] Built target ocs2_core_cppadcg
[68.427s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_core' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_core -- -j32 -l32
[68.429s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_core': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_core
[68.440s] -- Install configuration: "Release"
[68.440s] -- Execute custom install script
[68.442s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/ComputationRequest.h
[68.443s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/NumericTraits.h
[68.443s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/PreComputation.h
[68.443s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/Types.h
[68.443s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/AugmentedLagrangian.h
[68.443s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangian.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianCollection.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianInterface.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangian.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianCollection.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianInterface.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdInterface.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdSparsity.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/FiniteDifferenceMethods.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/ConstraintOrder.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateConstraint.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateInputConstraint.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraint.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCollection.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCppAd.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraint.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCollection.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCppAd.h
[68.444s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerAdjustmentBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerType.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/FeedforwardController.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/LinearController.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/StateBasedLinearController.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateCost.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateInputCost.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCost.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCollection.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCppAd.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCost.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCollection.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCppAd.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputGaussNewtonCostAd.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/ControlledSystemBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/LinearSystemDynamics.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBaseAD.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsLinearizer.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/TransferFunctionBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/DefaultInitializer.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/Initializer.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/OperatingPoints.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Integrator.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/IntegratorBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Observer.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeBase.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeFunc.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/RungeKuttaDormandPrince5.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegrator.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegratorImpl.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/StateTriggeredEventHandler.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SystemEventHandler.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/TrapezoidalIntegration.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/eigenIntegration.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/implementation/Integrator.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/steppers.h
[68.445s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/Loopshaping.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingDefinition.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingFilter.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPreComputation.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPropertyTree.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraint.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintEliminatePattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintOutputPattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateConstraint.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateInputConstraint.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCost.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostEliminatePattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostOutputPattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateCost.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateInputCost.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamics.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingFilterDynamics.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/initialization/LoopshapingInitializer.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraint.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Benchmark.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Collection.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/CommandLine.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Display.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LTI_Equations.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearAlgebra.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearFunction.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearInterpolation.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadData.h
[68.446s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadStdVectorOfPair.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Log.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Lookup.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Numerics.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/implementation/LinearInterpolation.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/randomMatrices.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Metrics.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelData.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelDataLinearInterpolation.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Multiplier.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/MultidimensionalPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/Penalties.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/AugmentedPenaltyBase.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/ModifiedRelaxedBarrierPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/QuadraticPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SlacknessSquaredHingePenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SmoothAbsolutePenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/DoubleSidedPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/PenaltyBase.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/QuadraticPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/RelaxedBarrierPenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SmoothAbsolutePenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SquaredHingePenalty.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/ModeSchedule.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/TargetTrajectories.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftBoxConstraint.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftConstraint.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateSoftConstraint.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/BufferedValue.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ExecuteAndSleep.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/SetThreadPriority.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/Synchronized.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ThreadPool.h
[68.447s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/test/testTools.h
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.sh
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.dsv
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/package_run_dependencies/ocs2_core
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/parent_prefix_path/ocs2_core
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.sh
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.dsv
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.sh
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.dsv
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.bash
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.sh
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.zsh
[68.448s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.dsv
[68.448s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.dsv
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/packages/ocs2_core
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_cxx_flags.cmake
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_targets-extras.cmake
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_dependencies-extras.cmake
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig.cmake
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig-version.cmake
[68.456s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.xml
[68.456s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_core/lib/libocs2_core.a
[68.461s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport.cmake
[68.461s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport-release.cmake
[68.463s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_core' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_core
