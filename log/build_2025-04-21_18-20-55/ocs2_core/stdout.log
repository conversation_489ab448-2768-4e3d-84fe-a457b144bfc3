-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_thirdparty: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_thirdparty/share/ocs2_thirdparty/cmake)
-- OCS2_CXX_FLAGS: -pthread-Wfatal-errors-Wl,--no-as-needed-fPIC-DBOOST_ALL_DYN_LINK-fopenmp
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_core/include>;$<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_core/test/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_core
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[  1%] Built target gtest
[  2%] Built target gtest_main
[35m[1mConsolidate compiler generated dependencies of target ocs2_core[0m
[  3%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateAugmentedLagrangianCollection.cpp.o[0m
[  3%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/Types.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/AugmentedLagrangian.cpp.o[0m
[  5%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateAugmentedLagrangian.cpp.o[0m
[  5%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateInputAugmentedLagrangian.cpp.o[0m
[  6%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/augmented_lagrangian/StateInputAugmentedLagrangianCollection.cpp.o[0m
[  7%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/CppAdSparsity.cpp.o[0m
[  7%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/CppAdInterface.cpp.o[0m
[  8%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/automatic_differentation/FiniteDifferenceMethods.cpp.o[0m
[  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateInputConstraintCppAd.cpp.o[0m
[  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateConstraintCppAd.cpp.o[0m
[  9%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateInputConstraintCollection.cpp.o[0m
[ 10%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/StateConstraintCollection.cpp.o[0m
[ 11%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/LinearStateConstraint.cpp.o[0m
[ 12%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/constraint/LinearStateInputConstraint.cpp.o[0m
[ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/StateBasedLinearController.cpp.o[0m
[ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/FeedforwardController.cpp.o[0m
[ 13%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/QuadraticStateCost.cpp.o[0m
[ 14%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/control/LinearController.cpp.o[0m
[ 15%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateCostCollection.cpp.o[0m
[ 15%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateCostCppAd.cpp.o[0m
[ 16%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/QuadraticStateInputCost.cpp.o[0m
[ 17%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputCostCollection.cpp.o[0m
[ 18%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputCostCppAd.cpp.o[0m
[ 19%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/ControlledSystemBase.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/LinearSystemDynamics.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/cost/StateInputGaussNewtonCostAd.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsBase.cpp.o[0m
[ 21%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SensitivityIntegrator.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsBaseAD.cpp.o[0m
[ 23%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/SystemDynamicsLinearizer.cpp.o[0m
[ 23%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/dynamics/TransferFunctionBase.cpp.o[0m
[ 24%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SensitivityIntegratorImpl.cpp.o[0m
[ 24%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/Integrator.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/IntegratorBase.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/RungeKuttaDormandPrince5.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/OdeBase.cpp.o[0m
[ 27%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/Observer.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/StateTriggeredEventHandler.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/integration/SystemEventHandler.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/reference/ModeSchedule.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/reference/TargetTrajectories.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingDefinition.cpp.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingPropertyTree.cpp.o[0m
[ 32%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingFilter.cpp.o[0m
[ 32%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/LoopshapingPreComputation.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCost.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingStateCost.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingStateInputCost.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCostEliminatePattern.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/cost/LoopshapingCostOutputPattern.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraint.cpp.o[0m
[ 37%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraint.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingStateConstraint.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingStateInputConstraint.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraintEliminatePattern.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/constraint/LoopshapingConstraintOutputPattern.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamics.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/dynamics/LoopshapingFilterDynamics.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/ModelData.cpp.o[0m
[ 49%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/loopshaping/initialization/LoopshapingInitializer.cpp.o[0m
[ 49%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/Metrics.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/model_data/Multiplier.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/misc/LinearAlgebra.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/misc/Log.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateSoftConstraint.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateInputSoftConstraint.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/soft_constraint/StateInputSoftBoxConstraint.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/MultidimensionalPenalty.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/Penalties.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/penalties/RelaxedBarrierPenalty.cpp.o[0m
[ 56%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/penalties/penalties/SquaredHingePenalty.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/ocs2_core.dir/src/thread_support/ThreadPool.cpp.o[0m
[ 57%] [32m[1mLinking CXX static library libocs2_core.a[0m
[ 57%] Built target ocs2_core
[35m[1mConsolidate compiler generated dependencies of target test_control[0m
[35m[1mConsolidate compiler generated dependencies of target test_cost[0m
[35m[1mConsolidate compiler generated dependencies of target test_ModeSchedule[0m
[35m[1mConsolidate compiler generated dependencies of target initialization_unittest[0m
[35m[1mConsolidate compiler generated dependencies of target interpolation_unittest[0m
[35m[1mConsolidate compiler generated dependencies of target test_transferfunctionbase[0m
[35m[1mConsolidate compiler generated dependencies of target test_metrics[0m
[35m[1mConsolidate compiler generated dependencies of target test_integration[0m
[35m[1mConsolidate compiler generated dependencies of target test_dynamics[0m
[35m[1mConsolidate compiler generated dependencies of target test_ModelData[0m
[35m[1mConsolidate compiler generated dependencies of target test_multiplier[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_core[0m
[35m[1mConsolidate compiler generated dependencies of target test_softConstraint[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_core_cppadcg[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_thread_support[0m
[35m[1mConsolidate compiler generated dependencies of target test_constraint[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_core_test_misc[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_core_loopshaping[0m
[ 57%] [32mBuilding CXX object CMakeFiles/test_control.dir/test/control/testLinearController.cpp.o[0m
[ 59%] [32mBuilding CXX object CMakeFiles/test_control.dir/test/control/testFeedforwardController.cpp.o[0m
[ 59%] [32mBuilding CXX object CMakeFiles/test_ModeSchedule.dir/test/reference/testModeSchedule.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testCostCollection.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/test_metrics.dir/test/model_data/testMetrics.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/interpolation_unittest.dir/test/misc/testInterpolation.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/test_transferfunctionbase.dir/test/dynamics/testTransferfunctionBase.cpp.o[0m
[ 62%] [32mBuilding CXX object CMakeFiles/test_multiplier.dir/test/model_data/testMultiplier.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/initialization_unittest.dir/test/initialization/InitializationTest.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testBufferedValue.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/test_softConstraint.dir/test/soft_constraint/testDoubleSidedPenalty.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/test_ModelData.dir/test/model_data/testModelData.cpp.o[0m
[ 65%] [32mBuilding CXX object CMakeFiles/test_softConstraint.dir/test/soft_constraint/testSoftConstraint.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testCostCppAd.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_core.dir/test/testPrecomputation.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/test_dynamics.dir/test/dynamics/testSystemDynamicsLinearizer.cpp.o[0m
[ 68%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testThreadPool.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_thread_support.dir/test/thread_support/testSynchronized.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/test_dynamics.dir/test/dynamics/testSystemDynamicsPreComputation.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/testSensitivityIntegrator.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/test_cost.dir/test/cost/testQuadraticCostFunction.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_core.dir/test/testTypes.cpp.o[0m
[ 74%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testConstraintCollection.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/TrapezoidalIntegrationTest.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/IntegrationTest.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/test_integration.dir/test/integration/testRungeKuttaDormandPrince5.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testLinearConstraint.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/test_constraint.dir/test/constraint/testConstraintCppAd.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testInterpolation.cpp.o[0m
[ 78%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testCppADCG_dynamics.cpp.o[0m
[ 79%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingConfiguration.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testSparsityHelpers.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingAugmentedLagrangian.cpp.o[0m
[ 81%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingConstraint.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLinearAlgebra.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingCost.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingSoftConstraint.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/ocs2_core_cppadcg.dir/test/cppad_cg/testCppAdInterface.cpp.o[0m
[ 84%] [32m[1mLinking CXX executable ocs2_core_test_core[0m
[ 85%] [32m[1mLinking CXX executable test_control[0m
[ 86%] [32m[1mLinking CXX executable test_metrics[0m
[ 86%] Built target ocs2_core_test_core
[ 87%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLogging.cpp.o[0m
[ 87%] Built target test_control
[ 88%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingDefinition.cpp.o[0m
[ 88%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLoadData.cpp.o[0m
[ 88%] [32m[1mLinking CXX executable initialization_unittest[0m
[ 88%] Built target test_metrics
[ 89%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingDynamics.cpp.o[0m
[ 90%] [32mBuilding CXX object CMakeFiles/ocs2_core_test_misc.dir/test/misc/testLookup.cpp.o[0m
[ 90%] Built target initialization_unittest
[ 90%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingFilterDynamics.cpp.o[0m
[ 90%] [32m[1mLinking CXX executable test_multiplier[0m
[ 90%] Built target test_multiplier
[ 91%] [32mBuilding CXX object CMakeFiles/ocs2_core_loopshaping.dir/test/loopshaping/testLoopshapingPreComputation.cpp.o[0m
[ 92%] [32m[1mLinking CXX executable test_transferfunctionbase[0m
[ 92%] Built target test_transferfunctionbase
[ 92%] [32m[1mLinking CXX executable test_cost[0m
[ 92%] Built target test_cost
[ 93%] [32m[1mLinking CXX executable test_ModeSchedule[0m
[ 93%] Built target test_ModeSchedule
[ 94%] [32m[1mLinking CXX executable test_integration[0m
[ 95%] [32m[1mLinking CXX executable test_ModelData[0m
[ 95%] [32m[1mLinking CXX executable test_softConstraint[0m
[ 95%] Built target test_ModelData
[ 95%] Built target test_integration
[ 95%] [32m[1mLinking CXX executable test_dynamics[0m
[ 95%] Built target test_softConstraint
[ 95%] Built target test_dynamics
[ 96%] [32m[1mLinking CXX executable interpolation_unittest[0m
[ 96%] Built target interpolation_unittest
[ 97%] [32m[1mLinking CXX executable test_constraint[0m
[ 97%] [32m[1mLinking CXX executable ocs2_core_test_thread_support[0m
[ 97%] Built target test_constraint
[ 97%] Built target ocs2_core_test_thread_support
[ 98%] [32m[1mLinking CXX executable ocs2_core_loopshaping[0m
[ 98%] Built target ocs2_core_loopshaping
[ 99%] [32m[1mLinking CXX executable ocs2_core_test_misc[0m
[ 99%] Built target ocs2_core_test_misc
[100%] [32m[1mLinking CXX executable ocs2_core_cppadcg[0m
[100%] Built target ocs2_core_cppadcg
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/ComputationRequest.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/NumericTraits.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/PreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/AugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdSparsity.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/FiniteDifferenceMethods.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/ConstraintOrder.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerAdjustmentBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerType.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/FeedforwardController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/LinearController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/StateBasedLinearController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputGaussNewtonCostAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/ControlledSystemBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/LinearSystemDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBaseAD.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsLinearizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/TransferFunctionBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/DefaultInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/Initializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/OperatingPoints.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Integrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/IntegratorBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Observer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeFunc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/RungeKuttaDormandPrince5.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegratorImpl.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/StateTriggeredEventHandler.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SystemEventHandler.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/TrapezoidalIntegration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/eigenIntegration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/implementation/Integrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/steppers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/Loopshaping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingFilter.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPropertyTree.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingFilterDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/initialization/LoopshapingInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Benchmark.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Collection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/CommandLine.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Display.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LTI_Equations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearAlgebra.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearFunction.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadStdVectorOfPair.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Log.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Lookup.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Numerics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/implementation/LinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/randomMatrices.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Metrics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelDataLinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Multiplier.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/MultidimensionalPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/Penalties.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/AugmentedPenaltyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/ModifiedRelaxedBarrierPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/QuadraticPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SlacknessSquaredHingePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SmoothAbsolutePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/DoubleSidedPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/PenaltyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/QuadraticPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/RelaxedBarrierPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SmoothAbsolutePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SquaredHingePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/ModeSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/TargetTrajectories.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftBoxConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/BufferedValue.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ExecuteAndSleep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/SetThreadPriority.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/Synchronized.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ThreadPool.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/test/testTools.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/package_run_dependencies/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/parent_prefix_path/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/packages/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_cxx_flags.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_core/lib/libocs2_core.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport-release.cmake
