[0.012s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[0.059s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.152s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.156s] -- Found ocs2_core: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake)
[0.169s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.206s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.233s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.235s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.235s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_oc/include>;$<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/core/ocs2_oc/test/include>
[0.235s] -- Configured cppcheck exclude dirs and/or files: 
[0.237s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.237s] -- Configured cpplint exclude dirs and/or files: 
[0.238s] -- Added test 'lint_cmake' to check CMake code style
[0.240s] -- Added test 'uncrustify' to check C / C++ code style
[0.240s] -- Configured uncrustify additional arguments: 
[0.240s] -- Added test 'xmllint' to check XML markup files
[0.241s] -- Configuring done
[0.252s] -- Generating done
[0.256s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_oc
[0.276s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.276s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.283s] [35m[1mConsolidate compiler generated dependencies of target ocs2_oc[0m
[0.287s] [  3%] Built target gtest
[0.289s] [  7%] Built target gtest_main
[0.319s] [  9%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/Initialization.cpp.o[0m
[0.319s] [ 11%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/LagrangianEvaluation.cpp.o[0m
[0.320s] [ 12%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/approximate_model/ChangeOfInputVariables.cpp.o[0m
[0.320s] [ 14%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/Transcription.cpp.o[0m
[0.320s] [ 16%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_data/LoopshapingPrimalSolution.cpp.o[0m
[0.321s] [ 18%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/approximate_model/LinearQuadraticApproximator.cpp.o[0m
[0.321s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_data/PerformanceIndex.cpp.o[0m
[0.321s] [ 22%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/Helpers.cpp.o[0m
[0.322s] [ 24%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_problem/OptimalControlProblem.cpp.o[0m
[0.322s] [ 25%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/MetricsComputation.cpp.o[0m
[0.323s] [ 27%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/PerformanceIndexComputation.cpp.o[0m
[0.323s] [ 29%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/multiple_shooting/ProjectionMultiplierCoefficients.cpp.o[0m
[0.323s] [ 31%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_data/TimeDiscretization.cpp.o[0m
[0.325s] [ 33%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_problem/OcpSize.cpp.o[0m
[0.326s] [ 35%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_problem/OcpToKkt.cpp.o[0m
[0.327s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_problem/OptimalControlProblemHelperFunction.cpp.o[0m
[0.327s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_problem/LoopshapingOptimalControlProblem.cpp.o[0m
[0.329s] [ 40%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/oc_solver/SolverBase.cpp.o[0m
[0.330s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/precondition/Ruzi.cpp.o[0m
[0.330s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/RolloutBase.cpp.o[0m
[0.335s] [ 46%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/PerformanceIndicesRollout.cpp.o[0m
[0.335s] [ 48%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/RootFinder.cpp.o[0m
[0.335s] [ 50%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/InitializerRollout.cpp.o[0m
[0.335s] [ 51%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/StateTriggeredRollout.cpp.o[0m
[0.335s] [ 53%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/synchronized_module/ReferenceManager.cpp.o[0m
[0.335s] [ 55%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/TimeTriggeredRollout.cpp.o[0m
[0.335s] [ 57%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/rollout/RolloutSettings.cpp.o[0m
[0.337s] [ 59%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/synchronized_module/LoopshapingReferenceManager.cpp.o[0m
[0.337s] [ 61%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/synchronized_module/LoopshapingSynchronizedModule.cpp.o[0m
[0.338s] [ 62%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/synchronized_module/SolverObserver.cpp.o[0m
[0.345s] [ 64%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/trajectory_adjustment/TrajectorySpreading.cpp.o[0m
[0.362s] [ 66%] [32mBuilding CXX object CMakeFiles/ocs2_oc.dir/src/search_strategy/FilterLinesearch.cpp.o[0m
[5.087s] [ 68%] [32m[1mLinking CXX static library libocs2_oc.a[0m
[5.146s] [ 68%] Built target ocs2_oc
[5.163s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_oc_rollout[0m
[5.163s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_oc_data[0m
[5.164s] [35m[1mConsolidate compiler generated dependencies of target test_change_of_variables[0m
[5.164s] [35m[1mConsolidate compiler generated dependencies of target test_trajectory_spreading[0m
[5.164s] [35m[1mConsolidate compiler generated dependencies of target test_ocp_to_kkt[0m
[5.164s] [35m[1mConsolidate compiler generated dependencies of target test_precondition[0m
[5.165s] [35m[1mConsolidate compiler generated dependencies of target test_ocs2_oc_multiple_shooting[0m
[5.176s] [ 70%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_rollout.dir/test/rollout/testStateTriggeredRollout.cpp.o[0m
[5.177s] [ 72%] [32mBuilding CXX object CMakeFiles/test_trajectory_spreading.dir/test/trajectory_adjustment/TrajectorySpreadingTest.cpp.o[0m
[5.178s] [ 75%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_rollout.dir/test/rollout/testTimeTriggeredRollout.cpp.o[0m
[5.178s] [ 74%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_data.dir/test/oc_data/testTimeDiscretization.cpp.o[0m
[5.179s] [ 77%] [32mBuilding CXX object CMakeFiles/test_change_of_variables.dir/test/testChangeOfInputVariables.cpp.o[0m
[5.180s] [ 81%] [32mBuilding CXX object CMakeFiles/test_ocp_to_kkt.dir/test/oc_problem/testOcpToKkt.cpp.o[0m
[5.180s] [ 79%] [32mBuilding CXX object CMakeFiles/test_precondition.dir/test/precondition/testPrecondition.cpp.o[0m
[5.186s] [ 83%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_multiple_shooting.dir/test/multiple_shooting/testTranscriptionPerformanceIndex.cpp.o[0m
[5.187s] [ 85%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_multiple_shooting.dir/test/multiple_shooting/testProjectionMultiplierCoefficients.cpp.o[0m
[5.188s] [ 87%] [32mBuilding CXX object CMakeFiles/test_ocs2_oc_multiple_shooting.dir/test/multiple_shooting/testTranscriptionMetrics.cpp.o[0m
[7.380s] [ 88%] [32m[1mLinking CXX executable test_ocs2_oc_data[0m
[7.496s] [ 88%] Built target test_ocs2_oc_data
[8.017s] [ 90%] [32m[1mLinking CXX executable test_ocs2_oc_rollout[0m
[8.162s] [ 90%] Built target test_ocs2_oc_rollout
[8.468s] [ 92%] [32m[1mLinking CXX executable test_trajectory_spreading[0m
[8.593s] [ 92%] Built target test_trajectory_spreading
[8.708s] [ 94%] [32m[1mLinking CXX executable test_change_of_variables[0m
[8.727s] [ 96%] [32m[1mLinking CXX executable test_ocp_to_kkt[0m
[8.799s] [ 96%] Built target test_change_of_variables
[8.818s] [ 96%] Built target test_ocp_to_kkt
[9.476s] [ 98%] [32m[1mLinking CXX executable test_precondition[0m
[9.542s] [ 98%] Built target test_precondition
[10.468s] [100%] [32m[1mLinking CXX executable test_ocs2_oc_multiple_shooting[0m
[10.579s] [100%] Built target test_ocs2_oc_multiple_shooting
[10.590s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[10.591s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
[10.602s] -- Install configuration: "Release"
[10.602s] -- Execute custom install script
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/ChangeOfInputVariables.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/LinearQuadraticApproximator.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Helpers.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Initialization.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/LagrangianEvaluation.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/MetricsComputation.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/PerformanceIndexComputation.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/ProjectionMultiplierCoefficients.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Transcription.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/DualSolution.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/LoopshapingPrimalSolution.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PerformanceIndex.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PrimalSolution.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/ProblemMetrics.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/TimeDiscretization.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/LoopshapingOptimalControlProblem.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpSize.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpToKkt.h
[10.603s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblem.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblemHelperFunction.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_solver/SolverBase.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/precondition/Ruzi.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/InitializerRollout.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/PerformanceIndicesRollout.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutBase.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutSettings.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinder.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinderType.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/StateTriggeredRollout.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/TimeTriggeredRollout.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/search_strategy/FilterLinesearch.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingReferenceManager.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingSynchronizedModule.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManager.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerDecorator.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerInterface.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverObserver.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverSynchronizedModule.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreading.h
[10.604s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreadingHelperFunctions.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/DoubleIntegratorReachingTask.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP0.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP1.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/ball_dynamics_staterollout.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/circular_kinematics.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/dynamics_hybrid_slq_test.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/pendulum_dynamics_staterollout.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/testProblemsGeneration.h
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.sh
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.dsv
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/package_run_dependencies/ocs2_oc
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/parent_prefix_path/ocs2_oc
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.sh
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.dsv
[10.605s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.sh
[10.606s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.dsv
[10.606s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.bash
[10.606s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.sh
[10.606s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.zsh
[10.606s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.dsv
[10.606s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.dsv
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/packages/ocs2_oc
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_dependencies-extras.cmake
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_targets-extras.cmake
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig.cmake
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig-version.cmake
[10.616s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.xml
[10.616s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_oc/lib/libocs2_oc.a
[10.617s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport.cmake
[10.617s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport-release.cmake
[10.619s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
