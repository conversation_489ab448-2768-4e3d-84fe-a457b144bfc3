-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_pinocchio_interface: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found ocs2_robotic_assets: 10.3.0 (/home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_sphere_approximation
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_sphere_approximation[0m
[ 18%] Built target gtest
[ 36%] Built target gtest_main
[ 45%] [32mBuilding CXX object CMakeFiles/ocs2_sphere_approximation.dir/src/SphereApproximation.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/ocs2_sphere_approximation.dir/src/PinocchioSphereKinematics.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/ocs2_sphere_approximation.dir/src/PinocchioSphereInterface.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/ocs2_sphere_approximation.dir/src/PinocchioSphereKinematicsCppAd.cpp.o[0m
[ 81%] [32m[1mLinking CXX static library libocs2_sphere_approximation.a[0m
[ 81%] Built target ocs2_sphere_approximation
[35m[1mConsolidate compiler generated dependencies of target PinocchioSphereKinematicsTest[0m
[ 90%] [32mBuilding CXX object CMakeFiles/PinocchioSphereKinematicsTest.dir/test/testPinocchioSphereKinematics.cpp.o[0m
[100%] [32m[1mLinking CXX executable PinocchioSphereKinematicsTest[0m
[100%] Built target PinocchioSphereKinematicsTest
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematicsCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/SphereApproximation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/package_run_dependencies/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/parent_prefix_path/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ament_index/resource_index/packages/ocs2_sphere_approximation
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ocs2_sphere_approximationConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/ocs2_sphere_approximationConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib/libocs2_sphere_approximation.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/export_ocs2_sphere_approximationExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake/export_ocs2_sphere_approximationExport-release.cmake
