[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:25 (find_package)

[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/shape/geometric_shapes.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/include/ocs2_sphere_approximation/SphereApproximation.h:35[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/src/SphereApproximation.cpp:30[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/geometry.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/src/PinocchioSphereInterface.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/src/PinocchioSphereKinematics.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/include/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/include/ocs2_sphere_approximation/PinocchioSphereKinematicsCppAd.h:38[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/src/PinocchioSphereKinematicsCppAd.cpp:33[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/include/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/include/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation/test/testPinocchioSphereKinematics.cpp:34[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
