[0.026s] Invoking command in '/home/<USER>/ros2_ws/build/qpoases_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/qpoases_colcon -- -j32 -l32
[0.091s] [100%] Built target qpOASES
[0.110s] Invoked command in '/home/<USER>/ros2_ws/build/qpoases_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/qpoases_colcon -- -j32 -l32
[0.130s] Invoking command in '/home/<USER>/ros2_ws/build/qpoases_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/qpoases_colcon
[0.144s] -- Install configuration: "Release"
[0.145s] -- Execute custom install script
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/lib/libqpOASES.so.3.2
[0.145s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/library_path.sh
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/library_path.dsv
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/ament_index/resource_index/package_run_dependencies/qpoases_colcon
[0.146s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/ament_index/resource_index/parent_prefix_path/qpoases_colcon
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/ament_prefix_path.sh
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/ament_prefix_path.dsv
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/path.sh
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/environment/path.dsv
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/local_setup.bash
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/local_setup.sh
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/local_setup.zsh
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/local_setup.dsv
[0.148s] -- Symlinking: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/package.dsv
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/ament_index/resource_index/packages/qpoases_colcon
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake/ament_cmake_export_include_directories-extras.cmake
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake/ament_cmake_export_libraries-extras.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake/qpoases_colconConfig.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake/qpoases_colconConfig-version.cmake
[0.167s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/package.xml
[0.167s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES
[0.167s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SQProblemSchur.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Constraints.ipp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Options.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Constraints.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SubjectTo.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Utils.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/UnitTesting.hpp
[0.168s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Types.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Bounds.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Constants.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SparseSolver.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.ipp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/LapackBlasReplacement.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/extras
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/extras/SolutionAnalysis.ipp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/extras/SolutionAnalysis.hpp
[0.169s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/extras/OQPinterface.hpp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/MessageHandling.hpp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.hpp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Utils.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Flipper.hpp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SQProblem.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SubjectTo.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Bounds.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Indexlist.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SQProblemSchur.ipp
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Matrices.hpp
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/Indexlist.hpp
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/MessageHandling.ipp
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/SQProblem.hpp
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.hpp
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/lib/cmake/qpOASES/qpOASESConfig.cmake
[0.171s] -- Up-to-date: /home/<USER>/ros2_ws/install/qpoases_colcon/lib/cmake/qpOASES/qpOASESConfigVersion.cmake
[0.173s] Invoked command in '/home/<USER>/ros2_ws/build/qpoases_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/qpoases_colcon
