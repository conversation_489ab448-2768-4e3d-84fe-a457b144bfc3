[0.024s] Invoking command in '/home/<USER>/ros2_ws/build/grid_map_sdf': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/grid_map_sdf -- -j32 -l32
[0.047s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.188s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.198s] -- Found grid_map_cmake_helpers: 2.0.1 (/opt/ros/humble/share/grid_map_cmake_helpers/cmake)
[0.199s] -- Found grid_map_core: 2.0.1 (/opt/ros/humble/share/grid_map_core/cmake)
[0.220s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[0.235s] -- looking for PCL_COMMON
[0.244s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.357s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.376s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.376s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/submodules/grid_map_sdf/include>;/home/<USER>/ros2_ws/src/ocs2_ros2/submodules/grid_map_sdf/SYSTEM
[0.376s] -- Configured cppcheck exclude dirs and/or files: 
[0.378s] -- Added test 'lint_cmake' to check CMake code style
[0.381s] -- Added test 'uncrustify' to check C / C++ code style
[0.381s] -- Configured uncrustify additional arguments: 
[0.383s] -- Added test 'xmllint' to check XML markup files
[0.390s] -- Configuring done
[0.408s] -- Generating done
[0.415s] -- Build files have been written to: /home/<USER>/ros2_ws/build/grid_map_sdf
[0.431s] [35m[1mConsolidate compiler generated dependencies of target grid_map_sdf[0m
[0.431s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.431s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.433s] [ 22%] Built target gtest
[0.433s] [ 44%] Built target gtest_main
[0.433s] [ 55%] [32mBuilding CXX object CMakeFiles/grid_map_sdf.dir/src/SignedDistanceField.cpp.o[0m
[6.829s] [ 66%] [32m[1mLinking CXX static library libgrid_map_sdf.a[0m
[7.033s] [ 66%] Built target grid_map_sdf
[7.098s] [35m[1mConsolidate compiler generated dependencies of target grid_map_sdf-test[0m
[7.175s] [ 77%] [32mBuilding CXX object CMakeFiles/grid_map_sdf-test.dir/test/SignedDistanceFieldTest.cpp.o[0m
[7.189s] [ 88%] [32mBuilding CXX object CMakeFiles/grid_map_sdf-test.dir/test/test_grid_map_sdf.cpp.o[0m
[16.145s] [100%] [32m[1mLinking CXX executable grid_map_sdf-test[0m
[16.246s] [100%] Built target grid_map_sdf-test
[16.264s] Invoked command in '/home/<USER>/ros2_ws/build/grid_map_sdf' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/grid_map_sdf -- -j32 -l32
[16.266s] Invoking command in '/home/<USER>/ros2_ws/build/grid_map_sdf': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/grid_map_sdf
[16.280s] -- Install configuration: "Release"
[16.281s] -- Execute custom install script
[16.281s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/lib/libgrid_map_sdf.a
[16.281s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/library_path.sh
[16.281s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/library_path.dsv
[16.281s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/package_run_dependencies/grid_map_sdf
[16.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/parent_prefix_path/grid_map_sdf
[16.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/ament_prefix_path.sh
[16.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/ament_prefix_path.dsv
[16.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/path.sh
[16.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/environment/path.dsv
[16.283s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.bash
[16.283s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.sh
[16.283s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.zsh
[16.283s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/local_setup.dsv
[16.283s] -- Symlinking: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/package.dsv
[16.294s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/ament_index/resource_index/packages/grid_map_sdf
[16.294s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_include_directories-extras.cmake
[16.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_libraries-extras.cmake
[16.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/ament_cmake_export_dependencies-extras.cmake
[16.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/grid_map_sdfConfig.cmake
[16.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake/grid_map_sdfConfig-version.cmake
[16.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/package.xml
[16.295s] -- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf
[16.295s] -- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform
[16.295s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/pnmfile.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/dt.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/image.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/imutil.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/imconv.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/distance_transform/misc.hpp
[16.296s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_sdf/include/grid_map_sdf/SignedDistanceField.hpp
[16.299s] Invoked command in '/home/<USER>/ros2_ws/build/grid_map_sdf' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/grid_map_sdf
