[0.226s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
[0.275s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.355s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.359s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.392s] -- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)
[0.403s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.404s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.407s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.414s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.421s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.499s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.501s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.509s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.517s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.530s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.538s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.559s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.599s] -- Found controller_common: 0.0.0 (/home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake)
[0.600s] -- Found ocs2_legged_robot_ros: 0.0.1 (/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake)
[0.634s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.635s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.649s] -- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
[0.657s] -- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
[0.657s] -- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
[0.657s] -- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
[0.657s] -- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
[0.658s] -- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
[0.658s] -- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[0.711s] [33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
[0.711s]   Please update your CMake from 'hpp-fcl' to 'coal'
[0.712s] Call Stack (most recent call first):
[0.712s]   /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
[0.712s]   /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig.cmake:41 (include)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake:41 (include)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.712s]   /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig.cmake:41 (include)
[0.712s]   CMakeLists.txt:34 (find_package)
[0.712s] 
[0.712s] [0m
[0.712s] -- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
[0.752s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
[0.943s] -- Default C++ standard: 201703
[0.943s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.943s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.962s] -- Found ocs2_self_collision: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake)
[0.963s] -- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)
[0.967s] -- Found angles: 1.15.0 (/opt/ros/humble/share/angles/cmake)
[0.969s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[0.976s] -- Found qpoases_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/qpoases_colcon/share/qpoases_colcon/cmake)
[0.977s] -- Found convex_plane_decomposition_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/share/convex_plane_decomposition_msgs/cmake)
[0.992s] -- Found convex_plane_decomposition_ros: 0.0.0 (/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/share/convex_plane_decomposition_ros/cmake)
[1.067s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: system filesystem 
[1.074s] -- Found grid_map_sdf: 2.2.1 (/home/<USER>/ros2_ws/install/grid_map_sdf/share/grid_map_sdf/cmake)
[1.084s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.145s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[1.416s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.421s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[1.531s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.536s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.541s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[1.654s] -- Found Qhull version 8.0.2
[1.772s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.882s] -- Found ocs2_sphere_approximation: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/share/ocs2_sphere_approximation/cmake)
[1.927s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.970s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.970s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include>;/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src
[1.970s] -- Configured cppcheck exclude dirs and/or files: 
[1.970s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.971s] -- Added test 'lint_cmake' to check CMake code style
[1.971s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.973s] -- Added test 'uncrustify' to check C / C++ code style
[1.973s] -- Configured uncrustify additional arguments: 
[1.973s] -- Added test 'xmllint' to check XML markup files
[1.974s] -- Configuring done
[2.033s] -- Generating done
[2.040s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
[2.109s] [35m[1mConsolidate compiler generated dependencies of target ocs2_quadruped_controller[0m
[2.239s] [  2%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/FSM/StateOCS2.cpp.o[0m
[2.239s] [  5%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/Ocs2QuadrupedController.cpp.o[0m
[2.242s] [  8%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/estimator/GroundTruth.cpp.o[0m
[2.244s] [ 11%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/estimator/LinearKalmanFilter.cpp.o[0m
[2.245s] [ 14%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/estimator/StateEstimateBase.cpp.o[0m
[2.247s] [ 17%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/estimator/FromOdomTopic.cpp.o[0m
[2.247s] [ 20%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/wbc/HierarchicalWbc.cpp.o[0m
[2.248s] [ 23%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/EndEffectorLinearConstraint.cpp.o[0m
[2.248s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/wbc/HoQp.cpp.o[0m
[2.249s] [ 29%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/FrictionConeConstraint.cpp.o[0m
[2.250s] [ 32%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/wbc/WbcBase.cpp.o[0m
[2.251s] [ 35%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/wbc/WeightedWbc.cpp.o[0m
[2.251s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/ZeroForceConstraint.cpp.o[0m
[2.251s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/NormalVelocityConstraintCppAd.cpp.o[0m
[2.256s] [ 44%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/ZeroVelocityConstraintCppAd.cpp.o[0m
[2.257s] [ 47%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/constraint/SwingTrajectoryPlanner.cpp.o[0m
[2.260s] [ 50%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/initialization/LeggedRobotInitializer.cpp.o[0m
[2.262s] [ 52%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/SwitchedModelReferenceManager.cpp.o[0m
[2.266s] [ 55%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/control/GaitManager.cpp.o[0m
[2.266s] [ 58%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedRobotPreComputation.cpp.o[0m
[2.269s] [ 61%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o[0m
[2.273s] [ 64%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/control/TargetManager.cpp.o[0m
[2.277s] [ 67%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/control/CtrlComponent.cpp.o[0m
[2.278s] [ 70%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/constraint/FootCollisionConstraint.cpp.o[0m
[2.278s] [ 73%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/constraint/FootPlacementConstraint.cpp.o[0m
[2.288s] [ 76%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/constraint/SphereSdfConstraint.cpp.o[0m
[2.289s] [ 79%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/interface/ConvexRegionSelector.cpp.o[0m
[2.292s] [ 82%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/interface/PerceptiveLeggedInterface.cpp.o[0m
[2.296s] [ 85%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/interface/PerceptiveLeggedPrecomputation.cpp.o[0m
[2.302s] [ 88%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp.o[0m
[2.306s] [ 91%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/visualize/FootPlacementVisualization.cpp.o[0m
[2.314s] [ 94%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/visualize/SphereVisualization.cpp.o[0m
[6.148s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::SwitchedModelReferenceManager::modifyReferences(ocs2::scalar_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&, ocs2::ModeSchedule&)[m[K’:
[6.152s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:55:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KinitState[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.152s]    55 |                                                          [01;35m[Kconst vector_t &initState[m[K,
[6.152s]       |                                                          [01;35m[K~~~~~~~~~~~~~~~~^~~~~~~~~[m[K
[6.152s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:56:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetTrajectories[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.152s]    56 |                                                          [01;35m[KTargetTrajectories &targetTrajectories[m[K,
[6.152s]       |                                                          [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~[m[K
[6.193s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:30[m[K:
[6.193s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[6.193s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.193s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[6.193s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[6.349s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::EndEffectorLinearConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[6.349s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:67:61:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.349s]    67 |     vector_t EndEffectorLinearConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
[6.350s]       |                                                    [01;35m[K~~~~~~~~~^~~~[m[K
[6.350s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:68:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.350s]    68 |                                                    [01;35m[Kconst PreComputation &preComp[m[K) const {
[6.350s]       |                                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[6.421s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::EndEffectorLinearConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[6.422s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:83:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.423s]    83 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
[6.424s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[6.890s] At global scope:
[6.890s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[6.949s] [ 97%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/perceptive/synchronize/PlanarTerrainReceiver.cpp.o[0m
[7.027s] In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.hpp:1014[m[K,
[7.028s]                  from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:66[m[K,
[7.029s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/HoQp.cpp:9[m[K:
[7.033s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:[m[K In member function ‘[01m[Kvoid qpOASES::QProblemB::applyGivens(qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t&, qpOASES::real_t&) const[m[K’:
[7.033s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:447:68:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Knu[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[7.033s]   447 | inline void QProblemB::applyGivens(     real_t c, real_t s, [01;35m[Kreal_t nu[m[K, real_t xold, real_t yold,
[7.034s]       |                                                             [01;35m[K~~~~~~~^~[m[K
[7.034s] In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.hpp:43[m[K,
[7.034s]                  from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:67[m[K,
[7.034s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/HoQp.cpp:9[m[K:
[7.035s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In copy constructor ‘[01m[KqpOASES::ConstraintProduct::ConstraintProduct(const qpOASES::ConstraintProduct&)[m[K’:
[7.035s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:64:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[7.035s]    64 |                 ConstraintProduct(      [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
[7.036s]       |                                         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[7.036s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In member function ‘[01m[KqpOASES::ConstraintProduct& qpOASES::ConstraintProduct::operator=(const qpOASES::ConstraintProduct&)[m[K’:
[7.036s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:71:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[7.036s]    71 |                 ConstraintProduct &operator=(   [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
[7.041s]       |                                                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[8.005s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:35[m[K,
[8.006s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/NormalVelocityConstraintCppAd.cpp:30[m[K:
[8.006s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[8.006s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.006s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[8.007s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[8.029s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/NormalVelocityConstraintCppAd.cpp:30[m[K:
[8.029s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[8.029s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.030s]    62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
[8.030s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[8.057s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:35[m[K,
[8.057s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroVelocityConstraintCppAd.cpp:30[m[K:
[8.058s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[8.058s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.058s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[8.058s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[8.067s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroVelocityConstraintCppAd.cpp:30[m[K:
[8.068s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[8.068s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.068s]    64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[8.068s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[8.431s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:30[m[K:
[8.431s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[8.432s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.432s]    55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[8.432s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[8.451s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::ZeroForceConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[8.452s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:51:53:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.452s]    51 |     vector_t ZeroForceConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
[8.452s]       |                                            [01;35m[K~~~~~~~~~^~~~[m[K
[8.452s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:51:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.453s]    51 |     vector_t ZeroForceConstraint::getValue(scalar_t time, [01;35m[Kconst vector_t &state[m[K, const vector_t &input,
[8.453s]       |                                                           [01;35m[K~~~~~~~~~~~~~~~~^~~~~[m[K
[8.453s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:52:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.457s]    52 |                                            [01;35m[Kconst PreComputation &preComp[m[K) const {
[8.458s]       |                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[8.458s] At global scope:
[8.458s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[8.931s] In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.hpp:1014[m[K,
[8.931s]                  from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:66[m[K,
[8.931s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WeightedWbc.cpp:7[m[K:
[8.935s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:[m[K In member function ‘[01m[Kvoid qpOASES::QProblemB::applyGivens(qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t&, qpOASES::real_t&) const[m[K’:
[8.936s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:447:68:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Knu[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.936s]   447 | inline void QProblemB::applyGivens(     real_t c, real_t s, [01;35m[Kreal_t nu[m[K, real_t xold, real_t yold,
[8.936s]       |                                                             [01;35m[K~~~~~~~^~[m[K
[8.936s] In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.hpp:43[m[K,
[8.936s]                  from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:67[m[K,
[8.936s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WeightedWbc.cpp:7[m[K:
[8.936s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In copy constructor ‘[01m[KqpOASES::ConstraintProduct::ConstraintProduct(const qpOASES::ConstraintProduct&)[m[K’:
[8.936s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:64:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.936s]    64 |                 ConstraintProduct(      [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
[8.937s]       |                                         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[8.937s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In member function ‘[01m[KqpOASES::ConstraintProduct& qpOASES::ConstraintProduct::operator=(const qpOASES::ConstraintProduct&)[m[K’:
[8.937s] [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:71:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.937s]    71 |                 ConstraintProduct &operator=(   [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
[8.937s]       |                                                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[9.568s] At global scope:
[9.568s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[10.499s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[10.499s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
[10.499s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedRobotPreComputation.cpp:32[m[K:
[10.499s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[10.500s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[10.500s]       |                                                                       [01;36m[K^[m[K
[10.526s] At global scope:
[10.526s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[10.588s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SwingTrajectoryPlanner::update(const ocs2::ModeSchedule&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&)[m[K’:
[10.588s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:76:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[10.588s]    76 |             for (int p = 0; [01;35m[Kp < modeSchedule.modeSequence.size()[m[K; ++p) {
[10.590s]       |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[10.622s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SwingTrajectoryPlanner::update(const ocs2::ModeSchedule&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&)[m[K’:
[10.622s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:104:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[10.622s]   104 |             for (int p = 0; [01;35m[Kp < modeSequence.size()[m[K; ++p) {
[10.623s]       |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~[m[K
[10.632s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[10.633s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[10.633s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedPrecomputation.cpp:5[m[K:
[10.633s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[10.633s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[10.633s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[10.633s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[10.665s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In static member function ‘[01m[Kstatic void ocs2::legged_robot::SwingTrajectoryPlanner::checkThatIndicesAreValid(int, int, int, int, const std::vector<long unsigned int>&)[m[K’:
[10.665s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:216:24:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Klong unsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[10.665s]   216 |         if ([01;35m[KfinalIndex >= numSubsystems - 1[m[K) {
[10.665s]       |             [01;35m[K~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~[m[K
[11.059s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[11.059s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/kinematics.hpp:8[m[K,
[11.060s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:7[m[K:
[11.060s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[11.060s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[11.060s]       |                                                                       [01;36m[K^[m[K
[12.587s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[12.587s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/centroidal.hpp:8[m[K,
[12.588s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WbcBase.cpp:12[m[K:
[12.588s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[12.588s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[12.588s]       |                                                                       [01;36m[K^[m[K
[12.597s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[12.597s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
[12.597s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
[12.597s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h:10[m[K,
[12.597s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:5[m[K:
[12.597s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[12.597s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[12.600s]       |                                                                       [01;36m[K^[m[K
[12.935s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::StateEstimateBase::updateJointStates()[m[K’:
[12.935s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:34:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[12.935s]    34 |         for (int i = 0; [01;35m[Ki < size[m[K; i++)
[12.935s]       |                         [01;35m[K~~^~~~~~[m[K
[12.953s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::StateEstimateBase::updateContact()[m[K’:
[12.954s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:47:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[12.954s]    47 |         for (int i = 0; [01;35m[Ki < size[m[K; i++)
[12.954s]       |                         [01;35m[K~~^~~~~~[m[K
[13.753s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/GroundTruth.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::GroundTruth::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[13.753s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/GroundTruth.cpp:18:84:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[13.753s]    18 |     vector_t GroundTruth::update(const rclcpp::Time& time, [01;35m[Kconst rclcpp::Duration& period[m[K)
[13.753s]       |                                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[13.925s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[13.925s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
[13.925s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
[13.925s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h:10[m[K,
[13.925s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:7[m[K:
[13.925s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[13.925s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[13.925s]       |                                                                       [01;36m[K^[m[K
[14.341s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[14.341s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[14.341s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/FootPlacementConstraint.cpp:6[m[K:
[14.342s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[14.342s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.342s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[14.342s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[14.400s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/ConvexRegionSelector.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::vector3_t ocs2::legged_robot::ConvexRegionSelector::getNominalFoothold(size_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&)[m[K’:
[14.401s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/ConvexRegionSelector.cpp:194:14:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kfeedback[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[14.401s]   194 |         auto [01;35m[Kfeedback[m[K = (vector3_t() << (std::sqrt(height / 9.81) * (measuredVel - desiredVel)).head(2), 0).finished();
[14.401s]       |              [01;35m[K^~~~~~~~[m[K
[14.548s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[14.548s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[14.548s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:6[m[K:
[14.548s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[14.548s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.548s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[14.549s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[14.810s] At global scope:
[14.810s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[14.956s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:30[m[K:
[14.956s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[14.956s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.956s]   100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
[14.956s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[14.963s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::FrictionConeConstraint::setSurfaceNormalInWorld(const vector3_t&)[m[K’:
[14.963s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:46:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KsurfaceNormalInWorld[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.963s]    46 |     void FrictionConeConstraint::setSurfaceNormalInWorld([01;35m[Kconst vector3_t &surfaceNormalInWorld[m[K) {
[14.963s]       |                                                          [01;35m[K~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~[m[K
[15.015s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/initialization/LeggedRobotInitializer.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedRobotInitializer::compute(ocs2::scalar_t, const vector_t&, ocs2::scalar_t, ocs2::vector_t&, ocs2::vector_t&)[m[K’:
[15.015s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/initialization/LeggedRobotInitializer.cpp:49:89:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KnextTime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.015s]    49 |     void LeggedRobotInitializer::compute(scalar_t time, const vector_t &state, [01;35m[Kscalar_t nextTime[m[K, vector_t &input,
[15.015s]       |                                                                                [01;35m[K~~~~~~~~~^~~~~~~~[m[K
[15.030s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::FrictionConeConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[15.030s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:57:56:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.030s]    57 |     vector_t FrictionConeConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
[15.030s]       |                                               [01;35m[K~~~~~~~~~^~~~[m[K
[15.030s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:57:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.030s]    57 |     vector_t FrictionConeConstraint::getValue(scalar_t time, [01;35m[Kconst vector_t &state[m[K, const vector_t &input,
[15.030s]       |                                                              [01;35m[K~~~~~~~~~~~~~~~~^~~~~[m[K
[15.031s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:58:69:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.031s]    58 |                                               [01;35m[Kconst PreComputation &preComp[m[K) const {
[15.031s]       |                                               [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[15.068s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::FrictionConeConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[15.068s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:66:18:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.068s]    66 |         [01;35m[Kscalar_t time[m[K, const vector_t &state,
[15.068s]       |         [01;35m[K~~~~~~~~~^~~~[m[K
[15.068s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:68:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.069s]    68 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
[15.069s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[15.073s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionQuadraticApproximation ocs2::legged_robot::FrictionConeConstraint::getQuadraticApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[15.074s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:85:18:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.074s]    85 |         [01;35m[Kscalar_t time[m[K, const vector_t &state,
[15.074s]       |         [01;35m[K~~~~~~~~~^~~~[m[K
[15.074s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:87:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.074s]    87 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
[15.074s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[15.074s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::FrictionConeConstraint::LocalForceDerivatives ocs2::legged_robot::FrictionConeConstraint::computeLocalForceDerivatives(const vector3_t&) const[m[K’:
[15.074s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:107:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KforcesInWorldFrame[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.074s]   107 |         [01;35m[Kconst vector3_t &forcesInWorldFrame[m[K) const {
[15.074s]       |         [01;35m[K~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~[m[K
[15.171s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[15.171s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedRobotPreComputation.cpp:39[m[K:
[15.171s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[15.171s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.171s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[15.172s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[15.259s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kocs2::matrix_t ocs2::legged_robot::FrictionConeConstraint::frictionConeSecondDerivativeState(size_t, const ocs2::legged_robot::FrictionConeConstraint::ConeDerivatives&) const[m[K’:
[15.259s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:183:95:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KconeDerivatives[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[15.259s]   183 |                                                                        [01;35m[Kconst ConeDerivatives &coneDerivatives[m[K) const {
[15.260s]       |                                                                        [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~[m[K
[15.822s] At global scope:
[15.823s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[16.704s] At global scope:
[16.704s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[16.719s] At global scope:
[16.719s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[16.736s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[16.736s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
[16.736s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
[16.736s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[16.737s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[16.737s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[16.737s]       |                                                                       [01;36m[K^[m[K
[17.275s] At global scope:
[17.275s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[17.317s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SphereVisualization::update(const ocs2::SystemObservation&)[m[K’:
[17.318s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:51:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[17.318s]    51 |             for (int i = 0; [01;35m[Ki < numSpheres.size()[m[K; ++i)
[17.318s]       |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~[m[K
[17.318s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:62:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<long unsigned int>, long unsigned int>::value_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[17.318s]    62 |                 for (int j = 0; j < numSpheres[i]; ++j)
[17.385s] At global scope:
[17.385s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[17.394s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[17.394s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[17.394s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:11[m[K:
[17.394s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[17.394s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[17.394s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[17.394s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[17.464s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::SphereSdfConstraint::getValue(ocs2::scalar_t, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[17.464s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:28:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[17.464s]    28 |         for (int i = 0; [01;35m[Ki < numConstraints_[m[K; ++i)
[17.464s]       |                         [01;35m[K~~^~~~~~~~~~~~~~~~~[m[K
[17.464s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::SphereSdfConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[17.465s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:45:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[17.465s]    45 |         for (int i = 0; [01;35m[Ki < numConstraints_[m[K; ++i)
[17.465s]       |                         [01;35m[K~~^~~~~~~~~~~~~~~~~[m[K
[17.480s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PerceptiveLeggedInterface::setupOptimalControlProblem(const string&, const string&, const string&, bool)[m[K’:
[17.480s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:85:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KthighExcess[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[17.480s]    85 |         scalar_t [01;35m[KthighExcess[m[K = 0.025;
[17.480s]       |                  [01;35m[K^~~~~~~~~~~[m[K
[18.036s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[18.036s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[18.036s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[18.036s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[18.036s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[18.036s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[18.037s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
[18.037s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:5[m[K:
[18.037s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[18.037s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[18.037s]       |                                                                       [01;36m[K^[m[K
[18.523s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[18.523s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[18.523s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
[18.523s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:5[m[K:
[18.523s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.523s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.523s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[18.523s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.573s] At global scope:
[18.573s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[18.816s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[18.817s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:23[m[K:
[18.817s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.817s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.817s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[18.817s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.828s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:24[m[K:
[18.828s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.828s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.829s]   100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
[18.829s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.843s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:26[m[K:
[18.844s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.844s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.844s]    62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
[18.844s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.844s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:27[m[K:
[18.845s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.845s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.845s]    55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[18.845s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.845s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:28[m[K:
[18.845s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[18.845s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[18.845s]    64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[18.846s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[18.847s] At global scope:
[18.847s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[18.873s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:29[m[K:
[18.873s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::LeggedRobotStateQuadraticCost::getStateDeviation(ocs2::scalar_t, const vector_t&, const ocs2::TargetTrajectories&) const[m[K’:
[18.873s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:93:24:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[KcontactFlags[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[18.873s]    93 |             const auto [01;35m[KcontactFlags[m[K = referenceManagerPtr_->getContactFlags(time);
[18.873s]       |                        [01;35m[K^~~~~~~~~~~~[m[K
[18.873s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::FootPlacementVisualization::update(const ocs2::SystemObservation&)[m[K’:
[18.874s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:37:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[18.874s]    37 |             for (int leg = 0; [01;35m[Kleg < num_foot_[m[K; ++leg)
[18.874s]       |                               [01;35m[K~~~~^~~~~~~~~~~[m[K
[18.874s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:42:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<double, std::allocator<double> >::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[18.874s]    42 |                 for (int k = 0; [01;35m[Kk < middleTimes.size()[m[K; ++k)
[18.874s]       |                                 [01;35m[K~~^~~~~~~~~~~~~~~~~~~~[m[K
[19.052s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedInterface::setupReferenceManager(const string&, const string&, const string&, bool)[m[K’:
[19.053s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:198:97:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KurdfFile[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[19.053s]   198 |     void LeggedInterface::setupReferenceManager(const std::string& taskFile, [01;35m[Kconst std::string& urdfFile[m[K,
[19.053s]       |                                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[19.960s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::FromOdomTopic::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[19.961s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:18:56:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[19.961s]    18 |     vector_t FromOdomTopic::update([01;35m[Kconst rclcpp::Time &time[m[K, const rclcpp::Duration &period) {
[19.961s]       |                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~[m[K
[19.961s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:18:86:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[19.961s]    18 |     vector_t FromOdomTopic::update(const rclcpp::Time &time, [01;35m[Kconst rclcpp::Duration &period[m[K) {
[19.961s]       |                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[20.017s] At global scope:
[20.017s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[20.288s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[20.288s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
[20.288s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
[20.288s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
[20.288s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
[20.288s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[20.288s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[20.288s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[20.289s]       |                                                                       [01;36m[K^[m[K
[20.649s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[20.650s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
[20.650s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
[20.650s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
[20.650s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[20.650s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[20.650s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[20.650s]       |                                                                       [01;36m[K^[m[K
[20.788s] At global scope:
[20.788s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[20.931s] At global scope:
[20.931s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[20.982s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:8[m[K:
[20.983s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h:[m[K In constructor ‘[01m[Kocs2::legged_robot::PerceptiveLeggedReferenceManager::PerceptiveLeggedReferenceManager(ocs2::CentroidalModelInfo, std::shared_ptr<ocs2::legged_robot::GaitSchedule>, std::shared_ptr<ocs2::legged_robot::SwingTrajectoryPlanner>, std::shared_ptr<ocs2::legged_robot::ConvexRegionSelector>, const ocs2::EndEffectorKinematics<double>&, ocs2::scalar_t)[m[K’:
[20.983s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h:45:35:[m[K [01;35m[Kwarning: [m[K‘[01m[Kocs2::legged_robot::PerceptiveLeggedReferenceManager::info_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[20.983s]    45 |         const CentroidalModelInfo [01;35m[Kinfo_[m[K;
[20.983s]       |                                   [01;35m[K^~~~~[m[K
[20.983s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:25:31:[m[K [01;35m[Kwarning: [m[K  base ‘[01m[Kocs2::legged_robot::SwitchedModelReferenceManager[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[20.983s]    25 |           comHeight_(comHeight[01;35m[K)[m[K
[20.983s]       |                               [01;35m[K^[m[K
[20.983s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:12:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[20.984s]    12 |     [01;35m[KPerceptiveLeggedReferenceManager[m[K::PerceptiveLeggedReferenceManager(CentroidalModelInfo info,
[20.984s]       |     [01;35m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[20.984s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PerceptiveLeggedReferenceManager::modifyReferences(ocs2::scalar_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&, ocs2::ModeSchedule&)[m[K’:
[20.984s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:39:30:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} and ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[20.984s]    39 |         for (size_t i = 0; [01;35m[Ki < nodeNum[m[K; ++i)
[20.984s]       |                            [01;35m[K~~^~~~~~~~~[m[K
[21.037s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::PerceptiveLeggedReferenceManager::modifyProjections(ocs2::scalar_t, const vector_t&, size_t, size_t, const std::vector<bool>&, std::vector<convex_plane_decomposition::PlanarTerrainProjection>&)[m[K’:
[21.037s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:116:39:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<convex_plane_decomposition::PlanarTerrainProjection>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[21.037s]   116 |             for (int i = initIndex; [01;35m[Ki < projections.size()[m[K; ++i)
[21.037s]       |                                     [01;35m[K~~^~~~~~~~~~~~~~~~~~~~[m[K
[21.039s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::contact_flag_t ocs2::legged_robot::PerceptiveLeggedReferenceManager::getFootPlacementFlags(ocs2::scalar_t) const[m[K’:
[21.039s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:200:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::array<bool, 4>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[21.040s]   200 |         for (int i = 0; [01;35m[Ki < flag.size()[m[K; ++i)
[21.040s]       |                         [01;35m[K~~^~~~~~~~~~~~~[m[K
[21.289s] At global scope:
[21.289s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[21.545s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
[21.545s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[21.546s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[21.546s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[21.546s]       |                                                                       [01;36m[K^[m[K
[23.119s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/synchronize/PlanarTerrainReceiver.cpp:5[m[K:
[23.119s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PlanarTerrainReceiver::postSolverRun(const ocs2::PrimalSolution&)[m[K’:
[23.120s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:32:50:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KprimalSolution[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[23.120s]    32 |         void postSolverRun([01;35m[Kconst PrimalSolution& primalSolution[m[K) override
[23.120s]       |                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~[m[K
[23.138s] At global scope:
[23.138s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[23.462s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:[m[K In constructor ‘[01m[Kocs2::legged_robot::KalmanFilterEstimate::KalmanFilterEstimate(ocs2::PinocchioInterface, ocs2::CentroidalModelInfo, const ocs2::PinocchioEndEffectorKinematics&, CtrlInterfaces&, const SharedPtr&)[m[K’:
[23.462s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:39:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kssize_t[m[K’ {aka ‘[01m[Klong int[m[K’} and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[23.462s]    39 |         for (ssize_t i = 0; [01;35m[Ki < numContacts_[m[K; ++i) {
[23.462s]       |                             [01;35m[K~~^~~~~~~~~~~~~~[m[K
[23.546s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[23.546s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[23.546s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
[23.546s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
[23.546s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[23.546s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[23.547s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[23.547s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[23.547s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[23.575s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In constructor ‘[01m[Kocs2::legged_robot::PlanarTerrainReceiver::PlanarTerrainReceiver(const SharedPtr&, const std::shared_ptr<convex_plane_decomposition::PlanarTerrain>&, const std::shared_ptr<grid_map::SignedDistanceField>&, const string&, const string&)[m[K’:
[23.575s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:48:56:[m[K [01;35m[Kwarning: [m[K‘[01m[Kocs2::legged_robot::PlanarTerrainReceiver::sdfPtr_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[23.575s]    48 |         std::shared_ptr<grid_map::SignedDistanceField> [01;35m[KsdfPtr_[m[K;
[23.575s]       |                                                        [01;35m[K^~~~~~~[m[K
[23.575s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:42:21:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kstd::string ocs2::legged_robot::PlanarTerrainReceiver::sdfElevationLayer_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[23.575s]    42 |         std::string [01;35m[KsdfElevationLayer_[m[K;
[23.575s]       |                     [01;35m[K^~~~~~~~~~~~~~~~~~[m[K
[23.584s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/synchronize/PlanarTerrainReceiver.cpp:13:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[23.584s]    13 |     [01;35m[KPlanarTerrainReceiver[m[K::PlanarTerrainReceiver(const rclcpp_lifecycle::LifecycleNode::SharedPtr& node,
[23.584s]       |     [01;35m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[23.638s] At global scope:
[23.638s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[23.651s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::KalmanFilterEstimate::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[23.651s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:105:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[23.651s]   105 |         for (int i = 0; [01;35m[Ki < numContacts_[m[K; i++) {
[23.651s]       |                         [01;35m[K~~^~~~~~~~~~~~~~[m[K
[23.923s] At global scope:
[23.924s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[24.302s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
[24.303s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[24.303s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[24.303s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[24.303s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[24.303s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[24.364s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:21[m[K:
[24.364s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PlanarTerrainReceiver::postSolverRun(const ocs2::PrimalSolution&)[m[K’:
[24.364s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:32:50:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KprimalSolution[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[24.364s]    32 |         void postSolverRun([01;35m[Kconst PrimalSolution& primalSolution[m[K) override
[24.364s]       |                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~[m[K
[24.716s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[24.717s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
[24.717s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
[24.717s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
[24.717s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
[24.717s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[24.717s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[24.717s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[24.717s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[24.717s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[27.819s] At global scope:
[27.819s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[27.848s] At global scope:
[27.848s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[28.286s] At global scope:
[28.286s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[29.546s] At global scope:
[29.546s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[32.395s] At global scope:
[32.395s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[33.598s] At global scope:
[33.599s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[34.998s] At global scope:
[34.998s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[35.553s] At global scope:
[35.554s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[37.524s] At global scope:
[37.525s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[41.598s] [100%] [32m[1mLinking CXX shared library libocs2_quadruped_controller.so[0m
[42.568s] [100%] Built target ocs2_quadruped_controller
[42.585s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
[42.587s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
[42.598s] -- Install configuration: "Release"
[42.598s] -- Execute custom install script
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//ocs2_quadruped_controller.xml
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/FSM/StateOCS2.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/CtrlComponent.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/GaitManager.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/TargetManager.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/FromOdomTopic.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/GroundTruth.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/LinearKalmanFilter.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/StateEstimateBase.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedInterface.h
[42.599s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/SwitchedModelReferenceManager.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/LeggedSelfCollisionConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/SwingTrajectoryPlanner.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/initialization/LeggedRobotInitializer.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootCollisionConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootPlacementConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/ConvexRegionSelector.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedInterface.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/SphereVisualization.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HierarchicalWbc.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HoQp.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/Task.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WbcBase.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WeightedWbc.h
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/convex_plane_decomposition.yaml
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/elevation_mapping.yaml
[42.600s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/visualize_ocs2.rviz
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/elevation_mapping.launch.py
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/gazebo.launch.py
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/mujoco.launch.py
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.sh
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.dsv
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/package_run_dependencies/ocs2_quadruped_controller
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/parent_prefix_path/ocs2_quadruped_controller
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.sh
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.dsv
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.sh
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.dsv
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.bash
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.sh
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.zsh
[42.601s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.dsv
[42.601s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.dsv
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/packages/ocs2_quadruped_controller
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/ocs2_quadruped_controller
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_targets-extras.cmake
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig.cmake
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig-version.cmake
[42.610s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.xml
[42.611s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib/ocs2_quadruped_controller/libocs2_quadruped_controller.so
[42.619s] -- Set runtime path of "/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib/ocs2_quadruped_controller/libocs2_quadruped_controller.so" to ""
[42.619s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport.cmake
[42.619s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport-release.cmake
[42.620s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_quadruped_controller
