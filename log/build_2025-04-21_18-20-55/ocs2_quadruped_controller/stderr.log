[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake:41 (include)
  /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake/ocs2_centroidal_modelConfig.cmake:41 (include)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake:41 (include)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig.cmake:41 (include)
  CMakeLists.txt:34 (find_package)

[0m
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::SwitchedModelReferenceManager::modifyReferences(ocs2::scalar_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&, ocs2::ModeSchedule&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:55:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KinitState[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   55 |                                                          [01;35m[Kconst vector_t &initState[m[K,
      |                                                          [01;35m[K~~~~~~~~~~~~~~~~^~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/SwitchedModelReferenceManager.cpp:56:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetTrajectories[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   56 |                                                          [01;35m[KTargetTrajectories &targetTrajectories[m[K,
      |                                                          [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::EndEffectorLinearConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:67:61:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   67 |     vector_t EndEffectorLinearConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
      |                                                    [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:68:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   68 |                                                    [01;35m[Kconst PreComputation &preComp[m[K) const {
      |                                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::EndEffectorLinearConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/EndEffectorLinearConstraint.cpp:83:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   83 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
      |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.hpp:1014[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:66[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/HoQp.cpp:9[m[K:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:[m[K In member function ‘[01m[Kvoid qpOASES::QProblemB::applyGivens(qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t&, qpOASES::real_t&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:447:68:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Knu[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  447 | inline void QProblemB::applyGivens(     real_t c, real_t s, [01;35m[Kreal_t nu[m[K, real_t xold, real_t yold,
      |                                                             [01;35m[K~~~~~~~^~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.hpp:43[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:67[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/HoQp.cpp:9[m[K:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In copy constructor ‘[01m[KqpOASES::ConstraintProduct::ConstraintProduct(const qpOASES::ConstraintProduct&)[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:64:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |                 ConstraintProduct(      [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
      |                                         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In member function ‘[01m[KqpOASES::ConstraintProduct& qpOASES::ConstraintProduct::operator=(const qpOASES::ConstraintProduct&)[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:71:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   71 |                 ConstraintProduct &operator=(   [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
      |                                                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:35[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/NormalVelocityConstraintCppAd.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/NormalVelocityConstraintCppAd.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:35[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroVelocityConstraintCppAd.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroVelocityConstraintCppAd.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::ZeroForceConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:51:53:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   51 |     vector_t ZeroForceConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
      |                                            [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:51:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   51 |     vector_t ZeroForceConstraint::getValue(scalar_t time, [01;35m[Kconst vector_t &state[m[K, const vector_t &input,
      |                                                           [01;35m[K~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/ZeroForceConstraint.cpp:52:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   52 |                                            [01;35m[Kconst PreComputation &preComp[m[K) const {
      |                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.hpp:1014[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:66[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WeightedWbc.cpp:7[m[K:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:[m[K In member function ‘[01m[Kvoid qpOASES::QProblemB::applyGivens(qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t, qpOASES::real_t&, qpOASES::real_t&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblemB.ipp:447:68:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Knu[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  447 | inline void QProblemB::applyGivens(     real_t c, real_t s, [01;35m[Kreal_t nu[m[K, real_t xold, real_t yold,
      |                                                             [01;35m[K~~~~~~~^~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/QProblem.hpp:43[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES.hpp:67[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WeightedWbc.cpp:7[m[K:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In copy constructor ‘[01m[KqpOASES::ConstraintProduct::ConstraintProduct(const qpOASES::ConstraintProduct&)[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:64:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |                 ConstraintProduct(      [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
      |                                         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:[m[K In member function ‘[01m[KqpOASES::ConstraintProduct& qpOASES::ConstraintProduct::operator=(const qpOASES::ConstraintProduct&)[m[K’:
[01m[K/home/<USER>/ros2_ws/install/qpoases_colcon/include/qpOASES/ConstraintProduct.hpp:71:74:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtoCopy[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   71 |                 ConstraintProduct &operator=(   [01;35m[Kconst ConstraintProduct &toCopy[m[K /**< Rhs object. */
      |                                                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedRobotPreComputation.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SwingTrajectoryPlanner::update(const ocs2::ModeSchedule&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:76:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   76 |             for (int p = 0; [01;35m[Kp < modeSchedule.modeSequence.size()[m[K; ++p) {
      |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SwingTrajectoryPlanner::update(const ocs2::ModeSchedule&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&, ocs2::legged_robot::feet_array_t<std::vector<double> >&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:104:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  104 |             for (int p = 0; [01;35m[Kp < modeSequence.size()[m[K; ++p) {
      |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedPrecomputation.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:[m[K In static member function ‘[01m[Kstatic void ocs2::legged_robot::SwingTrajectoryPlanner::checkThatIndicesAreValid(int, int, int, int, const std::vector<long unsigned int>&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/SwingTrajectoryPlanner.cpp:216:24:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Klong unsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  216 |         if ([01;35m[KfinalIndex >= numSubsystems - 1[m[K) {
      |             [01;35m[K~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/kinematics.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:7[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/centroidal.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/wbc/WbcBase.cpp:12[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::StateEstimateBase::updateJointStates()[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:34:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   34 |         for (int i = 0; [01;35m[Ki < size[m[K; i++)
      |                         [01;35m[K~~^~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::StateEstimateBase::updateContact()[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/StateEstimateBase.cpp:47:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   47 |         for (int i = 0; [01;35m[Ki < size[m[K; i++)
      |                         [01;35m[K~~^~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/GroundTruth.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::GroundTruth::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/GroundTruth.cpp:18:84:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   18 |     vector_t GroundTruth::update(const rclcpp::Time& time, [01;35m[Kconst rclcpp::Duration& period[m[K)
      |                                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/include/ocs2_sphere_approximation/ocs2_sphere_approximation/PinocchioSphereKinematics.h:38[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:7[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/FootPlacementConstraint.cpp:6[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/ConvexRegionSelector.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::vector3_t ocs2::legged_robot::ConvexRegionSelector::getNominalFoothold(size_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/ConvexRegionSelector.cpp:194:14:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kfeedback[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  194 |         auto [01;35m[Kfeedback[m[K = (vector3_t() << (std::sqrt(height / 9.81) * (measuredVel - desiredVel)).head(2), 0).finished();
      |              [01;35m[K^~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:6[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:30[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::FrictionConeConstraint::setSurfaceNormalInWorld(const vector3_t&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:46:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KsurfaceNormalInWorld[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   46 |     void FrictionConeConstraint::setSurfaceNormalInWorld([01;35m[Kconst vector3_t &surfaceNormalInWorld[m[K) {
      |                                                          [01;35m[K~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/initialization/LeggedRobotInitializer.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedRobotInitializer::compute(ocs2::scalar_t, const vector_t&, ocs2::scalar_t, ocs2::vector_t&, ocs2::vector_t&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/initialization/LeggedRobotInitializer.cpp:49:89:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KnextTime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   49 |     void LeggedRobotInitializer::compute(scalar_t time, const vector_t &state, [01;35m[Kscalar_t nextTime[m[K, vector_t &input,
      |                                                                                [01;35m[K~~~~~~~~~^~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::FrictionConeConstraint::getValue(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:57:56:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   57 |     vector_t FrictionConeConstraint::getValue([01;35m[Kscalar_t time[m[K, const vector_t &state, const vector_t &input,
      |                                               [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:57:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   57 |     vector_t FrictionConeConstraint::getValue(scalar_t time, [01;35m[Kconst vector_t &state[m[K, const vector_t &input,
      |                                                              [01;35m[K~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:58:69:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   58 |                                               [01;35m[Kconst PreComputation &preComp[m[K) const {
      |                                               [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::FrictionConeConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:66:18:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   66 |         [01;35m[Kscalar_t time[m[K, const vector_t &state,
      |         [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:68:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   68 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
      |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionQuadraticApproximation ocs2::legged_robot::FrictionConeConstraint::getQuadraticApproximation(ocs2::scalar_t, const vector_t&, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:85:18:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   85 |         [01;35m[Kscalar_t time[m[K, const vector_t &state,
      |         [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:87:31:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KpreComp[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   87 |         [01;35m[Kconst PreComputation &preComp[m[K) const {
      |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::FrictionConeConstraint::LocalForceDerivatives ocs2::legged_robot::FrictionConeConstraint::computeLocalForceDerivatives(const vector3_t&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:107:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KforcesInWorldFrame[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  107 |         [01;35m[Kconst vector3_t &forcesInWorldFrame[m[K) const {
      |         [01;35m[K~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedRobotPreComputation.cpp:39[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:[m[K In member function ‘[01m[Kocs2::matrix_t ocs2::legged_robot::FrictionConeConstraint::frictionConeSecondDerivativeState(size_t, const ocs2::legged_robot::FrictionConeConstraint::ConeDerivatives&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/constraint/FrictionConeConstraint.cpp:183:95:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KconeDerivatives[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  183 |                                                                        [01;35m[Kconst ConeDerivatives &coneDerivatives[m[K) const {
      |                                                                        [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::SphereVisualization::update(const ocs2::SystemObservation&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:51:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<long unsigned int>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   51 |             for (int i = 0; [01;35m[Ki < numSpheres.size()[m[K; ++i)
      |                             [01;35m[K~~^~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/SphereVisualization.cpp:62:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<long unsigned int>, long unsigned int>::value_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   62 |                 for (int j = 0; j < numSpheres[i]; ++j)
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:11[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::SphereSdfConstraint::getValue(ocs2::scalar_t, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:28:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   28 |         for (int i = 0; [01;35m[Ki < numConstraints_[m[K; ++i)
      |                         [01;35m[K~~^~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::VectorFunctionLinearApproximation ocs2::legged_robot::SphereSdfConstraint::getLinearApproximation(ocs2::scalar_t, const vector_t&, const ocs2::PreComputation&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/constraint/SphereSdfConstraint.cpp:45:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kconst size_t[m[K’ {aka ‘[01m[Kconst long unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   45 |         for (int i = 0; [01;35m[Ki < numConstraints_[m[K; ++i)
      |                         [01;35m[K~~^~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PerceptiveLeggedInterface::setupOptimalControlProblem(const string&, const string&, const string&, bool)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedInterface.cpp:85:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KthighExcess[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   85 |         scalar_t [01;35m[KthighExcess[m[K = 0.025;
      |                  [01;35m[K^~~~~~~~~~~[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:23[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:24[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:26[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:27[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:28[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:29[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::LeggedRobotStateQuadraticCost::getStateDeviation(ocs2::scalar_t, const vector_t&, const ocs2::TargetTrajectories&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:93:24:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[KcontactFlags[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
   93 |             const auto [01;35m[KcontactFlags[m[K = referenceManagerPtr_->getContactFlags(time);
      |                        [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::FootPlacementVisualization::update(const ocs2::SystemObservation&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:37:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   37 |             for (int leg = 0; [01;35m[Kleg < num_foot_[m[K; ++leg)
      |                               [01;35m[K~~~~^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/visualize/FootPlacementVisualization.cpp:42:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<double, std::allocator<double> >::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   42 |                 for (int k = 0; [01;35m[Kk < middleTimes.size()[m[K; ++k)
      |                                 [01;35m[K~~^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedInterface::setupReferenceManager(const string&, const string&, const string&, bool)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:198:97:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KurdfFile[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  198 |     void LeggedInterface::setupReferenceManager(const std::string& taskFile, [01;35m[Kconst std::string& urdfFile[m[K,
      |                                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::FromOdomTopic::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:18:56:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   18 |     vector_t FromOdomTopic::update([01;35m[Kconst rclcpp::Time &time[m[K, const rclcpp::Duration &period) {
      |                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/FromOdomTopic.cpp:18:86:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   18 |     vector_t FromOdomTopic::update(const rclcpp::Time &time, [01;35m[Kconst rclcpp::Duration &period[m[K) {
      |                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:8[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h:[m[K In constructor ‘[01m[Kocs2::legged_robot::PerceptiveLeggedReferenceManager::PerceptiveLeggedReferenceManager(ocs2::CentroidalModelInfo, std::shared_ptr<ocs2::legged_robot::GaitSchedule>, std::shared_ptr<ocs2::legged_robot::SwingTrajectoryPlanner>, std::shared_ptr<ocs2::legged_robot::ConvexRegionSelector>, const ocs2::EndEffectorKinematics<double>&, ocs2::scalar_t)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h:45:35:[m[K [01;35m[Kwarning: [m[K‘[01m[Kocs2::legged_robot::PerceptiveLeggedReferenceManager::info_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   45 |         const CentroidalModelInfo [01;35m[Kinfo_[m[K;
      |                                   [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:25:31:[m[K [01;35m[Kwarning: [m[K  base ‘[01m[Kocs2::legged_robot::SwitchedModelReferenceManager[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   25 |           comHeight_(comHeight[01;35m[K)[m[K
      |                               [01;35m[K^[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:12:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   12 |     [01;35m[KPerceptiveLeggedReferenceManager[m[K::PerceptiveLeggedReferenceManager(CentroidalModelInfo info,
      |     [01;35m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PerceptiveLeggedReferenceManager::modifyReferences(ocs2::scalar_t, ocs2::scalar_t, const vector_t&, ocs2::TargetTrajectories&, ocs2::ModeSchedule&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:39:30:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} and ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   39 |         for (size_t i = 0; [01;35m[Ki < nodeNum[m[K; ++i)
      |                            [01;35m[K~~^~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kvoid ocs2::legged_robot::PerceptiveLeggedReferenceManager::modifyProjections(ocs2::scalar_t, const vector_t&, size_t, size_t, const std::vector<bool>&, std::vector<convex_plane_decomposition::PlanarTerrainProjection>&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:116:39:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<convex_plane_decomposition::PlanarTerrainProjection>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  116 |             for (int i = initIndex; [01;35m[Ki < projections.size()[m[K; ++i)
      |                                     [01;35m[K~~^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:[m[K In member function ‘[01m[Kocs2::legged_robot::contact_flag_t ocs2::legged_robot::PerceptiveLeggedReferenceManager::getFootPlacementFlags(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/interface/PerceptiveLeggedReferenceManager.cpp:200:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::array<bool, 4>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  200 |         for (int i = 0; [01;35m[Ki < flag.size()[m[K; ++i)
      |                         [01;35m[K~~^~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/synchronize/PlanarTerrainReceiver.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PlanarTerrainReceiver::postSolverRun(const ocs2::PrimalSolution&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:32:50:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KprimalSolution[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   32 |         void postSolverRun([01;35m[Kconst PrimalSolution& primalSolution[m[K) override
      |                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:[m[K In constructor ‘[01m[Kocs2::legged_robot::KalmanFilterEstimate::KalmanFilterEstimate(ocs2::PinocchioInterface, ocs2::CentroidalModelInfo, const ocs2::PinocchioEndEffectorKinematics&, CtrlInterfaces&, const SharedPtr&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:39:31:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kssize_t[m[K’ {aka ‘[01m[Klong int[m[K’} and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
   39 |         for (ssize_t i = 0; [01;35m[Ki < numContacts_[m[K; ++i) {
      |                             [01;35m[K~~^~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In constructor ‘[01m[Kocs2::legged_robot::PlanarTerrainReceiver::PlanarTerrainReceiver(const SharedPtr&, const std::shared_ptr<convex_plane_decomposition::PlanarTerrain>&, const std::shared_ptr<grid_map::SignedDistanceField>&, const string&, const string&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:48:56:[m[K [01;35m[Kwarning: [m[K‘[01m[Kocs2::legged_robot::PlanarTerrainReceiver::sdfPtr_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   48 |         std::shared_ptr<grid_map::SignedDistanceField> [01;35m[KsdfPtr_[m[K;
      |                                                        [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:42:21:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kstd::string ocs2::legged_robot::PlanarTerrainReceiver::sdfElevationLayer_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   42 |         std::string [01;35m[KsdfElevationLayer_[m[K;
      |                     [01;35m[K^~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/perceptive/synchronize/PlanarTerrainReceiver.cpp:13:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   13 |     [01;35m[KPlanarTerrainReceiver[m[K::PlanarTerrainReceiver(const rclcpp_lifecycle::LifecycleNode::SharedPtr& node,
      |     [01;35m[K^~~~~~~~~~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::KalmanFilterEstimate::update(const rclcpp::Time&, const rclcpp::Duration&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/estimator/LinearKalmanFilter.cpp:105:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  105 |         for (int i = 0; [01;35m[Ki < numContacts_[m[K; i++) {
      |                         [01;35m[K~~^~~~~~~~~~~~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/Ocs2QuadrupedController.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/control/CtrlComponent.cpp:21[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::PlanarTerrainReceiver::postSolverRun(const ocs2::PrimalSolution&)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h:32:50:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KprimalSolution[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   32 |         void postSolverRun([01;35m[Kconst PrimalSolution& primalSolution[m[K) override
      |                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h:7[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/control/CtrlComponent.h:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/FSM/StateOCS2.h:11[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/FSM/StateOCS2.cpp:5[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
