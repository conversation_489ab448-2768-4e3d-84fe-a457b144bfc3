-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
-- Found blasfeo_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake)
-- Found hpipm_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/hpipm_colcon/share/hpipm_colcon/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_ipm
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_ipm[0m
[ 11%] Built target gtest_main
[ 23%] Built target gtest
[ 29%] [32mBuilding CXX object CMakeFiles/ocs2_ipm.dir/src/IpmSettings.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/ocs2_ipm.dir/src/IpmInitialization.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_ipm.dir/src/IpmPerformanceIndexComputation.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/ocs2_ipm.dir/src/IpmHelpers.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/ocs2_ipm.dir/src/IpmSolver.cpp.o[0m
[ 58%] [32m[1mLinking CXX static library libocs2_ipm.a[0m
[ 58%] Built target ocs2_ipm
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_ipm[0m
[ 64%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/Exp0Test.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/Exp1Test.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/testCircularKinematics.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/testSwitchedProblem.cpp.o[0m
[ 88%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/testUnconstrained.cpp.o[0m
[ 94%] [32mBuilding CXX object CMakeFiles/test_ocs2_ipm.dir/test/testValuefunction.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_ocs2_ipm[0m
[100%] Built target test_ocs2_ipm
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmInitialization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmMpc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmPerformanceIndexComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSolver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSolverStatus.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/package_run_dependencies/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/parent_prefix_path/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/packages/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ocs2_ipmConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ocs2_ipmConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_ipm/lib/libocs2_ipm.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/export_ocs2_ipmExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/export_ocs2_ipmExport-release.cmake
