-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found blasfeo_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake)
-- Found hpipm_colcon: 0.0.0 (/home/<USER>/ros2_ws/install/hpipm_colcon/share/hpipm_colcon/cmake)
-- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_sqp
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_sqp[0m
[ 15%] Built target gtest
[ 30%] Built target gtest_main
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_sqp.dir/src/SqpLogging.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/ocs2_sqp.dir/src/SqpSolver.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/ocs2_sqp.dir/src/SqpSettings.cpp.o[0m
[ 61%] [32m[1mLinking CXX static library libocs2_sqp.a[0m
[ 61%] Built target ocs2_sqp
[35m[1mConsolidate compiler generated dependencies of target test_ocs2_sqp[0m
[ 69%] [32mBuilding CXX object CMakeFiles/test_ocs2_sqp.dir/test/testCircularKinematics.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/test_ocs2_sqp.dir/test/testSwitchedProblem.cpp.o[0m
[ 84%] [32mBuilding CXX object CMakeFiles/test_ocs2_sqp.dir/test/testUnconstrained.cpp.o[0m
[ 92%] [32mBuilding CXX object CMakeFiles/test_ocs2_sqp.dir/test/testValuefunction.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_ocs2_sqp[0m
[100%] Built target test_ocs2_sqp
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/include/ocs2_sqp/ocs2_sqp/SqpLogging.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/include/ocs2_sqp/ocs2_sqp/SqpMpc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/include/ocs2_sqp/ocs2_sqp/SqpSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/include/ocs2_sqp/ocs2_sqp/SqpSolver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/include/ocs2_sqp/ocs2_sqp/SqpSolverStatus.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ament_index/resource_index/package_run_dependencies/ocs2_sqp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ament_index/resource_index/parent_prefix_path/ocs2_sqp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ament_index/resource_index/packages/ocs2_sqp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/ocs2_sqpConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/ocs2_sqpConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_sqp/lib/libocs2_sqp.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/export_ocs2_sqpExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake/export_ocs2_sqpExport-release.cmake
