[0.039s] Invoking command in '/home/<USER>/ros2_ws/build/blasfeo_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/blasfeo_colcon -- -j32 -l32
[0.066s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.218s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.413s] [0mCMake Deprecation Warning at /home/<USER>/ros2_ws/build/blasfeo_colcon/download/CMakeLists.txt:36 (cmake_minimum_required):
[0.414s]   Compatibility with CMake < 2.8.12 will be removed from a future version of
[0.414s]   CMake.
[0.414s] 
[0.414s]   Update the VERSION argument <min> value or use a ...<max> suffix to tell
[0.414s]   CMake that the project does not need compatibility with older versions.
[0.414s] 
[0.415s] [0m
[0.418s] -- Using linear algebra: HIGH_PERFORMANCE
[0.419s] -- Using matrix format: PANELMAJ
[0.419s] -- Using external BLAS: 0
[0.529s] -- Testing target X64_INTEL_HASWELL: assembly compilation [success]
[0.529s] -- Testing target X64_INTEL_HASWELL: assembly run [success]
[0.824s] -- Testing target X64_INTEL_HASWELL: intrinsic compilation [success]
[0.824s] -- Testing target X64_INTEL_HASWELL: intrinsic run [success]
[0.824s] -- Detected target X64_INTEL_HASWELL
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_block_size.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_common.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux_ext_dep.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux_ext_dep_ref.h
[0.830s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux_old.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux_ref.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_aux_test.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_blas.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_blas_api.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_blasfeo_api.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_blasfeo_api_ref.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_blasfeo_ref_api.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_d_kernel.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_i_aux_ext_dep.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_m_aux.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_memory.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_naming.h
[0.831s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_processor_features.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux_ext_dep.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux_ext_dep_ref.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux_old.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux_ref.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_aux_test.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_blas.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_blas_api.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_blasfeo_api.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_blasfeo_api_ref.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_blasfeo_ref_api.h
[0.832s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_s_kernel.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_stdlib.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_target.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_timing.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/blasfeo_v_aux_ext_dep.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/d_blas.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/d_blas_64.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/s_blas.h
[0.833s] -- FOUND HEADER: /home/<USER>/ros2_ws/build/blasfeo_colcon/download/include/s_blas_64.h
[0.892s] -- Configuring done
[0.903s] -- Generating done
[0.906s] -- Build files have been written to: /home/<USER>/ros2_ws/build/blasfeo_colcon
[0.945s] [35m[1mScanning dependencies of target blasfeo[0m
[0.953s] [35m[1mConsolidate compiler generated dependencies of target blasfeo[0m
[1.003s] [100%] Built target blasfeo
[1.021s] Invoked command in '/home/<USER>/ros2_ws/build/blasfeo_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/blasfeo_colcon -- -j32 -l32
[1.024s] Invoking command in '/home/<USER>/ros2_ws/build/blasfeo_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/blasfeo_colcon
[1.038s] -- Install configuration: "Release"
[1.039s] -- Execute custom install script
[1.039s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo.h
[1.039s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_block_size.h
[1.039s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_common.h
[1.039s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep_ref.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_old.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ref.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_test.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas_api.h
[1.040s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api_ref.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_ref_api.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_kernel.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_i_aux_ext_dep.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_m_aux.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_memory.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_naming.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_processor_features.h
[1.041s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep_ref.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_old.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ref.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_test.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas_api.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api.h
[1.042s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api_ref.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_ref_api.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_kernel.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_stdlib.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_target.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_timing.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_v_aux_ext_dep.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas_64.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas.h
[1.043s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas_64.h
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/package_run_dependencies/blasfeo_colcon
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/parent_prefix_path/blasfeo_colcon
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.sh
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.dsv
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.sh
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.dsv
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.bash
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.sh
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.zsh
[1.044s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.dsv
[1.045s] -- Symlinking: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.dsv
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/packages/blasfeo_colcon
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo-extras.cmake
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_include_directories-extras.cmake
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_dependencies-extras.cmake
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig.cmake
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig-version.cmake
[1.060s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.xml
[1.061s] -- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig.cmake
[1.061s] -- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig-release.cmake
[1.065s] Invoked command in '/home/<USER>/ros2_ws/build/blasfeo_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/blasfeo_colcon
