[0.043s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_pinocchio_interface': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface -- -j32 -l32
[0.078s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.247s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.255s] -- Found ocs2_robotic_tools: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake)
[0.276s] -- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
[0.300s] -- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
[0.301s] -- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
[0.302s] -- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
[0.312s] -- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
[0.313s] -- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
[0.315s] -- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
[0.330s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: filesystem serialization system 
[0.331s] [33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
[0.331s]   Please update your CMake from 'hpp-fcl' to 'coal'
[0.332s] Call Stack (most recent call first):
[0.332s]   /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
[0.332s]   /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
[0.332s]   CMakeLists.txt:17 (find_package)
[0.332s] 
[0.332s] [0m
[0.334s] -- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
[0.347s] -- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
[0.909s] -- Default C++ standard: 201703
[0.910s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.921s] -- C++ standard sufficient: Minimal required 11, currently defined: 17
[0.968s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.048s] -- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.049s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.071s] -- Configuring done
[1.097s] -- Generating done
[1.105s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface
[1.151s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[1.155s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[1.167s] [35m[1mConsolidate compiler generated dependencies of target ocs2_pinocchio_interface[0m
[1.184s] [ 15%] Built target gtest
[1.184s] [ 30%] Built target gtest_main
[1.250s] [ 38%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioInterface.cpp.o[0m
[1.250s] [ 46%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioInterfaceCppAd.cpp.o[0m
[1.251s] [ 53%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioEndEffectorKinematicsCppAd.cpp.o[0m
[1.253s] [ 61%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/urdf.cpp.o[0m
[1.254s] [ 69%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioEndEffectorKinematics.cpp.o[0m
[4.846s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[4.846s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[4.846s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[4.846s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[4.846s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[4.846s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[4.847s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:31[m[K:
[4.847s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[4.847s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[4.847s]       |                                                                       [01;36m[K^[m[K
[5.128s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[5.129s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
[5.129s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematics.cpp:32[m[K:
[5.129s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[5.129s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[5.129s]       |                                                                       [01;36m[K^[m[K
[5.982s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[5.983s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[5.983s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[5.983s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[5.983s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[5.983s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[5.983s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
[5.983s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[5.983s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[5.983s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[5.983s]       |                                                                       [01;36m[K^[m[K
[5.997s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[5.997s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[5.997s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[5.997s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[5.998s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[5.998s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[5.998s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
[5.998s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematicsCppAd.cpp:36[m[K:
[5.998s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[5.998s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[5.998s]       |                                                                       [01;36m[K^[m[K
[7.204s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[7.204s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[7.204s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[7.204s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[7.204s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[7.204s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
[7.205s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
[7.205s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterfaceCppAd.cpp:31[m[K:
[7.205s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[7.205s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[7.205s]       |                                                                       [01;36m[K^[m[K
[8.074s] In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
[8.074s]                  from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
[8.074s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
[8.074s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
[8.074s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
[8.075s]                  from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
[8.075s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
[8.075s] /opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::DistanceRequest; _Args = {const coal::DistanceRequest&}][m[K’:
[8.075s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; bool _TrivialValueTypes = false][m[K’
[8.075s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*][m[K’
[8.075s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; _Tp = coal::DistanceRequest][m[K’
[8.075s] [01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::DistanceRequest; _Alloc = std::allocator<coal::DistanceRequest>][m[K’
[8.075s] [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:128:5:[m[K   required from here
[8.075s] [01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
[8.075s]   985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
[8.075s]       |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[8.076s] [01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
[8.076s]   996 |   bool [01;36m[Kenable_nearest_points[m[K;
[8.076s]       |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[8.076s] [01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
[8.076s]   985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
[8.076s]       |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[8.076s] [01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
[8.076s]   996 |   bool [01;36m[Kenable_nearest_points[m[K;
[8.076s]       |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[8.076s] In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
[8.076s]                  from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
[8.076s]                  from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/string:55[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/ios:42[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/istream:38[m[K,
[8.077s]                  from [01m[K/usr/include/c++/11/sstream:38[m[K,
[8.077s]                  from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
[8.077s]                  from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
[8.077s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[8.077s] [01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::DistanceRequest::DistanceRequest(const coal::DistanceRequest&)[m[K’ first required here
[8.077s]   119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
[8.077s]       |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[8.078s] In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
[8.078s]                  from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
[8.078s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
[8.078s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
[8.078s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
[8.078s]                  from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
[8.078s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
[8.078s] /opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::CollisionRequest; _Args = {const coal::CollisionRequest&}][m[K’:
[8.078s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; bool _TrivialValueTypes = false][m[K’
[8.078s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*][m[K’
[8.078s] [01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; _Tp = coal::CollisionRequest][m[K’
[8.078s] [01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::CollisionRequest; _Alloc = std::allocator<coal::CollisionRequest>][m[K’
[8.079s] [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:130:5:[m[K   required from here
[8.079s] [01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
[8.079s]   311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
[8.079s]       |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[8.079s] [01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
[8.079s]   321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
[8.079s]       |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[8.079s] [01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
[8.079s]   311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
[8.079s]       |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[8.079s] [01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
[8.079s]   321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
[8.080s]       |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[8.080s] In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/string:55[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/ios:42[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/istream:38[m[K,
[8.080s]                  from [01m[K/usr/include/c++/11/sstream:38[m[K,
[8.080s]                  from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
[8.080s]                  from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
[8.081s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[8.081s] [01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::CollisionRequest::CollisionRequest(const coal::CollisionRequest&)[m[K’ first required here
[8.081s]   119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
[8.081s]       |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[12.384s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[12.766s] In file included from [01m[K/opt/openrobots/include/pinocchio/autodiff/cppad.hpp:228[m[K,
[12.766s]                  from [01m[K/opt/openrobots/include/pinocchio/codegen/cppadcg.hpp:16[m[K,
[12.766s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:31[m[K,
[12.766s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[12.766s] [01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:[m[K In static member function ‘[01m[Kstatic pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::ReturnType pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::run(pinocchio::internal::ComparisonOperators, const CppAD::AD<Base>&, const CppAD::AD<Base>&, const ThenType&, const ElseType&) [with Scalar = CppAD::cg::CG<double>; ThenType = CppAD::AD<CppAD::cg::CG<double> >; ElseType = CppAD::AD<CppAD::cg::CG<double> >][m[K’:
[12.766s] [01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:39:7:[m[K [01;35m[Kwarning: [m[Kcontrol reaches end of non-void function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreturn-type-Wreturn-type]8;;[m[K]
[12.766s]    39 |       [01;35m[K}[m[K
[12.766s]       |       [01;35m[K^[m[K
[22.747s] At global scope:
[22.748s] [01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[26.596s] [ 76%] [32m[1mLinking CXX shared library libocs2_pinocchio_interface.so[0m
[26.777s] [ 76%] Built target ocs2_pinocchio_interface
[26.798s] [35m[1mConsolidate compiler generated dependencies of target testPinocchioInterface[0m
[26.831s] [ 84%] [32mBuilding CXX object CMakeFiles/testPinocchioInterface.dir/test/testPinocchioInterface.cpp.o[0m
[26.834s] [ 92%] [32mBuilding CXX object CMakeFiles/testPinocchioInterface.dir/test/testPinocchioEndEffectorKinematics.cpp.o[0m
[28.855s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
[28.855s]                  from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
[28.855s]                  from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/test/testPinocchioEndEffectorKinematics.cpp:31[m[K:
[28.855s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[28.855s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[28.855s]       |                                                                       [01;36m[K^[m[K
[39.966s] [100%] [32m[1mLinking CXX executable testPinocchioInterface[0m
[40.081s] [100%] Built target testPinocchioInterface
[40.089s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_pinocchio_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface -- -j32 -l32
[40.090s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_pinocchio_interface': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface
[40.100s] -- Install configuration: "Release"
[40.101s] -- Execute custom install script
[40.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematics.h
[40.101s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematicsCppAd.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioInterface.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioStateInputMapping.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/implementation/PinocchioInterface.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/pinocchio_forward_declaration.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/urdf.h
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.sh
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.dsv
[40.102s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_pinocchio_interface
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_pinocchio_interface
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.sh
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.dsv
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.sh
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.dsv
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.bash
[40.103s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.sh
[40.104s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.zsh
[40.104s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.dsv
[40.104s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.dsv
[40.110s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/packages/ocs2_pinocchio_interface
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/pinocchio_config.cmake
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_targets-extras.cmake
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig-version.cmake
[40.111s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.xml
[40.111s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib/libocs2_pinocchio_interface.so
[40.115s] -- Set runtime path of "/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib/libocs2_pinocchio_interface.so" to ""
[40.116s] -- Old export file "/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport-release.cmake].
[40.116s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport.cmake
[40.116s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport-release.cmake
[40.117s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_pinocchio_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface
