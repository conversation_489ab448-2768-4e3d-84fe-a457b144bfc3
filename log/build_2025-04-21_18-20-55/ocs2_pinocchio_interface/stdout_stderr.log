-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_robotic_tools: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_robotic_tools/share/ocs2_robotic_tools/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: filesystem serialization system 
[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  CMakeLists.txt:17 (find_package)

[0m
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_pinocchio_interface
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_pinocchio_interface[0m
[ 15%] Built target gtest
[ 30%] Built target gtest_main
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioInterface.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioInterfaceCppAd.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioEndEffectorKinematicsCppAd.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/urdf.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/ocs2_pinocchio_interface.dir/src/PinocchioEndEffectorKinematics.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematics.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematicsCppAd.cpp:36[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterfaceCppAd.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
                 from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
/opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::DistanceRequest; _Args = {const coal::DistanceRequest&}][m[K’:
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; bool _TrivialValueTypes = false][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; _Tp = coal::DistanceRequest][m[K’
[01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::DistanceRequest; _Alloc = std::allocator<coal::DistanceRequest>][m[K’
[01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:128:5:[m[K   required from here
[01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
  996 |   bool [01;36m[Kenable_nearest_points[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
  996 |   bool [01;36m[Kenable_nearest_points[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
                 from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
                 from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
                 from [01m[K/usr/include/c++/11/string:55[m[K,
                 from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
                 from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
                 from [01m[K/usr/include/c++/11/ios:42[m[K,
                 from [01m[K/usr/include/c++/11/istream:38[m[K,
                 from [01m[K/usr/include/c++/11/sstream:38[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::DistanceRequest::DistanceRequest(const coal::DistanceRequest&)[m[K’ first required here
  119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
      |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
                 from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
/opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::CollisionRequest; _Args = {const coal::CollisionRequest&}][m[K’:
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; bool _TrivialValueTypes = false][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; _Tp = coal::CollisionRequest][m[K’
[01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::CollisionRequest; _Alloc = std::allocator<coal::CollisionRequest>][m[K’
[01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:130:5:[m[K   required from here
[01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
  321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
  321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
                 from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
                 from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
                 from [01m[K/usr/include/c++/11/string:55[m[K,
                 from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
                 from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
                 from [01m[K/usr/include/c++/11/ios:42[m[K,
                 from [01m[K/usr/include/c++/11/istream:38[m[K,
                 from [01m[K/usr/include/c++/11/sstream:38[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::CollisionRequest::CollisionRequest(const coal::CollisionRequest&)[m[K’ first required here
  119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
      |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/pinocchio/autodiff/cppad.hpp:228[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/codegen/cppadcg.hpp:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:31[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:[m[K In static member function ‘[01m[Kstatic pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::ReturnType pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::run(pinocchio::internal::ComparisonOperators, const CppAD::AD<Base>&, const CppAD::AD<Base>&, const ThenType&, const ElseType&) [with Scalar = CppAD::cg::CG<double>; ThenType = CppAD::AD<CppAD::cg::CG<double> >; ElseType = CppAD::AD<CppAD::cg::CG<double> >][m[K’:
[01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:39:7:[m[K [01;35m[Kwarning: [m[Kcontrol reaches end of non-void function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreturn-type-Wreturn-type]8;;[m[K]
   39 |       [01;35m[K}[m[K
      |       [01;35m[K^[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
[ 76%] [32m[1mLinking CXX shared library libocs2_pinocchio_interface.so[0m
[ 76%] Built target ocs2_pinocchio_interface
[35m[1mConsolidate compiler generated dependencies of target testPinocchioInterface[0m
[ 84%] [32mBuilding CXX object CMakeFiles/testPinocchioInterface.dir/test/testPinocchioInterface.cpp.o[0m
[ 92%] [32mBuilding CXX object CMakeFiles/testPinocchioInterface.dir/test/testPinocchioEndEffectorKinematics.cpp.o[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/test/testPinocchioEndEffectorKinematics.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
[100%] [32m[1mLinking CXX executable testPinocchioInterface[0m
[100%] Built target testPinocchioInterface
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematicsCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioStateInputMapping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/implementation/PinocchioInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/pinocchio_forward_declaration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/urdf.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/packages/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/pinocchio_config.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib/libocs2_pinocchio_interface.so
-- Set runtime path of "/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib/libocs2_pinocchio_interface.so" to ""
-- Old export file "/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport-release.cmake].
-- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport.cmake
-- Installing: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport-release.cmake
