[33mCMake Warning at /opt/openrobots/lib/cmake/hpp-fcl/hpp-fclConfig.cmake:3 (message):
  Please update your CMake from 'hpp-fcl' to 'coal'
Call Stack (most recent call first):
  /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake:47 (find_package)
  /opt/openrobots/lib/cmake/pinocchio/pinocchioConfig.cmake:161 (find_dependency)
  CMakeLists.txt:17 (find_package)

[0m
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematics.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioEndEffectorKinematicsCppAd.cpp:36[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/data.hpp:17[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:34[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterfaceCppAd.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
                 from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
/opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::DistanceRequest; _Args = {const coal::DistanceRequest&}][m[K’:
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; bool _TrivialValueTypes = false][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::DistanceRequest*, std::vector<coal::DistanceRequest> >; _ForwardIterator = coal::DistanceRequest*; _Tp = coal::DistanceRequest][m[K’
[01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::DistanceRequest; _Alloc = std::allocator<coal::DistanceRequest>][m[K’
[01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:128:5:[m[K   required from here
[01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
  996 |   bool [01;36m[Kenable_nearest_points[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:985:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::DistanceRequest::enable_nearest_points[m[K’ is deprecated: Nearest points are always computed : they are the points of the shapes that achieve a distance of `DistanceResult::min_distance` .\n Use `enable_signed_distance` if you want to compute a signed minimum distance(and thus its corresponding nearest points) . [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  985 | struct COAL_DLLAPI [01;35m[KDistanceRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:996:8:[m[K [01;36m[Knote: [m[Kdeclared here
  996 |   bool [01;36m[Kenable_nearest_points[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
                 from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
                 from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
                 from [01m[K/usr/include/c++/11/string:55[m[K,
                 from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
                 from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
                 from [01m[K/usr/include/c++/11/ios:42[m[K,
                 from [01m[K/usr/include/c++/11/istream:38[m[K,
                 from [01m[K/usr/include/c++/11/sstream:38[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::DistanceRequest::DistanceRequest(const coal::DistanceRequest&)[m[K’ first required here
  119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
      |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/opt/openrobots/include/coal/collision.h:44[m[K,
                 from [01m[K/opt/openrobots/include/hpp/fcl/collision.h:2[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/fcl.hpp:40[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry-object.hpp:12[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/parsers/urdf.hpp:10[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:32[m[K:
/opt/openrobots/include/coal/collision_data.h: In instantiation of ‘[01m[Kvoid std::_Construct(_Tp*, _Args&& ...) [with _Tp = coal::CollisionRequest; _Args = {const coal::CollisionRequest&}][m[K’:
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:92:18:[m[K   required from ‘[01m[Kstatic _ForwardIterator std::__uninitialized_copy<_TrivialValueTypes>::__uninit_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; bool _TrivialValueTypes = false][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:151:15:[m[K   required from ‘[01m[K_ForwardIterator std::uninitialized_copy(_InputIterator, _InputIterator, _ForwardIterator) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*][m[K’
[01m[K/usr/include/c++/11/bits/stl_uninitialized.h:333:37:[m[K   required from ‘[01m[K_ForwardIterator std::__uninitialized_copy_a(_InputIterator, _InputIterator, _ForwardIterator, std::allocator<_Tp>&) [with _InputIterator = __gnu_cxx::__normal_iterator<const coal::CollisionRequest*, std::vector<coal::CollisionRequest> >; _ForwardIterator = coal::CollisionRequest*; _Tp = coal::CollisionRequest][m[K’
[01m[K/usr/include/c++/11/bits/stl_vector.h:558:31:[m[K   required from ‘[01m[Kstd::vector<_Tp, _Alloc>::vector(const std::vector<_Tp, _Alloc>&) [with _Tp = coal::CollisionRequest; _Alloc = std::allocator<coal::CollisionRequest>][m[K’
[01m[K/opt/openrobots/include/pinocchio/multibody/geometry.hxx:130:5:[m[K   required from here
[01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
  321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:311:20:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcoal::CollisionRequest::enable_distance_lower_bound[m[K’ is deprecated: A lower bound on distance is always computed. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
  311 | struct COAL_DLLAPI [01;35m[KCollisionRequest[m[K : QueryRequest {
      |                    [01;35m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/opt/openrobots/include/coal/collision_data.h:321:8:[m[K [01;36m[Knote: [m[Kdeclared here
  321 |   bool [01;36m[Kenable_distance_lower_bound[m[K;
      |        [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/usr/include/c++/11/bits/alloc_traits.h:33[m[K,
                 from [01m[K/usr/include/c++/11/ext/alloc_traits.h:34[m[K,
                 from [01m[K/usr/include/c++/11/bits/basic_string.h:40[m[K,
                 from [01m[K/usr/include/c++/11/string:55[m[K,
                 from [01m[K/usr/include/c++/11/bits/locale_classes.h:40[m[K,
                 from [01m[K/usr/include/c++/11/bits/ios_base.h:41[m[K,
                 from [01m[K/usr/include/c++/11/ios:42[m[K,
                 from [01m[K/usr/include/c++/11/istream:38[m[K,
                 from [01m[K/usr/include/c++/11/sstream:38[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/macros.hpp:8[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/fwd.hpp:21[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/urdf.cpp:30[m[K:
[01m[K/usr/include/c++/11/bits/stl_construct.h:119:7:[m[K [01;36m[Knote: [m[Ksynthesized method ‘[01m[Kcoal::CollisionRequest::CollisionRequest(const coal::CollisionRequest&)[m[K’ first required here
  119 |       [01;36m[K::new((void*)__p) _Tp(std::forward<_Args>(__args)...)[m[K;
      |       [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/pinocchio/autodiff/cppad.hpp:228[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/codegen/cppadcg.hpp:16[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/implementation/PinocchioInterface.h:31[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/src/PinocchioInterface.cpp:32[m[K:
[01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:[m[K In static member function ‘[01m[Kstatic pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::ReturnType pinocchio::internal::if_then_else_impl<CppAD::AD<Base>, CppAD::AD<Base>, ThenType, ElseType>::run(pinocchio::internal::ComparisonOperators, const CppAD::AD<Base>&, const CppAD::AD<Base>&, const ThenType&, const ElseType&) [with Scalar = CppAD::cg::CG<double>; ThenType = CppAD::AD<CppAD::cg::CG<double> >; ElseType = CppAD::AD<CppAD::cg::CG<double> >][m[K’:
[01m[K/opt/openrobots/include/pinocchio/autodiff/cppad/utils/static-if.hpp:39:7:[m[K [01;35m[Kwarning: [m[Kcontrol reaches end of non-void function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreturn-type-Wreturn-type]8;;[m[K]
   39 |       [01;35m[K}[m[K
      |       [01;35m[K^[m[K
At global scope:
[01m[Kcc1plus:[m[K [01;36m[Knote: [m[Kunrecognized command-line option ‘[01m[K-Wno-invalid-partial-specialization[m[K’ may have been intended to silence earlier diagnostics
In file included from [01m[K/opt/openrobots/include/hpp/fcl/config.hh:1[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/eigen.hpp:20[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/serialization/fwd.hpp:11[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-composite.hpp:14[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/joint/joint-generic.hpp:9[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/multibody/model.hpp:17[m[K,
                 from [01m[K/opt/openrobots/include/pinocchio/algorithm/frames-derivatives.hpp:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface/test/testPinocchioEndEffectorKinematics.cpp:31[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
