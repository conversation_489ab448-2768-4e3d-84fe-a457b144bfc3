-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found cgal5_colcon: 5.0.2 (/home/<USER>/ros2_ws/install/cgal5_colcon/share/cgal5_colcon/cmake)
-- Found grid_map_core: 2.0.1 (/opt/ros/humble/share/grid_map_core/cmake)
-- Found grid_map_cv: 2.0.1 (/opt/ros/humble/share/grid_map_cv/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found grid_map_filters_rsl: 0.1.0 (/home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/convex_plane_decomposition
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target convex_plane_decomposition[0m
[  8%] Built target gtest
[ 17%] Built target gtest_main
[ 26%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/contour_extraction/ContourExtraction.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/contour_extraction/Upsampling.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/ransac/RansacPlaneExtractor.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/sliding_window_plane_extraction/SlidingWindowPlaneExtractor.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/LoadGridmapFromImage.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/ConvexRegionGrowing.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/PlanarRegion.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/Draw.cpp.o[0m
[ 56%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/GridMapPreprocessing.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/SegmentedPlaneProjection.cpp.o[0m
[ 65%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/PlaneDecompositionPipeline.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/convex_plane_decomposition.dir/src/Postprocessing.cpp.o[0m
[ 73%] [32m[1mLinking CXX static library libconvex_plane_decomposition.a[0m
[ 73%] Built target convex_plane_decomposition
[35m[1mConsolidate compiler generated dependencies of target test_convex_plane_decomposition[0m
[ 78%] [32mBuilding CXX object CMakeFiles/test_convex_plane_decomposition.dir/test/testConvexApproximation.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/test_convex_plane_decomposition.dir/test/testPipeline.cpp.o[0m
[ 86%] [32mBuilding CXX object CMakeFiles/test_convex_plane_decomposition.dir/test/testPlanarRegion.cpp.o[0m
[ 91%] [32mBuilding CXX object CMakeFiles/test_convex_plane_decomposition.dir/test/testRegionGrowing.cpp.o[0m
[ 95%] [32mBuilding CXX object CMakeFiles/test_convex_plane_decomposition.dir/test/testUpsampling.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_convex_plane_decomposition[0m
[100%] Built target test_convex_plane_decomposition
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ConvexRegionGrowing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Draw.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/GeometryUtils.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/GridMapPreprocessing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/LoadGridmapFromImage.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PlanarRegion.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PlaneDecompositionPipeline.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PolygonTypes.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Postprocessing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/SegmentedPlaneProjection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/SegmentedPlanesMap.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Timer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/ContourExtraction.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/ContourExtractionParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/Upsampling.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ransac/RansacPlaneExtractor.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ransac/RansacPlaneExtractorParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/sliding_window_plane_extraction/SlidingWindowPlaneExtractor.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/sliding_window_plane_extraction/SlidingWindowPlaneExtractorParameters.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/package_run_dependencies/convex_plane_decomposition
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/parent_prefix_path/convex_plane_decomposition
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/packages/convex_plane_decomposition
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ConfigExtras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/convex_plane_decompositionConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/convex_plane_decompositionConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/package.xml
-- Installing: /home/<USER>/ros2_ws/install/convex_plane_decomposition/lib/libconvex_plane_decomposition.a
-- Old export file "/home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport.cmake" will be replaced.  Removing files [/home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport-noconfig.cmake].
-- Installing: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport.cmake
-- Installing: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport-noconfig.cmake
