-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_ddp: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake)
-- Found Boost: /usr/local/lib/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem log_setup log 
-- Found ocs2_sqp: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_sqp/share/ocs2_sqp/cmake)
-- Found ocs2_ipm: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake)
-- Found ocs2_centroidal_model: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_centroidal_model/share/ocs2_centroidal_model/cmake)
-- pinocchio_default FOUND. pinocchio_default at /opt/openrobots/lib/libpinocchio_default.so
-- pinocchio_parsers FOUND. pinocchio_parsers at /opt/openrobots/lib/libpinocchio_parsers.so
-- pinocchio_casadi FOUND. pinocchio_casadi at /opt/openrobots/lib/libpinocchio_casadi.so
-- boost_filesystem FOUND. boost_filesystem at /usr/local/lib/libboost_filesystem.so
-- boost_serialization FOUND. boost_serialization at /usr/local/lib/libboost_serialization.so
-- boost_system FOUND. boost_system at /usr/local/lib/libboost_system.so
-- coal FOUND. coal at /opt/openrobots/lib/libcoal.so
-- Found Boost: /usr/local/include (found version "1.74.0") found components: chrono thread date_time serialization filesystem atomic 
-- Default C++ standard: 201703
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- C++ standard sufficient: Minimal required 11, currently defined: 17
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_legged_robot
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[  7%] Built target gtest
[35m[1mConsolidate compiler generated dependencies of target ocs2_legged_robot[0m
[ 14%] Built target gtest_main
[ 17%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/common/ModelSettings.cpp.o[0m
[ 21%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/dynamics/LeggedRobotDynamicsAD.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/constraint/EndEffectorLinearConstraint.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/constraint/ZeroVelocityConstraintCppAd.cpp.o[0m
[ 32%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/constraint/FrictionConeConstraint.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/reference_manager/SwitchedModelReferenceManager.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/constraint/ZeroForceConstraint.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/foot_planner/SplineCpg.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/foot_planner/CubicSpline.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/constraint/NormalVelocityConstraintCppAd.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/gait/Gait.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/gait/GaitSchedule.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/initialization/LeggedRobotInitializer.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/gait/ModeSequenceTemplate.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/foot_planner/SwingTrajectoryPlanner.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/gait/LegLogic.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/LeggedRobotInterface.cpp.o[0m
[ 78%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot.dir/src/LeggedRobotPreComputation.cpp.o[0m
[ 82%] [32m[1mLinking CXX static library libocs2_legged_robot.a[0m
[ 82%] Built target ocs2_legged_robot
[35m[1mConsolidate compiler generated dependencies of target ocs2_legged_robot_test[0m
[ 85%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_test.dir/test/AnymalFactoryFunctions.cpp.o[0m
[ 89%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_test.dir/test/constraint/testEndEffectorLinearConstraint.cpp.o[0m
[ 92%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_test.dir/test/constraint/testFrictionConeConstraint.cpp.o[0m
[ 96%] [32mBuilding CXX object CMakeFiles/ocs2_legged_robot_test.dir/test/constraint/testZeroForceConstraint.cpp.o[0m
[100%] [32m[1mLinking CXX executable ocs2_legged_robot_test[0m
[100%] Built target ocs2_legged_robot_test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotPreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/ModelSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/utils.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/EndEffectorLinearConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/FrictionConeConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/NormalVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroForceConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/cost/LeggedRobotQuadraticTrackingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/dynamics/LeggedRobotDynamicsAD.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/CubicSpline.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SplineCpg.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SwingTrajectoryPlanner.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/Gait.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/GaitSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/LegLogic.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/ModeSequenceTemplate.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/MotionPhaseDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/initialization/LeggedRobotInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/package_path.h.in
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/reference_manager/SwitchedModelReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/gait.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/reference.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/mpc/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/friction_cone.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/zero_velocity.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/package_run_dependencies/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/parent_prefix_path/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/packages/ocs2_legged_robot
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_legged_robot/lib/libocs2_legged_robot.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport-release.cmake
