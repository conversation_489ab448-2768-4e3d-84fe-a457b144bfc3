-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_oc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_mpc/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_mpc
[35m[1mConsolidate compiler generated dependencies of target ocs2_mpc[0m
[ 14%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/LoopshapingSystemObservation.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/SystemObservation.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/MPC_BASE.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/MPC_MRT_Interface.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/MPC_Settings.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/ocs2_mpc.dir/src/MRT_BASE.cpp.o[0m
[100%] [32m[1mLinking CXX static library libocs2_mpc.a[0m
[100%] Built target ocs2_mpc
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/CommandData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/LoopshapingSystemObservation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_BASE.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_MRT_Interface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_Settings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MRT_BASE.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MrtObserver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/SystemObservation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/packages/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_mpc/lib/libocs2_mpc.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport-release.cmake
