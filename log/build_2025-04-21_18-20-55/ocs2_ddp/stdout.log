-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
-- Found ocs2_qp_solver: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_ddp/include>;$<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_ddp/test/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_ddp
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[35m[1mConsolidate compiler generated dependencies of target ocs2_ddp[0m
[  4%] Built target gtest_main
[  9%] Built target gtest
[ 12%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/ContinuousTimeRiccatiEquations.cpp.o[0m
[ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/GaussNewtonDDP_MPC.cpp.o[0m
[ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/DiscreteTimeRiccatiEquations.cpp.o[0m
[ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/RiccatiModification.cpp.o[0m
[ 21%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/GaussNewtonDDP.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/LineSearchStrategy.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/LevenbergMarquardtStrategy.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/StrategySettings.cpp.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/ContinuousTimeLqr.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/HessianCorrection.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/ILQR.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/DDP_HelperFunctions.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/SLQ.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/DDP_Settings.cpp.o[0m
[ 46%] [32m[1mLinking CXX static library libocs2_ddp.a[0m
[ 46%] Built target ocs2_ddp
[35m[1mConsolidate compiler generated dependencies of target bouncing_mass_test[0m
[35m[1mConsolidate compiler generated dependencies of target riccati_ode_test[0m
[35m[1mConsolidate compiler generated dependencies of target exp1_ddp_test[0m
[35m[1mConsolidate compiler generated dependencies of target hybrid_slq_test[0m
[35m[1mConsolidate compiler generated dependencies of target correctness_test[0m
[35m[1mConsolidate compiler generated dependencies of target exp0_ddp_test[0m
[35m[1mConsolidate compiler generated dependencies of target testContinuousTimeLqr[0m
[35m[1mConsolidate compiler generated dependencies of target circular_kinematics_ddp_test[0m
[35m[1mConsolidate compiler generated dependencies of target testDdpHelperFunction[0m
[35m[1mConsolidate compiler generated dependencies of target testReachingTask[0m
[ 48%] [32mBuilding CXX object CMakeFiles/testContinuousTimeLqr.dir/test/testContinuousTimeLqr.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/exp1_ddp_test.dir/test/Exp1Test.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/hybrid_slq_test.dir/test/HybridSlqTest.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/testDdpHelperFunction.dir/test/testDdpHelperFunction.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/riccati_ode_test.dir/test/RiccatiTest.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/correctness_test.dir/test/CorrectnessTest.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/exp0_ddp_test.dir/test/Exp0Test.cpp.o[0m
[ 65%] [32mBuilding CXX object CMakeFiles/testReachingTask.dir/test/testReachingTask.cpp.o[0m
[ 68%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/BouncingMassTest.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/OverallReference.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/circular_kinematics_ddp_test.dir/test/CircularKinematicsTest.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/Reference.cpp.o[0m
[ 78%] [32m[1mLinking CXX executable testDdpHelperFunction[0m
[ 78%] Built target testDdpHelperFunction
[ 80%] [32m[1mLinking CXX executable riccati_ode_test[0m
[ 80%] Built target riccati_ode_test
[ 82%] [32m[1mLinking CXX executable exp0_ddp_test[0m
[ 82%] Built target exp0_ddp_test
[ 85%] [32m[1mLinking CXX executable hybrid_slq_test[0m
[ 85%] Built target hybrid_slq_test
[ 87%] [32m[1mLinking CXX executable exp1_ddp_test[0m
[ 87%] Built target exp1_ddp_test
[ 90%] [32m[1mLinking CXX executable testReachingTask[0m
[ 90%] Built target testReachingTask
[ 92%] [32m[1mLinking CXX executable testContinuousTimeLqr[0m
[ 92%] Built target testContinuousTimeLqr
[ 95%] [32m[1mLinking CXX executable circular_kinematics_ddp_test[0m
[ 97%] [32m[1mLinking CXX executable bouncing_mass_test[0m
[ 97%] Built target circular_kinematics_ddp_test
[ 97%] Built target bouncing_mass_test
[100%] [32m[1mLinking CXX executable correctness_test[0m
[100%] Built target correctness_test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ContinuousTimeLqr.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Data.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_HelperFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Settings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP_MPC.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/HessianCorrection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ILQR.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/SLQ.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/ContinuousTimeRiccatiEquations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/DiscreteTimeRiccatiEquations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModification.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModificationInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiTransversalityConditions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LevenbergMarquardtStrategy.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LineSearchStrategy.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/SearchStrategyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/StrategySettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/OverallReference.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/Reference.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/SystemModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/package_run_dependencies/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/parent_prefix_path/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/packages/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.xml
-- Installing: /home/<USER>/ros2_ws/install/ocs2_ddp/lib/libocs2_ddp.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport-release.cmake
