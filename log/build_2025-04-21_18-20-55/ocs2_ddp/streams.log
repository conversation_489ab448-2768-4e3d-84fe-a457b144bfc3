[0.031s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ddp': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ddp -- -j32 -l32
[0.062s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.196s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.205s] -- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
[0.233s] -- Found ocs2_qp_solver: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_qp_solver/share/ocs2_qp_solver/cmake)
[0.241s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.305s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.372s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.375s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.376s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_ddp/include>;$<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/mpc/ocs2_ddp/test/include>
[0.376s] -- Configured cppcheck exclude dirs and/or files: 
[0.379s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.379s] -- Configured cpplint exclude dirs and/or files: 
[0.380s] -- Added test 'lint_cmake' to check CMake code style
[0.383s] -- Added test 'uncrustify' to check C / C++ code style
[0.383s] -- Configured uncrustify additional arguments: 
[0.384s] -- Added test 'xmllint' to check XML markup files
[0.385s] -- Configuring done
[0.424s] -- Generating done
[0.436s] -- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_ddp
[0.471s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.472s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.477s] [35m[1mConsolidate compiler generated dependencies of target ocs2_ddp[0m
[0.490s] [  4%] Built target gtest_main
[0.495s] [  9%] Built target gtest
[0.521s] [ 12%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/ContinuousTimeRiccatiEquations.cpp.o[0m
[0.521s] [ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/GaussNewtonDDP_MPC.cpp.o[0m
[0.521s] [ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/DiscreteTimeRiccatiEquations.cpp.o[0m
[0.521s] [ 19%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/riccati_equations/RiccatiModification.cpp.o[0m
[0.522s] [ 21%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/GaussNewtonDDP.cpp.o[0m
[0.523s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/LineSearchStrategy.cpp.o[0m
[0.523s] [ 26%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/LevenbergMarquardtStrategy.cpp.o[0m
[0.526s] [ 29%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/search_strategy/StrategySettings.cpp.o[0m
[0.526s] [ 31%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/ContinuousTimeLqr.cpp.o[0m
[0.528s] [ 34%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/HessianCorrection.cpp.o[0m
[0.528s] [ 36%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/ILQR.cpp.o[0m
[0.528s] [ 39%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/DDP_HelperFunctions.cpp.o[0m
[0.529s] [ 41%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/SLQ.cpp.o[0m
[0.531s] [ 43%] [32mBuilding CXX object CMakeFiles/ocs2_ddp.dir/src/DDP_Settings.cpp.o[0m
[12.097s] [ 46%] [32m[1mLinking CXX static library libocs2_ddp.a[0m
[12.170s] [ 46%] Built target ocs2_ddp
[12.190s] [35m[1mConsolidate compiler generated dependencies of target bouncing_mass_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target riccati_ode_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target exp1_ddp_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target hybrid_slq_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target correctness_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target exp0_ddp_test[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target testContinuousTimeLqr[0m
[12.192s] [35m[1mConsolidate compiler generated dependencies of target circular_kinematics_ddp_test[0m
[12.193s] [35m[1mConsolidate compiler generated dependencies of target testDdpHelperFunction[0m
[12.193s] [35m[1mConsolidate compiler generated dependencies of target testReachingTask[0m
[12.221s] [ 48%] [32mBuilding CXX object CMakeFiles/testContinuousTimeLqr.dir/test/testContinuousTimeLqr.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/exp1_ddp_test.dir/test/Exp1Test.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/hybrid_slq_test.dir/test/HybridSlqTest.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/testDdpHelperFunction.dir/test/testDdpHelperFunction.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/riccati_ode_test.dir/test/RiccatiTest.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/correctness_test.dir/test/CorrectnessTest.cpp.o[0m
[12.228s] [ 63%] [32mBuilding CXX object CMakeFiles/exp0_ddp_test.dir/test/Exp0Test.cpp.o[0m
[12.229s] [ 65%] [32mBuilding CXX object CMakeFiles/testReachingTask.dir/test/testReachingTask.cpp.o[0m
[12.233s] [ 68%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/BouncingMassTest.cpp.o[0m
[12.239s] [ 70%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/OverallReference.cpp.o[0m
[12.240s] [ 73%] [32mBuilding CXX object CMakeFiles/circular_kinematics_ddp_test.dir/test/CircularKinematicsTest.cpp.o[0m
[12.246s] [ 75%] [32mBuilding CXX object CMakeFiles/bouncing_mass_test.dir/test/bouncingmass/Reference.cpp.o[0m
[17.008s] [ 78%] [32m[1mLinking CXX executable testDdpHelperFunction[0m
[17.137s] [ 78%] Built target testDdpHelperFunction
[17.412s] [ 80%] [32m[1mLinking CXX executable riccati_ode_test[0m
[17.540s] [ 80%] Built target riccati_ode_test
[18.789s] [ 82%] [32m[1mLinking CXX executable exp0_ddp_test[0m
[18.978s] [ 82%] Built target exp0_ddp_test
[21.149s] [ 85%] [32m[1mLinking CXX executable hybrid_slq_test[0m
[21.388s] [ 85%] Built target hybrid_slq_test
[21.806s] [ 87%] [32m[1mLinking CXX executable exp1_ddp_test[0m
[22.053s] [ 87%] Built target exp1_ddp_test
[22.157s] [ 90%] [32m[1mLinking CXX executable testReachingTask[0m
[22.354s] [ 90%] Built target testReachingTask
[23.608s] [ 92%] [32m[1mLinking CXX executable testContinuousTimeLqr[0m
[23.816s] [ 92%] Built target testContinuousTimeLqr
[24.933s] [ 95%] [32m[1mLinking CXX executable circular_kinematics_ddp_test[0m
[25.074s] [ 97%] [32m[1mLinking CXX executable bouncing_mass_test[0m
[25.227s] [ 97%] Built target circular_kinematics_ddp_test
[25.294s] [ 97%] Built target bouncing_mass_test
[28.164s] [100%] [32m[1mLinking CXX executable correctness_test[0m
[28.329s] [100%] Built target correctness_test
[28.343s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ddp' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ddp -- -j32 -l32
[28.345s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ddp': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ddp
[28.359s] -- Install configuration: "Release"
[28.360s] -- Execute custom install script
[28.360s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ContinuousTimeLqr.h
[28.360s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Data.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_HelperFunctions.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Settings.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP_MPC.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/HessianCorrection.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ILQR.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/SLQ.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/ContinuousTimeRiccatiEquations.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/DiscreteTimeRiccatiEquations.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModification.h
[28.361s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModificationInterpolation.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiTransversalityConditions.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LevenbergMarquardtStrategy.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LineSearchStrategy.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/SearchStrategyBase.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/StrategySettings.h
[28.362s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/OverallReference.h
[28.363s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/Reference.h
[28.363s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/SystemModel.h
[28.363s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.sh
[28.363s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.dsv
[28.363s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/package_run_dependencies/ocs2_ddp
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/parent_prefix_path/ocs2_ddp
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.sh
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.dsv
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.sh
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.dsv
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.bash
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.sh
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.zsh
[28.364s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.dsv
[28.364s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.dsv
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/packages/ocs2_ddp
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_dependencies-extras.cmake
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_targets-extras.cmake
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig.cmake
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig-version.cmake
[28.377s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.xml
[28.377s] -- Installing: /home/<USER>/ros2_ws/install/ocs2_ddp/lib/libocs2_ddp.a
[28.379s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport.cmake
[28.379s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport-release.cmake
[28.382s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ddp' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ddp
