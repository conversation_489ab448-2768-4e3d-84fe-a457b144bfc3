-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found grid_map_cv: 2.0.1 (/opt/ros/humble/share/grid_map_cv/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/grid_map_filters_rsl
[35m[1mConsolidate compiler generated dependencies of target grid_map_filters_rsl[0m
[35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[35m[1mConsolidate compiler generated dependencies of target gtest[0m
[ 14%] Built target gtest_main
[ 28%] Built target gtest
[ 35%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/inpainting.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/GridMapDerivative.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/lookup.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/smoothing.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/processing.cpp.o[0m
[ 71%] [32m[1mLinking CXX static library libgrid_map_filters_rsl.a[0m
[ 71%] Built target grid_map_filters_rsl
[35m[1mConsolidate compiler generated dependencies of target test_grid_map_filters_rsl[0m
[ 78%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestFilters.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestDerivativeFilter.cpp.o[0m
[ 92%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestLookup.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_grid_map_filters_rsl[0m
[100%] Built target test_grid_map_filters_rsl
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/GridMapDerivative.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/inpainting.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/lookup.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/processing.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/smoothing.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/package_run_dependencies/grid_map_filters_rsl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/parent_prefix_path/grid_map_filters_rsl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/packages/grid_map_filters_rsl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/grid_map_filters_rslConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/grid_map_filters_rslConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/package.xml
-- Installing: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib/libgrid_map_filters_rsl.a
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/export_grid_map_filters_rslExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/export_grid_map_filters_rslExport-noconfig.cmake
