[0.033s] Invoking command in '/home/<USER>/ros2_ws/build/grid_map_filters_rsl': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/grid_map_filters_rsl -- -j32 -l32
[0.058s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.193s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.203s] -- Found grid_map_cv: 2.0.1 (/opt/ros/humble/share/grid_map_cv/cmake)
[0.260s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.263s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.271s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.287s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.308s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.475s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.480s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.507s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.517s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.542s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.554s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.574s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.653s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.740s] -- Found ament_cmake_gtest: 1.3.11 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.740s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.790s] -- Configuring done
[0.822s] -- Generating done
[0.826s] -- Build files have been written to: /home/<USER>/ros2_ws/build/grid_map_filters_rsl
[0.868s] [35m[1mConsolidate compiler generated dependencies of target grid_map_filters_rsl[0m
[0.870s] [35m[1mConsolidate compiler generated dependencies of target gtest_main[0m
[0.870s] [35m[1mConsolidate compiler generated dependencies of target gtest[0m
[0.891s] [ 14%] Built target gtest_main
[0.894s] [ 28%] Built target gtest
[0.897s] [ 35%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/inpainting.cpp.o[0m
[0.898s] [ 42%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/GridMapDerivative.cpp.o[0m
[0.900s] [ 50%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/lookup.cpp.o[0m
[0.900s] [ 64%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/smoothing.cpp.o[0m
[0.900s] [ 64%] [32mBuilding CXX object CMakeFiles/grid_map_filters_rsl.dir/src/processing.cpp.o[0m
[16.513s] [ 71%] [32m[1mLinking CXX static library libgrid_map_filters_rsl.a[0m
[16.627s] [ 71%] Built target grid_map_filters_rsl
[16.647s] [35m[1mConsolidate compiler generated dependencies of target test_grid_map_filters_rsl[0m
[16.672s] [ 78%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestFilters.cpp.o[0m
[16.673s] [ 85%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestDerivativeFilter.cpp.o[0m
[16.674s] [ 92%] [32mBuilding CXX object CMakeFiles/test_grid_map_filters_rsl.dir/test/TestLookup.cpp.o[0m
[18.433s] [100%] [32m[1mLinking CXX executable test_grid_map_filters_rsl[0m
[19.037s] [100%] Built target test_grid_map_filters_rsl
[19.050s] Invoked command in '/home/<USER>/ros2_ws/build/grid_map_filters_rsl' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/grid_map_filters_rsl -- -j32 -l32
[19.050s] Invoking command in '/home/<USER>/ros2_ws/build/grid_map_filters_rsl': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/grid_map_filters_rsl
[19.061s] -- Install configuration: ""
[19.062s] -- Execute custom install script
[19.062s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/GridMapDerivative.hpp
[19.062s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/inpainting.hpp
[19.062s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/lookup.hpp
[19.062s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/processing.hpp
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/include/grid_map_filters_rsl/grid_map_filters_rsl/smoothing.hpp
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/library_path.sh
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/library_path.dsv
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/package_run_dependencies/grid_map_filters_rsl
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/parent_prefix_path/grid_map_filters_rsl
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/ament_prefix_path.sh
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/ament_prefix_path.dsv
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/path.sh
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/environment/path.dsv
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.bash
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.sh
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.zsh
[19.063s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/local_setup.dsv
[19.063s] -- Symlinking: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/package.dsv
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/ament_index/resource_index/packages/grid_map_filters_rsl
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/ament_cmake_export_dependencies-extras.cmake
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/ament_cmake_export_targets-extras.cmake
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/grid_map_filters_rslConfig.cmake
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/grid_map_filters_rslConfig-version.cmake
[19.075s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/package.xml
[19.075s] -- Installing: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib/libgrid_map_filters_rsl.a
[19.080s] -- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/export_grid_map_filters_rslExport.cmake
[19.081s] -- Up-to-date: /home/<USER>/ros2_ws/install/grid_map_filters_rsl/share/grid_map_filters_rsl/cmake/export_grid_map_filters_rslExport-noconfig.cmake
[19.082s] Invoked command in '/home/<USER>/ros2_ws/build/grid_map_filters_rsl' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/grid_map_filters_rsl
