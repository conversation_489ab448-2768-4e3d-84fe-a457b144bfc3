[0.007s] Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[0.025s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.107s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.114s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.135s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.136s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.140s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.146s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.155s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.176s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.176s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.253s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.284s] -- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)
[0.333s] -- Configuring done
[0.338s] -- Generating done
[0.340s] -- Build files have been written to: /home/<USER>/ros2_ws/build/keyboard_input
[0.362s] [35m[1mConsolidate compiler generated dependencies of target keyboard_input[0m
[0.376s] [ 50%] [32mBuilding CXX object CMakeFiles/keyboard_input.dir/src/KeyboardInput.cpp.o[0m
[4.286s] [100%] [32m[1mLinking CXX executable keyboard_input[0m
[4.448s] [100%] Built target keyboard_input
[4.459s] Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[4.461s] Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[4.467s] -- Install configuration: ""
[4.467s] -- Execute custom install script
[4.467s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/lib/keyboard_input/keyboard_input
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/package_run_dependencies/keyboard_input
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/parent_prefix_path/keyboard_input
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.sh
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.dsv
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.sh
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.dsv
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.bash
[4.468s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.sh
[4.469s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.zsh
[4.469s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.dsv
[4.469s] -- Symlinking: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv
[4.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/packages/keyboard_input
[4.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig.cmake
[4.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig-version.cmake
[4.479s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.xml
[4.482s] Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
