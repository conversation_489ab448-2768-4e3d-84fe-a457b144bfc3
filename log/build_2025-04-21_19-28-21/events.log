[0.000000] (-) TimerEvent: {}
[0.000129] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000156] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000174] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000189] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000205] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000220] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000237] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000253] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000269] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000284] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000326] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000367] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000433] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000450] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000466] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000481] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000497] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000512] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000527] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000544] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000560] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000575] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000604] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000665] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000683] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000713] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000763] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000809] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000837] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000877] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.000907] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.000916] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.000924] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.000931] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.000938] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.000945] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.000952] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.000959] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.000965] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.000973] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.000980] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.000988] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.000995] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001002] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001009] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001015] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001022] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001029] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001036] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001043] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001050] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001056] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001063] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001069] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001080] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.001088] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.001096] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.001103] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.001109] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.001116] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.001124] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.001136] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.001144] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.001150] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.001157] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.001164] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.001171] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.001178] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.001184] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.001191] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.001198] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.001206] (-) JobUnselected: {'identifier': 'x30_description'}
[0.001215] (control_input_msgs) JobQueued: {'identifier': 'control_input_msgs', 'dependencies': OrderedDict()}
[0.001226] (go2_description) JobQueued: {'identifier': 'go2_description', 'dependencies': OrderedDict()}
[0.001234] (controller_common) JobQueued: {'identifier': 'controller_common', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs')])}
[0.001244] (keyboard_input) JobQueued: {'identifier': 'keyboard_input', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs')])}
[0.001253] (unitree_guide_controller) JobQueued: {'identifier': 'unitree_guide_controller', 'dependencies': OrderedDict([('control_input_msgs', '/home/<USER>/ros2_ws/install/control_input_msgs'), ('controller_common', '/home/<USER>/ros2_ws/install/controller_common')])}
[0.001267] (control_input_msgs) JobStarted: {'identifier': 'control_input_msgs'}
[0.004087] (go2_description) JobStarted: {'identifier': 'go2_description'}
[0.006440] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'cmake'}
[0.006838] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'build'}
[0.007542] (control_input_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/control_input_msgs', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/control_input_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/control_input_msgs'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.010042] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'cmake'}
[0.012635] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/go2_description'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.021461] (go2_description) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.048253] (control_input_msgs) StdoutLine: {'line': b'[  3%] Built target control_input_msgs__cpp\n'}
[0.049152] (control_input_msgs) StdoutLine: {'line': b'[ 12%] Built target control_input_msgs__rosidl_generator_c\n'}
[0.057102] (control_input_msgs) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_symlink_control_input_msgs\n'}
[0.065986] (control_input_msgs) StdoutLine: {'line': b'[ 22%] Built target control_input_msgs__rosidl_typesupport_introspection_c\n'}
[0.066310] (control_input_msgs) StdoutLine: {'line': b'[ 32%] Built target control_input_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.066809] (control_input_msgs) StdoutLine: {'line': b'[ 41%] Built target control_input_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.067532] (control_input_msgs) StdoutLine: {'line': b'[ 51%] Built target control_input_msgs__rosidl_typesupport_c\n'}
[0.067863] (control_input_msgs) StdoutLine: {'line': b'[ 61%] Built target control_input_msgs__rosidl_typesupport_cpp\n'}
[0.068423] (control_input_msgs) StdoutLine: {'line': b'[ 70%] Built target control_input_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.084429] (control_input_msgs) StdoutLine: {'line': b'[ 70%] Built target control_input_msgs\n'}
[0.099403] (-) TimerEvent: {}
[0.101931] (control_input_msgs) StdoutLine: {'line': b'[ 74%] Built target control_input_msgs__py\n'}
[0.108099] (go2_description) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.120222] (control_input_msgs) StdoutLine: {'line': b'[ 80%] Built target control_input_msgs__rosidl_generator_py\n'}
[0.137365] (control_input_msgs) StdoutLine: {'line': b'[ 87%] Built target control_input_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.140680] (control_input_msgs) StdoutLine: {'line': b'[ 93%] Built target control_input_msgs__rosidl_typesupport_c__pyext\n'}
[0.141199] (control_input_msgs) StdoutLine: {'line': b'[100%] Built target control_input_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[0.164336] (go2_description) StdoutLine: {'line': b'-- Configuring done\n'}
[0.167073] (go2_description) StdoutLine: {'line': b'-- Generating done\n'}
[0.168658] (go2_description) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros2_ws/build/go2_description\n'}
[0.171743] (go2_description) CommandEnded: {'returncode': 0}
[0.172340] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'build'}
[0.172362] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/go2_description', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.199582] (-) TimerEvent: {}
[0.206839] (go2_description) CommandEnded: {'returncode': 0}
[0.207492] (go2_description) JobProgress: {'identifier': 'go2_description', 'progress': 'install'}
[0.215521] (go2_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/go2_description'], 'cwd': '/home/<USER>/ros2_ws/build/go2_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/go2_description'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.222660] (go2_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.223083] (go2_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.223545] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf.dae\n'}
[0.223625] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf_mirror.dae\n'}
[0.223725] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/foot.dae\n'}
[0.223837] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/hip.dae\n'}
[0.223931] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh.dae\n'}
[0.224000] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh_mirror.dae\n'}
[0.224087] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/trunk.dae\n'}
[0.224223] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/const.xacro\n'}
[0.224316] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo.xacro\n'}
[0.224460] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo_classic.xacro\n'}
[0.224609] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/leg.xacro\n'}
[0.224683] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/materials.xacro\n'}
[0.224778] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/robot.xacro\n'}
[0.224842] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/ros2_control.xacro\n'}
[0.224928] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/transmission.xacro\n'}
[0.225098] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/gazebo_rl_control.launch.py\n'}
[0.225214] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/visualize.launch.py\n'}
[0.225657] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/gazebo.yaml\n'}
[0.225863] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/himloco/config.yaml\n'}
[0.226100] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/himloco/himloco.pt\n'}
[0.226195] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config.yaml\n'}
[0.226286] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/policy.pt\n'}
[0.226377] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/gait.info\n'}
[0.226468] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/reference.info\n'}
[0.226557] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/task.info\n'}
[0.226753] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/robot_control.yaml\n'}
[0.226956] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/visualize_urdf.rviz\n'}
[0.227139] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//urdf/robot.urdf\n'}
[0.227298] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/package_run_dependencies/go2_description\n'}
[0.227438] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/parent_prefix_path/go2_description\n'}
[0.227572] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.sh\n'}
[0.227693] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.dsv\n'}
[0.227872] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.sh\n'}
[0.228147] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.dsv\n'}
[0.228228] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.bash\n'}
[0.228397] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.sh\n'}
[0.228494] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.zsh\n'}
[0.228676] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.dsv\n'}
[0.228770] (go2_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv\n'}
[0.231652] (control_input_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.232107] (control_input_msgs) StdoutLine: {'line': b'writing control_input_msgs.egg-info/PKG-INFO\n'}
[0.232373] (control_input_msgs) StdoutLine: {'line': b'writing dependency_links to control_input_msgs.egg-info/dependency_links.txt\n'}
[0.232458] (control_input_msgs) StdoutLine: {'line': b'writing top-level names to control_input_msgs.egg-info/top_level.txt\n'}
[0.234197] (control_input_msgs) StdoutLine: {'line': b"reading manifest file 'control_input_msgs.egg-info/SOURCES.txt'\n"}
[0.234538] (control_input_msgs) StdoutLine: {'line': b"writing manifest file 'control_input_msgs.egg-info/SOURCES.txt'\n"}
[0.237113] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/packages/go2_description\n'}
[0.237339] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig.cmake\n'}
[0.237416] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig-version.cmake\n'}
[0.237603] (go2_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.xml\n'}
[0.239597] (go2_description) CommandEnded: {'returncode': 0}
[0.250880] (go2_description) JobEnded: {'identifier': 'go2_description', 'rc': 0}
[0.252709] (control_input_msgs) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_control_input_msgs_egg\n'}
[0.261130] (control_input_msgs) CommandEnded: {'returncode': 0}
[0.261782] (control_input_msgs) JobProgress: {'identifier': 'control_input_msgs', 'progress': 'install'}
[0.262169] (control_input_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/control_input_msgs'], 'cwd': '/home/<USER>/ros2_ws/build/control_input_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/control_input_msgs'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.269744] (control_input_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.270495] (control_input_msgs) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.270612] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/rosidl_interfaces/control_input_msgs\n'}
[0.270840] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__functions.h\n'}
[0.270962] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__struct.h\n'}
[0.271037] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__type_support.h\n'}
[0.271103] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/inputs.h\n'}
[0.271202] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[0.271271] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/library_path.sh\n'}
[0.271365] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/library_path.dsv\n'}
[0.271465] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_fastrtps_c.h\n'}
[0.271530] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.271641] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_introspection_c.h\n'}
[0.271704] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.271867] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__builder.hpp\n'}
[0.272020] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__struct.hpp\n'}
[0.272091] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__traits.hpp\n'}
[0.272156] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__type_support.hpp\n'}
[0.272246] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/inputs.hpp\n'}
[0.272312] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.272406] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.272467] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.272560] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/include/control_input_msgs/control_input_msgs/msg/detail/inputs__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.272629] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/pythonpath.sh\n'}
[0.272714] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/pythonpath.dsv\n'}
[0.272796] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.272898] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.272983] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.273043] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.273193] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/__init__.py\n'}
[0.273278] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_c.c\n'}
[0.273365] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.273474] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/_control_input_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.273540] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.273605] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.273669] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.273753] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/libcontrol_input_msgs__rosidl_generator_py.so\n'}
[0.273857] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/__init__.py\n'}
[0.273944] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/_inputs.py\n'}
[0.274007] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg/_inputs_s.c\n'}
[0.274071] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.274169] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.274233] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/control_input_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.274318] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/msg/Inputs.idl\n'}
[0.274422] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/msg/Inputs.msg\n'}
[0.274492] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/package_run_dependencies/control_input_msgs\n'}
[0.274556] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/parent_prefix_path/control_input_msgs\n'}
[0.274689] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/ament_prefix_path.sh\n'}
[0.274753] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/ament_prefix_path.dsv\n'}
[0.274817] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/path.sh\n'}
[0.274883] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/environment/path.dsv\n'}
[0.274944] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.bash\n'}
[0.275053] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.sh\n'}
[0.275114] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.zsh\n'}
[0.275172] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/local_setup.dsv\n'}
[0.275231] (control_input_msgs) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv\n'}
[0.280689] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/ament_index/resource_index/packages/control_input_msgs\n'}
[0.280806] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[0.280890] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.280956] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.281022] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.281129] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.281201] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.281298] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.281359] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgsConfig.cmake\n'}
[0.281423] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgsConfig-version.cmake\n'}
[0.281513] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.xml\n'}
[0.281574] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_generator_c.so\n'}
[0.281636] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[0.281712] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_introspection_c.so\n'}
[0.281794] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_c.so\n'}
[0.281876] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.281955] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[0.282054] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_typesupport_cpp.so\n'}
[0.299655] (-) TimerEvent: {}
[0.304414] (control_input_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs'...\n"}
[0.304526] (control_input_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages/control_input_msgs/msg'...\n"}
[0.307408] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/lib/libcontrol_input_msgs__rosidl_generator_py.so\n'}
[0.307517] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cExport.cmake\n'}
[0.307619] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[0.307687] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.307752] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.307836] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.308013] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.308108] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cExport.cmake\n'}
[0.308184] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.308248] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_cppExport.cmake\n'}
[0.308310] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.308370] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.308429] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.308486] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.308544] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cppExport.cmake\n'}
[0.308603] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/control_input_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.308661] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_pyExport.cmake\n'}
[0.308725] (control_input_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake/export_control_input_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.309604] (control_input_msgs) CommandEnded: {'returncode': 0}
[0.318314] (control_input_msgs) JobEnded: {'identifier': 'control_input_msgs', 'rc': 0}
[0.318773] (controller_common) JobStarted: {'identifier': 'controller_common'}
[0.320347] (keyboard_input) JobStarted: {'identifier': 'keyboard_input'}
[0.323863] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'cmake'}
[0.324223] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'build'}
[0.324384] (controller_common) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/controller_common', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/controller_common', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/controller_common'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.326135] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'cmake'}
[0.326991] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'build'}
[0.327089] (keyboard_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/keyboard_input', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/keyboard_input', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/keyboard_input'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.345236] (keyboard_input) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.369004] (controller_common) StdoutLine: {'line': b'[100%] Built target controller_common\n'}
[0.378234] (controller_common) CommandEnded: {'returncode': 0}
[0.378992] (controller_common) JobProgress: {'identifier': 'controller_common', 'progress': 'install'}
[0.379856] (controller_common) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/controller_common'], 'cwd': '/home/<USER>/ros2_ws/build/controller_common', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/controller_common'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.386541] (controller_common) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.387162] (controller_common) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.388102] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/CtrlInterfaces.h\n'}
[0.388224] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/BaseFixedStand.h\n'}
[0.388294] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/FSMState.h\n'}
[0.388452] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/StateFixedDown.h\n'}
[0.388546] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/FSM/StatePassive.h\n'}
[0.388645] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/enumClass.h\n'}
[0.388715] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/mathTools.h\n'}
[0.388804] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/include/controller_common/controller_common/common/mathTypes.h\n'}
[0.388874] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/library_path.sh\n'}
[0.388974] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/library_path.dsv\n'}
[0.389076] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/package_run_dependencies/controller_common\n'}
[0.389212] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/parent_prefix_path/controller_common\n'}
[0.389343] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/ament_prefix_path.sh\n'}
[0.389466] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/ament_prefix_path.dsv\n'}
[0.389592] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/path.sh\n'}
[0.389716] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/environment/path.dsv\n'}
[0.389832] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.bash\n'}
[0.389960] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.sh\n'}
[0.390077] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.zsh\n'}
[0.390193] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/local_setup.dsv\n'}
[0.390283] (controller_common) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv\n'}
[0.398381] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/ament_index/resource_index/packages/controller_common\n'}
[0.398487] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.398609] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/controller_commonConfig.cmake\n'}
[0.398728] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/controller_commonConfig-version.cmake\n'}
[0.398816] (controller_common) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.xml\n'}
[0.399008] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/lib/libcontroller_common.so\n'}
[0.399146] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/export_controller_commonExport.cmake\n'}
[0.399230] (controller_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake/export_controller_commonExport-release.cmake\n'}
[0.399714] (-) TimerEvent: {}
[0.400286] (controller_common) CommandEnded: {'returncode': 0}
[0.408722] (controller_common) JobEnded: {'identifier': 'controller_common', 'rc': 0}
[0.409256] (unitree_guide_controller) JobStarted: {'identifier': 'unitree_guide_controller'}
[0.416875] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'cmake'}
[0.417497] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'build'}
[0.418258] (unitree_guide_controller) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/unitree_guide_controller', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/unitree_guide_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.427608] (keyboard_input) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.434296] (keyboard_input) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.437198] (unitree_guide_controller) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.455304] (keyboard_input) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.456945] (keyboard_input) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.460945] (keyboard_input) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.467142] (keyboard_input) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.475277] (keyboard_input) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.496357] (keyboard_input) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.497119] (keyboard_input) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.499838] (-) TimerEvent: {}
[0.524208] (unitree_guide_controller) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.528553] (unitree_guide_controller) StdoutLine: {'line': b'-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)\n'}
[0.549316] (unitree_guide_controller) StdoutLine: {'line': b'-- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)\n'}
[0.560433] (unitree_guide_controller) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.561973] (unitree_guide_controller) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.565734] (unitree_guide_controller) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.571704] (unitree_guide_controller) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.573932] (keyboard_input) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.578125] (unitree_guide_controller) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.599914] (-) TimerEvent: {}
[0.604818] (keyboard_input) StdoutLine: {'line': b'-- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)\n'}
[0.653458] (keyboard_input) StdoutLine: {'line': b'-- Configuring done\n'}
[0.658322] (keyboard_input) StdoutLine: {'line': b'-- Generating done\n'}
[0.660245] (keyboard_input) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros2_ws/build/keyboard_input\n'}
[0.675946] (unitree_guide_controller) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.676794] (unitree_guide_controller) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.682614] (keyboard_input) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target keyboard_input\x1b[0m\n'}
[0.688946] (unitree_guide_controller) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.695112] (unitree_guide_controller) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.697004] (keyboard_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/keyboard_input.dir/src/KeyboardInput.cpp.o\x1b[0m\n'}
[0.699980] (-) TimerEvent: {}
[0.709666] (unitree_guide_controller) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.716724] (unitree_guide_controller) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[0.724586] (unitree_guide_controller) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.755901] (unitree_guide_controller) StdoutLine: {'line': b'-- Found realtime_tools: 2.12.0 (/opt/ros/humble/share/realtime_tools/cmake)\n'}
[0.762709] (unitree_guide_controller) StdoutLine: {'line': b'-- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)\n'}
[0.768178] (unitree_guide_controller) StdoutLine: {'line': b'-- Found controller_common: 0.0.0 (/home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake)\n'}
[0.769008] (unitree_guide_controller) StdoutLine: {'line': b'-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)\n'}
[0.769935] (unitree_guide_controller) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.770762] (unitree_guide_controller) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.778042] (unitree_guide_controller) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.800126] (-) TimerEvent: {}
[0.825426] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[0.825676] (unitree_guide_controller) StdoutLine: {'line': b'-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/include>;/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/src\n'}
[0.825758] (unitree_guide_controller) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[0.826031] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[0.826398] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.826654] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[0.827816] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[0.827932] (unitree_guide_controller) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[0.828148] (unitree_guide_controller) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.829070] (unitree_guide_controller) StdoutLine: {'line': b'-- Configuring done\n'}
[0.847102] (unitree_guide_controller) StdoutLine: {'line': b'-- Generating done\n'}
[0.850511] (unitree_guide_controller) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros2_ws/build/unitree_guide_controller\n'}
[0.884589] (unitree_guide_controller) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target unitree_guide_controller\x1b[0m\n'}
[0.900190] (-) TimerEvent: {}
[0.917218] (unitree_guide_controller) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateSwingTest.cpp.o\x1b[0m\n'}
[0.917573] (unitree_guide_controller) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/UnitreeGuideController.cpp.o\x1b[0m\n'}
[0.917680] (unitree_guide_controller) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFixedStand.cpp.o\x1b[0m\n'}
[0.919266] (unitree_guide_controller) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFreeStand.cpp.o\x1b[0m\n'}
[0.919667] (unitree_guide_controller) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/RobotLeg.cpp.o\x1b[0m\n'}
[0.920097] (unitree_guide_controller) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/QuadrupedRobot.cpp.o\x1b[0m\n'}
[0.920279] (unitree_guide_controller) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateBalanceTest.cpp.o\x1b[0m\n'}
[0.920926] (unitree_guide_controller) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateTrotting.cpp.o\x1b[0m\n'}
[0.921096] (unitree_guide_controller) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/LowPassFilter.cpp.o\x1b[0m\n'}
[0.921375] (unitree_guide_controller) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/Estimator.cpp.o\x1b[0m\n'}
[0.922457] (unitree_guide_controller) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/WaveGenerator.cpp.o\x1b[0m\n'}
[0.923309] (unitree_guide_controller) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/Array.cc.o\x1b[0m\n'}
[0.923605] (unitree_guide_controller) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/BalanceCtrl.cpp.o\x1b[0m\n'}
[0.924147] (unitree_guide_controller) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/QuadProg++.cc.o\x1b[0m\n'}
[0.925103] (unitree_guide_controller) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/FeetEndCalc.cpp.o\x1b[0m\n'}
[0.927089] (unitree_guide_controller) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/GaitGenerator.cpp.o\x1b[0m\n'}
[1.000342] (-) TimerEvent: {}
[1.100784] (-) TimerEvent: {}
[1.201252] (-) TimerEvent: {}
[1.301702] (-) TimerEvent: {}
[1.402159] (-) TimerEvent: {}
[1.502579] (-) TimerEvent: {}
[1.603045] (-) TimerEvent: {}
[1.703479] (-) TimerEvent: {}
[1.803877] (-) TimerEvent: {}
[1.904315] (-) TimerEvent: {}
[2.004758] (-) TimerEvent: {}
[2.105198] (-) TimerEvent: {}
[2.205612] (-) TimerEvent: {}
[2.306047] (-) TimerEvent: {}
[2.406446] (-) TimerEvent: {}
[2.506871] (-) TimerEvent: {}
[2.607279] (-) TimerEvent: {}
[2.707691] (-) TimerEvent: {}
[2.808120] (-) TimerEvent: {}
[2.908536] (-) TimerEvent: {}
[3.008942] (-) TimerEvent: {}
[3.109448] (-) TimerEvent: {}
[3.209894] (-) TimerEvent: {}
[3.310342] (-) TimerEvent: {}
[3.410786] (-) TimerEvent: {}
[3.511247] (-) TimerEvent: {}
[3.611632] (-) TimerEvent: {}
[3.712081] (-) TimerEvent: {}
[3.812459] (-) TimerEvent: {}
[3.912837] (-) TimerEvent: {}
[4.013281] (-) TimerEvent: {}
[4.113763] (-) TimerEvent: {}
[4.214169] (-) TimerEvent: {}
[4.314588] (-) TimerEvent: {}
[4.414982] (-) TimerEvent: {}
[4.515374] (-) TimerEvent: {}
[4.606845] (keyboard_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable keyboard_input\x1b[0m\n'}
[4.615455] (-) TimerEvent: {}
[4.715751] (-) TimerEvent: {}
[4.768135] (keyboard_input) StdoutLine: {'line': b'[100%] Built target keyboard_input\n'}
[4.779524] (keyboard_input) CommandEnded: {'returncode': 0}
[4.780235] (keyboard_input) JobProgress: {'identifier': 'keyboard_input', 'progress': 'install'}
[4.781136] (keyboard_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/keyboard_input'], 'cwd': '/home/<USER>/ros2_ws/build/keyboard_input', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/keyboard_input'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[4.787407] (keyboard_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[4.787804] (keyboard_input) StdoutLine: {'line': b'-- Execute custom install script\n'}
[4.788032] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/lib/keyboard_input/keyboard_input\n'}
[4.788187] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/package_run_dependencies/keyboard_input\n'}
[4.788320] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/parent_prefix_path/keyboard_input\n'}
[4.788443] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.sh\n'}
[4.788584] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.dsv\n'}
[4.788696] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.sh\n'}
[4.788788] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.dsv\n'}
[4.788959] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.bash\n'}
[4.789055] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.sh\n'}
[4.789173] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.zsh\n'}
[4.789278] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.dsv\n'}
[4.789421] (keyboard_input) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv\n'}
[4.799449] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/packages/keyboard_input\n'}
[4.799663] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig.cmake\n'}
[4.799790] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig-version.cmake\n'}
[4.799996] (keyboard_input) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.xml\n'}
[4.801590] (keyboard_input) CommandEnded: {'returncode': 0}
[4.815844] (-) TimerEvent: {}
[4.818549] (keyboard_input) JobEnded: {'identifier': 'keyboard_input', 'rc': 0}
[4.915947] (-) TimerEvent: {}
[5.016273] (-) TimerEvent: {}
[5.116603] (-) TimerEvent: {}
[5.217003] (-) TimerEvent: {}
[5.317359] (-) TimerEvent: {}
[5.417661] (-) TimerEvent: {}
[5.518035] (-) TimerEvent: {}
[5.618453] (-) TimerEvent: {}
[5.718783] (-) TimerEvent: {}
[5.819102] (-) TimerEvent: {}
[5.919428] (-) TimerEvent: {}
[6.019792] (-) TimerEvent: {}
[6.120131] (-) TimerEvent: {}
[6.220422] (-) TimerEvent: {}
[6.320781] (-) TimerEvent: {}
[6.421019] (-) TimerEvent: {}
[6.521249] (-) TimerEvent: {}
[6.621449] (-) TimerEvent: {}
[6.721726] (-) TimerEvent: {}
[6.822045] (-) TimerEvent: {}
[6.922297] (-) TimerEvent: {}
[7.022453] (-) TimerEvent: {}
[7.122708] (-) TimerEvent: {}
[7.222999] (-) TimerEvent: {}
[7.323276] (-) TimerEvent: {}
[7.423552] (-) TimerEvent: {}
[7.523836] (-) TimerEvent: {}
[7.624117] (-) TimerEvent: {}
[7.724549] (-) TimerEvent: {}
[7.824817] (-) TimerEvent: {}
[7.925089] (-) TimerEvent: {}
[8.025361] (-) TimerEvent: {}
[8.125575] (-) TimerEvent: {}
[8.225814] (-) TimerEvent: {}
[8.326049] (-) TimerEvent: {}
[8.426280] (-) TimerEvent: {}
[8.526591] (-) TimerEvent: {}
[8.626893] (-) TimerEvent: {}
[8.727186] (-) TimerEvent: {}
[8.827487] (-) TimerEvent: {}
[8.927825] (-) TimerEvent: {}
[9.028125] (-) TimerEvent: {}
[9.128404] (-) TimerEvent: {}
[9.228693] (-) TimerEvent: {}
[9.328975] (-) TimerEvent: {}
[9.429263] (-) TimerEvent: {}
[9.529560] (-) TimerEvent: {}
[9.629814] (-) TimerEvent: {}
[9.730025] (-) TimerEvent: {}
[9.830205] (-) TimerEvent: {}
[9.930389] (-) TimerEvent: {}
[10.030601] (-) TimerEvent: {}
[10.130935] (-) TimerEvent: {}
[10.231227] (-) TimerEvent: {}
[10.331483] (-) TimerEvent: {}
[10.431801] (-) TimerEvent: {}
[10.532023] (-) TimerEvent: {}
[10.632328] (-) TimerEvent: {}
[10.732549] (-) TimerEvent: {}
[10.832805] (-) TimerEvent: {}
[10.933044] (-) TimerEvent: {}
[11.033294] (-) TimerEvent: {}
[11.133642] (-) TimerEvent: {}
[11.234017] (-) TimerEvent: {}
[11.334252] (-) TimerEvent: {}
[11.434581] (-) TimerEvent: {}
[11.534931] (-) TimerEvent: {}
[11.635132] (-) TimerEvent: {}
[11.735326] (-) TimerEvent: {}
[11.835580] (-) TimerEvent: {}
[11.935820] (-) TimerEvent: {}
[12.036032] (-) TimerEvent: {}
[12.136258] (-) TimerEvent: {}
[12.236485] (-) TimerEvent: {}
[12.336722] (-) TimerEvent: {}
[12.437058] (-) TimerEvent: {}
[12.537291] (-) TimerEvent: {}
[12.637552] (-) TimerEvent: {}
[12.737773] (-) TimerEvent: {}
[12.802632] (unitree_guide_controller) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX shared library libunitree_guide_controller.so\x1b[0m\n'}
[12.837971] (-) TimerEvent: {}
[12.938294] (-) TimerEvent: {}
[12.949131] (unitree_guide_controller) StdoutLine: {'line': b'[100%] Built target unitree_guide_controller\n'}
[12.959370] (unitree_guide_controller) CommandEnded: {'returncode': 0}
[12.959790] (unitree_guide_controller) JobProgress: {'identifier': 'unitree_guide_controller', 'progress': 'install'}
[12.960027] (unitree_guide_controller) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/unitree_guide_controller'], 'cwd': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws/src/ocs2_ros2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3262'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3262,unix/cg215:/tmp/.ICE-unix/3262'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/5879f753_c56b_4b65_b0a0_49e4029d0347'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.134'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/unitree_guide_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=2f543480cffe45c93423bc146805afa3'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[12.965503] (unitree_guide_controller) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[12.965800] (unitree_guide_controller) StdoutLine: {'line': b'-- Execute custom install script\n'}
[12.965997] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//unitree_guide_controller.xml\n'}
[12.966181] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateBalanceTest.h\n'}
[12.966306] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFixedStand.h\n'}
[12.966451] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFreeStand.h\n'}
[12.966481] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateSwingTest.h\n'}
[12.966563] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateTrotting.h\n'}
[12.966594] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/UnitreeGuideController.h\n'}
[12.966622] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTools.h\n'}
[12.966650] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTypes.h\n'}
[12.966698] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/BalanceCtrl.h\n'}
[12.966727] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/CtrlComponent.h\n'}
[12.966778] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/Estimator.h\n'}
[12.966806] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/LowPassFilter.h\n'}
[12.966834] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/FeetEndCalc.h\n'}
[12.966896] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/GaitGenerator.h\n'}
[12.966925] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/WaveGenerator.h\n'}
[12.966956] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/QuadrupedRobot.h\n'}
[12.966981] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/RobotLeg.h\n'}
[12.967022] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo.launch.py\n'}
[12.967065] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo_classic.launch.py\n'}
[12.967107] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/mujoco.launch.py\n'}
[12.967187] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.sh\n'}
[12.967254] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.dsv\n'}
[12.967329] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/package_run_dependencies/unitree_guide_controller\n'}
[12.967398] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/parent_prefix_path/unitree_guide_controller\n'}
[12.967467] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.sh\n'}
[12.967529] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.dsv\n'}
[12.967590] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.sh\n'}
[12.967654] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.dsv\n'}
[12.967713] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.bash\n'}
[12.967774] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.sh\n'}
[12.967833] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.zsh\n'}
[12.967894] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.dsv\n'}
[12.967943] (unitree_guide_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv\n'}
[12.974307] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/packages/unitree_guide_controller\n'}
[12.974403] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/unitree_guide_controller\n'}
[12.974454] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[12.974496] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_targets-extras.cmake\n'}
[12.974563] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig.cmake\n'}
[12.974611] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig-version.cmake\n'}
[12.974680] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.xml\n'}
[12.975257] (unitree_guide_controller) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so\n'}
[12.976641] (unitree_guide_controller) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so" to ""\n'}
[12.976702] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport.cmake\n'}
[12.976729] (unitree_guide_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport-release.cmake\n'}
[12.978027] (unitree_guide_controller) CommandEnded: {'returncode': 0}
[12.984349] (unitree_guide_controller) JobEnded: {'identifier': 'unitree_guide_controller', 'rc': 0}
[12.984628] (-) EventReactorShutdown: {}
