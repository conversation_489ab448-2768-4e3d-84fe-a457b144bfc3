[0.010s] Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[0.028s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.115s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.119s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.140s] -- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)
[0.151s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.153s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.157s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.162s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.169s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.267s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.268s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.280s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.286s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.300s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.308s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.315s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.347s] -- Found realtime_tools: 2.12.0 (/opt/ros/humble/share/realtime_tools/cmake)
[0.354s] -- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)
[0.359s] -- Found controller_common: 0.0.0 (/home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake)
[0.360s] -- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
[0.361s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.362s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.369s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.416s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.416s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/include>;/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/src
[0.417s] -- Configured cppcheck exclude dirs and/or files: 
[0.417s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.417s] -- Added test 'lint_cmake' to check CMake code style
[0.417s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.419s] -- Added test 'uncrustify' to check C / C++ code style
[0.419s] -- Configured uncrustify additional arguments: 
[0.419s] -- Added test 'xmllint' to check XML markup files
[0.420s] -- Configuring done
[0.438s] -- Generating done
[0.441s] -- Build files have been written to: /home/<USER>/ros2_ws/build/unitree_guide_controller
[0.475s] [35m[1mConsolidate compiler generated dependencies of target unitree_guide_controller[0m
[0.508s] [  5%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateSwingTest.cpp.o[0m
[0.508s] [ 11%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/UnitreeGuideController.cpp.o[0m
[0.508s] [ 17%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFixedStand.cpp.o[0m
[0.510s] [ 23%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFreeStand.cpp.o[0m
[0.510s] [ 29%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/RobotLeg.cpp.o[0m
[0.511s] [ 35%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/QuadrupedRobot.cpp.o[0m
[0.511s] [ 41%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateBalanceTest.cpp.o[0m
[0.512s] [ 52%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateTrotting.cpp.o[0m
[0.512s] [ 52%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/LowPassFilter.cpp.o[0m
[0.512s] [ 58%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/Estimator.cpp.o[0m
[0.513s] [ 64%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/WaveGenerator.cpp.o[0m
[0.514s] [ 70%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/Array.cc.o[0m
[0.514s] [ 76%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/BalanceCtrl.cpp.o[0m
[0.515s] [ 82%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/QuadProg++.cc.o[0m
[0.516s] [ 88%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/FeetEndCalc.cpp.o[0m
[0.518s] [ 94%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/GaitGenerator.cpp.o[0m
[12.393s] [100%] [32m[1mLinking CXX shared library libunitree_guide_controller.so[0m
[12.540s] [100%] Built target unitree_guide_controller
[12.550s] Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[12.551s] Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[12.556s] -- Install configuration: "Release"
[12.557s] -- Execute custom install script
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//unitree_guide_controller.xml
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateBalanceTest.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFixedStand.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFreeStand.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateSwingTest.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateTrotting.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/UnitreeGuideController.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTools.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTypes.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/BalanceCtrl.h
[12.557s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/CtrlComponent.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/Estimator.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/LowPassFilter.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/FeetEndCalc.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/GaitGenerator.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/WaveGenerator.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/QuadrupedRobot.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/RobotLeg.h
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo.launch.py
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo_classic.launch.py
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/mujoco.launch.py
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.sh
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.dsv
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/package_run_dependencies/unitree_guide_controller
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/parent_prefix_path/unitree_guide_controller
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.sh
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.dsv
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.sh
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.dsv
[12.558s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.bash
[12.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.sh
[12.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.zsh
[12.559s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.dsv
[12.559s] -- Symlinking: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/packages/unitree_guide_controller
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/unitree_guide_controller
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_targets-extras.cmake
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig.cmake
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig-version.cmake
[12.565s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.xml
[12.566s] -- Installing: /home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so
[12.567s] -- Set runtime path of "/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so" to ""
[12.567s] -- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport.cmake
[12.567s] -- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport-release.cmake
[12.569s] Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
