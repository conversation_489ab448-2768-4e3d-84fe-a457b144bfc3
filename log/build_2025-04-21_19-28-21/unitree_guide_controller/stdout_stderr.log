-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
-- Found controller_interface: 2.49.0 (/opt/ros/humble/share/controller_interface/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found realtime_tools: 2.12.0 (/opt/ros/humble/share/realtime_tools/cmake)
-- Found control_input_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/cmake)
-- Found controller_common: 0.0.0 (/home/<USER>/ros2_ws/install/controller_common/share/controller_common/cmake)
-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/include>;/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller/src
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/unitree_guide_controller
[35m[1mConsolidate compiler generated dependencies of target unitree_guide_controller[0m
[  5%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateSwingTest.cpp.o[0m
[ 11%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/UnitreeGuideController.cpp.o[0m
[ 17%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFixedStand.cpp.o[0m
[ 23%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateFreeStand.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/RobotLeg.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/robot/QuadrupedRobot.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateBalanceTest.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/FSM/StateTrotting.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/LowPassFilter.cpp.o[0m
[ 58%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/Estimator.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/WaveGenerator.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/Array.cc.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/control/BalanceCtrl.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/quadProgpp/QuadProg++.cc.o[0m
[ 88%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/FeetEndCalc.cpp.o[0m
[ 94%] [32mBuilding CXX object CMakeFiles/unitree_guide_controller.dir/src/gait/GaitGenerator.cpp.o[0m
[100%] [32m[1mLinking CXX shared library libunitree_guide_controller.so[0m
[100%] Built target unitree_guide_controller
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//unitree_guide_controller.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateBalanceTest.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFixedStand.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateFreeStand.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateSwingTest.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/FSM/StateTrotting.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/UnitreeGuideController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTools.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/common/mathTypes.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/BalanceCtrl.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/CtrlComponent.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/Estimator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/control/LowPassFilter.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/FeetEndCalc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/GaitGenerator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/gait/WaveGenerator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/QuadrupedRobot.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/include/unitree_guide_controller/unitree_guide_controller/robot/RobotLeg.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/gazebo_classic.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller//launch/mujoco.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/package_run_dependencies/unitree_guide_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/parent_prefix_path/unitree_guide_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/packages/unitree_guide_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/unitree_guide_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/unitree_guide_controllerConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.xml
-- Installing: /home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so
-- Set runtime path of "/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/unitree_guide_controller/libunitree_guide_controller.so" to ""
-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/cmake/export_unitree_guide_controllerExport-release.cmake
