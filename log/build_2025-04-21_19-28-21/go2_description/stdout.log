-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/go2_description
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/calf_mirror.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/foot.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/hip.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/thigh_mirror.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//meshes/trunk.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/const.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/gazebo_classic.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/leg.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/materials.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/robot.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/ros2_control.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//xacro/transmission.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/gazebo_rl_control.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//launch/visualize.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/gazebo.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/himloco/config.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/himloco/himloco.pt
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/config.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/legged_gym/policy.pt
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/gait.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/reference.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/ocs2/task.info
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/robot_control.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//config/visualize_urdf.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description//urdf/robot.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/package_run_dependencies/go2_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/parent_prefix_path/go2_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/ament_index/resource_index/packages/go2_description
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/cmake/go2_descriptionConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.xml
