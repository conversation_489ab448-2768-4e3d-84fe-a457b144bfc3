[0.048s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-up-to', 'unitree_guide_controller', 'go2_description', 'keyboard_input', '--symlink-install']
[0.048s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=['unitree_guide_controller', 'go2_description', 'keyboard_input'], packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7840be169e10>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7840be169990>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7840be169990>>)
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.135s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ros'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['cmake', 'python']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'cmake'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['python_setup_py']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python_setup_py'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/总结) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams/零散的/感知规划控制) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ros'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python_setup_py'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ros'
[0.148s] DEBUG:colcon.colcon_core.package_identification:Package 'src/elevation_map_converter' with type 'ros.ament_cmake' and name 'elevation_map_converter'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ros'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['cmake', 'python']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'cmake'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['python_setup_py']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python_setup_py'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ros'
[0.150s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_ballbot_mpcnet'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ros'
[0.150s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_mpcnet'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_pkg']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_pkg'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_meta']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_meta'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ros']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ros'
[0.151s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core' with type 'ros.ament_cmake' and name 'ocs2_mpcnet_core'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ros'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['cmake', 'python']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'cmake'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['python_setup_py']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python_setup_py'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ros'
[0.152s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands' with type 'ros.ament_cmake' and name 'ocs2_anymal_commands'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore_ament_install'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_pkg']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_pkg'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_meta']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ros'
[0.153s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_loopshaping_mpc'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ignore', 'ignore_ament_install']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore_ament_install'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_pkg']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_pkg'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_meta']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ros'
[0.154s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models' with type 'ros.ament_cmake' and name 'ocs2_anymal_models'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore_ament_install'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_pkg']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_pkg'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_meta']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_meta'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ros']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ros'
[0.155s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_mpc'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ignore', 'ignore_ament_install']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore_ament_install'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_pkg']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_pkg'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_meta']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_meta'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ros']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ros'
[0.156s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_interface'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore_ament_install'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ros'
[0.157s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_loopshaping_interface'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ros'
[0.158s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface' with type 'ros.ament_cmake' and name 'ocs2_switched_model_interface'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore_ament_install'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_pkg']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_pkg'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_meta']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_meta'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ros']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ros'
[0.158s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs' with type 'ros.ament_cmake' and name 'ocs2_switched_model_msgs'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ignore', 'ignore_ament_install']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore_ament_install'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_pkg']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_pkg'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_meta']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_meta'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ros']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ros'
[0.159s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model' with type 'ros.ament_cmake' and name 'segmented_planes_terrain_model'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore_ament_install'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_pkg']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_pkg'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_meta']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_meta'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ros']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ros']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ros'
[0.160s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_raisim'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ros'
[0.166s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core' with type 'ros.ament_cmake' and name 'ocs2_raisim_core'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ros'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['python_setup_py']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ros'
[0.168s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot' with type 'ros.ament_cmake' and name 'ocs2_ballbot'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ros'
[0.169s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros' with type 'ros.ament_cmake' and name 'ocs2_ballbot_ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ros'
[0.170s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole' with type 'ros.ament_cmake' and name 'ocs2_cartpole'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ros'
[0.170s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros' with type 'ros.ament_cmake' and name 'ocs2_cartpole_ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ros'
[0.171s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator' with type 'ros.ament_cmake' and name 'ocs2_double_integrator'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ros'
[0.172s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros' with type 'ros.ament_cmake' and name 'ocs2_double_integrator_ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot' with type 'ros.ament_cmake' and name 'ocs2_legged_robot'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_ros'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator_ros'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor' with type 'ros.ament_cmake' and name 'ocs2_quadrotor'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros' with type 'ros.ament_cmake' and name 'ocs2_quadrotor_ros'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ros'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['cmake', 'python']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'cmake'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['python_setup_py']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python_setup_py'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_core' with type 'ros.ament_cmake' and name 'ocs2_core'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_oc' with type 'ros.ament_cmake' and name 'ocs2_oc'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_thirdparty' with type 'ros.ament_cmake' and name 'ocs2_thirdparty'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ros'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['cmake', 'python']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'cmake'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['python_setup_py']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python_setup_py'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ddp' with type 'ros.ament_cmake' and name 'ocs2_ddp'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ipm' with type 'ros.ament_cmake' and name 'ocs2_ipm'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_mpc' with type 'ros.ament_cmake' and name 'ocs2_mpc'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_qp_solver' with type 'ros.ament_cmake' and name 'ocs2_qp_solver'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_slp' with type 'ros.ament_cmake' and name 'ocs2_slp'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ros'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['cmake', 'python']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'cmake'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['python_setup_py']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python_setup_py'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon' with type 'ros.ament_cmake' and name 'blasfeo_colcon'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon' with type 'ros.ament_cmake' and name 'hpipm_colcon'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ros'
[0.186s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp' with type 'ros.ament_cmake' and name 'ocs2_sqp'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ros'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['cmake', 'python']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'cmake'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['python_setup_py']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python_setup_py'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_msgs' with type 'ros.ament_cmake' and name 'ocs2_msgs'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ros'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['cmake', 'python']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'cmake'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['python_setup_py']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python_setup_py'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ros'
[0.188s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model' with type 'ros.ament_cmake' and name 'ocs2_centroidal_model'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ros'
[0.189s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface' with type 'ros.ament_cmake' and name 'ocs2_pinocchio_interface'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_meta'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ros']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ros'
[0.189s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision' with type 'ros.ament_cmake' and name 'ocs2_self_collision'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ros'
[0.190s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization' with type 'ros.ament_cmake' and name 'ocs2_self_collision_visualization'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ros'
[0.191s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation' with type 'ros.ament_cmake' and name 'ocs2_sphere_approximation'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ros'
[0.191s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_python_interface' with type 'ros.ament_cmake' and name 'ocs2_python_interface'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ros'
[0.192s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_robotic_tools' with type 'ros.ament_cmake' and name 'ocs2_robotic_tools'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ros'
[0.193s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_ros_interfaces' with type 'ros.ament_cmake' and name 'ocs2_ros_interfaces'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ros'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['cmake', 'python']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'cmake'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['python_setup_py']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python_setup_py'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/grid_map_sdf' with type 'ros.ament_cmake' and name 'grid_map_sdf'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/ocs2_robotic_assets' with type 'ros.ament_cmake' and name 'ocs2_robotic_assets'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ros'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['cmake', 'python']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'cmake'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['python_setup_py']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python_setup_py'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon' with type 'ros.ament_cmake' and name 'cgal5_colcon'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition' with type 'ros.ament_cmake' and name 'convex_plane_decomposition'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ros'
[0.197s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_msgs'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ros'
[0.198s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ros'
[0.199s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl' with type 'ros.ament_cmake' and name 'grid_map_filters_rsl'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'python'
[0.201s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qpoases_vendor' with type 'cmake' and name 'qpOASES'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ros'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['cmake', 'python']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['python_setup_py']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python_setup_py'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ros'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['cmake', 'python']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['python_setup_py']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python_setup_py'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ros'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['cmake', 'python']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'cmake'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python_setup_py'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ros'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['cmake', 'python']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'cmake'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python_setup_py'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ros'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['cmake', 'python']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'cmake'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python_setup_py'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/control_input_msgs' with type 'ros.ament_cmake' and name 'control_input_msgs'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/joystick_input' with type 'ros.ament_cmake' and name 'joystick_input'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/keyboard_input' with type 'ros.ament_cmake' and name 'keyboard_input'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/unitree_joystick_input) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/unitree_joystick_input' with type 'ros.ament_cmake' and name 'unitree_joystick_input'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ros'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['cmake', 'python']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'cmake'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['python_setup_py']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python_setup_py'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ros'
[0.206s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/leg_pd_controller' with type 'ros.ament_cmake' and name 'leg_pd_controller'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ros'
[0.207s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller' with type 'ros.ament_cmake' and name 'ocs2_quadruped_controller'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ros'
[0.208s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/rl_quadruped_controller' with type 'ros.ament_cmake' and name 'rl_quadruped_controller'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ros'
[0.209s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/unitree_guide_controller' with type 'ros.ament_cmake' and name 'unitree_guide_controller'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['python_setup_py']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['cmake', 'python']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'cmake'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['python_setup_py']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python_setup_py'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ros'
[0.210s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description' with type 'ros.ament_cmake' and name 'anymal_c_description'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ros'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['cmake', 'python']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'cmake'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['python_setup_py']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ros'
[0.211s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description' with type 'ros.ament_cmake' and name 'lite3_description'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description' with type 'ros.ament_cmake' and name 'x30_description'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['cmake', 'python']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'cmake'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['python_setup_py']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python_setup_py'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/a1_description' with type 'ros.ament_cmake' and name 'a1_description'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ros'
[0.213s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description' with type 'ros.ament_cmake' and name 'aliengo_description'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/b2_description' with type 'ros.ament_cmake' and name 'b2_description'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go1_description' with type 'ros.ament_cmake' and name 'go1_description'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go2_description' with type 'ros.ament_cmake' and name 'go2_description'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ros'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['cmake', 'python']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'cmake'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python_setup_py'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description' with type 'ros.ament_cmake' and name 'cyberdog_description'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ros'
[0.217s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware' with type 'ros.ament_cmake' and name 'gz_quadruped_hardware'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ros'
[0.218s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco' with type 'ros.ament_cmake' and name 'hardware_unitree_mujoco'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ros'
[0.219s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/controller_common' with type 'ros.ament_cmake' and name 'controller_common'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ros'
[0.219s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/gz_quadruped_playground' with type 'ros.ament_cmake' and name 'gz_quadruped_playground'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ros'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/qpoases_colcon' with type 'ros.ament_cmake' and name 'qpoases_colcon'
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_loopshaping_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_loopshaping_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_controller' in 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_raisim' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadruped_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadrotor_ros' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mobile_manipulator_ros' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot_ros' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_double_integrator_ros' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot_ros' in 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'segmented_planes_terrain_model' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model'
[0.246s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_self_collision_visualization' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_quadrotor' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mpcnet_core' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mobile_manipulator' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_legged_robot' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_double_integrator' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_cartpole_ros' in 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ballbot' in 'src/ocs2_ros2/basic examples/ocs2_ballbot'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_models' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_anymal_commands' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_switched_model_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_sqp' in 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_sphere_approximation' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_self_collision' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_python_interface' in 'src/ocs2_ros2/robotics/ocs2_python_interface'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ipm' in 'src/ocs2_ros2/mpc/ocs2_ipm'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_centroidal_model' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_cartpole' in 'src/ocs2_ros2/basic examples/ocs2_cartpole'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_slp' in 'src/ocs2_ros2/mpc/ocs2_slp'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ros_interfaces' in 'src/ocs2_ros2/robotics/ocs2_ros_interfaces'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_pinocchio_interface' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_ddp' in 'src/ocs2_ros2/mpc/ocs2_ddp'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'hpipm_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_robotic_tools' in 'src/ocs2_ros2/robotics/ocs2_robotic_tools'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_raisim_core' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_qp_solver' in 'src/ocs2_ros2/mpc/ocs2_qp_solver'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_mpc' in 'src/ocs2_ros2/mpc/ocs2_mpc'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'rl_quadruped_controller' in 'src/quadruped_ros2_control/controllers/rl_quadruped_controller'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_oc' in 'src/ocs2_ros2/core/ocs2_oc'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition_ros' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'unitree_joystick_input' in 'src/quadruped_ros2_control/commands/unitree_joystick_input'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_switched_model_msgs' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_core' in 'src/ocs2_ros2/core/ocs2_core'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'joystick_input' in 'src/quadruped_ros2_control/commands/joystick_input'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'gz_quadruped_playground' in 'src/quadruped_ros2_control/libraries/gz_quadruped_playground'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'x30_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'qpoases_colcon' in 'src/quadruped_ros2_control/libraries/qpoases_colcon'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'qpOASES' in 'src/qpoases_vendor'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_thirdparty' in 'src/ocs2_ros2/core/ocs2_thirdparty'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_robotic_assets' in 'src/ocs2_ros2/submodules/ocs2_robotic_assets'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'ocs2_msgs' in 'src/ocs2_ros2/robotics/ocs2_msgs'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'lite3_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'leg_pd_controller' in 'src/quadruped_ros2_control/controllers/leg_pd_controller'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'hardware_unitree_mujoco' in 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'gz_quadruped_hardware' in 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'grid_map_sdf' in 'src/ocs2_ros2/submodules/grid_map_sdf'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'grid_map_filters_rsl' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'go1_description' in 'src/quadruped_ros2_control/descriptions/unitree/go1_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'elevation_map_converter' in 'src/elevation_map_converter'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'cyberdog_description' in 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'convex_plane_decomposition_msgs' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'cgal5_colcon' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'blasfeo_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'b2_description' in 'src/quadruped_ros2_control/descriptions/unitree/b2_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'anymal_c_description' in 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'aliengo_description' in 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description'
[0.247s] INFO:colcon.colcon_core.package_selection:Skipping package 'a1_description' in 'src/quadruped_ros2_control/descriptions/unitree/a1_description'
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.249s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 423 installed packages in /opt/ros/humble
[0.250s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_target' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'control_input_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.277s] DEBUG:colcon.colcon_core.verb:Building package 'control_input_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/control_input_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/control_input_msgs', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs', 'symlink_install': True, 'test_result_base': None}
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_target' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.277s] Level 5:colcon.colcon_core.verb:set package 'go2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.277s] DEBUG:colcon.colcon_core.verb:Building package 'go2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/go2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/go2_description', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description', 'symlink_install': True, 'test_result_base': None}
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_target' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_clean_cache' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_clean_first' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'cmake_force_configure' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'ament_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'catkin_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'controller_common' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.278s] DEBUG:colcon.colcon_core.verb:Building package 'controller_common' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/controller_common', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/controller_common', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common', 'symlink_install': True, 'test_result_base': None}
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_target' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_clean_cache' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_clean_first' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'cmake_force_configure' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'ament_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'catkin_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'keyboard_input' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.278s] DEBUG:colcon.colcon_core.verb:Building package 'keyboard_input' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/keyboard_input', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/keyboard_input', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input', 'symlink_install': True, 'test_result_base': None}
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_target' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_clean_cache' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_clean_first' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'cmake_force_configure' from command line to 'False'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'ament_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'catkin_cmake_args' from command line to 'None'
[0.278s] Level 5:colcon.colcon_core.verb:set package 'unitree_guide_controller' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.278s] DEBUG:colcon.colcon_core.verb:Building package 'unitree_guide_controller' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/unitree_guide_controller', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/unitree_guide_controller', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller', 'symlink_install': True, 'test_result_base': None}
[0.278s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.279s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.279s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs' with build type 'ament_cmake'
[0.279s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/control_input_msgs'
[0.282s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.282s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.282s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.284s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description' with build type 'ament_cmake'
[0.284s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description'
[0.284s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.284s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.289s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/control_input_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/control_input_msgs -- -j32 -l32
[0.293s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/go2_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/go2_description
[0.452s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/go2_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/go2_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/install/go2_description
[0.452s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/go2_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go2_description -- -j32 -l32
[0.487s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/go2_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/go2_description -- -j32 -l32
[0.496s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/go2_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go2_description
[0.519s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(go2_description)
[0.519s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/go2_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/go2_description
[0.521s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake module files
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake config files
[0.522s] Level 1:colcon.colcon_core.shell:create_environment_hook('go2_description', 'cmake_prefix_path')
[0.522s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.ps1'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.dsv'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.sh'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/pkgconfig/go2_description.pc'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/python3.10/site-packages'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.ps1'
[0.525s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.sh'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.bash'
[0.527s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.zsh'
[0.527s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/go2_description/share/colcon-core/packages/go2_description)
[0.527s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(go2_description)
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake module files
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description' for CMake config files
[0.528s] Level 1:colcon.colcon_core.shell:create_environment_hook('go2_description', 'cmake_prefix_path')
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.ps1'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.dsv'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/hook/cmake_prefix_path.sh'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/pkgconfig/go2_description.pc'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/lib/python3.10/site-packages'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/go2_description/bin'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.ps1'
[0.529s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.dsv'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.sh'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.bash'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/go2_description/share/go2_description/package.zsh'
[0.530s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/go2_description/share/colcon-core/packages/go2_description)
[0.541s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/control_input_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/control_input_msgs -- -j32 -l32
[0.542s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/control_input_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/control_input_msgs
[0.589s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(control_input_msgs)
[0.589s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake module files
[0.590s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/control_input_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/control_input_msgs
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake config files
[0.590s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'cmake_prefix_path')
[0.591s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.ps1'
[0.591s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.dsv'
[0.591s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.sh'
[0.591s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib'
[0.591s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'ld_library_path_lib')
[0.591s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.ps1'
[0.592s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.dsv'
[0.592s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.sh'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/pkgconfig/control_input_msgs.pc'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/python3.10/site-packages'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.ps1'
[0.593s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.sh'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.bash'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.zsh'
[0.594s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/control_input_msgs/share/colcon-core/packages/control_input_msgs)
[0.594s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(control_input_msgs)
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake module files
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs' for CMake config files
[0.595s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'cmake_prefix_path')
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.ps1'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.dsv'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/cmake_prefix_path.sh'
[0.595s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib'
[0.595s] Level 1:colcon.colcon_core.shell:create_environment_hook('control_input_msgs', 'ld_library_path_lib')
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.ps1'
[0.596s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.dsv'
[0.596s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/hook/ld_library_path_lib.sh'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/pkgconfig/control_input_msgs.pc'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/lib/python3.10/site-packages'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/control_input_msgs/bin'
[0.596s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.ps1'
[0.597s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.dsv'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.sh'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.bash'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/control_input_msgs/share/control_input_msgs/package.zsh'
[0.597s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/control_input_msgs/share/colcon-core/packages/control_input_msgs)
[0.598s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common' with build type 'ament_cmake'
[0.598s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/controller_common'
[0.598s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.598s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.600s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input' with build type 'ament_cmake'
[0.600s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/commands/keyboard_input'
[0.600s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.600s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.606s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/controller_common': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/controller_common -- -j32 -l32
[0.607s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[0.658s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/controller_common' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/controller_common -- -j32 -l32
[0.660s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/controller_common': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/controller_common
[0.680s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(controller_common)
[0.680s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake module files
[0.680s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/controller_common' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/controller_common
[0.681s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake config files
[0.681s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'cmake_prefix_path')
[0.681s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.ps1'
[0.681s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.dsv'
[0.681s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.sh'
[0.682s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib'
[0.682s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'ld_library_path_lib')
[0.682s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.ps1'
[0.682s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.dsv'
[0.682s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.sh'
[0.682s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[0.682s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/pkgconfig/controller_common.pc'
[0.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/python3.10/site-packages'
[0.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[0.683s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.ps1'
[0.683s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv'
[0.683s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.sh'
[0.684s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.bash'
[0.684s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.zsh'
[0.684s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/controller_common/share/colcon-core/packages/controller_common)
[0.684s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(controller_common)
[0.684s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake module files
[0.685s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common' for CMake config files
[0.685s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'cmake_prefix_path')
[0.685s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.ps1'
[0.685s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.dsv'
[0.685s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/cmake_prefix_path.sh'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib'
[0.686s] Level 1:colcon.colcon_core.shell:create_environment_hook('controller_common', 'ld_library_path_lib')
[0.686s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.ps1'
[0.686s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.dsv'
[0.686s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/hook/ld_library_path_lib.sh'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/pkgconfig/controller_common.pc'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/lib/python3.10/site-packages'
[0.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/controller_common/bin'
[0.687s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.ps1'
[0.687s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.dsv'
[0.687s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.sh'
[0.687s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.bash'
[0.688s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/controller_common/share/controller_common/package.zsh'
[0.688s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/controller_common/share/colcon-core/packages/controller_common)
[0.688s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller' with build type 'ament_cmake'
[0.688s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/unitree_guide_controller'
[0.688s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.688s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.699s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[5.059s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/keyboard_input -- -j32 -l32
[5.062s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/keyboard_input': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[5.081s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyboard_input)
[5.082s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake module files
[5.082s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/keyboard_input' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/keyboard_input
[5.083s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake config files
[5.083s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyboard_input', 'cmake_prefix_path')
[5.084s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.ps1'
[5.084s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.dsv'
[5.085s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.sh'
[5.085s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib'
[5.085s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[5.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/pkgconfig/keyboard_input.pc'
[5.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/python3.10/site-packages'
[5.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[5.087s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.ps1'
[5.087s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv'
[5.088s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.sh'
[5.088s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.bash'
[5.089s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.zsh'
[5.089s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/keyboard_input/share/colcon-core/packages/keyboard_input)
[5.090s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyboard_input)
[5.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake module files
[5.091s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input' for CMake config files
[5.091s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyboard_input', 'cmake_prefix_path')
[5.091s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.ps1'
[5.092s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.dsv'
[5.092s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/hook/cmake_prefix_path.sh'
[5.093s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib'
[5.093s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[5.093s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/pkgconfig/keyboard_input.pc'
[5.094s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/lib/python3.10/site-packages'
[5.094s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/keyboard_input/bin'
[5.094s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.ps1'
[5.096s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv'
[5.096s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.sh'
[5.097s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.bash'
[5.097s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.zsh'
[5.098s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/keyboard_input/share/colcon-core/packages/keyboard_input)
[13.239s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/unitree_guide_controller -- -j32 -l32
[13.240s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/unitree_guide_controller': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[13.257s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(unitree_guide_controller)
[13.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake module files
[13.258s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/unitree_guide_controller' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins PYTHONPATH=/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/unitree_guide_controller
[13.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake config files
[13.258s] Level 1:colcon.colcon_core.shell:create_environment_hook('unitree_guide_controller', 'cmake_prefix_path')
[13.258s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.ps1'
[13.259s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.dsv'
[13.259s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.sh'
[13.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib'
[13.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[13.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/pkgconfig/unitree_guide_controller.pc'
[13.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/python3.10/site-packages'
[13.260s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[13.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.ps1'
[13.260s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv'
[13.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.sh'
[13.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.bash'
[13.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.zsh'
[13.261s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/unitree_guide_controller/share/colcon-core/packages/unitree_guide_controller)
[13.261s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(unitree_guide_controller)
[13.261s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake module files
[13.261s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller' for CMake config files
[13.262s] Level 1:colcon.colcon_core.shell:create_environment_hook('unitree_guide_controller', 'cmake_prefix_path')
[13.262s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.ps1'
[13.262s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.dsv'
[13.262s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/hook/cmake_prefix_path.sh'
[13.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib'
[13.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[13.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/pkgconfig/unitree_guide_controller.pc'
[13.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib/python3.10/site-packages'
[13.263s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/unitree_guide_controller/bin'
[13.263s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.ps1'
[13.263s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.dsv'
[13.263s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.sh'
[13.263s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.bash'
[13.264s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/unitree_guide_controller/share/unitree_guide_controller/package.zsh'
[13.264s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/unitree_guide_controller/share/colcon-core/packages/unitree_guide_controller)
[13.264s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[13.264s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[13.264s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[13.264s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[13.269s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[13.269s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[13.269s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[13.278s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[13.278s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.ps1'
[13.279s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_ps1.py'
[13.280s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.ps1'
[13.280s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.sh'
[13.281s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_sh.py'
[13.281s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.sh'
[13.281s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.bash'
[13.282s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.bash'
[13.282s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.zsh'
[13.283s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.zsh'
