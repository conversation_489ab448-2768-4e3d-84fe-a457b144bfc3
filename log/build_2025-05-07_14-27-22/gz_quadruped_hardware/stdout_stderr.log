-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
-- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
-- Compiling against Gazebo fortress
[31mCMake Error at CMakeLists.txt:34 (find_package):
  By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "ignition-gazebo6", but CMake did not find one.

  Could not find a package configuration file provided by "ignition-gazebo6"
  with any of the following names:

    ignition-gazebo6Config.cmake
    ignition-gazebo6-config.cmake

  Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or
  set "ignition-gazebo6_DIR" to a directory containing one of the above
  files.  If "ignition-gazebo6" provides a separate development package or
  SDK, be sure it has been installed.

[0m
-- Configuring incomplete, errors occurred!
See also "/home/<USER>/ros2_ws/build/gz_quadruped_hardware/CMakeFiles/CMakeOutput.log".
gmake: *** [Makefile:308: cmake_check_build_system] Error 1
