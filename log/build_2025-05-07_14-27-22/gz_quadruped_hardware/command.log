Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
