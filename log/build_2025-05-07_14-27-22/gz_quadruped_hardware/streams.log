[0.019s] Invoking command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
[0.057s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.181s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.187s] -- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[0.188s] -- Found controller_manager: 2.49.0 (/opt/ros/humble/share/controller_manager/cmake)
[0.213s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.215s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.221s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.232s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.245s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.359s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.361s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.374s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.384s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.404s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.414s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.432s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.485s] -- Found yaml_cpp_vendor: 8.0.2 (/opt/ros/humble/share/yaml_cpp_vendor/cmake)
[0.495s] -- Compiling against Gazebo fortress
[0.505s] [31mCMake Error at CMakeLists.txt:34 (find_package):
[0.505s]   By not providing "Findignition-gazebo6.cmake" in CMAKE_MODULE_PATH this
[0.505s]   project has asked CMake to find a package configuration file provided by
[0.505s]   "ignition-gazebo6", but CMake did not find one.
[0.505s] 
[0.505s]   Could not find a package configuration file provided by "ignition-gazebo6"
[0.505s]   with any of the following names:
[0.505s] 
[0.505s]     ignition-gazebo6Config.cmake
[0.505s]     ignition-gazebo6-config.cmake
[0.505s] 
[0.505s]   Add the installation prefix of "ignition-gazebo6" to CMAKE_PREFIX_PATH or
[0.506s]   set "ignition-gazebo6_DIR" to a directory containing one of the above
[0.506s]   files.  If "ignition-gazebo6" provides a separate development package or
[0.506s]   SDK, be sure it has been installed.
[0.506s] 
[0.506s] [0m
[0.506s] -- Configuring incomplete, errors occurred!
[0.506s] See also "/home/<USER>/ros2_ws/build/gz_quadruped_hardware/CMakeFiles/CMakeOutput.log".
[0.512s] gmake: *** [Makefile:308: cmake_check_build_system] Error 1
[0.515s] Invoked command in '/home/<USER>/ros2_ws/build/gz_quadruped_hardware' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/gz_quadruped_hardware -- -j32 -l32
