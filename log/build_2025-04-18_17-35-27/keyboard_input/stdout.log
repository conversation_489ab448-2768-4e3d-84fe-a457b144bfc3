[100%] Built target keyboard_input
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/lib/keyboard_input/keyboard_input
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/package_run_dependencies/keyboard_input
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/parent_prefix_path/keyboard_input
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/ament_index/resource_index/packages/keyboard_input
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/cmake/keyboard_inputConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/keyboard_input/share/keyboard_input/package.xml
