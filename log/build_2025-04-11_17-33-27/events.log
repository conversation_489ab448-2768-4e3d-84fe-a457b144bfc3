[0.000000] (-) TimerEvent: {}
[0.000147] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000171] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000201] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000216] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000245] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000260] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000273] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000293] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000359] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000374] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000391] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000405] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000464] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000479] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000494] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000508] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000531] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000570] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000607] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000629] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000643] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000683] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000696] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000715] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000728] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000843] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000862] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000899] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000917] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000947] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000969] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000984] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000998] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001012] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001035] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001071] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001085] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001099] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001117] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001146] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001167] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001181] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001195] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001209] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001223] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001256] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001270] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001283] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001297] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001315] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001397] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001410] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001423] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001441] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001455] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001535] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001553] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.001567] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.001632] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.001646] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.001660] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.001799] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002178] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002194] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002207] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002225] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002254] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002323] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002337] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002349] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002361] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002373] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002385] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002397] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002410] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002426] (ocs2_anymal_loopshaping_mpc) JobQueued: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'dependencies': OrderedDict([('blasfeo_colcon', '/home/<USER>/ros2_ws/install/blasfeo_colcon'), ('cgal5_colcon', '/home/<USER>/ros2_ws/install/cgal5_colcon'), ('convex_plane_decomposition_msgs', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs'), ('grid_map_filters_rsl', '/home/<USER>/ros2_ws/install/grid_map_filters_rsl'), ('grid_map_sdf', '/home/<USER>/ros2_ws/install/grid_map_sdf'), ('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('convex_plane_decomposition', '/home/<USER>/ros2_ws/install/convex_plane_decomposition'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_switched_model_msgs', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs'), ('convex_plane_decomposition_ros', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc'), ('ocs2_qp_solver', '/home/<USER>/ros2_ws/install/ocs2_qp_solver'), ('ocs2_robotic_tools', '/home/<USER>/ros2_ws/install/ocs2_robotic_tools'), ('hpipm_colcon', '/home/<USER>/ros2_ws/install/hpipm_colcon'), ('ocs2_ddp', '/home/<USER>/ros2_ws/install/ocs2_ddp'), ('ocs2_pinocchio_interface', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface'), ('ocs2_ros_interfaces', '/home/<USER>/ros2_ws/install/ocs2_ros_interfaces'), ('ocs2_sqp', '/home/<USER>/ros2_ws/install/ocs2_sqp'), ('ocs2_switched_model_interface', '/home/<USER>/ros2_ws/install/ocs2_switched_model_interface'), ('ocs2_anymal_commands', '/home/<USER>/ros2_ws/install/ocs2_anymal_commands'), ('ocs2_anymal_models', '/home/<USER>/ros2_ws/install/ocs2_anymal_models'), ('segmented_planes_terrain_model', '/home/<USER>/ros2_ws/install/segmented_planes_terrain_model'), ('ocs2_quadruped_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_interface'), ('ocs2_anymal_mpc', '/home/<USER>/ros2_ws/install/ocs2_anymal_mpc'), ('ocs2_quadruped_loopshaping_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface')])}
[0.002464] (ocs2_anymal_loopshaping_mpc) JobStarted: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.099783] (-) TimerEvent: {}
[0.130752] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'cmake'}
[0.135887] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'build'}
[0.136278] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.199967] (-) TimerEvent: {}
[0.253269] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 11%] Built target gtest\n'}
[0.257307] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 22%] Built target gtest_main\n'}
[0.264999] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 33%] Built target ocs2_anymal_loopshaping_mpc\n'}
[0.284861] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o\x1b[0m\n'}
[0.297271] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 50%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[0.297558] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 61%] Built target ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[0.298051] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 83%] Built target ocs2_anymal_loopshaping_mpc_test\n'}
[0.300068] (-) TimerEvent: {}
[0.301961] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 94%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[0.400270] (-) TimerEvent: {}
[0.500573] (-) TimerEvent: {}
[0.600872] (-) TimerEvent: {}
[0.701170] (-) TimerEvent: {}
[0.801435] (-) TimerEvent: {}
[0.901660] (-) TimerEvent: {}
[1.001947] (-) TimerEvent: {}
[1.102181] (-) TimerEvent: {}
[1.202378] (-) TimerEvent: {}
[1.302686] (-) TimerEvent: {}
[1.402899] (-) TimerEvent: {}
[1.503108] (-) TimerEvent: {}
[1.603341] (-) TimerEvent: {}
[1.703597] (-) TimerEvent: {}
[1.803937] (-) TimerEvent: {}
[1.904192] (-) TimerEvent: {}
[2.004402] (-) TimerEvent: {}
[2.104641] (-) TimerEvent: {}
[2.204931] (-) TimerEvent: {}
[2.305185] (-) TimerEvent: {}
[2.405434] (-) TimerEvent: {}
[2.505648] (-) TimerEvent: {}
[2.605847] (-) TimerEvent: {}
[2.706059] (-) TimerEvent: {}
[2.806286] (-) TimerEvent: {}
[2.906486] (-) TimerEvent: {}
[3.006716] (-) TimerEvent: {}
[3.106982] (-) TimerEvent: {}
[3.207186] (-) TimerEvent: {}
[3.307375] (-) TimerEvent: {}
[3.407572] (-) TimerEvent: {}
[3.507832] (-) TimerEvent: {}
[3.608084] (-) TimerEvent: {}
[3.708286] (-) TimerEvent: {}
[3.808517] (-) TimerEvent: {}
[3.908716] (-) TimerEvent: {}
[4.008964] (-) TimerEvent: {}
[4.109144] (-) TimerEvent: {}
[4.209279] (-) TimerEvent: {}
[4.309371] (-) TimerEvent: {}
[4.409470] (-) TimerEvent: {}
[4.509620] (-) TimerEvent: {}
[4.609802] (-) TimerEvent: {}
[4.709978] (-) TimerEvent: {}
[4.810210] (-) TimerEvent: {}
[4.910421] (-) TimerEvent: {}
[5.010622] (-) TimerEvent: {}
[5.110860] (-) TimerEvent: {}
[5.211125] (-) TimerEvent: {}
[5.311320] (-) TimerEvent: {}
[5.411530] (-) TimerEvent: {}
[5.511712] (-) TimerEvent: {}
[5.612001] (-) TimerEvent: {}
[5.712334] (-) TimerEvent: {}
[5.764996] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.765118] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:214:36:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass convex_plane_decomposition::PlaneDecompositionPipeline\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KgetPlaneDecomposition\x1b[m\x1b[K\xe2\x80\x99\n'}
[5.765151] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'  214 |         planeDecompositionPipeline.\x1b[01;31m\x1b[KgetPlaneDecomposition\x1b[m\x1b[K());\n'}
[5.765174] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'      |                                    \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.765195] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'compilation terminated due to -Wfatal-errors.\n'}
[5.810159] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1\n'}
[5.810313] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2\n'}
[5.810536] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[5.812053] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 2}
[5.812384] (-) TimerEvent: {}
[5.817242] (ocs2_anymal_loopshaping_mpc) JobEnded: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'rc': 2}
[5.827516] (-) EventReactorShutdown: {}
