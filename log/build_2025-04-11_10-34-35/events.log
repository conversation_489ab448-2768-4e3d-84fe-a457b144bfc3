[0.000000] (-) TimerEvent: {}
[0.000352] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000381] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000399] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000416] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000432] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000448] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000465] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000508] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000623] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000695] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000713] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000787] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000805] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000825] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000842] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000861] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000878] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000894] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000909] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000926] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000957] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001004] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001021] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001062] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001089] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001106] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001126] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001301] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001402] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001450] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001469] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001493] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001530] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001557] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001574] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001592] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001609] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001634] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001658] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001694] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001712] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001734] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001772] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001799] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001817] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001834] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001852] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001898] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001922] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002037] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002061] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002078] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002094] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002110] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002131] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002253] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002274] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002301] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002408] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002430] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002633] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002800] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002866] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002907] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.003256] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.003279] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.003300] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.003318] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003351] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003397] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.003418] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003435] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003452] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003470] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003490] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003512] (ocs2_anymal_loopshaping_mpc) JobQueued: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'dependencies': OrderedDict([('blasfeo_colcon', '/home/<USER>/ros2_ws/install/blasfeo_colcon'), ('cgal5_colcon', '/home/<USER>/ros2_ws/install/cgal5_colcon'), ('convex_plane_decomposition_msgs', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs'), ('grid_map_filters_rsl', '/home/<USER>/ros2_ws/install/grid_map_filters_rsl'), ('grid_map_sdf', '/home/<USER>/ros2_ws/install/grid_map_sdf'), ('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('convex_plane_decomposition', '/home/<USER>/ros2_ws/install/convex_plane_decomposition'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_switched_model_msgs', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs'), ('convex_plane_decomposition_ros', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc'), ('ocs2_qp_solver', '/home/<USER>/ros2_ws/install/ocs2_qp_solver'), ('ocs2_robotic_tools', '/home/<USER>/ros2_ws/install/ocs2_robotic_tools'), ('hpipm_colcon', '/home/<USER>/ros2_ws/install/hpipm_colcon'), ('ocs2_ddp', '/home/<USER>/ros2_ws/install/ocs2_ddp'), ('ocs2_pinocchio_interface', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface'), ('ocs2_ros_interfaces', '/home/<USER>/ros2_ws/install/ocs2_ros_interfaces'), ('ocs2_sqp', '/home/<USER>/ros2_ws/install/ocs2_sqp'), ('ocs2_switched_model_interface', '/home/<USER>/ros2_ws/install/ocs2_switched_model_interface'), ('ocs2_anymal_commands', '/home/<USER>/ros2_ws/install/ocs2_anymal_commands'), ('ocs2_anymal_models', '/home/<USER>/ros2_ws/install/ocs2_anymal_models'), ('segmented_planes_terrain_model', '/home/<USER>/ros2_ws/install/segmented_planes_terrain_model'), ('ocs2_quadruped_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_interface'), ('ocs2_anymal_mpc', '/home/<USER>/ros2_ws/install/ocs2_anymal_mpc'), ('ocs2_quadruped_loopshaping_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface')])}
[0.003566] (ocs2_anymal_loopshaping_mpc) JobStarted: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.099722] (-) TimerEvent: {}
[0.133520] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'cmake'}
[0.134547] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'build'}
[0.134639] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.195942] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 16%] Built target gtest\n'}
[0.196146] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 22%] Built target gtest_main\n'}
[0.198729] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 33%] Built target ocs2_anymal_loopshaping_mpc\n'}
[0.199794] (-) TimerEvent: {}
[0.242328] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 44%] Built target ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[0.242538] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 66%] Built target ocs2_anymal_loopshaping_mpc_test\n'}
[0.243200] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 77%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[0.248636] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 88%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[0.262256] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_demo.dir/src/keyboard_control/PerceptiveMpcKeyboardDemo.cpp.o\x1b[0m\n'}
[0.299906] (-) TimerEvent: {}
[0.400232] (-) TimerEvent: {}
[0.500521] (-) TimerEvent: {}
[0.600861] (-) TimerEvent: {}
[0.701219] (-) TimerEvent: {}
[0.801509] (-) TimerEvent: {}
[0.901800] (-) TimerEvent: {}
[1.002011] (-) TimerEvent: {}
[1.102297] (-) TimerEvent: {}
[1.202532] (-) TimerEvent: {}
[1.302925] (-) TimerEvent: {}
[1.403219] (-) TimerEvent: {}
[1.503498] (-) TimerEvent: {}
[1.603775] (-) TimerEvent: {}
[1.704121] (-) TimerEvent: {}
[1.804481] (-) TimerEvent: {}
[1.904724] (-) TimerEvent: {}
[2.005010] (-) TimerEvent: {}
[2.105286] (-) TimerEvent: {}
[2.205620] (-) TimerEvent: {}
[2.305898] (-) TimerEvent: {}
[2.406163] (-) TimerEvent: {}
[2.506379] (-) TimerEvent: {}
[2.606598] (-) TimerEvent: {}
[2.706836] (-) TimerEvent: {}
[2.807200] (-) TimerEvent: {}
[2.907460] (-) TimerEvent: {}
[3.007826] (-) TimerEvent: {}
[3.108087] (-) TimerEvent: {}
[3.208332] (-) TimerEvent: {}
[3.308583] (-) TimerEvent: {}
[3.408936] (-) TimerEvent: {}
[3.509299] (-) TimerEvent: {}
[3.609586] (-) TimerEvent: {}
[3.709960] (-) TimerEvent: {}
[3.810297] (-) TimerEvent: {}
[3.910574] (-) TimerEvent: {}
[4.010901] (-) TimerEvent: {}
[4.111259] (-) TimerEvent: {}
[4.211573] (-) TimerEvent: {}
[4.311894] (-) TimerEvent: {}
[4.412157] (-) TimerEvent: {}
[4.512384] (-) TimerEvent: {}
[4.612588] (-) TimerEvent: {}
[4.712789] (-) TimerEvent: {}
[4.812998] (-) TimerEvent: {}
[4.913266] (-) TimerEvent: {}
[5.013506] (-) TimerEvent: {}
[5.113728] (-) TimerEvent: {}
[5.214029] (-) TimerEvent: {}
[5.314233] (-) TimerEvent: {}
[5.414487] (-) TimerEvent: {}
[5.514762] (-) TimerEvent: {}
[5.615051] (-) TimerEvent: {}
[5.715304] (-) TimerEvent: {}
[5.815590] (-) TimerEvent: {}
[5.915842] (-) TimerEvent: {}
[6.016149] (-) TimerEvent: {}
[6.116377] (-) TimerEvent: {}
[6.216626] (-) TimerEvent: {}
[6.316855] (-) TimerEvent: {}
[6.417106] (-) TimerEvent: {}
[6.517341] (-) TimerEvent: {}
[6.617568] (-) TimerEvent: {}
[6.717813] (-) TimerEvent: {}
[6.818049] (-) TimerEvent: {}
[6.918277] (-) TimerEvent: {}
[7.018519] (-) TimerEvent: {}
[7.118866] (-) TimerEvent: {}
[7.219347] (-) TimerEvent: {}
[7.319589] (-) TimerEvent: {}
[7.419814] (-) TimerEvent: {}
[7.520143] (-) TimerEvent: {}
[7.620363] (-) TimerEvent: {}
[7.720617] (-) TimerEvent: {}
[7.820940] (-) TimerEvent: {}
[7.921309] (-) TimerEvent: {}
[8.021545] (-) TimerEvent: {}
[8.121856] (-) TimerEvent: {}
[8.222168] (-) TimerEvent: {}
[8.322435] (-) TimerEvent: {}
[8.422705] (-) TimerEvent: {}
[8.522940] (-) TimerEvent: {}
[8.623204] (-) TimerEvent: {}
[8.723564] (-) TimerEvent: {}
[8.823813] (-) TimerEvent: {}
[8.924261] (-) TimerEvent: {}
[9.024626] (-) TimerEvent: {}
[9.124962] (-) TimerEvent: {}
[9.225303] (-) TimerEvent: {}
[9.325515] (-) TimerEvent: {}
[9.425755] (-) TimerEvent: {}
[9.525993] (-) TimerEvent: {}
[9.626349] (-) TimerEvent: {}
[9.726602] (-) TimerEvent: {}
[9.826856] (-) TimerEvent: {}
[9.927220] (-) TimerEvent: {}
[10.027485] (-) TimerEvent: {}
[10.127822] (-) TimerEvent: {}
[10.228166] (-) TimerEvent: {}
[10.328411] (-) TimerEvent: {}
[10.428651] (-) TimerEvent: {}
[10.528897] (-) TimerEvent: {}
[10.629243] (-) TimerEvent: {}
[10.729464] (-) TimerEvent: {}
[10.829697] (-) TimerEvent: {}
[10.929937] (-) TimerEvent: {}
[11.030162] (-) TimerEvent: {}
[11.130374] (-) TimerEvent: {}
[11.230700] (-) TimerEvent: {}
[11.330982] (-) TimerEvent: {}
[11.367612] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable ocs2_anymal_loopshaping_mpc_perceptive_keyboard_demo\x1b[0m\n'}
[11.431241] (-) TimerEvent: {}
[11.531501] (-) TimerEvent: {}
[11.631834] (-) TimerEvent: {}
[11.732106] (-) TimerEvent: {}
[11.832368] (-) TimerEvent: {}
[11.932614] (-) TimerEvent: {}
[12.032920] (-) TimerEvent: {}
[12.133222] (-) TimerEvent: {}
[12.233605] (-) TimerEvent: {}
[12.333883] (-) TimerEvent: {}
[12.434249] (-) TimerEvent: {}
[12.534494] (-) TimerEvent: {}
[12.634847] (-) TimerEvent: {}
[12.735187] (-) TimerEvent: {}
[12.835476] (-) TimerEvent: {}
[12.935811] (-) TimerEvent: {}
[13.036199] (-) TimerEvent: {}
[13.136423] (-) TimerEvent: {}
[13.202423] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[100%] Built target ocs2_anymal_loopshaping_mpc_perceptive_keyboard_demo\n'}
[13.213892] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 0}
[13.215093] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'install'}
[13.228212] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[13.236522] (-) TimerEvent: {}
[13.238931] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[13.239299] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Execute custom install script\n'}
[13.239386] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/include/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc/AnymalLoopshapingInterface.h\n'}
[13.239528] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[13.239598] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[13.239670] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[13.239743] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/ocs2_anymal_loopshaping_mpc/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_demo\n'}
[13.239964] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/frame_declaration.info\n'}
[13.240034] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/loopshaping.info\n'}
[13.240087] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/multiple_shooting.info\n'}
[13.240137] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/c_series/task.info\n'}
[13.240193] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//config/rviz/demo_config.rviz\n'}
[13.240320] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/anymal_c.launch.py\n'}
[13.240371] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/mpc.launch.py\n'}
[13.240422] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_demo.launch.py\n'}
[13.240473] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc//launch/perceptive_mpc_keyboard_demo.launch.py\n'}
[13.240563] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.sh\n'}
[13.240639] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/library_path.dsv\n'}
[13.240723] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_anymal_loopshaping_mpc\n'}
[13.240803] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_anymal_loopshaping_mpc\n'}
[13.240880] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.sh\n'}
[13.240950] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/ament_prefix_path.dsv\n'}
[13.241023] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.sh\n'}
[13.241100] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/environment/path.dsv\n'}
[13.241181] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.bash\n'}
[13.241259] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.sh\n'}
[13.241337] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.zsh\n'}
[13.241422] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/local_setup.dsv\n'}
[13.241483] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv\n'}
[13.258157] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ament_index/resource_index/packages/ocs2_anymal_loopshaping_mpc\n'}
[13.258271] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[13.258344] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ament_cmake_export_targets-extras.cmake\n'}
[13.258469] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig.cmake\n'}
[13.258522] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/ocs2_anymal_loopshaping_mpcConfig-version.cmake\n'}
[13.258550] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.xml\n'}
[13.258576] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/libocs2_anymal_loopshaping_mpc.a\n'}
[13.258641] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport.cmake\n'}
[13.258670] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/cmake/export_ocs2_anymal_loopshaping_mpcExport-release.cmake\n'}
[13.260106] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 0}
[13.269500] (ocs2_anymal_loopshaping_mpc) JobEnded: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'rc': 0}
[13.269932] (-) EventReactorShutdown: {}
