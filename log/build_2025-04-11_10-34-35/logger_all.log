[0.060s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install', '--packages-select', 'ocs2_anymal_loopshaping_mpc']
[0.060s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['ocs2_anymal_loopshaping_mpc'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x79625e66a2f0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79625e669e70>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79625e669e70>>)
[0.158s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.158s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.159s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'ros'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['cmake', 'python']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'cmake'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extensions ['python_setup_py']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(diagrams) by extension 'python_setup_py'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'ros'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extensions ['python_setup_py']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(drawio) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'ros'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extensions ['python_setup_py']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(output) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/elevation_map_converter) by extension 'ros'
[0.169s] DEBUG:colcon.colcon_core.package_identification:Package 'src/elevation_map_converter' with type 'ros.ament_cmake' and name 'elevation_map_converter'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet) by extension 'ros'
[0.171s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_ballbot_mpcnet'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet) by extension 'ros'
[0.172s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_mpcnet'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core' with type 'ros.ament_cmake' and name 'ocs2_mpcnet_core'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands' with type 'ros.ament_cmake' and name 'ocs2_anymal_commands'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_loopshaping_mpc'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models' with type 'ros.ament_cmake' and name 'ocs2_anymal_models'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc' with type 'ros.ament_cmake' and name 'ocs2_anymal_mpc'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_interface'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface' with type 'ros.ament_cmake' and name 'ocs2_quadruped_loopshaping_interface'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface' with type 'ros.ament_cmake' and name 'ocs2_switched_model_interface'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs' with type 'ros.ament_cmake' and name 'ocs2_switched_model_msgs'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model' with type 'ros.ament_cmake' and name 'segmented_planes_terrain_model'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'ros'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['cmake', 'python']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'cmake'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extensions ['python_setup_py']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim) by extension 'python_setup_py'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_raisim'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core' with type 'ros.ament_cmake' and name 'ocs2_raisim_core'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'ros'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['cmake', 'python']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'cmake'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extensions ['python_setup_py']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples) by extension 'python_setup_py'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot' with type 'ros.ament_cmake' and name 'ocs2_ballbot'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_ballbot_ros) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros' with type 'ros.ament_cmake' and name 'ocs2_ballbot_ros'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole' with type 'ros.ament_cmake' and name 'ocs2_cartpole'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_cartpole_ros) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros' with type 'ros.ament_cmake' and name 'ocs2_cartpole_ros'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator' with type 'ros.ament_cmake' and name 'ocs2_double_integrator'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_double_integrator_ros) by extension 'ros'
[0.186s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros' with type 'ros.ament_cmake' and name 'ocs2_double_integrator_ros'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot' with type 'ros.ament_cmake' and name 'ocs2_legged_robot'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_legged_robot_ros) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros' with type 'ros.ament_cmake' and name 'ocs2_legged_robot_ros'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros' with type 'ros.ament_cmake' and name 'ocs2_mobile_manipulator_ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor' with type 'ros.ament_cmake' and name 'ocs2_quadrotor'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/ocs2_quadrotor_ros) by extension 'ros'
[0.197s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros' with type 'ros.ament_cmake' and name 'ocs2_quadrotor_ros'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'ros'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/basic examples/worlds) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/build) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/build) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/build) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_core) by extension 'ros'
[0.199s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_core' with type 'ros.ament_cmake' and name 'ocs2_core'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_oc) by extension 'ros'
[0.199s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_oc' with type 'ros.ament_cmake' and name 'ocs2_oc'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/core/ocs2_thirdparty) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/core/ocs2_thirdparty' with type 'ros.ament_cmake' and name 'ocs2_thirdparty'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/install) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/install) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/install) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/log) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/log) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/log) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'cmake'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ddp) by extension 'ros'
[0.201s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ddp' with type 'ros.ament_cmake' and name 'ocs2_ddp'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_ipm) by extension 'ros'
[0.202s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_ipm' with type 'ros.ament_cmake' and name 'ocs2_ipm'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_mpc) by extension 'ros'
[0.202s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_mpc' with type 'ros.ament_cmake' and name 'ocs2_mpc'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_qp_solver) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_qp_solver' with type 'ros.ament_cmake' and name 'ocs2_qp_solver'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_slp) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_slp' with type 'ros.ament_cmake' and name 'ocs2_slp'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'ros'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['cmake', 'python']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'cmake'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extensions ['python_setup_py']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp) by extension 'python_setup_py'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon' with type 'ros.ament_cmake' and name 'blasfeo_colcon'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon' with type 'ros.ament_cmake' and name 'hpipm_colcon'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp) by extension 'ros'
[0.206s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp' with type 'ros.ament_cmake' and name 'ocs2_sqp'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'ros'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['cmake', 'python']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'cmake'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extensions ['python_setup_py']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics) by extension 'python_setup_py'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_msgs) by extension 'ros'
[0.207s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_msgs' with type 'ros.ament_cmake' and name 'ocs2_msgs'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'ros'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['cmake', 'python']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'cmake'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extensions ['python_setup_py']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio) by extension 'python_setup_py'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model) by extension 'ros'
[0.208s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model' with type 'ros.ament_cmake' and name 'ocs2_centroidal_model'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface) by extension 'ros'
[0.208s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface' with type 'ros.ament_cmake' and name 'ocs2_pinocchio_interface'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision) by extension 'ros'
[0.209s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision' with type 'ros.ament_cmake' and name 'ocs2_self_collision'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization) by extension 'ros'
[0.209s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization' with type 'ros.ament_cmake' and name 'ocs2_self_collision_visualization'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation) by extension 'ros'
[0.210s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation' with type 'ros.ament_cmake' and name 'ocs2_sphere_approximation'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_python_interface) by extension 'ros'
[0.211s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_python_interface' with type 'ros.ament_cmake' and name 'ocs2_python_interface'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_robotic_tools) by extension 'ros'
[0.211s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_robotic_tools' with type 'ros.ament_cmake' and name 'ocs2_robotic_tools'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/robotics/ocs2_ros_interfaces) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/robotics/ocs2_ros_interfaces' with type 'ros.ament_cmake' and name 'ocs2_ros_interfaces'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'ros'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['cmake', 'python']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'cmake'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extensions ['python_setup_py']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules) by extension 'python_setup_py'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/grid_map_sdf) by extension 'ros'
[0.213s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/grid_map_sdf' with type 'ros.ament_cmake' and name 'grid_map_sdf'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/ocs2_robotic_assets) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/ocs2_robotic_assets' with type 'ros.ament_cmake' and name 'ocs2_robotic_assets'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extensions ['python_setup_py']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon' with type 'ros.ament_cmake' and name 'cgal5_colcon'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition' with type 'ros.ament_cmake' and name 'convex_plane_decomposition'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_msgs'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros) by extension 'ros'
[0.217s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros' with type 'ros.ament_cmake' and name 'convex_plane_decomposition_ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl) by extension 'ros'
[0.218s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl' with type 'ros.ament_cmake' and name 'grid_map_filters_rsl'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/qpoases_vendor) by extension 'python'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qpoases_vendor' with type 'cmake' and name 'qpOASES'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'ros'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['cmake', 'python']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'ros'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['cmake', 'python']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES) by extension 'python_setup_py'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/legged_control) by extension 'python_setup_py'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/LICENSES/unitree_guide) by extension 'python_setup_py'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['cmake', 'python']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'cmake'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extensions ['python_setup_py']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/control_input_msgs) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/control_input_msgs' with type 'ros.ament_cmake' and name 'control_input_msgs'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/joystick_input) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/joystick_input' with type 'ros.ament_cmake' and name 'joystick_input'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/commands/keyboard_input) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/commands/keyboard_input' with type 'ros.ament_cmake' and name 'keyboard_input'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'ros'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['cmake', 'python']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'cmake'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extensions ['python_setup_py']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers) by extension 'python_setup_py'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/leg_pd_controller) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/leg_pd_controller' with type 'ros.ament_cmake' and name 'leg_pd_controller'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/ocs2_quadruped_controller) by extension 'ros'
[0.226s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller' with type 'ros.ament_cmake' and name 'ocs2_quadruped_controller'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/rl_quadruped_controller) by extension 'ros'
[0.226s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/rl_quadruped_controller' with type 'ros.ament_cmake' and name 'rl_quadruped_controller'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/controllers/unitree_guide_controller) by extension 'ros'
[0.227s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/controllers/unitree_guide_controller' with type 'ros.ament_cmake' and name 'unitree_guide_controller'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'ros'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['cmake', 'python']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'cmake'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description) by extension 'ros'
[0.228s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description' with type 'ros.ament_cmake' and name 'anymal_c_description'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'ros'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['cmake', 'python']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'cmake'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extensions ['python_setup_py']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics) by extension 'python_setup_py'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description) by extension 'ros'
[0.229s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description' with type 'ros.ament_cmake' and name 'lite3_description'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/deep_robotics/x30_description) by extension 'ros'
[0.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description' with type 'ros.ament_cmake' and name 'x30_description'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/a1_description) by extension 'ros'
[0.231s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/a1_description' with type 'ros.ament_cmake' and name 'a1_description'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/aliengo_description) by extension 'ros'
[0.232s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description' with type 'ros.ament_cmake' and name 'aliengo_description'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/b2_description) by extension 'ros'
[0.232s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/b2_description' with type 'ros.ament_cmake' and name 'b2_description'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go1_description) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go1_description' with type 'ros.ament_cmake' and name 'go1_description'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/unitree/go2_description) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/unitree/go2_description' with type 'ros.ament_cmake' and name 'go2_description'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description) by extension 'ros'
[0.234s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description' with type 'ros.ament_cmake' and name 'cyberdog_description'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'ros'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['cmake', 'python']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'cmake'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extensions ['python_setup_py']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/gz_quadruped_hardware) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware' with type 'ros.ament_cmake' and name 'gz_quadruped_hardware'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco' with type 'ros.ament_cmake' and name 'hardware_unitree_mujoco'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'ros'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['cmake', 'python']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'cmake'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extensions ['python_setup_py']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/controller_common) by extension 'ros'
[0.237s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/controller_common' with type 'ros.ament_cmake' and name 'controller_common'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/gz_quadruped_playground) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/gz_quadruped_playground' with type 'ros.ament_cmake' and name 'gz_quadruped_playground'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/quadruped_ros2_control/libraries/qpoases_colcon) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/quadruped_ros2_control/libraries/qpoases_colcon' with type 'ros.ament_cmake' and name 'qpoases_colcon'
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'a1_description' in 'src/quadruped_ros2_control/descriptions/unitree/a1_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'aliengo_description' in 'src/quadruped_ros2_control/descriptions/unitree/aliengo_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'anymal_c_description' in 'src/quadruped_ros2_control/descriptions/anybotics/anymal_c_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'b2_description' in 'src/quadruped_ros2_control/descriptions/unitree/b2_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'blasfeo_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/blasfeo_colcon'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'cgal5_colcon' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/cgal5_colcon'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'control_input_msgs' in 'src/quadruped_ros2_control/commands/control_input_msgs'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition_msgs' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_msgs'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'cyberdog_description' in 'src/quadruped_ros2_control/descriptions/xiaomi/cyberdog_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'elevation_map_converter' in 'src/elevation_map_converter'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'go1_description' in 'src/quadruped_ros2_control/descriptions/unitree/go1_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'go2_description' in 'src/quadruped_ros2_control/descriptions/unitree/go2_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'grid_map_filters_rsl' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/grid_map_filters_rsl'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'grid_map_sdf' in 'src/ocs2_ros2/submodules/grid_map_sdf'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gz_quadruped_hardware' in 'src/quadruped_ros2_control/hardwares/gz_quadruped_hardware'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hardware_unitree_mujoco' in 'src/quadruped_ros2_control/hardwares/hardware_unitree_mujoco'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leg_pd_controller' in 'src/quadruped_ros2_control/controllers/leg_pd_controller'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'lite3_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/lite3_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_msgs' in 'src/ocs2_ros2/robotics/ocs2_msgs'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_robotic_assets' in 'src/ocs2_ros2/submodules/ocs2_robotic_assets'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_thirdparty' in 'src/ocs2_ros2/core/ocs2_thirdparty'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qpOASES' in 'src/qpoases_vendor'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qpoases_colcon' in 'src/quadruped_ros2_control/libraries/qpoases_colcon'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'x30_description' in 'src/quadruped_ros2_control/descriptions/deep_robotics/x30_description'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'controller_common' in 'src/quadruped_ros2_control/libraries/controller_common'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gz_quadruped_playground' in 'src/quadruped_ros2_control/libraries/gz_quadruped_playground'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'joystick_input' in 'src/quadruped_ros2_control/commands/joystick_input'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'keyboard_input' in 'src/quadruped_ros2_control/commands/keyboard_input'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_core' in 'src/ocs2_ros2/core/ocs2_core'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_switched_model_msgs' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_msgs'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'convex_plane_decomposition_ros' in 'src/ocs2_ros2/submodules/plane_segmentation_ros2/convex_plane_decomposition_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_oc' in 'src/ocs2_ros2/core/ocs2_oc'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rl_quadruped_controller' in 'src/quadruped_ros2_control/controllers/rl_quadruped_controller'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'unitree_guide_controller' in 'src/quadruped_ros2_control/controllers/unitree_guide_controller'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mpc' in 'src/ocs2_ros2/mpc/ocs2_mpc'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_qp_solver' in 'src/ocs2_ros2/mpc/ocs2_qp_solver'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_raisim_core' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_raisim_core'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_robotic_tools' in 'src/ocs2_ros2/robotics/ocs2_robotic_tools'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hpipm_colcon' in 'src/ocs2_ros2/mpc/ocs2_sqp/hpipm_colcon'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ddp' in 'src/ocs2_ros2/mpc/ocs2_ddp'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_pinocchio_interface' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_pinocchio_interface'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ros_interfaces' in 'src/ocs2_ros2/robotics/ocs2_ros_interfaces'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_slp' in 'src/ocs2_ros2/mpc/ocs2_slp'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_cartpole' in 'src/ocs2_ros2/basic examples/ocs2_cartpole'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_centroidal_model' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_centroidal_model'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ipm' in 'src/ocs2_ros2/mpc/ocs2_ipm'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_python_interface' in 'src/ocs2_ros2/robotics/ocs2_python_interface'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_self_collision' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_sphere_approximation' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_sphere_approximation'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_sqp' in 'src/ocs2_ros2/mpc/ocs2_sqp/ocs2_sqp'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_switched_model_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_switched_model_interface'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_commands' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_commands'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_models' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_models'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot' in 'src/ocs2_ros2/basic examples/ocs2_ballbot'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_cartpole_ros' in 'src/ocs2_ros2/basic examples/ocs2_cartpole_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_double_integrator' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mobile_manipulator' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mpcnet_core' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_mpcnet_core'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadrotor' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_self_collision_visualization' in 'src/ocs2_ros2/robotics/ocs2_pinocchio/ocs2_self_collision_visualization'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'segmented_planes_terrain_model' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/segmented_planes_terrain_model'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot_ros' in 'src/ocs2_ros2/basic examples/ocs2_ballbot_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_double_integrator_ros' in 'src/ocs2_ros2/basic examples/ocs2_double_integrator_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_ros' in 'src/ocs2_ros2/basic examples/ocs2_legged_robot_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_mobile_manipulator_ros' in 'src/ocs2_ros2/basic examples/ocs2_mobile_manipulator_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadrotor_ros' in 'src/ocs2_ros2/basic examples/ocs2_quadrotor_ros'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_interface'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_anymal_mpc' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_mpc'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_ballbot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_ballbot_mpcnet'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_raisim' in 'src/ocs2_ros2/advance examples/ocs2_raisim/ocs2_legged_robot_raisim'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_controller' in 'src/quadruped_ros2_control/controllers/ocs2_quadruped_controller'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_quadruped_loopshaping_interface' in 'src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_quadruped_loopshaping_interface'
[0.266s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'ocs2_legged_robot_mpcnet' in 'src/ocs2_ros2/advance examples/ocs2_mpcnet/ocs2_legged_robot_mpcnet'
[0.267s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.267s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.270s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 68 installed packages in /home/<USER>/ros2_ws/install
[0.271s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 422 installed packages in /opt/ros/humble
[0.273s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_args' from command line to 'None'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_target' from command line to 'None'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_clean_cache' from command line to 'False'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_clean_first' from command line to 'False'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'cmake_force_configure' from command line to 'False'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'ament_cmake_args' from command line to 'None'
[0.302s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'catkin_cmake_args' from command line to 'None'
[0.303s] Level 5:colcon.colcon_core.verb:set package 'ocs2_anymal_loopshaping_mpc' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.303s] DEBUG:colcon.colcon_core.verb:Building package 'ocs2_anymal_loopshaping_mpc' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc', 'symlink_install': True, 'test_result_base': None}
[0.303s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.303s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.304s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc' with build type 'ament_cmake'
[0.304s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc'
[0.306s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.306s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.306s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.440s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc -- -j32 -l32
[13.518s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc -- -j32 -l32
[13.532s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc
[13.564s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ocs2_anymal_loopshaping_mpc)
[13.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc
[13.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc' for CMake module files
[13.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc' for CMake config files
[13.566s] Level 1:colcon.colcon_core.shell:create_environment_hook('ocs2_anymal_loopshaping_mpc', 'cmake_prefix_path')
[13.566s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.ps1'
[13.566s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.dsv'
[13.566s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.sh'
[13.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib'
[13.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/bin'
[13.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/pkgconfig/ocs2_anymal_loopshaping_mpc.pc'
[13.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/python3.10/site-packages'
[13.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/bin'
[13.567s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.ps1'
[13.568s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv'
[13.568s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.sh'
[13.568s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.bash'
[13.569s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.zsh'
[13.569s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/colcon-core/packages/ocs2_anymal_loopshaping_mpc)
[13.569s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ocs2_anymal_loopshaping_mpc)
[13.570s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc' for CMake module files
[13.570s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc' for CMake config files
[13.570s] Level 1:colcon.colcon_core.shell:create_environment_hook('ocs2_anymal_loopshaping_mpc', 'cmake_prefix_path')
[13.570s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.ps1'
[13.571s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.dsv'
[13.571s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/hook/cmake_prefix_path.sh'
[13.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib'
[13.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/bin'
[13.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/pkgconfig/ocs2_anymal_loopshaping_mpc.pc'
[13.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib/python3.10/site-packages'
[13.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/bin'
[13.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.ps1'
[13.572s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.dsv'
[13.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.sh'
[13.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.bash'
[13.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/ocs2_anymal_loopshaping_mpc/package.zsh'
[13.573s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/share/colcon-core/packages/ocs2_anymal_loopshaping_mpc)
[13.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[13.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[13.573s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[13.573s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[13.577s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[13.577s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[13.577s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[13.585s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[13.585s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.ps1'
[13.586s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_ps1.py'
[13.586s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.ps1'
[13.589s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.sh'
[13.589s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_sh.py'
[13.589s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.sh'
[13.591s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.bash'
[13.592s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.bash'
[13.593s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.zsh'
[13.594s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.zsh'
