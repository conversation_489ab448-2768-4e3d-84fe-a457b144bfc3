running egg_info
writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO
writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt
writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt
writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt
writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt
reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
running build
running build_py
copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)
Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info
running install_scripts
Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'
