-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/include/ocs2_robotic_assets/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/include/ocs2_robotic_assets/package_path.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/include/ocs2_robotic_assets/package_path.h.in
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/base.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/base.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/battery.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/battery.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/bottom_shell.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/bottom_shell.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/depth_camera.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/depth_camera.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/drive.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/drive.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/face.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/face.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/foot.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/foot.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/handle.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/handle.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/hatch.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/hatch.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/hip.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/hip_l.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/hip_r.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/lidar.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/lidar.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/lidar_cage.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/lidar_cage.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/remote.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/remote.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/shank.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/shank_l.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/shank_r.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/thigh.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/thigh.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/top_shell.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/top_shell.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/wide_angle_camera.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/meshes/wide_angle_camera.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/anymal_c/urdf/anymal.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/ballbot/meshes/base.obj
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/ballbot/urdf/ballbot.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/cartpole/urdf/cartpole.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/double_integrator/urdf/double_integrator.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/base_link.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/gripper_base.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link1.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link2.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link3.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link4.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link5.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link6.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link7.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link8.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/urdf/compile
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/urdf/piper_description.csv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/urdf/piper_description.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/urdf/piper_description.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/urdf/piper_description_gazebo.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/hand.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link0.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link1.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link2.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link3.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link4.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link5.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link6.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/collision/link7.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/hand.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link0.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link1.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link2.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link3.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link4.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link5.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link6.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/collision/link7.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/finger.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/hand.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link0.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link2.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link3.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link4.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link5.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link6.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/meshes/visual/link7.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/finger.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/hand.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link0.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link2.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link3.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link4.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link5.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link6.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/meshes/visual/link7.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/franka/urdf/panda.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm.SLDPRT
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_half_1.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_half_1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_half_2.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_half_2.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_mico.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm_mico.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/base.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/base.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm_mico.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm_mico.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_2finger.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_2finger.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_3finger.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_3finger.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_big.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_big.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_small.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_small.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/shoulder.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/shoulder.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist_spherical_1.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist_spherical_1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist_spherical_2.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist_spherical_2.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/urdf/j2n6s300.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/kinova/urdf/j2n7s300.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/mabi_mobile/urdf/mabi_mobile.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_caster_texture.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_wheel_left.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_wheel_right.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/pr2_wheel.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel_h.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel_h_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_H_Color_100430.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_H_UV_100430.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_tip_H_UV_100430.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_tip_l.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_tip_pad2_l.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_tip_pad2_r.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/finger_tip_r.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/fingertip_H_Color_100430.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/float_H_Color_100430.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/float_H_UV_100430.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_float.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_float_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_float_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_floating.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/palm_H_Color_100430.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/palm_H_UV_100430.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/upper_finger_l.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/upper_finger_r.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_color_red.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_color_yellow.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_green.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect2_v0/kinect2_assembly.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect2_v0/kinect2_assembly.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/115x100_swept_back--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/115x100_swept_back_no_sensors--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/115x100_swept_fwd--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/115x100_swept_fwd_no_sensors--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/68-04546_Kinect_Sensor--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_prosilica_v0/Prosilica_w_Lens--coarse.STL
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_v0/kinect.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_v0/kinect.tga
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_v0/kinect_color.tga
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/sensors/kinect_v0/kinect_mount.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_yaw.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/hok_tilt.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/.gitignore
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll_L.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm_color.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm_normals.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/pr2/urdf/pr2.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/LICENSE
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/3dm-gxX.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/axle.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/body-collision.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/body.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/end-cover.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/lights.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/rocker.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/side-cover.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/top.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/ust-10lx.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/wheel.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/base.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/forearm.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/shoulder.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/upperarm.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist1.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist2.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist3.stl
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/base.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/forearm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/shoulder.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/upperarm.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist2.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist3.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/urdf/ridgeback_ur5.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/quadrotor/meshes/quadrotor.obj
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/resources/quadrotor/urdf/quadrotor.urdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ament_index/resource_index/package_run_dependencies/ocs2_robotic_assets
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ament_index/resource_index/parent_prefix_path/ocs2_robotic_assets
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ament_index/resource_index/packages/ocs2_robotic_assets
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/cmake/ocs2_robotic_assetsConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/cmake/ocs2_robotic_assetsConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_robotic_assets/share/ocs2_robotic_assets/package.xml
