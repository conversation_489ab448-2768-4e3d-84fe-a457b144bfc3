Invoking command in '/home/<USER>/ros2_ws/build/ocs2_thirdparty': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_thirdparty -- -j32 -l32
Invoked command in '/home/<USER>/ros2_ws/build/ocs2_thirdparty' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_thirdparty -- -j32 -l32
Invoking command in '/home/<USER>/ros2_ws/build/ocs2_thirdparty': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_thirdparty
Invoked command in '/home/<USER>/ros2_ws/build/ocs2_thirdparty' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_thirdparty
