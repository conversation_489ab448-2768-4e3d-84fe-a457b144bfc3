[100%] Built target ocs2_quadruped_controller
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//ocs2_quadruped_controller.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/FSM/StateOCS2.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/CtrlComponent.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/GaitManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/control/TargetManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/FromOdomTopic.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/GroundTruth.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/LinearKalmanFilter.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/estimator/StateEstimateBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/SwitchedModelReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/LeggedSelfCollisionConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/SwingTrajectoryPlanner.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/interface/initialization/LeggedRobotInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootCollisionConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/FootPlacementConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/constraint/SphereSdfConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/ConvexRegionSelector.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedPrecomputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/interface/PerceptiveLeggedReferenceManager.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/synchronize/PlanarTerrainReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/FootPlacementVisualization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/perceptive/visualize/SphereVisualization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HierarchicalWbc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/HoQp.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/Task.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WbcBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/include/ocs2_quadruped_controller/ocs2_quadruped_controller/wbc/WeightedWbc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/convex_plane_decomposition.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/elevation_mapping.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//config/visualize_ocs2.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/elevation_mapping.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/gazebo.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller//launch/mujoco.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/package_run_dependencies/ocs2_quadruped_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/parent_prefix_path/ocs2_quadruped_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/packages/ocs2_quadruped_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/ocs2_quadruped_controller
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/ocs2_quadruped_controllerConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib/ocs2_quadruped_controller/libocs2_quadruped_controller.so
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_quadruped_controller/share/ocs2_quadruped_controller/cmake/export_ocs2_quadruped_controllerExport-release.cmake
