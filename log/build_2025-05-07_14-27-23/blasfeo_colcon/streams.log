[0.072s] Invoking command in '/home/<USER>/ros2_ws/build/blasfeo_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/blasfeo_colcon -- -j32 -l32
[0.146s] [100%] Built target blasfeo
[0.227s] Invoked command in '/home/<USER>/ros2_ws/build/blasfeo_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/blasfeo_colcon -- -j32 -l32
[0.239s] Invoking command in '/home/<USER>/ros2_ws/build/blasfeo_colcon': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/blasfeo_colcon
[0.273s] -- Install configuration: "Release"
[0.273s] -- Execute custom install script
[0.273s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo.h
[0.273s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_block_size.h
[0.273s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_common.h
[0.273s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux.h
[0.273s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep.h
[0.274s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep_ref.h
[0.274s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_old.h
[0.274s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ref.h
[0.274s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_test.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas_api.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api_ref.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_ref_api.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_kernel.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_i_aux_ext_dep.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_m_aux.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_memory.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_naming.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_processor_features.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep_ref.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_old.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ref.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_test.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas_api.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api_ref.h
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_ref_api.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_kernel.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_stdlib.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_target.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_timing.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_v_aux_ext_dep.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas_64.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas.h
[0.282s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas_64.h
[0.283s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/package_run_dependencies/blasfeo_colcon
[0.284s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/parent_prefix_path/blasfeo_colcon
[0.284s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.sh
[0.284s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.dsv
[0.287s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.sh
[0.287s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.dsv
[0.287s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.bash
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.sh
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.zsh
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.dsv
[0.288s] -- Symlinking: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.dsv
[0.294s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/packages/blasfeo_colcon
[0.294s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo-extras.cmake
[0.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_include_directories-extras.cmake
[0.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_dependencies-extras.cmake
[0.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig.cmake
[0.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig-version.cmake
[0.295s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.xml
[0.296s] -- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig.cmake
[0.296s] -- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig-release.cmake
[0.301s] Invoked command in '/home/<USER>/ros2_ws/build/blasfeo_colcon' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/blasfeo_colcon
