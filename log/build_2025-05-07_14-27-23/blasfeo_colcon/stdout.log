[100%] Built target blasfeo
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_block_size.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_common.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ext_dep_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_old.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_aux_test.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blas_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_api_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_blasfeo_ref_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_d_kernel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_i_aux_ext_dep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_m_aux.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_memory.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_naming.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_processor_features.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ext_dep_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_old.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_aux_test.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blas_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_api_ref.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_blasfeo_ref_api.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_s_kernel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_stdlib.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_target.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_timing.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/blasfeo_v_aux_ext_dep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/d_blas_64.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/include/s_blas_64.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/package_run_dependencies/blasfeo_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/parent_prefix_path/blasfeo_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/ament_index/resource_index/packages/blasfeo_colcon
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/cmake/blasfeo_colconConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/blasfeo_colcon/share/blasfeo_colcon/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/blasfeo_colcon/cmake/blasfeoConfig-release.cmake
