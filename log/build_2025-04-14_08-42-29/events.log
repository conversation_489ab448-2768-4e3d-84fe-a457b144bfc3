[0.000000] (-) TimerEvent: {}
[0.000195] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000222] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000240] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000257] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000273] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000361] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000378] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000401] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000424] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000496] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000513] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000532] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000548] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000564] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000580] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000609] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000654] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000671] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000694] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000732] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000748] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000774] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000790] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000809] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.000859] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001032] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001081] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001131] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001147] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001164] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001180] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001196] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001213] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001236] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001259] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001295] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001311] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001333] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001349] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001384] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001410] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001431] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001448] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001466] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001482] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001523] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001539] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001556] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001579] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001689] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001711] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001729] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001843] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001924] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001942] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.001963] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.001979] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002167] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002323] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002395] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002702] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002730] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002769] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002868] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002884] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002898] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002912] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002927] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002941] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002956] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002973] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002990] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003009] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003027] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003045] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003078] (ocs2_anymal_loopshaping_mpc) JobQueued: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'dependencies': OrderedDict([('blasfeo_colcon', '/home/<USER>/ros2_ws/install/blasfeo_colcon'), ('cgal5_colcon', '/home/<USER>/ros2_ws/install/cgal5_colcon'), ('convex_plane_decomposition_msgs', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs'), ('grid_map_filters_rsl', '/home/<USER>/ros2_ws/install/grid_map_filters_rsl'), ('grid_map_sdf', '/home/<USER>/ros2_ws/install/grid_map_sdf'), ('ocs2_msgs', '/home/<USER>/ros2_ws/install/ocs2_msgs'), ('ocs2_thirdparty', '/home/<USER>/ros2_ws/install/ocs2_thirdparty'), ('convex_plane_decomposition', '/home/<USER>/ros2_ws/install/convex_plane_decomposition'), ('ocs2_core', '/home/<USER>/ros2_ws/install/ocs2_core'), ('ocs2_switched_model_msgs', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs'), ('convex_plane_decomposition_ros', '/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros'), ('ocs2_oc', '/home/<USER>/ros2_ws/install/ocs2_oc'), ('ocs2_mpc', '/home/<USER>/ros2_ws/install/ocs2_mpc'), ('ocs2_qp_solver', '/home/<USER>/ros2_ws/install/ocs2_qp_solver'), ('ocs2_robotic_tools', '/home/<USER>/ros2_ws/install/ocs2_robotic_tools'), ('hpipm_colcon', '/home/<USER>/ros2_ws/install/hpipm_colcon'), ('ocs2_ddp', '/home/<USER>/ros2_ws/install/ocs2_ddp'), ('ocs2_pinocchio_interface', '/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface'), ('ocs2_ros_interfaces', '/home/<USER>/ros2_ws/install/ocs2_ros_interfaces'), ('ocs2_sqp', '/home/<USER>/ros2_ws/install/ocs2_sqp'), ('ocs2_switched_model_interface', '/home/<USER>/ros2_ws/install/ocs2_switched_model_interface'), ('ocs2_anymal_commands', '/home/<USER>/ros2_ws/install/ocs2_anymal_commands'), ('ocs2_anymal_models', '/home/<USER>/ros2_ws/install/ocs2_anymal_models'), ('segmented_planes_terrain_model', '/home/<USER>/ros2_ws/install/segmented_planes_terrain_model'), ('ocs2_quadruped_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_interface'), ('ocs2_anymal_mpc', '/home/<USER>/ros2_ws/install/ocs2_anymal_mpc'), ('ocs2_quadruped_loopshaping_interface', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface')])}
[0.003130] (ocs2_anymal_loopshaping_mpc) JobStarted: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.063192] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'cmake'}
[0.064047] (ocs2_anymal_loopshaping_mpc) JobProgress: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'progress': 'build'}
[0.064283] (ocs2_anymal_loopshaping_mpc) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_cursorvk7miN'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('TERM_PROGRAM_VERSION', '0.48.8'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('PERLLIB', '/tmp/.mount_cursorvk7miN/usr/share/perl5/:/tmp/.mount_cursorvk7miN/usr/lib/perl5/:'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/cursor.desktop'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_cursorvk7miN/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_cursorvk7miN/usr/share/cursor/cursor'), ('MANAGERPID', '2777'), ('SYSTEMD_EXEC_PID', '3344'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '25832'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('OWD', '/home/<USER>'), ('JOURNAL_STREAM', '8:13643'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3270,unix/cg215:/tmp/.ICE-unix/3270'), ('INVOCATION_ID', 'fb8f215eb15742cda761774c63395fdd'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/opt/cursor.appimage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-cfe34cecdc.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', 'd53662abb3b742a9bee764094531af5c'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_cursorvk7miN/usr/share/glib-2.0/schemas/:'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/opt/cursor.appimage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/ocs2_anymal_loopshaping_mpc'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/tmp/.mount_cursorvk7miN/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_cursorvk7miN/usr/lib/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt4/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib32/qt5/plugins/:/tmp/.mount_cursorvk7miN/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.099460] (-) TimerEvent: {}
[0.121905] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 11%] Built target gtest_main\n'}
[0.122430] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 22%] Built target gtest\n'}
[0.133215] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 33%] Built target ocs2_anymal_loopshaping_mpc\n'}
[0.158370] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o\x1b[0m\n'}
[0.168889] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 61%] Built target ocs2_anymal_loopshaping_mpc_test\n'}
[0.169381] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 72%] Built target ocs2_anymal_loopshaping_mpc_mpc_node\n'}
[0.171368] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 83%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node\n'}
[0.185170] (ocs2_anymal_loopshaping_mpc) StdoutLine: {'line': b'[ 94%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo\n'}
[0.199553] (-) TimerEvent: {}
[0.299844] (-) TimerEvent: {}
[0.400201] (-) TimerEvent: {}
[0.500599] (-) TimerEvent: {}
[0.600933] (-) TimerEvent: {}
[0.701353] (-) TimerEvent: {}
[0.801712] (-) TimerEvent: {}
[0.902030] (-) TimerEvent: {}
[1.002340] (-) TimerEvent: {}
[1.102610] (-) TimerEvent: {}
[1.203021] (-) TimerEvent: {}
[1.303322] (-) TimerEvent: {}
[1.403645] (-) TimerEvent: {}
[1.503993] (-) TimerEvent: {}
[1.604299] (-) TimerEvent: {}
[1.704597] (-) TimerEvent: {}
[1.804982] (-) TimerEvent: {}
[1.905289] (-) TimerEvent: {}
[2.005575] (-) TimerEvent: {}
[2.105959] (-) TimerEvent: {}
[2.206234] (-) TimerEvent: {}
[2.306504] (-) TimerEvent: {}
[2.406890] (-) TimerEvent: {}
[2.507228] (-) TimerEvent: {}
[2.607506] (-) TimerEvent: {}
[2.707819] (-) TimerEvent: {}
[2.808176] (-) TimerEvent: {}
[2.908458] (-) TimerEvent: {}
[3.008832] (-) TimerEvent: {}
[3.109241] (-) TimerEvent: {}
[3.209604] (-) TimerEvent: {}
[3.309899] (-) TimerEvent: {}
[3.410230] (-) TimerEvent: {}
[3.510665] (-) TimerEvent: {}
[3.611035] (-) TimerEvent: {}
[3.711311] (-) TimerEvent: {}
[3.811605] (-) TimerEvent: {}
[3.911872] (-) TimerEvent: {}
[4.012112] (-) TimerEvent: {}
[4.112490] (-) TimerEvent: {}
[4.212833] (-) TimerEvent: {}
[4.313195] (-) TimerEvent: {}
[4.413539] (-) TimerEvent: {}
[4.513882] (-) TimerEvent: {}
[4.614241] (-) TimerEvent: {}
[4.714563] (-) TimerEvent: {}
[4.814948] (-) TimerEvent: {}
[4.915250] (-) TimerEvent: {}
[5.015553] (-) TimerEvent: {}
[5.115870] (-) TimerEvent: {}
[5.216115] (-) TimerEvent: {}
[5.316395] (-) TimerEvent: {}
[5.416783] (-) TimerEvent: {}
[5.517150] (-) TimerEvent: {}
[5.617526] (-) TimerEvent: {}
[5.717805] (-) TimerEvent: {}
[5.818132] (-) TimerEvent: {}
[5.918407] (-) TimerEvent: {}
[6.018792] (-) TimerEvent: {}
[6.119228] (-) TimerEvent: {}
[6.219571] (-) TimerEvent: {}
[6.319895] (-) TimerEvent: {}
[6.420202] (-) TimerEvent: {}
[6.520474] (-) TimerEvent: {}
[6.620746] (-) TimerEvent: {}
[6.721035] (-) TimerEvent: {}
[6.821321] (-) TimerEvent: {}
[6.921552] (-) TimerEvent: {}
[7.021782] (-) TimerEvent: {}
[7.122130] (-) TimerEvent: {}
[7.222482] (-) TimerEvent: {}
[7.322740] (-) TimerEvent: {}
[7.423014] (-) TimerEvent: {}
[7.523293] (-) TimerEvent: {}
[7.623559] (-) TimerEvent: {}
[7.715640] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.715781] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:237:53:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno match for \xe2\x80\x98\x1b[01m\x1b[Koperator=\x1b[m\x1b[K\xe2\x80\x99 (operand types are \xe2\x80\x98\x1b[01m\x1b[Kocs2::ReferenceManagerInterface\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::shared_ptr<ocs2::RosReferenceManager>\x1b[m\x1b[K\xe2\x80\x99)\n'}
[7.715819] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'  237 |     mpcPtr->getSolverPtr()->getReferenceManager() = \x1b[01;31m\x1b[KrosReferenceManagerPtr\x1b[m\x1b[K;\n'}
[7.715849] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'      |                                                     \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[7.715875] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'compilation terminated due to -Wfatal-errors.\n'}
[7.723639] (-) TimerEvent: {}
[7.767318] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1\n'}
[7.767464] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2\n'}
[7.767642] (ocs2_anymal_loopshaping_mpc) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[7.770026] (ocs2_anymal_loopshaping_mpc) CommandEnded: {'returncode': 2}
[7.776238] (ocs2_anymal_loopshaping_mpc) JobEnded: {'identifier': 'ocs2_anymal_loopshaping_mpc', 'rc': 2}
[7.786639] (-) EventReactorShutdown: {}
