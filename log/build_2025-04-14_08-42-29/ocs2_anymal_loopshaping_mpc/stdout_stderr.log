[ 11%] Built target gtest_main
[ 22%] Built target gtest
[ 33%] Built target ocs2_anymal_loopshaping_mpc
[ 38%] [32mBuilding CXX object CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o[0m
[ 61%] Built target ocs2_anymal_loopshaping_mpc_test
[ 72%] Built target ocs2_anymal_loopshaping_mpc_mpc_node
[ 83%] Built target ocs2_anymal_loopshaping_mpc_dummy_mrt_node
[ 94%] Built target ocs2_anymal_loopshaping_mpc_perceptive_demo
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/advance examples/ocs2_perceptive_anymal/ocs2_anymal_loopshaping_mpc/src/PerceptiveMpcKeyboardControl.cpp:237:53:[m[K [01;31m[Kerror: [m[Kno match for ‘[01m[Koperator=[m[K’ (operand types are ‘[01m[Kocs2::ReferenceManagerInterface[m[K’ and ‘[01m[Kstd::shared_ptr<ocs2::RosReferenceManager>[m[K’)
  237 |     mpcPtr->getSolverPtr()->getReferenceManager() = [01;31m[KrosReferenceManagerPtr[m[K;
      |                                                     [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/build.make:76: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:269: CMakeFiles/ocs2_anymal_loopshaping_mpc_perceptive_keyboard_control.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
