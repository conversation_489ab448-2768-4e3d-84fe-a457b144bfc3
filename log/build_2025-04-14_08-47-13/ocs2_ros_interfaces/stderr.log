[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/src/PerceptiveMpcKeyboardControl.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/src/PerceptiveMpcKeyboardControl.cpp:81:33:[m[K [01;31m[Kerror: [m[K‘[01m[Krclcpp::Node::SharedPtr PerceptiveMpcKeyboardControl::node_[m[K’ is private within this context
   81 |         rclcpp::spin(controller.[01;31m[Knode_[m[K);
      |                                 [01;31m[K^~~~~[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/perceptive_mpc_keyboard_control.dir/build.make:76: CMakeFiles/perceptive_mpc_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:221: CMakeFiles/perceptive_mpc_keyboard_control.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
