-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rclcpp_lifecycle: 16.0.12 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found ocs2_msgs: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_msgs/share/ocs2_msgs/cmake)
-- Found ocs2_mpc: 0.0.0 (/home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake)
-- Found visualization_msgs: 4.8.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found interactive_markers: 2.3.2 (/opt/ros/humble/share/interactive_markers/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros2_ws/build/ocs2_ros_interfaces
[35m[1mConsolidate compiler generated dependencies of target ocs2_ros_interfaces[0m
[ 70%] Built target ocs2_ros_interfaces
[35m[1mConsolidate compiler generated dependencies of target multiplot_remap[0m
[35m[1mConsolidate compiler generated dependencies of target test_custom_callback_queue[0m
[ 75%] [32mBuilding CXX object CMakeFiles/perceptive_mpc_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o[0m
[ 95%] Built target multiplot_remap
[ 95%] Built target test_custom_callback_queue
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/src/PerceptiveMpcKeyboardControl.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/ocs2_ros2/robotics/ocs2_ros_interfaces/src/PerceptiveMpcKeyboardControl.cpp:81:33:[m[K [01;31m[Kerror: [m[K‘[01m[Krclcpp::Node::SharedPtr PerceptiveMpcKeyboardControl::node_[m[K’ is private within this context
   81 |         rclcpp::spin(controller.[01;31m[Knode_[m[K);
      |                                 [01;31m[K^~~~~[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/perceptive_mpc_keyboard_control.dir/build.make:76: CMakeFiles/perceptive_mpc_keyboard_control.dir/src/PerceptiveMpcKeyboardControl.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:221: CMakeFiles/perceptive_mpc_keyboard_control.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
