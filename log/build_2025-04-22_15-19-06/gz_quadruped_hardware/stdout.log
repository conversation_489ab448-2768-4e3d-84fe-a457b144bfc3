[35m[1mConsolidate compiler generated dependencies of target gz_quadruped_plugins[0m
[35m[1mConsolidate compiler generated dependencies of target gz_quadruped_hardware-system[0m
[ 50%] Built target gz_quadruped_hardware-system
[100%] Built target gz_quadruped_plugins
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_plugins.so
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_quadruped_plugin.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/include/gz_quadruped_hardware/gz_system_interface.hpp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//xacro/foot_force_sensor.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib/libgz_quadruped_hardware-system.so
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware//gz_quadruped_hardware.xml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_hardware
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_hardware
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/packages/gz_quadruped_hardware
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/ament_index/resource_index/gz_quadruped_hardware__pluginlib__plugin/gz_quadruped_hardware
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/ament_cmake_export_libraries-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/cmake/gz_quadruped_hardwareConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_hardware/share/gz_quadruped_hardware/package.xml
