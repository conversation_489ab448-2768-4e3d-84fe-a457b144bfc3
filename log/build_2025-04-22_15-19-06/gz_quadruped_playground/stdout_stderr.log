-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//worlds/baylands.sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//worlds/default.sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//worlds/warehouse.sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//launch/gazebo.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//launch/ocs2.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//launch/slam/cartographer.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//launch/slam/fast_lio.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//launch/unitree_guide.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/cartographer/cartographer.lua
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/cartographer/cartographer.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/fast_lio/fastlio.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/fast_lio/fastlio.yaml
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/ocs2.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//config/rviz.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/materials/scripts/model.material
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/materials/textures/RealSense_Albedo.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/materials/textures/RealSense_Metalness.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/materials/textures/RealSense_Normal.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/materials/textures/RealSense_Roughness.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/meshes/realsense.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/model.config
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/model.sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/model.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/thumbnails/1.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/thumbnails/2.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/thumbnails/3.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/D435/thumbnails/4.png
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/materials/textures/lidar_3d_v1.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/meshes/lidar_3d_v1.dae
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/meshes/lidar_3d_v1.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/model.config
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/model.sdf
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/model.xacro
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/thumbnails/1.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/thumbnails/2.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/thumbnails/3.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/thumbnails/4.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground//models/Lidar3DV1/thumbnails/5.jpg
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/ament_index/resource_index/package_run_dependencies/gz_quadruped_playground
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/ament_index/resource_index/parent_prefix_path/gz_quadruped_playground
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/ament_index/resource_index/packages/gz_quadruped_playground
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/cmake/gz_quadruped_playgroundConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/cmake/gz_quadruped_playgroundConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/gz_quadruped_playground/share/gz_quadruped_playground/package.xml
