[0.697s] Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
[0.877s] /home/<USER>/.local/lib/python3.10/site-packages/setuptools/_distutils/dist.py:289: UserWarning: Unknown distribution option: 'tests_require'
[0.877s]   warnings.warn(msg)
[0.977s] running egg_info
[0.991s] writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO
[0.991s] writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt
[0.991s] writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt
[0.992s] writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt
[0.992s] writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt
[1.023s] reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[1.024s] writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[1.025s] running build
[1.025s] running build_py
[1.025s] copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[1.025s] copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[1.025s] running install
[1.029s] running install_lib
[1.043s] copying /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2
[1.043s] byte-compiling /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2/sim_detection_node.py to sim_detection_node.cpython-310.pyc
[1.048s] running install_data
[1.048s] running install_egg_info
[1.061s] removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)
[1.061s] Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info
[1.062s] running install_scripts
[1.099s] Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.100s] Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.100s] Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.100s] writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'
[1.146s] Invoked command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
