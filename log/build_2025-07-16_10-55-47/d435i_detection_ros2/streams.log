[0.649s] Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
[0.776s] /home/<USER>/.local/lib/python3.10/site-packages/setuptools/_distutils/dist.py:289: UserWarning: Unknown distribution option: 'tests_require'
[0.776s]   warnings.warn(msg)
[0.848s] running egg_info
[0.865s] writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO
[0.865s] writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt
[0.866s] writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt
[0.866s] writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt
[0.866s] writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt
[0.905s] reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[0.906s] writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[0.906s] running build
[0.906s] running build_py
[0.906s] copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[0.907s] copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[0.907s] running install
[0.910s] running install_lib
[0.922s] copying /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2
[0.923s] byte-compiling /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2/sim_detection_node.py to sim_detection_node.cpython-310.pyc
[0.927s] running install_data
[0.927s] running install_egg_info
[0.939s] removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)
[0.940s] Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info
[0.940s] running install_scripts
[0.963s] Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.964s] Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.964s] Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[0.964s] writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'
[0.988s] Invoked command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
