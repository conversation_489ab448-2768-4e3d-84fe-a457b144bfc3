[0.015s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[0.070s] [  3%] Built target gtest_main
[0.070s] [  7%] Built target gtest
[0.086s] [ 68%] Built target ocs2_oc
[0.125s] [ 72%] Built target test_change_of_variables
[0.125s] [ 75%] Built target test_ocs2_oc_data
[0.126s] [ 79%] Built target test_precondition
[0.127s] [ 85%] Built target test_ocs2_oc_rollout
[0.132s] [ 88%] Built target test_ocp_to_kkt
[0.132s] [ 92%] Built target test_trajectory_spreading
[0.134s] [100%] Built target test_ocs2_oc_multiple_shooting
[0.147s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_oc -- -j32 -l32
[0.148s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_oc': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
[0.157s] -- Install configuration: "Release"
[0.157s] -- Execute custom install script
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/ChangeOfInputVariables.h
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/approximate_model/LinearQuadraticApproximator.h
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Helpers.h
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Initialization.h
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/LagrangianEvaluation.h
[0.158s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/MetricsComputation.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/PerformanceIndexComputation.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/ProjectionMultiplierCoefficients.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/multiple_shooting/Transcription.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/DualSolution.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/LoopshapingPrimalSolution.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PerformanceIndex.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/PrimalSolution.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/ProblemMetrics.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_data/TimeDiscretization.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/LoopshapingOptimalControlProblem.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpSize.h
[0.159s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OcpToKkt.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblem.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_problem/OptimalControlProblemHelperFunction.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/oc_solver/SolverBase.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/precondition/Ruzi.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/InitializerRollout.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/PerformanceIndicesRollout.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutBase.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RolloutSettings.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinder.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/RootFinderType.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/StateTriggeredRollout.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/rollout/TimeTriggeredRollout.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/search_strategy/FilterLinesearch.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingReferenceManager.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/LoopshapingSynchronizedModule.h
[0.160s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManager.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerDecorator.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/ReferenceManagerInterface.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverObserver.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/synchronized_module/SolverSynchronizedModule.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreading.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/trajectory_adjustment/TrajectorySpreadingHelperFunctions.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/DoubleIntegratorReachingTask.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP0.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/EXP1.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/ball_dynamics_staterollout.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/circular_kinematics.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/dynamics_hybrid_slq_test.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/pendulum_dynamics_staterollout.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/include/ocs2_oc/ocs2_oc/test/testProblemsGeneration.h
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.sh
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/library_path.dsv
[0.161s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/package_run_dependencies/ocs2_oc
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/parent_prefix_path/ocs2_oc
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.sh
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/ament_prefix_path.dsv
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.sh
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/environment/path.dsv
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.bash
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.sh
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.zsh
[0.162s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/local_setup.dsv
[0.162s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.dsv
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ament_index/resource_index/packages/ocs2_oc
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_dependencies-extras.cmake
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ament_cmake_export_targets-extras.cmake
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig.cmake
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/ocs2_ocConfig-version.cmake
[0.170s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/package.xml
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/lib/libocs2_oc.a
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport.cmake
[0.170s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_oc/share/ocs2_oc/cmake/export_ocs2_ocExport-release.cmake
[0.172s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_oc' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_oc
