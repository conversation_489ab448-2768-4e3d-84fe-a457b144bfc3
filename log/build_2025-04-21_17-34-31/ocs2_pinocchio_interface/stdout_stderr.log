[ 15%] Built target gtest_main
[ 30%] Built target gtest
[ 76%] Built target ocs2_pinocchio_interface
[100%] Built target testPinocchioInterface
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioEndEffectorKinematicsCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/PinocchioStateInputMapping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/implementation/PinocchioInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/pinocchio_forward_declaration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/include/ocs2_pinocchio_interface/ocs2_pinocchio_interface/urdf.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/package_run_dependencies/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/parent_prefix_path/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ament_index/resource_index/packages/ocs2_pinocchio_interface
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/pinocchio_config.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/ocs2_pinocchio_interfaceConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib/libocs2_pinocchio_interface.so
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/share/ocs2_pinocchio_interface/cmake/export_ocs2_pinocchio_interfaceExport-release.cmake
