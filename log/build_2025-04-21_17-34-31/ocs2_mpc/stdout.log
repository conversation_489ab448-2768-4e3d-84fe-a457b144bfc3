[100%] Built target ocs2_mpc
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/CommandData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/LoopshapingSystemObservation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_BASE.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_MRT_Interface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MPC_Settings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MRT_BASE.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/MrtObserver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/include/ocs2_mpc/ocs2_mpc/SystemObservation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/package_run_dependencies/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/parent_prefix_path/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ament_index/resource_index/packages/ocs2_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/ocs2_mpcConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/lib/libocs2_mpc.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_mpc/share/ocs2_mpc/cmake/export_ocs2_mpcExport-release.cmake
