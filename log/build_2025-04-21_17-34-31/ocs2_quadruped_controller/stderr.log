In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
   10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
      |                                                                       [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:23[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:24[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:26[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:27[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:28[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
      |                                  [01;35m[K~~~~~~~~~^~~~[m[K
In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:29[m[K:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::LeggedRobotStateQuadraticCost::getStateDeviation(ocs2::scalar_t, const vector_t&, const ocs2::TargetTrajectories&) const[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:93:24:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[KcontactFlags[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
   93 |             const auto [01;35m[KcontactFlags[m[K = referenceManagerPtr_->getContactFlags(time);
      |                        [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedInterface::setupReferenceManager(const string&, const string&, const string&, bool)[m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:198:97:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KurdfFile[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  198 |     void LeggedInterface::setupReferenceManager(const std::string& taskFile, [01;35m[Kconst std::string& urdfFile[m[K,
      |                                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
In file included from [01m[K/usr/include/c++/11/bits/locale_conv.h:41[m[K,
                 from [01m[K/usr/include/c++/11/locale:43[m[K,
                 from [01m[K/usr/include/c++/11/iomanip:43[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_thirdparty/include/cppad/cg/cppadcg.hpp:28[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_thirdparty/include/cppad/cg.hpp:18[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h:36[m[K,
                 from [01m[K/home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/CentroidalModelInfo.h:37[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:8[m[K,
                 from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
/usr/include/c++/11/bits/unique_ptr.h: In instantiation of ‘[01m[Ktypename std::_MakeUniq<_Tp>::__single_object std::make_unique(_Args&& ...) [with _Tp = ocs2::PinocchioGeometryInterface; _Args = {const ocs2::PinocchioInterfaceTpl<double>&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, std::vector<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, std::vector<std::pair<long unsigned int, long unsigned int>, std::allocator<std::pair<long unsigned int, long unsigned int> > >&}; typename std::_MakeUniq<_Tp>::__single_object = std::unique_ptr<ocs2::PinocchioGeometryInterface>][m[K’:
[01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:417:79:[m[K   required from here
[01m[K/usr/include/c++/11/bits/unique_ptr.h:962:30:[m[K [01;31m[Kerror: [m[Kno matching function for call to ‘[01m[Kocs2::PinocchioGeometryInterface::PinocchioGeometryInterface(const ocs2::PinocchioInterfaceTpl<double>&, const std::__cxx11::basic_string<char>&, std::vector<std::pair<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > >&, std::vector<std::pair<long unsigned int, long unsigned int> >&)[m[K’
  962 |     { return unique_ptr<_Tp>([01;31m[Knew _Tp(std::forward<_Args>(__args)...)[m[K); }
      |                              [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated due to -Wfatal-errors.
gmake[2]: *** [CMakeFiles/ocs2_quadruped_controller.dir/build.make:342: CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/ocs2_quadruped_controller.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
