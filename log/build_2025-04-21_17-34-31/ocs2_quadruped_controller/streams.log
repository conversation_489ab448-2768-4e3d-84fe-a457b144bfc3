[0.167s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
[0.296s] [35m[1mConsolidate compiler generated dependencies of target ocs2_quadruped_controller[0m
[0.417s] [  2%] [32mBuilding CXX object CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o[0m
[2.995s] In file included from [01m[K/opt/openrobots/include/hpp/fcl/collision_data.h:1[m[K,
[2.995s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h:32[m[K,
[2.995s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:18[m[K,
[2.995s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[2.995s] [01m[K/opt/openrobots/include/hpp/fcl/coal.hpp:10:71:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: Please update your includes from 'hpp/fcl' to 'coal'[m[K’
[2.995s]    10 | #pragma message("Please update your includes from 'hpp/fcl' to 'coal'"[01;36m[K)[m[K
[2.995s]       |                                                                       [01;36m[K^[m[K
[3.858s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedRobotPreComputation.h:41[m[K,
[3.858s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:23[m[K:
[3.858s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::EndEffectorLinearConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[3.858s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/EndEffectorLinearConstraint.h:79:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.858s]    79 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return numConstraints_; }
[3.858s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[3.864s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:24[m[K:
[3.864s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::FrictionConeConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[3.864s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/FrictionConeConstraint.h:100:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.864s]   100 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; };
[3.864s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[3.871s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:26[m[K:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::NormalVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/NormalVelocityConstraintCppAd.h:62:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.871s]    62 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 1; }
[3.871s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[3.871s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:27[m[K:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroForceConstraint::getNumConstraints(ocs2::scalar_t) const[m[K’:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroForceConstraint.h:55:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.871s]    55 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[3.871s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[3.871s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:28[m[K:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:[m[K In member function ‘[01m[Kvirtual size_t ocs2::legged_robot::ZeroVelocityConstraintCppAd::getNumConstraints(ocs2::scalar_t) const[m[K’:
[3.871s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/constraint/ZeroVelocityConstraintCppAd.h:64:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Ktime[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.871s]    64 |         size_t getNumConstraints([01;35m[Kscalar_t time[m[K) const override { return 3; }
[3.871s]       |                                  [01;35m[K~~~~~~~~~^~~~[m[K
[3.886s] In file included from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:29[m[K:
[3.886s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:[m[K In member function ‘[01m[Kvirtual ocs2::vector_t ocs2::legged_robot::LeggedRobotStateQuadraticCost::getStateDeviation(ocs2::scalar_t, const vector_t&, const ocs2::TargetTrajectories&) const[m[K’:
[3.886s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/cost/LeggedRobotQuadraticTrackingCost.h:93:24:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[KcontactFlags[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[3.886s]    93 |             const auto [01;35m[KcontactFlags[m[K = referenceManagerPtr_->getContactFlags(time);
[3.886s]       |                        [01;35m[K^~~~~~~~~~~~[m[K
[3.980s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:[m[K In member function ‘[01m[Kvirtual void ocs2::legged_robot::LeggedInterface::setupReferenceManager(const string&, const string&, const string&, bool)[m[K’:
[3.980s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:198:97:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KurdfFile[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[3.980s]   198 |     void LeggedInterface::setupReferenceManager(const std::string& taskFile, [01;35m[Kconst std::string& urdfFile[m[K,
[3.980s]       |                                                                              [01;35m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[4.340s] In file included from [01m[K/usr/include/c++/11/bits/locale_conv.h:41[m[K,
[4.340s]                  from [01m[K/usr/include/c++/11/locale:43[m[K,
[4.340s]                  from [01m[K/usr/include/c++/11/iomanip:43[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_thirdparty/include/cppad/cg/cppadcg.hpp:28[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_thirdparty/include/cppad/cg.hpp:18[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h:36[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/install/ocs2_centroidal_model/include/ocs2_centroidal_model/ocs2_centroidal_model/CentroidalModelInfo.h:37[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/include/ocs2_quadruped_controller/interface/LeggedInterface.h:8[m[K,
[4.340s]                  from [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:5[m[K:
[4.340s] /usr/include/c++/11/bits/unique_ptr.h: In instantiation of ‘[01m[Ktypename std::_MakeUniq<_Tp>::__single_object std::make_unique(_Args&& ...) [with _Tp = ocs2::PinocchioGeometryInterface; _Args = {const ocs2::PinocchioInterfaceTpl<double>&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&, std::vector<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, std::vector<std::pair<long unsigned int, long unsigned int>, std::allocator<std::pair<long unsigned int, long unsigned int> > >&}; typename std::_MakeUniq<_Tp>::__single_object = std::unique_ptr<ocs2::PinocchioGeometryInterface>][m[K’:
[4.340s] [01m[K/home/<USER>/ros2_ws/src/quadruped_ros2_control/controllers/ocs2_quadruped_controller/src/interface/LeggedInterface.cpp:417:79:[m[K   required from here
[4.340s] [01m[K/usr/include/c++/11/bits/unique_ptr.h:962:30:[m[K [01;31m[Kerror: [m[Kno matching function for call to ‘[01m[Kocs2::PinocchioGeometryInterface::PinocchioGeometryInterface(const ocs2::PinocchioInterfaceTpl<double>&, const std::__cxx11::basic_string<char>&, std::vector<std::pair<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > >&, std::vector<std::pair<long unsigned int, long unsigned int> >&)[m[K’
[4.340s]   962 |     { return unique_ptr<_Tp>([01;31m[Knew _Tp(std::forward<_Args>(__args)...)[m[K); }
[4.340s]       |                              [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[4.340s] compilation terminated due to -Wfatal-errors.
[4.398s] gmake[2]: *** [CMakeFiles/ocs2_quadruped_controller.dir/build.make:342: CMakeFiles/ocs2_quadruped_controller.dir/src/interface/LeggedInterface.cpp.o] Error 1
[4.399s] gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/ocs2_quadruped_controller.dir/all] Error 2
[4.399s] gmake: *** [Makefile:146: all] Error 2
[4.401s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_quadruped_controller' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_quadruped_controller -- -j32 -l32
