[0.023s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ddp': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ddp -- -j32 -l32
[0.081s] [  4%] Built target gtest
[0.081s] [  9%] Built target gtest_main
[0.098s] [ 46%] Built target ocs2_ddp
[0.124s] [ 51%] Built target testContinuousTimeLqr
[0.125s] [ 56%] Built target exp0_ddp_test
[0.125s] [ 60%] Built target testReachingTask
[0.125s] [ 65%] Built target riccati_ode_test
[0.127s] [ 70%] Built target exp1_ddp_test
[0.129s] [ 75%] Built target testDdpHelperFunction
[0.131s] [ 80%] Built target hybrid_slq_test
[0.132s] [ 85%] Built target correctness_test
[0.141s] [ 90%] Built target circular_kinematics_ddp_test
[0.141s] [100%] Built target bouncing_mass_test
[0.154s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ddp' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_ddp -- -j32 -l32
[0.155s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_ddp': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ddp
[0.163s] -- Install configuration: "Release"
[0.163s] -- Execute custom install script
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ContinuousTimeLqr.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Data.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_HelperFunctions.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Settings.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP_MPC.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/HessianCorrection.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ILQR.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/SLQ.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/ContinuousTimeRiccatiEquations.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/DiscreteTimeRiccatiEquations.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModification.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModificationInterpolation.h
[0.164s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiTransversalityConditions.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LevenbergMarquardtStrategy.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LineSearchStrategy.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/SearchStrategyBase.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/StrategySettings.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/OverallReference.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/Reference.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/SystemModel.h
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.sh
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.dsv
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/package_run_dependencies/ocs2_ddp
[0.165s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/parent_prefix_path/ocs2_ddp
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.sh
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.dsv
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.sh
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.dsv
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.bash
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.sh
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.zsh
[0.166s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.dsv
[0.167s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.dsv
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/packages/ocs2_ddp
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_dependencies-extras.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_targets-extras.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig-version.cmake
[0.176s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.xml
[0.176s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/lib/libocs2_ddp.a
[0.176s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport.cmake
[0.176s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport-release.cmake
[0.190s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_ddp' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_ddp
