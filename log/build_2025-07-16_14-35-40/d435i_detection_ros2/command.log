Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
