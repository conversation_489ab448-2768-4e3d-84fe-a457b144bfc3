[0.722s] Invoking command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
[0.908s] /home/<USER>/.local/lib/python3.10/site-packages/setuptools/_distutils/dist.py:289: UserWarning: Unknown distribution option: 'tests_require'
[0.908s]   warnings.warn(msg)
[1.046s] running egg_info
[1.069s] writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO
[1.069s] writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt
[1.070s] writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt
[1.070s] writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt
[1.070s] writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt
[1.099s] reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[1.101s] writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'
[1.101s] running build
[1.101s] running build_py
[1.101s] copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[1.101s] copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2
[1.101s] running install
[1.105s] running install_lib
[1.116s] copying /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2
[1.117s] byte-compiling /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2/sim_detection_node.py to sim_detection_node.cpython-310.pyc
[1.121s] running install_data
[1.122s] running install_egg_info
[1.139s] removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)
[1.139s] Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info
[1.140s] running install_scripts
[1.172s] Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.172s] Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.172s] Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2
[1.172s] writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'
[1.191s] Invoked command in '/home/<USER>/ros2_ws/src/d435i_detection_ros2' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/d435i_detection_ros2 build --build-base /home/<USER>/ros2_ws/build/d435i_detection_ros2/build install --record /home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log --single-version-externally-managed install_data
