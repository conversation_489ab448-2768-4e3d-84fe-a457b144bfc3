[100%] Built target ocs2_self_collision
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/PinocchioGeometryInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/SelfCollision.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/SelfCollisionConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/SelfCollisionConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/include/ocs2_self_collision/ocs2_self_collision/SelfCollisionCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ament_index/resource_index/package_run_dependencies/ocs2_self_collision
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ament_index/resource_index/parent_prefix_path/ocs2_self_collision
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ament_index/resource_index/packages/ocs2_self_collision
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/ocs2_self_collisionConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/ocs2_self_collisionConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_self_collision/lib/libocs2_self_collision.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/export_ocs2_self_collisionExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_self_collision/share/ocs2_self_collision/cmake/export_ocs2_self_collisionExport-release.cmake
