[ 25%] Built target ocs2_legged_robot_ros
[ 50%] Built target legged_robot_ddp_mpc
[ 50%] Built target legged_robot_sqp_mpc
[ 62%] Built target legged_robot_target
[ 75%] Built target legged_robot_gait_command
[ 87%] Built target legged_robot_dummy
[100%] Built target legged_robot_ipm_mpc
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/GaitKeyboardPublisher.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/GaitReceiver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/gait/ModeSequenceTemplateRos.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/include/ocs2_legged_robot_ros/ocs2_legged_robot_ros/visualization/LeggedRobotVisualizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_ddp_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_sqp_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_ipm_mpc
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_dummy
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_target
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/ocs2_legged_robot_ros/legged_robot_gait_command
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/basic.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/dummy.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/gait_command.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_ddp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_ipm.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/legged_robot_sqp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/mpc_ddp.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/multiplot.launch
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/multiplot.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//launch/robot_target.launch.py
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros//rviz/legged_robot.rviz
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/package_run_dependencies/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/parent_prefix_path/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ament_index/resource_index/packages/ocs2_legged_robot_ros
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/ocs2_legged_robot_rosConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib/libocs2_legged_robot_ros.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/share/ocs2_legged_robot_ros/cmake/export_ocs2_legged_robot_rosExport-release.cmake
