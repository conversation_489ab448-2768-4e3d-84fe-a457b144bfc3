[ 11%] Built target gtest_main
[ 23%] Built target gtest
[ 58%] Built target ocs2_ipm
[100%] Built target test_ocs2_ipm
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmHelpers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmInitialization.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmMpc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmPerformanceIndexComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSolver.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/include/ocs2_ipm/ocs2_ipm/IpmSolverStatus.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/package_run_dependencies/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/parent_prefix_path/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ament_index/resource_index/packages/ocs2_ipm
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ocs2_ipmConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/ocs2_ipmConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ipm/lib/libocs2_ipm.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/export_ocs2_ipmExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ipm/share/ocs2_ipm/cmake/export_ocs2_ipmExport-release.cmake
