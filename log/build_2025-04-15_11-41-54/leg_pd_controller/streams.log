[0.005s] Invoking command in '/home/<USER>/ros2_ws/build/leg_pd_controller': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/leg_pd_controller -- -j32 -l32
[0.096s] [100%] Built target leg_pd_controller
[0.114s] Invoked command in '/home/<USER>/ros2_ws/build/leg_pd_controller' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/leg_pd_controller -- -j32 -l32
[0.125s] Invoking command in '/home/<USER>/ros2_ws/build/leg_pd_controller': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/leg_pd_controller
[0.137s] -- Install configuration: "Release"
[0.138s] -- Execute custom install script
[0.138s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller//leg_pd_controller.xml
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/include/leg_pd_controller/leg_pd_controller/LegPdController.h
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.sh
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.dsv
[0.139s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/package_run_dependencies/leg_pd_controller
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/parent_prefix_path/leg_pd_controller
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.sh
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.dsv
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.sh
[0.140s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.dsv
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.bash
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.sh
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.zsh
[0.141s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.dsv
[0.141s] -- Symlinking: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.dsv
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/packages/leg_pd_controller
[0.147s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/leg_pd_controller
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_targets-extras.cmake
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig.cmake
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig-version.cmake
[0.148s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.xml
[0.157s] -- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so
[0.158s] -- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport.cmake
[0.158s] -- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport-release.cmake
[0.162s] Invoked command in '/home/<USER>/ros2_ws/build/leg_pd_controller' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/leg_pd_controller
