[0.000000] (-) TimerEvent: {}
[0.000134] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000152] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000162] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000168] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000174] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000180] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000185] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000191] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000196] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000202] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000208] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000214] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.000219] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.000224] (-) JobUnselected: {'identifier': 'elevation_mapping'}
[0.000230] (-) JobUnselected: {'identifier': 'go1_description'}
[0.000236] (-) JobUnselected: {'identifier': 'go2_description'}
[0.000241] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.000247] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.000254] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.000261] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.000266] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.000272] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.000278] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.000283] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.000294] (-) JobUnselected: {'identifier': 'kindr_msgs'}
[0.000300] (-) JobUnselected: {'identifier': 'kindr_ros'}
[0.000305] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.000311] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.000317] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.000323] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.000328] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.000334] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.000340] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.000345] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.000351] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.000356] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.000362] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.000368] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.000374] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.000379] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.000385] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.000391] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.000397] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.000403] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.000409] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.000415] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.000420] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.000426] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.000432] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.000437] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.000443] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.000448] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.000454] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.000460] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.000465] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.000471] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.000477] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.000483] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.000488] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.000494] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.000500] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.000509] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.000515] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.000521] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.000526] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.000532] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.000538] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.000544] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.000549] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.000554] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.000560] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.000565] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.000570] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.000575] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.000581] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.000586] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.000591] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.000601] (-) JobUnselected: {'identifier': 'x30_description'}
[0.000607] (leg_pd_controller) JobQueued: {'identifier': 'leg_pd_controller', 'dependencies': OrderedDict()}
[0.000615] (leg_pd_controller) JobStarted: {'identifier': 'leg_pd_controller'}
[0.005794] (leg_pd_controller) JobProgress: {'identifier': 'leg_pd_controller', 'progress': 'cmake'}
[0.006285] (leg_pd_controller) JobProgress: {'identifier': 'leg_pd_controller', 'progress': 'build'}
[0.006352] (leg_pd_controller) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/build/leg_pd_controller', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/build/leg_pd_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/leg_pd_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.086604] (leg_pd_controller) StdoutLine: {'line': b'[100%] Built target leg_pd_controller\n'}
[0.096926] (leg_pd_controller) CommandEnded: {'returncode': 0}
[0.097849] (leg_pd_controller) JobProgress: {'identifier': 'leg_pd_controller', 'progress': 'install'}
[0.099559] (-) TimerEvent: {}
[0.104431] (leg_pd_controller) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/build/leg_pd_controller'], 'cwd': '/home/<USER>/ros2_ws/build/leg_pd_controller', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'en'), ('USER', 'cg215'), ('LC_TIME', 'zh_CN.UTF-8'), ('all_proxy', 'socks://127.0.0.1:7891/'), ('XDG_SESSION_TYPE', 'x11'), ('__GLX_VENDOR_LIBRARY_NAME', 'nvidia'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ros2_ws'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NO_PROXY', 'localhost,*********/8,::1'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('LC_CTYPE', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '3064'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('COLORTERM', 'truecolor'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('https_proxy', 'http://127.0.0.1:7890/'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'cg215'), ('ALL_PROXY', 'socks://127.0.0.1:7891/'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'cg215'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/cg215:@/tmp/.ICE-unix/3064,unix/cg215:/tmp/.ICE-unix/3064'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/46275c2f_7705_4d3e_b196_2dee664ae817'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('__NV_PRIME_RENDER_OFFLOAD', '1'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.127'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '43'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ros2_ws/build/leg_pd_controller'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=c5749dd6eae214e9c0832e2867ff1c5b'), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble')]), 'shell': False}
[0.112180] (leg_pd_controller) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.113014] (leg_pd_controller) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.113324] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller//leg_pd_controller.xml\n'}
[0.113641] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/include/leg_pd_controller/leg_pd_controller/LegPdController.h\n'}
[0.113808] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.sh\n'}
[0.113870] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/library_path.dsv\n'}
[0.113965] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/package_run_dependencies/leg_pd_controller\n'}
[0.114101] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/parent_prefix_path/leg_pd_controller\n'}
[0.114184] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.sh\n'}
[0.114404] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/ament_prefix_path.dsv\n'}
[0.114547] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.sh\n'}
[0.114614] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/environment/path.dsv\n'}
[0.114668] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.bash\n'}
[0.114750] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.sh\n'}
[0.114852] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.zsh\n'}
[0.114902] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/local_setup.dsv\n'}
[0.114947] (leg_pd_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.dsv\n'}
[0.123746] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/packages/leg_pd_controller\n'}
[0.123921] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/ament_index/resource_index/controller_interface__pluginlib__plugin/leg_pd_controller\n'}
[0.124037] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.124113] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.124223] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig.cmake\n'}
[0.124319] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/leg_pd_controllerConfig-version.cmake\n'}
[0.124425] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/package.xml\n'}
[0.124573] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/lib/leg_pd_controller/libleg_pd_controller.so\n'}
[0.124806] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport.cmake\n'}
[0.124896] (leg_pd_controller) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/install/leg_pd_controller/share/leg_pd_controller/cmake/export_leg_pd_controllerExport-release.cmake\n'}
[0.127838] (leg_pd_controller) CommandEnded: {'returncode': 0}
[0.138254] (leg_pd_controller) JobEnded: {'identifier': 'leg_pd_controller', 'rc': 0}
[0.138532] (-) EventReactorShutdown: {}
