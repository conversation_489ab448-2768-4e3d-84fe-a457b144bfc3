[  4%] Built target gtest_main
[  9%] Built target gtest
[ 46%] Built target ocs2_ddp
[ 51%] Built target exp0_ddp_test
[ 56%] Built target exp1_ddp_test
[ 60%] Built target testContinuousTimeLqr
[ 65%] Built target testDdpHelperFunction
[ 70%] Built target riccati_ode_test
[ 75%] Built target testReachingTask
[ 80%] Built target correctness_test
[ 90%] Built target bouncing_mass_test
[ 95%] Built target circular_kinematics_ddp_test
[100%] Built target hybrid_slq_test
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ContinuousTimeLqr.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Data.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_HelperFunctions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/DDP_Settings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/GaussNewtonDDP_MPC.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/HessianCorrection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/ILQR.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/SLQ.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/ContinuousTimeRiccatiEquations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/DiscreteTimeRiccatiEquations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModification.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiModificationInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/riccati_equations/RiccatiTransversalityConditions.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LevenbergMarquardtStrategy.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/LineSearchStrategy.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/SearchStrategyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/search_strategy/StrategySettings.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/OverallReference.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/Reference.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/include/ocs2_ddp/ocs2_ddp/test/bouncingmass/SystemModel.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/package_run_dependencies/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/parent_prefix_path/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ament_index/resource_index/packages/ocs2_ddp
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/ocs2_ddpConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/lib/libocs2_ddp.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_ddp/share/ocs2_ddp/cmake/export_ocs2_ddpExport-release.cmake
