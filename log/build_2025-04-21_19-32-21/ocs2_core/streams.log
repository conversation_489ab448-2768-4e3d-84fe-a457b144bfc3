[0.028s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_core': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_core -- -j32 -l32
[0.125s] [  1%] Built target gtest_main
[0.126s] [  2%] Built target gtest
[0.207s] [ 57%] Built target ocs2_core
[0.250s] [ 59%] Built target interpolation_unittest
[0.251s] [ 60%] Built target initialization_unittest
[0.253s] [ 62%] Built target test_metrics
[0.253s] [ 63%] Built target ocs2_core_test_thread_support
[0.253s] [ 64%] Built target test_ModelData
[0.255s] [ 66%] Built target test_transferfunctionbase
[0.255s] [ 68%] Built target test_ModeSchedule
[0.259s] [ 69%] Built target test_multiplier
[0.260s] [ 71%] Built target ocs2_core_test_core
[0.261s] [ 73%] Built target test_control
[0.262s] [ 75%] Built target test_softConstraint
[0.263s] [ 77%] Built target test_dynamics
[0.263s] [ 81%] Built target test_cost
[0.263s] [ 83%] Built target test_integration
[0.264s] [ 86%] Built target test_constraint
[0.265s] [ 90%] Built target ocs2_core_test_misc
[0.269s] [ 93%] Built target ocs2_core_cppadcg
[0.273s] [100%] Built target ocs2_core_loopshaping
[0.297s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_core' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_core -- -j32 -l32
[0.299s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_core': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_core
[0.310s] -- Install configuration: "Release"
[0.311s] -- Execute custom install script
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/ComputationRequest.h
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/NumericTraits.h
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/PreComputation.h
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/Types.h
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/AugmentedLagrangian.h
[0.314s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangian.h
[0.315s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianCollection.h
[0.316s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianInterface.h
[0.316s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangian.h
[0.316s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianCollection.h
[0.316s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianInterface.h
[0.316s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdInterface.h
[0.317s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdSparsity.h
[0.317s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/FiniteDifferenceMethods.h
[0.317s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h
[0.317s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/ConstraintOrder.h
[0.317s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateConstraint.h
[0.318s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateInputConstraint.h
[0.318s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraint.h
[0.318s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCollection.h
[0.319s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCppAd.h
[0.319s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraint.h
[0.319s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCollection.h
[0.319s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCppAd.h
[0.319s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerAdjustmentBase.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerBase.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerType.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/FeedforwardController.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/LinearController.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/StateBasedLinearController.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateCost.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateInputCost.h
[0.320s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCost.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCollection.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCppAd.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCost.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCollection.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCppAd.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputGaussNewtonCostAd.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/ControlledSystemBase.h
[0.321s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/LinearSystemDynamics.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBase.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBaseAD.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsLinearizer.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/TransferFunctionBase.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/DefaultInitializer.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/Initializer.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/OperatingPoints.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Integrator.h
[0.322s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/IntegratorBase.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Observer.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeBase.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeFunc.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/RungeKuttaDormandPrince5.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegrator.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegratorImpl.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/StateTriggeredEventHandler.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SystemEventHandler.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/TrapezoidalIntegration.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/eigenIntegration.h
[0.323s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/implementation/Integrator.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/steppers.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/Loopshaping.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingDefinition.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingFilter.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPreComputation.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPropertyTree.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.h
[0.324s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraint.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintEliminatePattern.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintOutputPattern.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateConstraint.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateInputConstraint.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCost.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostEliminatePattern.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostOutputPattern.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateCost.h
[0.325s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateInputCost.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamics.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingFilterDynamics.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/initialization/LoopshapingInitializer.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraint.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.h
[0.326s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Benchmark.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Collection.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/CommandLine.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Display.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LTI_Equations.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearAlgebra.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearFunction.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearInterpolation.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadData.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadStdVectorOfPair.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Log.h
[0.327s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Lookup.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Numerics.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/implementation/LinearInterpolation.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/randomMatrices.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Metrics.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelData.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelDataLinearInterpolation.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Multiplier.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/MultidimensionalPenalty.h
[0.328s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/Penalties.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/AugmentedPenaltyBase.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/ModifiedRelaxedBarrierPenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/QuadraticPenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SlacknessSquaredHingePenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SmoothAbsolutePenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/DoubleSidedPenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/PenaltyBase.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/QuadraticPenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/RelaxedBarrierPenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SmoothAbsolutePenalty.h
[0.329s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SquaredHingePenalty.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/ModeSchedule.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/TargetTrajectories.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftBoxConstraint.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftConstraint.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateSoftConstraint.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/BufferedValue.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ExecuteAndSleep.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/SetThreadPriority.h
[0.330s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/Synchronized.h
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ThreadPool.h
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/test/testTools.h
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.sh
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.dsv
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/package_run_dependencies/ocs2_core
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/parent_prefix_path/ocs2_core
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.sh
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.dsv
[0.331s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.sh
[0.332s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.dsv
[0.332s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.bash
[0.332s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.sh
[0.332s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.zsh
[0.332s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.dsv
[0.332s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.dsv
[0.339s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/packages/ocs2_core
[0.339s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_cxx_flags.cmake
[0.339s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_targets-extras.cmake
[0.340s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_dependencies-extras.cmake
[0.340s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig.cmake
[0.340s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig-version.cmake
[0.340s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.xml
[0.340s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/lib/libocs2_core.a
[0.340s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport.cmake
[0.341s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport-release.cmake
[0.364s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_core' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_core
