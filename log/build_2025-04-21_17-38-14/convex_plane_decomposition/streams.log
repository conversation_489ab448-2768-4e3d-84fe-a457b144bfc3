[0.045s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition -- -j32 -l32
[0.111s] [  8%] Built target gtest_main
[0.117s] [ 17%] Built target gtest
[0.175s] [ 73%] Built target convex_plane_decomposition
[0.248s] [100%] Built target test_convex_plane_decomposition
[0.261s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --build /home/<USER>/ros2_ws/build/convex_plane_decomposition -- -j32 -l32
[0.264s] Invoking command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition
[0.273s] -- Install configuration: ""
[0.274s] -- Execute custom install script
[0.274s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ConvexRegionGrowing.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Draw.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/GeometryUtils.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/GridMapPreprocessing.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/LoadGridmapFromImage.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PlanarRegion.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PlaneDecompositionPipeline.h
[0.275s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/PolygonTypes.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Postprocessing.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/SegmentedPlaneProjection.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/SegmentedPlanesMap.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/Timer.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/ContourExtraction.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/ContourExtractionParameters.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/contour_extraction/Upsampling.h
[0.276s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ransac/RansacPlaneExtractor.hpp
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/ransac/RansacPlaneExtractorParameters.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/sliding_window_plane_extraction/SlidingWindowPlaneExtractor.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/include/convex_plane_decomposition/convex_plane_decomposition/sliding_window_plane_extraction/SlidingWindowPlaneExtractorParameters.h
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/library_path.sh
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/library_path.dsv
[0.277s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/package_run_dependencies/convex_plane_decomposition
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/parent_prefix_path/convex_plane_decomposition
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/ament_prefix_path.sh
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/ament_prefix_path.dsv
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/path.sh
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/environment/path.dsv
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.bash
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.sh
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.zsh
[0.278s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/local_setup.dsv
[0.279s] -- Symlinking: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/package.dsv
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/ament_index/resource_index/packages/convex_plane_decomposition
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ConfigExtras.cmake
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ament_cmake_export_dependencies-extras.cmake
[0.288s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/ament_cmake_export_targets-extras.cmake
[0.289s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/convex_plane_decompositionConfig.cmake
[0.289s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/convex_plane_decompositionConfig-version.cmake
[0.289s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/package.xml
[0.289s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition/lib/libconvex_plane_decomposition.a
[0.289s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport.cmake
[0.289s] -- Up-to-date: /home/<USER>/ros2_ws/install/convex_plane_decomposition/share/convex_plane_decomposition/cmake/export_convex_plane_decompositionExport-noconfig.cmake
[0.292s] Invoked command in '/home/<USER>/ros2_ws/build/convex_plane_decomposition' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/cgal5_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:${LD_LIBRARY_PATH} /usr/bin/cmake --install /home/<USER>/ros2_ws/build/convex_plane_decomposition
