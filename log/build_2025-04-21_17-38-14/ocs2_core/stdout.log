[  1%] Built target gtest_main
[  2%] Built target gtest
[ 57%] Built target ocs2_core
[ 58%] Built target initialization_unittest
[ 61%] Built target interpolation_unittest
[ 62%] Built target test_transferfunctionbase
[ 63%] Built target test_multiplier
[ 65%] Built target test_ModeSchedule
[ 66%] Built target test_metrics
[ 68%] Built target test_control
[ 73%] Built target test_integration
[ 73%] Built target test_ModelData
[ 75%] Built target test_softConstraint
[ 77%] Built target test_dynamics
[ 80%] Built target ocs2_core_test_core
[ 81%] Built target ocs2_core_test_thread_support
[ 84%] Built target test_constraint
[ 86%] Built target test_cost
[ 89%] Built target ocs2_core_cppadcg
[ 93%] Built target ocs2_core_test_misc
[100%] Built target ocs2_core_loopshaping
-- Install configuration: "Release"
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/ComputationRequest.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/NumericTraits.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/PreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/AugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateAugmentedLagrangianInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/augmented_lagrangian/StateInputAugmentedLagrangianInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdInterface.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/CppAdSparsity.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/FiniteDifferenceMethods.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/automatic_differentiation/Types.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/ConstraintOrder.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/LinearStateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/constraint/StateInputConstraintCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerAdjustmentBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/ControllerType.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/FeedforwardController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/LinearController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/control/StateBasedLinearController.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/QuadraticStateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateCostCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCollection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputCostCppAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/cost/StateInputGaussNewtonCostAd.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/ControlledSystemBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/LinearSystemDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsBaseAD.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/SystemDynamicsLinearizer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/dynamics/TransferFunctionBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/DefaultInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/Initializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/initialization/OperatingPoints.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Integrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/IntegratorBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/Observer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/OdeFunc.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/RungeKuttaDormandPrince5.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SensitivityIntegratorImpl.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/StateTriggeredEventHandler.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/SystemEventHandler.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/TrapezoidalIntegration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/eigenIntegration.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/implementation/Integrator.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/integration/steppers.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/Loopshaping.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingDefinition.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingFilter.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPreComputation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/LoopshapingPropertyTree.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingAugmentedLagrangianOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/augmented_lagrangian/LoopshapingStateInputAugmentedLagrangian.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingConstraintOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/constraint/LoopshapingStateInputConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingCostOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/cost/LoopshapingStateInputCost.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingDynamicsOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/dynamics/LoopshapingFilterDynamics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/initialization/LoopshapingInitializer.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintEliminatePattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingSoftConstraintOutputPattern.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/loopshaping/soft_constraint/LoopshapingStateInputSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Benchmark.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Collection.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/CommandLine.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Display.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LTI_Equations.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearAlgebra.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearFunction.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/LoadStdVectorOfPair.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Log.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Lookup.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/Numerics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/implementation/LinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/misc/randomMatrices.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Metrics.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelData.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/ModelDataLinearInterpolation.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/model_data/Multiplier.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/MultidimensionalPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/Penalties.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/AugmentedPenaltyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/ModifiedRelaxedBarrierPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/QuadraticPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SlacknessSquaredHingePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/augmented/SmoothAbsolutePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/DoubleSidedPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/PenaltyBase.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/QuadraticPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/RelaxedBarrierPenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SmoothAbsolutePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/penalties/penalties/SquaredHingePenalty.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/ModeSchedule.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/reference/TargetTrajectories.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftBoxConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateInputSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/soft_constraint/StateSoftConstraint.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/BufferedValue.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ExecuteAndSleep.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/SetThreadPriority.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/Synchronized.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/thread_support/ThreadPool.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/include/ocs2_core/ocs2_core/test/testTools.h
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/package_run_dependencies/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/parent_prefix_path/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/local_setup.dsv
-- Symlinking: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.dsv
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ament_index/resource_index/packages/ocs2_core
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_cxx_flags.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/ocs2_coreConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/package.xml
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/lib/libocs2_core.a
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport.cmake
-- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_core/share/ocs2_core/cmake/export_ocs2_coreExport-release.cmake
