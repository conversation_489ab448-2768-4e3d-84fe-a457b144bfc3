[0.039s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_legged_robot': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_legged_robot -- -j32 -l32
[0.094s] [  7%] Built target gtest_main
[0.097s] [ 14%] Built target gtest
[0.137s] [ 82%] Built target ocs2_legged_robot
[0.196s] [100%] Built target ocs2_legged_robot_test
[0.206s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_legged_robot' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/ros2_ws/build/ocs2_legged_robot -- -j32 -l32
[0.207s] Invoking command in '/home/<USER>/ros2_ws/build/ocs2_legged_robot': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_legged_robot
[0.213s] -- Install configuration: "Release"
[0.214s] -- Execute custom install script
[0.214s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotInterface.h
[0.214s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/LeggedRobotPreComputation.h
[0.214s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/ModelSettings.h
[0.214s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/Types.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/common/utils.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/EndEffectorLinearConstraint.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/FrictionConeConstraint.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/NormalVelocityConstraintCppAd.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroForceConstraint.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/constraint/ZeroVelocityConstraintCppAd.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/cost/LeggedRobotQuadraticTrackingCost.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/dynamics/LeggedRobotDynamicsAD.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/CubicSpline.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SplineCpg.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/foot_planner/SwingTrajectoryPlanner.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/Gait.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/GaitSchedule.h
[0.215s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/LegLogic.h
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/ModeSequenceTemplate.h
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/gait/MotionPhaseDefinition.h
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/initialization/LeggedRobotInitializer.h
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/package_path.h.in
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/include/ocs2_legged_robot/ocs2_legged_robot/reference_manager/SwitchedModelReferenceManager.h
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/gait.info
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/command/reference.info
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/mpc/task.info
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/friction_cone.xml
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot//config/multiplot/zero_velocity.xml
[0.216s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.sh
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/library_path.dsv
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/package_run_dependencies/ocs2_legged_robot
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/parent_prefix_path/ocs2_legged_robot
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.sh
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/ament_prefix_path.dsv
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.sh
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/environment/path.dsv
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.bash
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.sh
[0.217s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.zsh
[0.218s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/local_setup.dsv
[0.218s] -- Symlinking: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.dsv
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ament_index/resource_index/packages/ocs2_legged_robot
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_dependencies-extras.cmake
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ament_cmake_export_targets-extras.cmake
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig.cmake
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/ocs2_legged_robotConfig-version.cmake
[0.226s] -- Up-to-date symlink: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/package.xml
[0.226s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/lib/libocs2_legged_robot.a
[0.226s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport.cmake
[0.226s] -- Up-to-date: /home/<USER>/ros2_ws/install/ocs2_legged_robot/share/ocs2_legged_robot/cmake/export_ocs2_legged_robotExport-release.cmake
[0.229s] Invoked command in '/home/<USER>/ros2_ws/build/ocs2_legged_robot' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/blasfeo_colcon:/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/ros2_ws/build/ocs2_legged_robot
