function [ qpFeatures ] = setupQpFeaturesStruct( )

    qpFeatures = struct(    'nV',  0, ...
                            'nIB', 0, ...
                            'nEB', 0, ...
                            'nC',  0, ...
                            'nIC', 0, ...
                            'nEC', 0, ...
                            'nActB', 0, ...
                            'nActC', 0, ...
                            'hasLowerB', 0, ...
                            'hasUpperB', 0, ...
                            'hasLowerC', 0, ...
                            'hasUpperC', 0, ...
                            'isSparseH', 0, ...
                            'isSparseA', 0, ...
                            'makeInfeas', 0, ...
                            'hessianType', 0 ... % 0 = pos def; 1 = pos sem def; 2 = id; 3 = zero
                            );

end
