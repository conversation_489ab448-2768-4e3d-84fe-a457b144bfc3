#!/usr/bin/env python3
"""
仿真环境图像检测分割节点
订阅仿真环境中的/rgbd/image和/rgbd/depth_image话题
实现图像检测、分割和点云分割功能，用于生成分割高程图
修复版本：解决点云坐标系问题，添加检测+分割结合图片，支持原始和分割高程图
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, DurabilityPolicy
import cv2
import numpy as np
import time
import sys
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
import struct

# ROS2 消息类型
from sensor_msgs.msg import Image, PointCloud2, PointField, CameraInfo
from geometry_msgs.msg import Point, Vector3
from std_msgs.msg import Header, String
from cv_bridge import CvBridge

# 导入检测模块
current_dir = Path('/home/<USER>/ros2_ws/src/d435i_detetion')
sys.path.append(str(current_dir))

try:
    if current_dir.exists():
        from d435i_yolo_integration import YOLOv7D435iDetector
        import torch
        DETECTION_AVAILABLE = True
        print(f"成功导入检测模块，路径: {current_dir}")
    else:
        print(f"检测模块路径不存在: {current_dir}")
        DETECTION_AVAILABLE = False
except ImportError as e:
    print(f"警告: 无法导入检测模块: {e}")
    DETECTION_AVAILABLE = False


class SimDetectionNode(Node):
    """仿真环境图像检测分割节点"""
    
    def __init__(self):
        super().__init__('sim_detection_node')
        
        # 声明参数
        self.declare_node_parameters()
        
        # 初始化变量
        self.bridge = CvBridge()
        self.detector = None
        self.frame_count = 0
        self.last_time = time.time()
        
        # 图像缓存
        self.latest_rgb_image = None
        self.latest_depth_image = None
        self.rgb_timestamp = None
        self.depth_timestamp = None
        
        # 设置QoS配置
        self.setup_qos()
        
        # 创建订阅者
        self.setup_subscribers()
        
        # 创建发布者
        self.setup_publishers()
        
        # 初始化检测器
        if DETECTION_AVAILABLE:
            self.initialize_detector()
        else:
            self.get_logger().error("检测模块不可用，节点将退出")
            return
        
        # 创建定时器进行检测处理
        timer_period = 1.0 / self.get_parameter('fps').value
        self.timer = self.create_timer(timer_period, self.timer_callback)
        
        self.get_logger().info("仿真环境检测节点已启动")
    
    def declare_node_parameters(self):
        """声明ROS2参数"""
        # 检测参数
        self.declare_parameter('weights_path', 'yolov7-segmentation/weights/yolov7-seg.pt')
        self.declare_parameter('conf_threshold', 0.25)
        self.declare_parameter('iou_threshold', 0.45)
        self.declare_parameter('max_detections', 1000)
        self.declare_parameter('device', '')
        self.declare_parameter('fps', 5.0)
        
        # 点云参数
        self.declare_parameter('enable_pointcloud', True)
        self.declare_parameter('enable_segmented_pointcloud', True)
        
        # 分割颜色参数
        self.declare_parameter('use_stable_colors', True)
        self.declare_parameter('color_intensity', 1.0)
        self.declare_parameter('background_dimming', 0.3)
        self.declare_parameter('debug_color_info', True)
        
        # 输入话题参数
        self.declare_parameter('rgb_topic', '/rgbd/image')
        self.declare_parameter('depth_topic', '/rgbd/depth_image')
        
        # 输出话题参数
        self.declare_parameter('image_detection_topic', '/sim_detection/image_detection')
        self.declare_parameter('image_segmentation_topic', '/sim_detection/image_segmentation')
        self.declare_parameter('image_combined_topic', '/sim_detection/image_combined')
        self.declare_parameter('pointcloud_raw_topic', '/sim_detection/pointcloud_raw')
        self.declare_parameter('pointcloud_segmented_topic', '/sim_detection/pointcloud_segmented')
        self.declare_parameter('detections_topic', '/sim_detection/detections')
        self.declare_parameter('camera_info_topic', '/sim_detection/camera_info')
    
    def setup_qos(self):
        """设置QoS配置"""
        self.image_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=1,
            durability=DurabilityPolicy.VOLATILE
        )
        
        self.pointcloud_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=1,
            durability=DurabilityPolicy.VOLATILE
        )
    
    def setup_subscribers(self):
        """创建订阅者"""
        self.rgb_sub = self.create_subscription(
            Image,
            self.get_parameter('rgb_topic').value,
            self.rgb_callback,
            self.image_qos
        )
        
        self.depth_sub = self.create_subscription(
            Image,
            self.get_parameter('depth_topic').value,
            self.depth_callback,
            self.image_qos
        )
    
    def setup_publishers(self):
        """创建发布者"""
        # 图像发布者
        self.image_detection_pub = self.create_publisher(
            Image, self.get_parameter('image_detection_topic').value, self.image_qos)
        self.image_segmentation_pub = self.create_publisher(
            Image, self.get_parameter('image_segmentation_topic').value, self.image_qos)
        self.image_combined_pub = self.create_publisher(
            Image, self.get_parameter('image_combined_topic').value, self.image_qos)
        
        # 点云发布者
        if self.get_parameter('enable_pointcloud').value:
            self.pointcloud_raw_pub = self.create_publisher(
                PointCloud2, self.get_parameter('pointcloud_raw_topic').value, self.pointcloud_qos)
            if self.get_parameter('enable_segmented_pointcloud').value:
                self.pointcloud_segmented_pub = self.create_publisher(
                    PointCloud2, self.get_parameter('pointcloud_segmented_topic').value, self.pointcloud_qos)
        
        # 检测结果发布者
        self.detections_pub = self.create_publisher(
            String, self.get_parameter('detections_topic').value, self.pointcloud_qos)
        
        # 相机信息发布者
        self.camera_info_pub = self.create_publisher(
            CameraInfo, self.get_parameter('camera_info_topic').value, self.pointcloud_qos)
    
    def initialize_detector(self):
        """初始化检测器"""
        try:
            weights_path = self.get_parameter('weights_path').value
            if not os.path.isabs(weights_path):
                weights_path = str(current_dir / weights_path)
            
            self.detector = YOLOv7D435iDetector(
                weights=weights_path,
                device=self.get_parameter('device').value,
                conf_thres=self.get_parameter('conf_threshold').value,
                iou_thres=self.get_parameter('iou_threshold').value,
                max_det=self.get_parameter('max_detections').value
            )
            
            self.get_logger().info("检测器初始化成功")
            return True
            
        except Exception as e:
            self.get_logger().error(f"检测器初始化失败: {e}")
            return False
    
    def rgb_callback(self, msg):
        """RGB图像回调函数"""
        try:
            self.latest_rgb_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
            self.rgb_timestamp = msg.header.stamp
        except Exception as e:
            self.get_logger().error(f"RGB图像转换失败: {e}")
    
    def depth_callback(self, msg):
        """深度图像回调函数"""
        try:
            # 仿真环境的深度图通常是32FC1格式
            self.latest_depth_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='32FC1')
            self.depth_timestamp = msg.header.stamp
        except Exception as e:
            self.get_logger().error(f"深度图像转换失败: {e}")
    
    def timer_callback(self):
        """定时器回调函数"""
        if (self.latest_rgb_image is None or self.latest_depth_image is None or 
            not self.detector):
            return
        
        try:
            # 检查图像时间戳同步
            if (self.rgb_timestamp is None or self.depth_timestamp is None):
                return
            
            # 简单的时间戳同步检查（允许100ms差异）
            time_diff = abs((self.rgb_timestamp.sec + self.rgb_timestamp.nanosec * 1e-9) - 
                          (self.depth_timestamp.sec + self.depth_timestamp.nanosec * 1e-9))
            if time_diff > 0.1:  # 100ms
                return
            
            # 复制图像以避免并发问题
            rgb_image = self.latest_rgb_image.copy()
            depth_image = self.latest_depth_image.copy()

            # 使用原始点云的时间戳，确保与机器人位姿同步
            if hasattr(self, 'rgb_timestamp') and self.rgb_timestamp:
                timestamp = self.rgb_timestamp
            else:
                timestamp = self.get_clock().now().to_msg()
            
            # 转换深度图格式（仿真环境通常以米为单位）
            # 将float32深度图转换为uint16格式（毫米）
            depth_image_mm = (depth_image * 1000).astype(np.uint16)
            
            # 执行检测
            results = self.detector.detect(rgb_image, depth_image_mm)
            
            # 发布检测结果
            self.publish_detection_results(rgb_image, depth_image, results, timestamp)
            
            # 更新统计信息
            self.frame_count += 1
            if self.frame_count % 30 == 0:
                current_time = time.time()
                fps = 30.0 / (current_time - self.last_time)
                self.get_logger().info(f"处理FPS: {fps:.1f}, 检测到 {len(results['detections'])} 个对象")
                self.last_time = current_time
                
        except Exception as e:
            self.get_logger().error(f"处理帧时出错: {e}")
            import traceback
            self.get_logger().error(f"详细错误: {traceback.format_exc()}")
    
    def create_combined_image(self, rgb_image: np.ndarray, results: Dict[str, Any]) -> np.ndarray:
        """创建检测+分割结合的图片"""
        try:
            combined_image = rgb_image.copy()
            
            # 如果有分割掩码，先绘制分割区域
            if 'masks' in results and len(results['masks']) > 0:
                height, width = rgb_image.shape[:2]
                
                # 创建分割覆盖层
                overlay = np.zeros_like(rgb_image)
                
                for i, mask in enumerate(results['masks']):
                    mask_np = mask.cpu().numpy()
                    mask_resized = cv2.resize(mask_np, (width, height))
                    mask_binary = mask_resized > 0.5
                    
                    # 为每个对象使用不同颜色
                    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
                             (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
                    color = colors[i % len(colors)]
                    
                    overlay[mask_binary] = color
                
                # 将分割覆盖层与原图混合
                alpha = 0.4
                combined_image = cv2.addWeighted(combined_image, 1-alpha, overlay, alpha, 0)
            
            # 绘制检测框和标签
            if 'detections' in results:
                for detection in results['detections']:
                    bbox = detection['bbox']
                    x1, y1, x2, y2 = map(int, bbox)
                    
                    # 绘制检测框
                    cv2.rectangle(combined_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 绘制标签
                    label = f"{detection['class_name']}: {detection['confidence']:.2f}"
                    if detection.get('distance', 0) > 0:
                        label += f" ({detection['distance']:.2f}m)"
                    
                    # 计算文本大小
                    (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)
                    
                    # 绘制文本背景
                    cv2.rectangle(combined_image, (x1, y1 - text_height - 10), 
                                (x1 + text_width, y1), (0, 255, 0), -1)
                    
                    # 绘制文本
                    cv2.putText(combined_image, label, (x1, y1 - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            return combined_image
            
        except Exception as e:
            self.get_logger().error(f"创建结合图片时出错: {e}")
            return rgb_image

    def publish_detection_results(self, rgb_image: np.ndarray, depth_image: np.ndarray,
                                results: Dict[str, Any], timestamp):
        """发布检测结果"""
        try:
            # 发布检测图像
            if 'detection_only_image' in results:
                detection_msg = self.bridge.cv2_to_imgmsg(results['detection_only_image'], encoding='bgr8')
                detection_msg.header.stamp = timestamp
                detection_msg.header.frame_id = 'd435i_depth_frame'
                self.image_detection_pub.publish(detection_msg)

            # 发布分割图像
            if 'segmentation_only_image' in results:
                segmentation_msg = self.bridge.cv2_to_imgmsg(results['segmentation_only_image'], encoding='bgr8')
                segmentation_msg.header.stamp = timestamp
                segmentation_msg.header.frame_id = 'd435i_depth_frame'
                self.image_segmentation_pub.publish(segmentation_msg)

            # 发布检测+分割结合图像
            combined_image = self.create_combined_image(rgb_image, results)
            combined_msg = self.bridge.cv2_to_imgmsg(combined_image, encoding='bgr8')
            combined_msg.header.stamp = timestamp
            combined_msg.header.frame_id = 'd435i_depth_frame'
            self.image_combined_pub.publish(combined_msg)

            # 发布点云
            if self.get_parameter('enable_pointcloud').value:
                self.publish_pointclouds(rgb_image, depth_image, results, timestamp)

            # 发布检测信息
            self.publish_detections(results, timestamp)

            # 发布相机信息
            self.publish_camera_info(timestamp)

        except Exception as e:
            self.get_logger().error(f"发布检测结果时出错: {e}")

    def create_pointcloud_from_depth(self, color_image: np.ndarray, depth_image: np.ndarray,
                                   masks=None, det=None) -> Optional[PointCloud2]:
        """从深度图创建PointCloud2消息 - 修复坐标系问题"""
        try:
            # 仿真相机内参（根据仿真环境调整）
            height, width = color_image.shape[:2]
            fx = fy = 554.25  # 仿真相机焦距
            cx = width / 2
            cy = height / 2

            points = []
            colors = []

            # 准备分割掩码
            segmentation_mask = None
            mask_indices = {}

            if masks is not None and det is not None:
                segmentation_mask = np.zeros((height, width, 3), dtype=np.uint8)

                # 高对比度颜色映射
                class_color_map = {
                    0: [255, 0, 0],      # person - 红色
                    1: [0, 255, 0],      # bicycle - 绿色
                    2: [0, 0, 255],      # car - 蓝色
                    3: [255, 255, 0],    # motorcycle - 黄色
                    4: [255, 0, 255],    # airplane - 洋红色
                    5: [0, 255, 255],    # bus - 青色
                    6: [255, 165, 0],    # train - 橙色
                    7: [128, 0, 128],    # truck - 紫色
                    8: [255, 192, 203],  # boat - 粉红色
                    9: [50, 205, 50],    # traffic light - 酸橙绿
                }

                default_colors = [
                    [255, 69, 0], [50, 205, 50], [30, 144, 255], [255, 20, 147],
                    [255, 215, 0], [138, 43, 226], [220, 20, 60], [0, 191, 255]
                ]

                for i, mask in enumerate(masks):
                    mask_np = mask.cpu().numpy()
                    mask_resized = cv2.resize(mask_np, (width, height))
                    mask_binary = mask_resized > 0.5

                    y_indices, x_indices = np.where(mask_binary)

                    # 为每个像素分配掩码索引
                    for y, x in zip(y_indices, x_indices):
                        mask_indices[(y, x)] = i

                    # 获取颜色
                    if i < len(det):
                        class_id = int(det[i, 5])
                        if class_id in class_color_map:
                            color = class_color_map[class_id]
                        else:
                            color = default_colors[class_id % len(default_colors)]
                    else:
                        color = default_colors[i % len(default_colors)]

                    # BGR格式存储
                    segmentation_mask[mask_binary] = [color[2], color[1], color[0]]

            # 生成点云 - 修复坐标系问题
            step = 2  # 降采样以提高性能
            valid_points = 0
            segmented_points = 0

            for v in range(0, height, step):
                for u in range(0, width, step):
                    depth = depth_image[v, u]

                    # 仿真环境深度过滤（米为单位）
                    if depth <= 0.01 or depth > 10.0:
                        continue

                    valid_points += 1

                    # 使用与仿真环境/rgbd/points相同的坐标变换
                    # 标准相机坐标系变换（与仿真环境保持一致）
                    x = (u - cx) * depth / fx
                    y = (v - cy) * depth / fy
                    z = depth

                    points.append([x, y, z])

                    # 选择颜色
                    if segmentation_mask is not None:
                        mask_idx = mask_indices.get((v, u), None)
                        if mask_idx is not None:
                            seg_color = segmentation_mask[v, u]
                            colors.append([seg_color[2], seg_color[1], seg_color[0]])  # 转换为RGB
                            segmented_points += 1
                        else:
                            # 背景使用暗灰色
                            original_color = color_image[v, u]
                            dimming_factor = self.get_parameter('background_dimming').value
                            gray_value = int(0.299 * original_color[2] + 0.587 * original_color[1] + 0.114 * original_color[0])
                            dimmed_gray = int(gray_value * dimming_factor * 0.5)
                            colors.append([dimmed_gray, dimmed_gray, dimmed_gray])
                    else:
                        # 原始颜色
                        original_color = color_image[v, u]
                        colors.append([original_color[2], original_color[1], original_color[0]])

            # 统计信息
            if self.frame_count % 30 == 0:
                if segmentation_mask is not None:
                    self.get_logger().info(f"分割点云: 总点数={valid_points}, 分割点数={segmented_points}")
                else:
                    self.get_logger().info(f"原始点云: 总点数={valid_points}")

            if not points:
                return None

            return self.create_pointcloud2_msg(points, colors)

        except Exception as e:
            self.get_logger().error(f"创建点云时出错: {e}")
            return None

    def create_pointcloud2_msg(self, points: List[List[float]], colors: List[List[int]]) -> PointCloud2:
        """创建PointCloud2消息"""
        # 定义点云字段
        fields = [
            PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
            PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
            PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1),
            PointField(name='rgb', offset=12, datatype=PointField.FLOAT32, count=1),
        ]

        # 创建点云数据
        cloud_data = bytearray()
        for point, color in zip(points, colors):
            # 打包XYZ坐标
            cloud_data.extend(struct.pack('fff', point[0], point[1], point[2]))

            # 打包RGB颜色
            r, g, b = max(0, min(255, int(color[0]))), max(0, min(255, int(color[1]))), max(0, min(255, int(color[2])))
            rgb_int = (r << 16) | (g << 8) | b
            rgb_packed = struct.pack('I', rgb_int)
            rgb_float = struct.unpack('f', rgb_packed)[0]
            cloud_data.extend(struct.pack('f', rgb_float))

        # 创建PointCloud2消息
        cloud_msg = PointCloud2()
        cloud_msg.header.frame_id = 'd435i_depth_frame'
        # 时间戳将在调用函数中设置
        cloud_msg.height = 1
        cloud_msg.width = len(points)
        cloud_msg.fields = fields
        cloud_msg.is_bigendian = False
        cloud_msg.point_step = 16
        cloud_msg.row_step = cloud_msg.point_step * cloud_msg.width
        cloud_msg.data = bytes(cloud_data)
        cloud_msg.is_dense = True

        return cloud_msg

    def publish_pointclouds(self, color_image: np.ndarray, depth_image: np.ndarray,
                           results: Dict[str, Any], timestamp):
        """发布点云数据"""
        try:
            # 原始点云
            raw_pointcloud = self.create_pointcloud_from_depth(color_image, depth_image)
            if raw_pointcloud:
                raw_pointcloud.header.stamp = timestamp
                self.get_logger().debug(f"发布原始点云，时间戳: {timestamp}")
                self.pointcloud_raw_pub.publish(raw_pointcloud)

            # 分割点云
            if (self.get_parameter('enable_segmented_pointcloud').value and
                len(results.get('masks', [])) > 0 and len(results.get('detections', [])) > 0):

                # 准备检测数据
                det_data = []
                for detection in results['detections']:
                    bbox = detection['bbox']
                    det_data.append([bbox[0], bbox[1], bbox[2], bbox[3],
                                   detection['confidence'], detection['class_id']])

                if det_data:
                    det_tensor = torch.tensor(det_data)
                    segmented_pointcloud = self.create_pointcloud_from_depth(
                        color_image, depth_image, results['masks'], det_tensor)

                    if segmented_pointcloud:
                        segmented_pointcloud.header.stamp = timestamp
                        self.pointcloud_segmented_pub.publish(segmented_pointcloud)

        except Exception as e:
            self.get_logger().error(f"发布点云时出错: {e}")

    def publish_detections(self, results: Dict[str, Any], timestamp):
        """发布检测结果"""
        try:
            detections = results.get('detections', [])
            if detections:
                detection_info = []
                for detection in detections:
                    bbox = detection['bbox']
                    info = f"{detection['class_name']}:{detection['confidence']:.2f}"
                    if detection.get('distance', 0) > 0:
                        info += f":{detection['distance']:.2f}m"
                    detection_info.append(info)

                msg = String()
                msg.data = f"[{len(detections)}] " + " | ".join(detection_info)
                self.detections_pub.publish(msg)
            else:
                msg = String()
                msg.data = "[0] No detections"
                self.detections_pub.publish(msg)

        except Exception as e:
            self.get_logger().error(f"发布检测结果时出错: {e}")

    def publish_camera_info(self, timestamp):
        """发布相机信息"""
        try:
            camera_info = CameraInfo()
            camera_info.header.stamp = timestamp
            camera_info.header.frame_id = 'd435i_depth_frame'

            # 仿真相机参数
            camera_info.width = 640
            camera_info.height = 480

            # 内参矩阵
            fx = fy = 554.25
            cx = camera_info.width / 2
            cy = camera_info.height / 2

            camera_info.k = [fx, 0.0, cx,
                           0.0, fy, cy,
                           0.0, 0.0, 1.0]

            camera_info.p = [fx, 0.0, cx, 0.0,
                           0.0, fy, cy, 0.0,
                           0.0, 0.0, 1.0, 0.0]

            camera_info.d = [0.0, 0.0, 0.0, 0.0, 0.0]
            camera_info.distortion_model = "plumb_bob"

            self.camera_info_pub.publish(camera_info)

        except Exception as e:
            self.get_logger().error(f"发布相机信息时出错: {e}")


def main(args=None):
    """主函数"""
    rclpy.init(args=args)

    try:
        node = SimDetectionNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"节点运行出错: {e}")
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
