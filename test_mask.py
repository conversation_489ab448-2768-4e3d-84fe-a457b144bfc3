#!/usr/bin/env python3
import os
import torch
import numpy as np
import cv2
import sys

# 将YOLOv7-segmentation的路径添加到sys.path
YOLO_DIR = '/home/<USER>/ros2_ws/src/d435i_detetion/yolov7-segmentation'
if os.path.exists(YOLO_DIR):
    if YOLO_DIR not in sys.path:
        sys.path.insert(0, YOLO_DIR)
        print(f"Added {YOLO_DIR} to sys.path")
    
    # 添加models子目录
    models_dir = os.path.join(YOLO_DIR, 'models')
    if os.path.exists(models_dir) and models_dir not in sys.path:
        sys.path.insert(0, models_dir)
        print(f"Added {models_dir} to sys.path")

# 导入YOLO模块
try:
    from models.common import DetectMultiBackend
    from utils.general import check_img_size, non_max_suppression
    from utils.segment.general import process_mask
    from utils.segment.plots import plot_masks
    from utils.torch_utils import select_device
    from utils.plots import colors
    print("成功导入YOLOv7模块")
except ImportError as e:
    print(f"导入YOLOv7模块时出错: {e}")
    sys.exit(1)

def preprocess_image(image, imgsz=(640, 640)):
    """预处理图像用于YOLO推理"""
    # 调整大小
    img = cv2.resize(image, (imgsz[0], imgsz[1]))
    
    # 转换为RGB并规范化
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR转RGB, HWC转CHW
    img = np.ascontiguousarray(img)
    img = torch.from_numpy(img).float() / 255.0  # 0-255转0.0-1.0
    
    if len(img.shape) == 3:
        img = img[None]  # 扩展批次维度
    
    return img

def main():
    # 检查CUDA是否可用
    device = select_device('')
    print(f"使用设备: {device}")
    
    # 加载模型
    weights = os.path.join(YOLO_DIR, 'weights', 'yolov7-seg.pt')
    if not os.path.exists(weights):
        print(f"模型文件不存在: {weights}")
        sys.exit(1)
    
    model = DetectMultiBackend(weights, device=device)
    stride, names = model.stride, model.names
    imgsz = check_img_size((640, 640), s=stride)
    
    # 准备一个测试图像 - 使用已有的图像
    image_path = '/home/<USER>/ros_images/publish_image_debug.png'
    if not os.path.exists(image_path):
        print(f"测试图像不存在: {image_path}")
        sys.exit(1)
    
    # 读取图像
    img = cv2.imread(image_path)
    print(f"图像形状: {img.shape}")
    
    # 预处理图像
    img_tensor = preprocess_image(img, imgsz)
    img_tensor = img_tensor.to(device)
    
    # 推理
    with torch.no_grad():
        outputs = model(img_tensor)
        
    # 输出结果的形状
    for i, output in enumerate(outputs):
        if isinstance(output, torch.Tensor):
            print(f"输出 {i}: 形状={output.shape}, 类型={output.dtype}")
        elif isinstance(output, tuple):
            print(f"输出 {i}: 元组，长度={len(output)}")
            for j, subout in enumerate(output):
                if isinstance(subout, torch.Tensor):
                    print(f"  子输出 {j}: 形状={subout.shape}, 类型={subout.dtype}")
                else:
                    print(f"  子输出 {j}: 类型={type(subout)}")
        else:
            print(f"输出 {i}: 类型={type(output)}")
    
    # 处理检测结果
    pred, proto = outputs[0], outputs[1]
    
    # 运行NMS
    conf_thres = 0.25  # 置信度阈值
    iou_thres = 0.45   # IoU阈值
    results = non_max_suppression(pred, conf_thres, iou_thres, nm=32)
    
    # 检查是否有检测结果
    if len(results[0]) == 0:
        print("没有检测到物体")
        return
    
    # 获取检测结果
    det = results[0]
    print(f"检测到 {len(det)} 个物体")
    
    # 获取原始图像大小
    h, w = img.shape[:2]
    
    # 创建目录
    debug_dir = '/tmp/mask_test'
    os.makedirs(debug_dir, exist_ok=True)
    
    # 检查分割系数
    if det.shape[1] > 6:
        print(f"分割系数形状: {det[:, 6:].shape}")
        
        # 获取proto数据
        if isinstance(proto, tuple) and len(proto) > 1:
            proto_data = proto[1]
            print(f"使用proto[1]，形状: {proto_data.shape}")
        else:
            proto_data = proto
            print(f"使用proto，形状: {proto_data.shape}")
            
        # 确保proto_data是3D的形状 [c, h, w]
        if len(proto_data.shape) != 3:
            print(f"Warning: proto_data不是3D形状，尝试调整")
            if len(proto_data.shape) == 4 and proto_data.shape[0] == 1:
                proto_data = proto_data.squeeze(0)  # 移除批次维度
                print(f"调整后proto_data形状: {proto_data.shape}")
            else:
                print(f"无法调整proto_data到3D形状")
                return
        
        # 显示proto_data的范围
        print(f"proto_data值范围: min={proto_data.min().item():.4f}, max={proto_data.max().item():.4f}, mean={proto_data.mean().item():.4f}")
        
        # 处理掩码
        masks = process_mask(proto_data, det[:, 6:], det[:, :4], img_tensor.shape[2:], upsample=True)
        
        if masks is not None:
            print(f"掩码形状: {masks.shape}")
            
            # 保存单独的掩码
            masks_np = masks.cpu().numpy()
            for i, mask in enumerate(masks_np):
                mask_vis = (mask * 255).astype(np.uint8)
                mask_path = os.path.join(debug_dir, f'mask_{i}.png')
                cv2.imwrite(mask_path, mask_vis)
                print(f"已保存掩码 {i} 到 {mask_path}")
            
            # 创建颜色列表
            mcolors = [colors(int(cls), True) for cls in det[:, 5]]
            
            # 使用plot_masks函数创建可视化
            try:
                im_gpu = img_tensor[0].clone()
                result = plot_masks(im_gpu, masks, mcolors)
                
                if result is not None:
                    print(f"plot_masks返回形状: {result.shape}")
                    result_path = os.path.join(debug_dir, 'masks_result.png')
                    cv2.imwrite(result_path, result)
                    print(f"已保存分割结果到 {result_path}")
                else:
                    print("plot_masks返回了None")
            except Exception as e:
                print(f"使用plot_masks时出错: {e}")
                import traceback
                traceback.print_exc()
                
            # 手动创建掩码可视化
            try:
                mask_image = img.copy()
                for i, mask in enumerate(masks_np):
                    if i < len(det):
                        cls = int(det[i, 5].item())
                        color = [int(c) for c in colors(cls, True)]
                        
                        # 调整掩码大小以匹配原始图像
                        if mask.shape != (h, w):
                            mask_resized = cv2.resize(mask, (w, h))
                        else:
                            mask_resized = mask
                        
                        # 创建二值掩码
                        binary_mask = (mask_resized > 0.5).astype(np.uint8)
                        
                        # 将掩码应用到图像
                        for c in range(3):  # 对BGR三个通道分别处理
                            mask_image[:,:,c] = mask_image[:,:,c] * (1 - 0.5 * binary_mask) + color[c] * 0.5 * binary_mask
                
                manual_path = os.path.join(debug_dir, 'manual_masks.png')
                cv2.imwrite(manual_path, mask_image)
                print(f"已保存手动创建的分割结果到 {manual_path}")
            except Exception as e:
                print(f"手动创建掩码可视化时出错: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("process_mask返回了None")
    else:
        print("检测结果中没有分割系数")

if __name__ == "__main__":
    main() 