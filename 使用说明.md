# D435i ROS2检测系统使用说明

## 系统概述

本系统将您现有的D435i YOLO检测系统转换为ROS2格式，实现了以下功能：

### ✅ 已实现的功能

1. **图像话题发布**：
   - `/d435i_detection/image_raw` - 原始RGB图像
   - `/d435i_detection/image_detection` - 带检测框的图像  
   - `/d435i_detection/image_segmentation` - 带分割掩码的图像
   - `/d435i_detection/image_combined` - 检测+分割合并图像
   - `/d435i_detection/depth_image` - 深度彩色图

2. **点云话题发布**：
   - `/d435i_detection/pointcloud_raw` - 原始点云 (PointCloud2格式)
   - `/d435i_detection/pointcloud_segmented` - 分割后的点云

3. **检测结果话题**：
   - `/d435i_detection/detections` - 结构化检测信息
   - `/d435i_detection/camera_info` - 相机内参信息

4. **rviz2可视化**：
   - 实时显示所有图像和点云数据
   - 3D点云可视化
   - 检测结果叠加显示

## 快速开始

### 方法1：使用演示脚本（推荐）

```bash
cd /home/<USER>/ros2_ws
./demo_d435i_ros2.sh
```

选择选项1启动完整系统（检测节点 + rviz2）

### 方法2：手动启动

```bash
# 1. 进入工作空间
cd /home/<USER>/ros2_ws

# 2. Source环境
source /opt/ros/humble/setup.bash
source install/setup.bash

# 3. 启动系统
ros2 launch d435i_detection_ros2 d435i_detection.launch.py
```

## 在rviz2中查看结果

启动后，rviz2会自动打开并显示：

### 图像面板
- **Image_Raw**: 原始相机图像
- **Image_Detection**: 带检测框的图像
- **Image_Segmentation**: 分割掩码图像  
- **Image_Combined**: 检测+分割合并图像
- **Depth_Image**: 深度彩色图

### 3D视图
- **PointCloud2_Raw**: 原始点云（彩色）
- **PointCloud2_Segmented**: 分割后的点云（分割区域高亮显示）

### 操作提示
1. 可以在左侧面板中勾选/取消勾选不同的显示项
2. 使用鼠标在3D视图中旋转、缩放查看点云
3. 图像面板可以调整大小和位置

## 系统对比

| 功能 | 原始系统 | ROS2系统 |
|------|---------|----------|
| 显示方式 | 4个OpenCV窗口 | rviz2统一界面 |
| 数据格式 | 本地显示 | ROS2话题发布 |
| 点云格式 | Open3D显示 | PointCloud2标准格式 |
| 集成性 | 独立程序 | ROS2生态系统 |
| 可扩展性 | 有限 | 高度可扩展 |

## 参数调整

可以通过launch参数调整系统行为：

```bash
ros2 launch d435i_detection_ros2 d435i_detection.launch.py \
    camera_width:=640 \
    camera_height:=480 \
    conf_threshold:=0.3 \
    device:=0 \
    enable_pointcloud:=true
```

主要参数：
- `conf_threshold`: 检测置信度阈值 (默认0.25)
- `device`: 推理设备 ("cpu", "0", "1"等)
- `camera_width/height`: 相机分辨率
- `enable_pointcloud`: 是否启用点云功能

## 测试和调试

### 检查话题发布

```bash
# 查看所有话题
ros2 topic list | grep d435i_detection

# 查看话题频率
ros2 topic hz /d435i_detection/image_raw

# 查看检测结果
ros2 topic echo /d435i_detection/detections
```

### 运行测试订阅者

```bash
python3 test_d435i_ros2.py
```

这会显示各个话题的发布频率和统计信息。

## 故障排除

### 1. 相机连接问题
```bash
# 检查USB设备
lsusb | grep Intel

# 检查权限
sudo chmod 666 /dev/bus/usb/*/*
```

### 2. 权重文件问题
确保权重文件存在：
```bash
ls -la src/d435i_detetion/yolov7-segmentation/weights/yolov7-seg.pt
```

### 3. 性能问题
- 降低处理频率：设置 `fps:=5.0`
- 使用GPU：设置 `device:=0`
- 禁用点云：设置 `enable_pointcloud:=false`

### 4. rviz2显示问题
- 检查Fixed Frame设置为 `base_link`
- 确保话题名称正确
- 重新加载rviz配置

## 扩展功能

系统已经为扩展做好准备：

1. **添加新的图像处理节点**：订阅发布的图像话题
2. **集成导航系统**：使用点云数据进行SLAM
3. **添加机器人控制**：基于检测结果控制机器人行为
4. **数据记录**：使用rosbag记录所有话题数据

## 技术细节

- **坐标系**: 使用标准ROS相机坐标系
- **消息格式**: 遵循ROS2标准消息格式
- **QoS设置**: 图像使用BEST_EFFORT，点云使用RELIABLE
- **性能优化**: 点云降采样，异步处理

现在您可以在rviz2中实时查看D435i的检测分割结果、深度图、原图和点云数据了！
