<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-03-19T18:19:50</created>
    <modified>2019-03-19T18:19:50</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">49.13434</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <YF_dofdist sid="YF_dofdist" type="float">0</YF_dofdist>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="Lamp-light" name="Lamp">
      <technique_common>
        <point>
          <color sid="color">1 1 1</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">8192</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <halo_intensity sid="blnder_halo_intensity" type="float">1</halo_intensity>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">1.000799</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <compressthresh sid="compressthresh" type="float">0.04999995</compressthresh>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <filtertype sid="filtertype" type="int">0</filtertype>
          <bufflag sid="bufflag" type="int">0</bufflag>
          <buftype sid="buftype" type="int">2</buftype>
          <ray_samp sid="ray_samp" type="int">1</ray_samp>
          <ray_sampy sid="ray_sampy" type="int">1</ray_sampy>
          <ray_sampz sid="ray_sampz" type="int">1</ray_sampz>
          <ray_samp_type sid="ray_samp_type" type="int">0</ray_samp_type>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
          <adapt_thresh sid="adapt_thresh" type="float">0.000999987</adapt_thresh>
          <ray_samp_method sid="ray_samp_method" type="int">1</ray_samp_method>
          <shadhalostep sid="shadhalostep" type="int">0</shadhalostep>
          <sun_effect_type sid="sun_effect_type" type="int">0</sun_effect_type>
          <skyblendtype sid="skyblendtype" type="int">1</skyblendtype>
          <horizon_brightness sid="horizon_brightness" type="float">1</horizon_brightness>
          <spread sid="spread" type="float">1</spread>
          <sun_brightness sid="sun_brightness" type="float">1</sun_brightness>
          <sun_size sid="sun_size" type="float">1</sun_size>
          <backscattered_light sid="backscattered_light" type="float">1</backscattered_light>
          <sun_intensity sid="sun_intensity" type="float">1</sun_intensity>
          <atm_turbidity sid="atm_turbidity" type="float">2</atm_turbidity>
          <atm_extinction_factor sid="atm_extinction_factor" type="float">1</atm_extinction_factor>
          <atm_distance_factor sid="atm_distance_factor" type="float">1</atm_distance_factor>
          <skyblendfac sid="skyblendfac" type="float">1</skyblendfac>
          <sky_exposure sid="sky_exposure" type="float">1</sky_exposure>
          <sky_colorspace sid="sky_colorspace" type="int">0</sky_colorspace>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_images/>
  <library_effects>
    <effect id="Material_001-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0.1 0.1 0.1 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.627451 0.627451 0.627451 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="Material_001-material" name="Material_001">
      <instance_effect url="#Material_001-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="ring_small-mesh" name="ring small">
      <mesh>
        <source id="ring_small-mesh-positions">
          <float_array id="ring_small-mesh-positions-array" count="696">0.03358554 0.005604445 -0.008249998 0.03404998 0 0.008249998 0.03404998 0 -0.008249998 0.03358554 -0.005604445 0.008249998 0.03358554 -0.005604445 -0.008249998 0.03220504 -0.011056 0.008249998 0.03220504 -0.011056 -0.008249998 0.02994602 -0.01620596 0.008249998 0.02994602 -0.01620596 -0.008249998 0.02687019 -0.02091389 0.008249998 0.02687019 -0.02091389 -0.008249998 0.02306139 -0.02505135 0.008249998 0.02306139 -0.02505135 -0.008249998 0.01862359 -0.0285055 0.008249998 0.01862359 -0.0285055 -0.008249998 0.01367771 -0.03118205 0.008249998 0.01367771 -0.03118205 -0.008249998 0.008358776 -0.03300803 0.008249998 0.008358776 -0.03300803 -0.008249998 0.002811789 -0.03393369 0.008249998 0.002811789 -0.03393369 -0.008249998 -0.002811789 -0.03393369 0.008249998 -0.002811789 -0.03393369 -0.008249998 -0.008358776 -0.03300803 0.008249998 -0.008358776 -0.03300803 -0.008249998 -0.01367771 -0.03118205 0.008249998 -0.01367771 -0.03118205 -0.008249998 -0.01862359 -0.0285055 0.008249998 -0.01862359 -0.0285055 -0.008249998 -0.02306139 -0.02505135 0.008249998 -0.02306139 -0.02505135 -0.008249998 -0.02687019 -0.02091389 0.008249998 -0.02687019 -0.02091389 -0.008249998 -0.02994602 -0.01620596 0.008249998 -0.02994602 -0.01620596 -0.008249998 -0.03220504 -0.011056 0.008249998 -0.03220504 -0.011056 -0.008249998 -0.03358554 -0.005604445 0.008249998 -0.03358554 -0.005604445 -0.008249998 -0.03404998 0 0.008249998 -0.03404998 0 -0.008249998 -0.03358554 0.005604445 0.008249998 -0.03358554 0.005604445 -0.008249998 -0.03220504 0.011056 0.008249998 -0.03220504 0.011056 -0.008249998 -0.02994602 0.01620596 0.008249998 -0.02994602 0.01620596 -0.008249998 -0.02687019 0.02091389 0.008249998 -0.02687019 0.02091389 -0.008249998 -0.02306139 0.02505135 0.008249998 -0.02306139 0.02505135 -0.008249998 -0.01862359 0.0285055 0.008249998 -0.01862359 0.0285055 -0.008249998 -0.01367771 0.03118205 0.008249998 -0.01367771 0.03118205 -0.008249998 -0.008358776 0.03300803 0.008249998 -0.008358776 0.03300803 -0.008249998 -0.002811789 0.03393369 0.008249998 -0.002811789 0.03393369 -0.008249998 0.002811789 0.03393369 0.008249998 0.002811789 0.03393369 -0.008249998 0.008358776 0.03300803 0.008249998 0.008358776 0.03300803 -0.008249998 0.01367771 0.03118205 0.008249998 0.01367771 0.03118205 -0.008249998 0.01862359 0.0285055 0.008249998 0.01862359 0.0285055 -0.008249998 0.02306139 0.02505135 0.008249998 0.02306139 0.02505135 -0.008249998 0.02687019 0.02091389 0.008249998 0.02687019 0.02091389 -0.008249998 0.02994602 0.01620596 0.008249998 0.02994602 0.01620596 -0.008249998 0.03220504 0.011056 0.008249998 0.03220504 0.011056 -0.008249998 0.03358554 0.005604445 0.008249998 -0.03210604 0.005357503 0.009749948 -0.03078633 0.01056891 0.009749948 -0.02862685 0.01549208 0.009749948 -0.0256865 0.01999258 0.009749948 -0.02204549 0.02394777 0.009749948 -0.01780313 0.02724975 0.009749948 -0.01307517 0.0298084 0.009749948 -0.007990539 0.03155398 0.009749948 -0.002687931 0.03243881 0.009749948 0.002687931 0.03243881 0.009749948 0.007990539 0.03155398 0.009749948 0.01307517 0.0298084 0.009749948 0.01780313 0.02724975 0.009749948 0.02204549 0.02394777 0.009749948 0.0256865 0.01999258 0.009749948 0.02862685 0.01549208 0.009749948 0.03078633 0.01056891 0.009749948 0.03210604 0.005357503 0.009749948 0.03404998 0 0.008249998 0.03254997 0 0.009749948 0.03210604 -0.005357503 0.009749948 0.03078633 -0.01056891 0.009749948 0.02862685 -0.01549208 0.009749948 0.0256865 -0.01999258 0.009749948 0.02204549 -0.02394777 0.009749948 0.01780313 -0.02724975 0.009749948 0.01307517 -0.0298084 0.009749948 0.007990539 -0.03155398 0.009749948 0.002687931 -0.03243881 0.009749948 -0.002687931 -0.03243881 0.009749948 -0.007990539 -0.03155398 0.009749948 -0.01307517 -0.0298084 0.009749948 -0.01780313 -0.02724975 0.009749948 -0.02204549 -0.02394777 0.009749948 -0.0256865 -0.01999258 0.009749948 -0.02862685 -0.01549208 0.009749948 -0.03078633 -0.01056891 0.009749948 -0.03210604 -0.005357503 0.009749948 -0.03404998 0 0.008249998 -0.03254997 0 0.009749948 0.03210604 0.005357503 -0.009749948 0.03078633 0.01056891 -0.009749948 0.02862685 0.01549208 -0.009749948 0.0256865 0.01999258 -0.009749948 0.02204549 0.02394777 -0.009749948 0.01780313 0.02724975 -0.009749948 0.01307517 0.0298084 -0.009749948 0.007990539 0.03155398 -0.009749948 0.002687931 0.03243881 -0.009749948 -0.002687931 0.03243881 -0.009749948 -0.007990539 0.03155398 -0.009749948 -0.01307517 0.0298084 -0.009749948 -0.01780313 0.02724975 -0.009749948 -0.02204549 0.02394777 -0.009749948 -0.0256865 0.01999258 -0.009749948 -0.02862685 0.01549208 -0.009749948 -0.03078633 0.01056891 -0.009749948 -0.03210604 0.005357503 -0.009749948 -0.03404998 0 -0.008249998 -0.03254997 0 -0.009749948 -0.03210604 -0.005357503 -0.009749948 -0.03078633 -0.01056891 -0.009749948 -0.02862685 -0.01549208 -0.009749948 -0.0256865 -0.01999258 -0.009749948 -0.02204549 -0.02394777 -0.009749948 -0.01780313 -0.02724975 -0.009749948 -0.01307517 -0.0298084 -0.009749948 -0.007990539 -0.03155398 -0.009749948 -0.002687931 -0.03243881 -0.009749948 0.002687931 -0.03243881 -0.009749948 0.007990539 -0.03155398 -0.009749948 0.01307517 -0.0298084 -0.009749948 0.01780313 -0.02724975 -0.009749948 0.02204549 -0.02394777 -0.009749948 0.0256865 -0.01999258 -0.009749948 0.02862685 -0.01549208 -0.009749948 0.03078633 -0.01056891 -0.009749948 0.03210604 -0.005357503 -0.009749948 0.03254997 0 -0.009749948 -0.004054367 0.03166145 0.009749948 -0.009346902 0.03052079 0.009749948 -0.0143705 0.02850216 0.009749948 -0.01898068 0.02566355 0.009749948 -0.02304482 0.02208667 0.009749948 -0.02644598 0.01787436 0.009749948 -0.02908641 0.01314789 0.009749948 -0.03088998 0.00804311 0.009749948 -0.03180497 0.002707004 0.009749948 -0.03180497 -0.002707004 0.009749948 -0.03088998 -0.00804311 0.009749948 -0.02908641 -0.01314789 0.009749948 -0.02644598 -0.01787436 0.009749948 -0.02304482 -0.02208667 0.009749948 -0.01898068 -0.02566355 0.009749948 -0.0143705 -0.02850216 0.009749948 -0.009346902 -0.03052079 0.009749948 -0.004054367 -0.03166145 0.009749948 0.001354694 -0.03189122 0.009749948 0.006724834 -0.03120356 0.009749948 0.01190155 -0.0296182 0.009749948 0.01673585 -0.02718079 0.009749948 0.02108871 -0.02396142 0.009749948 0.02483487 -0.02005273 0.009749948 0.0278666 -0.01556718 0.009749948 0.03009665 -0.01063382 0.009749948 0.03146082 -0.005394458 0.009749948 0.03191995 0 0.009749948 0.03254997 0 0.009749948 0.03146082 0.005394458 0.009749948 0.03009665 0.01063382 0.009749948 0.0278666 0.01556718 0.009749948 0.02483487 0.02005273 0.009749948 0.02108871 0.02396142 0.009749948 0.01673585 0.02718079 0.009749948 0.01190155 0.0296182 0.009749948 0.006724834 0.03120356 0.009749948 0.001354694 0.03189122 0.009749948 -0.004054367 -0.03166145 -0.009749948 -0.009346902 -0.03052079 -0.009749948 -0.0143705 -0.02850216 -0.009749948 -0.01898068 -0.02566355 -0.009749948 -0.02304482 -0.02208667 -0.009749948 -0.02644598 -0.01787436 -0.009749948 -0.02908641 -0.01314789 -0.009749948 -0.03088998 -0.00804311 -0.009749948 -0.03180497 -0.002707004 -0.009749948 -0.03254997 0 -0.009749948 -0.03180497 0.002707004 -0.009749948 -0.03088998 0.00804311 -0.009749948 -0.02908641 0.01314789 -0.009749948 -0.02644598 0.01787436 -0.009749948 -0.02304482 0.02208667 -0.009749948 -0.01898068 0.02566355 -0.009749948 -0.0143705 0.02850216 -0.009749948 -0.009346902 0.03052079 -0.009749948 -0.004054367 0.03166145 -0.009749948 0.001354694 0.03189122 -0.009749948 0.006724834 0.03120356 -0.009749948 0.01190155 0.0296182 -0.009749948 0.01673585 0.02718079 -0.009749948 0.02108871 0.02396142 -0.009749948 0.02483487 0.02005273 -0.009749948 0.0278666 0.01556718 -0.009749948 0.03009665 0.01063382 -0.009749948 0.03146082 0.005394458 -0.009749948 0.03191995 0 -0.009749948 0.03146082 -0.005394458 -0.009749948 0.03009665 -0.01063382 -0.009749948 0.0278666 -0.01556718 -0.009749948 0.02483487 -0.02005273 -0.009749948 0.02108871 -0.02396142 -0.009749948 0.01673585 -0.02718079 -0.009749948 0.01190155 -0.0296182 -0.009749948 0.006724834 -0.03120356 -0.009749948 0.001354694 -0.03189122 -0.009749948 0.03191995 0 0.009749948</float_array>
          <technique_common>
            <accessor source="#ring_small-mesh-positions-array" count="232" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="ring_small-mesh-normals">
          <float_array id="ring_small-mesh-normals-array" count="969">0.9965844 0.08258068 0 0.9965844 -0.08258068 0 0.9965847 -0.08257824 0 0.9694004 -0.2454851 0 0.9157735 -0.4016951 0 0.9157731 -0.4016962 0 0.8371667 -0.5469478 0 0.8371661 -0.5469487 0 0.7357241 -0.6772814 0 0.6142125 -0.7891407 0 0.6142128 -0.7891404 0 0.4759474 -0.8794738 0 0.3246996 -0.9458172 0 0.3246995 -0.9458173 0 0.1645945 -0.9863613 0 0.1645945 -0.9863614 0 0 -1 0 -0.1645945 -0.9863614 0 -0.1645945 -0.9863613 0 -0.3246995 -0.9458173 0 -0.3246996 -0.9458172 0 -0.4759474 -0.8794738 0 -0.6142128 -0.7891404 0 -0.6142125 -0.7891407 0 -0.7357241 -0.6772814 0 -0.8371661 -0.5469487 0 -0.8371667 -0.5469478 0 -0.9157731 -0.4016962 0 -0.9157735 -0.4016951 0 -0.9694004 -0.2454851 0 -0.9965847 -0.08257824 0 -0.9965844 -0.08258068 0 -0.9965844 0.08258068 0 -0.9965847 0.08257824 0 -0.9694004 0.2454851 0 -0.9157735 0.4016951 0 -0.9157731 0.4016962 0 -0.8371667 0.5469478 0 -0.8371661 0.5469487 0 -0.7357241 0.6772814 0 -0.6142125 0.7891407 0 -0.6142128 0.7891404 0 -0.4759474 0.8794738 0 -0.3246996 0.9458172 0 -0.3246995 0.9458173 0 -0.1645945 0.9863613 0 -0.1645945 0.9863614 0 0 1 0 0.1645945 0.9863614 0 0.1645945 0.9863613 0 0.3246995 0.9458173 0 0.3246996 0.9458172 0 0.4759474 0.8794738 0 0.6142128 0.7891404 0 0.6142125 0.7891407 0 0.7357241 0.6772814 0 0.8371661 0.5469487 0 0.8371667 0.5469478 0 0.9157731 0.4016962 0 0.9157735 0.4016951 0 0.9694004 0.2454851 0 0.9965847 0.08257824 0 -0.6866414 0.1738813 0.7058958 -0.6866405 0.1738815 0.7058967 -0.6486557 0.2845268 0.7058969 -0.6486566 0.2845267 0.7058961 -0.5929769 0.3874124 0.7058967 -0.592978 0.3874111 0.7058964 -0.5211244 0.4797287 0.7058964 -0.5211246 0.4797284 0.7058966 -0.4350562 0.5589609 0.7058959 -0.4350561 0.5589602 0.7058964 -0.3371211 0.6229452 0.7058957 -0.3371213 0.6229451 0.7058957 -0.2299897 0.6699368 0.7058962 -0.2299898 0.6699372 0.7058957 -0.1165848 0.698655 0.7058961 -0.116585 0.6986553 0.7058958 0 0.7083154 0.7058961 0 0.7083158 0.7058958 0.1165848 0.698655 0.7058961 0.116585 0.6986553 0.7058957 0.2299895 0.6699367 0.7058963 0.2299898 0.6699373 0.7058957 0.3371214 0.6229459 0.705895 0.3371211 0.6229437 0.705897 0.4350559 0.5589609 0.705896 0.4350561 0.5589602 0.7058964 0.5211242 0.4797288 0.7058966 0.5211251 0.4797282 0.7058963 0.5929771 0.3874121 0.7058967 0.5929779 0.3874114 0.7058964 0.6486563 0.2845268 0.7058963 0.6486562 0.2845268 0.7058964 0.6866402 0.1738811 0.705897 0.6866416 0.1738816 0.7058955 0.7058967 0.05849194 0.7058956 0.7058967 0.05849164 0.7058957 0.7058964 -0.05849188 0.7058959 0.705897 -0.05849164 0.7058953 0.6866414 -0.1738813 0.7058958 0.6866405 -0.1738815 0.7058967 0.6486557 -0.2845268 0.7058969 0.6486566 -0.2845267 0.7058961 0.5929769 -0.3874124 0.7058967 0.592978 -0.3874111 0.7058964 0.5211244 -0.4797287 0.7058964 0.5211246 -0.4797284 0.7058966 0.4350562 -0.5589609 0.7058959 0.4350561 -0.5589602 0.7058964 0.3371211 -0.6229452 0.7058957 0.3371213 -0.6229451 0.7058957 0.2299897 -0.6699368 0.7058962 0.2299898 -0.6699372 0.7058957 0.1165848 -0.698655 0.7058961 0.116585 -0.6986553 0.7058958 0 -0.7083154 0.7058961 0 -0.7083158 0.7058958 -0.1165848 -0.698655 0.7058961 -0.116585 -0.6986553 0.7058957 -0.2299895 -0.6699367 0.7058963 -0.2299898 -0.6699373 0.7058957 -0.3371214 -0.6229459 0.705895 -0.3371211 -0.6229437 0.705897 -0.4350559 -0.5589609 0.705896 -0.4350561 -0.5589602 0.7058964 -0.5211242 -0.4797288 0.7058966 -0.5211251 -0.4797282 0.7058963 -0.5929771 -0.3874121 0.7058967 -0.5929779 -0.3874114 0.7058964 -0.6486563 -0.2845268 0.7058963 -0.6486562 -0.2845268 0.7058964 -0.6866402 -0.1738811 0.705897 -0.6866416 -0.1738816 0.7058955 -0.7058967 -0.05849194 0.7058956 -0.7058967 -0.05849164 0.7058957 -0.7058964 0.05849188 0.7058959 -0.705897 0.05849164 0.7058953 0.6866414 0.1738813 -0.7058958 0.6866405 0.1738815 -0.7058967 0.6486557 0.2845268 -0.7058969 0.6486566 0.2845267 -0.7058961 0.5929769 0.3874124 -0.7058967 0.592978 0.3874111 -0.7058964 0.5211244 0.4797287 -0.7058964 0.5211246 0.4797284 -0.7058966 0.4350562 0.5589609 -0.7058959 0.4350561 0.5589602 -0.7058964 0.3371211 0.6229452 -0.7058957 0.3371213 0.6229451 -0.7058957 0.2299897 0.6699368 -0.7058962 0.2299898 0.6699372 -0.7058957 0.1165848 0.698655 -0.7058961 0.116585 0.6986553 -0.7058958 0 0.7083154 -0.7058961 0 0.7083158 -0.7058958 -0.1165848 0.698655 -0.7058961 -0.116585 0.6986553 -0.7058957 -0.2299895 0.6699367 -0.7058963 -0.2299898 0.6699373 -0.7058957 -0.3371214 0.6229459 -0.705895 -0.3371211 0.6229437 -0.705897 -0.4350559 0.5589609 -0.705896 -0.4350561 0.5589602 -0.7058964 -0.5211242 0.4797288 -0.7058966 -0.5211251 0.4797282 -0.7058963 -0.5929771 0.3874121 -0.7058967 -0.5929779 0.3874114 -0.7058964 -0.6486563 0.2845268 -0.7058963 -0.6486562 0.2845268 -0.7058964 -0.6866402 0.1738811 -0.705897 -0.6866416 0.1738816 -0.7058955 -0.7058967 0.05849194 -0.7058956 -0.7058967 0.05849164 -0.7058957 -0.7058964 -0.05849188 -0.7058959 -0.705897 -0.05849164 -0.7058953 -0.6866414 -0.1738813 -0.7058958 -0.6866405 -0.1738815 -0.7058967 -0.6486557 -0.2845268 -0.7058969 -0.6486566 -0.2845267 -0.7058961 -0.5929769 -0.3874124 -0.7058967 -0.592978 -0.3874111 -0.7058964 -0.5211244 -0.4797287 -0.7058964 -0.5211246 -0.4797284 -0.7058966 -0.4350562 -0.5589609 -0.7058959 -0.4350561 -0.5589602 -0.7058964 -0.3371211 -0.6229452 -0.7058957 -0.3371213 -0.6229451 -0.7058957 -0.2299897 -0.6699368 -0.7058962 -0.2299898 -0.6699372 -0.7058957 -0.1165848 -0.698655 -0.7058961 -0.116585 -0.6986553 -0.7058958 0 -0.7083154 -0.7058961 0 -0.7083158 -0.7058958 0.1165848 -0.698655 -0.7058961 0.116585 -0.6986553 -0.7058957 0.2299895 -0.6699367 -0.7058963 0.2299898 -0.6699373 -0.7058957 0.3371214 -0.6229459 -0.705895 0.3371211 -0.6229437 -0.705897 0.4350559 -0.5589609 -0.705896 0.4350561 -0.5589602 -0.7058964 0.5211242 -0.4797288 -0.7058966 0.5211251 -0.4797282 -0.7058963 0.5929771 -0.3874121 -0.7058967 0.5929779 -0.3874114 -0.7058964 0.6486563 -0.2845268 -0.7058963 0.6486562 -0.2845268 -0.7058964 0.6866402 -0.1738811 -0.705897 0.6866416 -0.1738816 -0.7058955 0.7058967 -0.05849194 -0.7058956 0.7058967 -0.05849164 -0.7058957 0.7058964 0.05849188 -0.7058959 0.705897 0.05849164 -0.7058953 0 0 1 6.2445e-7 0 1 -4.63932e-7 0 1 -9.11911e-7 0 1 2.5766e-6 0 1 -9.08303e-7 0 1 -9.05521e-7 0 1 -2.59868e-6 0 1 9.03542e-7 0 1 -2.60506e-6 0 1 -9.02359e-7 0 1 9.03542e-7 0 1 -9.05521e-7 0 1 -9.11909e-7 0 1 -2.31966e-7 0 1 -4.71549e-7 0 1 -1.19011e-6 0 1 1.97255e-6 0 1 -2.27329e-6 0 1 -1.99022e-6 0 1 2.34457e-6 0 1 -2.40862e-6 0 1 1.90638e-6 0 1 -9.323e-7 0 1 -4.45468e-7 0 1 0 0 -1 6.2445e-7 0 -1 -4.63932e-7 0 -1 -9.11911e-7 0 -1 2.5766e-6 0 -1 -9.08303e-7 0 -1 -9.05521e-7 0 -1 -2.59868e-6 0 -1 9.03542e-7 0 -1 -2.60506e-6 0 -1 -9.02359e-7 0 -1 9.03542e-7 0 -1 -9.05521e-7 0 -1 -9.11909e-7 0 -1 -2.31966e-7 0 -1 -4.71549e-7 0 -1 -1.19011e-6 0 -1 1.97255e-6 0 -1 -2.27329e-6 0 -1 -1.99022e-6 0 -1 2.34457e-6 0 -1 -2.40862e-6 0 -1 1.90638e-6 0 -1 -9.323e-7 0 -1 -4.45468e-7 0 -1 -0.9963975 0.08480626 0 -0.9963975 -0.08480626 0 -0.9677332 -0.2519775 0 -0.9112284 -0.4119017 0 -0.9112288 -0.4119007 0 -0.8285091 -0.5599758 0 -0.8285101 -0.5599743 0 -0.7219563 -0.6919386 0 -0.721956 -0.6919389 0 -0.5946331 -0.8039973 0 -0.4502041 -0.8929257 0 -0.4502038 -0.8929259 0 -0.2928224 -0.9561669 0 -0.2928225 -0.9561668 0 -0.1270177 -0.9919005 0 -0.1270177 -0.9919005 0 0.04244095 -0.999099 0 0.2106794 -0.9775552 0 0.3728563 -0.9278891 0 0.3728562 -0.9278892 0 0.5243075 -0.851529 0 0.6606746 -0.7506725 0 0.6606749 -0.7506722 0 0.7780358 -0.62822 0 0.7780361 -0.6282197 0 0.8730139 -0.4876954 0 0.8730144 -0.4876946 0 0.9428772 -0.3331404 0 0.9856162 -0.1689994 0 0.9856159 -0.1690016 0 1 0 0 0.9856159 0.1690016 0 0.9856162 0.1689994 0 0.9428772 0.3331404 0 0.8730144 0.4876946 0 0.8730139 0.4876954 0 0.7780361 0.6282197 0 0.7780358 0.62822 0 0.6606749 0.7506722 0 0.6606746 0.7506725 0 0.5243075 0.851529 0 0.3728562 0.9278892 0 0.3728563 0.9278891 0 0.2106794 0.9775552 0 0.04244095 0.999099 0 -0.1270177 0.9919005 0 -0.1270177 0.9919005 0 -0.2928225 0.9561668 0 -0.2928224 0.9561669 0 -0.4502038 0.8929259 0 -0.4502041 0.8929257 0 -0.5946331 0.8039973 0 -0.721956 0.6919389 0 -0.7219563 0.6919386 0 -0.8285101 0.5599743 0 -0.8285091 0.5599758 0 -0.9112288 0.4119007 0 -0.9112284 0.4119017 0 -0.9677332 0.2519775 0</float_array>
          <technique_common>
            <accessor source="#ring_small-mesh-normals-array" count="323" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="ring_small-mesh-vertices">
          <input semantic="POSITION" source="#ring_small-mesh-positions"/>
        </vertices>
        <triangles material="Material_001-material" count="452">
          <input semantic="VERTEX" source="#ring_small-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#ring_small-mesh-normals" offset="1"/>
          <p>0 0 1 0 2 0 2 1 1 1 3 1 2 2 3 2 4 2 4 3 3 3 5 3 4 3 5 3 6 3 6 4 5 4 7 4 6 5 7 5 8 5 8 6 7 6 9 6 8 7 9 7 10 7 10 8 9 8 11 8 10 8 11 8 12 8 12 9 11 9 13 9 12 10 13 10 14 10 14 11 13 11 15 11 14 11 15 11 16 11 16 12 15 12 17 12 16 13 17 13 18 13 18 14 17 14 19 14 18 15 19 15 20 15 20 16 19 16 21 16 20 16 21 16 22 16 22 17 21 17 23 17 22 18 23 18 24 18 24 19 23 19 25 19 24 20 25 20 26 20 26 21 25 21 27 21 26 21 27 21 28 21 28 22 27 22 29 22 28 23 29 23 30 23 30 24 29 24 31 24 30 24 31 24 32 24 32 25 31 25 33 25 32 26 33 26 34 26 34 27 33 27 35 27 34 28 35 28 36 28 36 29 35 29 37 29 36 29 37 29 38 29 38 30 37 30 39 30 38 31 39 31 40 31 40 32 39 32 41 32 40 33 41 33 42 33 42 34 41 34 43 34 42 34 43 34 44 34 44 35 43 35 45 35 44 36 45 36 46 36 46 37 45 37 47 37 46 38 47 38 48 38 48 39 47 39 49 39 48 39 49 39 50 39 50 40 49 40 51 40 50 41 51 41 52 41 52 42 51 42 53 42 52 42 53 42 54 42 54 43 53 43 55 43 54 44 55 44 56 44 56 45 55 45 57 45 56 46 57 46 58 46 58 47 57 47 59 47 58 47 59 47 60 47 60 48 59 48 61 48 60 49 61 49 62 49 62 50 61 50 63 50 62 51 63 51 64 51 64 52 63 52 65 52 64 52 65 52 66 52 66 53 65 53 67 53 66 54 67 54 68 54 68 55 67 55 69 55 68 55 69 55 70 55 70 56 69 56 71 56 70 57 71 57 72 57 72 58 71 58 73 58 72 59 73 59 74 59 74 60 73 60 75 60 74 60 75 60 0 60 0 61 75 61 1 61 41 62 76 62 43 62 43 63 76 63 77 63 43 64 77 64 45 64 45 65 77 65 78 65 45 66 78 66 47 66 47 67 78 67 79 67 47 68 79 68 49 68 49 69 79 69 80 69 49 70 80 70 51 70 51 71 80 71 81 71 51 72 81 72 53 72 53 73 81 73 82 73 53 74 82 74 55 74 55 75 82 75 83 75 55 76 83 76 57 76 57 77 83 77 84 77 57 78 84 78 59 78 59 79 84 79 85 79 59 80 85 80 61 80 61 81 85 81 86 81 61 82 86 82 63 82 63 83 86 83 87 83 63 84 87 84 65 84 65 85 87 85 88 85 65 86 88 86 67 86 67 87 88 87 89 87 67 88 89 88 69 88 69 89 89 89 90 89 69 90 90 90 71 90 71 91 90 91 91 91 71 92 91 92 73 92 73 93 91 93 92 93 73 94 92 94 75 94 75 95 92 95 93 95 75 96 93 96 94 96 94 97 93 97 95 97 94 98 95 98 3 98 3 99 95 99 96 99 3 100 96 100 5 100 5 101 96 101 97 101 5 102 97 102 7 102 7 103 97 103 98 103 7 104 98 104 9 104 9 105 98 105 99 105 9 106 99 106 11 106 11 107 99 107 100 107 11 108 100 108 13 108 13 109 100 109 101 109 13 110 101 110 15 110 15 111 101 111 102 111 15 112 102 112 17 112 17 113 102 113 103 113 17 114 103 114 19 114 19 115 103 115 104 115 19 116 104 116 21 116 21 117 104 117 105 117 21 118 105 118 23 118 23 119 105 119 106 119 23 120 106 120 25 120 25 121 106 121 107 121 25 122 107 122 27 122 27 123 107 123 108 123 27 124 108 124 29 124 29 125 108 125 109 125 29 126 109 126 31 126 31 127 109 127 110 127 31 128 110 128 33 128 33 129 110 129 111 129 33 130 111 130 35 130 35 131 111 131 112 131 35 132 112 132 37 132 37 133 112 133 113 133 37 134 113 134 114 134 114 135 113 135 115 135 114 136 115 136 41 136 41 137 115 137 76 137 0 138 116 138 74 138 74 139 116 139 117 139 74 140 117 140 72 140 72 141 117 141 118 141 72 142 118 142 70 142 70 143 118 143 119 143 70 144 119 144 68 144 68 145 119 145 120 145 68 146 120 146 66 146 66 147 120 147 121 147 66 148 121 148 64 148 64 149 121 149 122 149 64 150 122 150 62 150 62 151 122 151 123 151 62 152 123 152 60 152 60 153 123 153 124 153 60 154 124 154 58 154 58 155 124 155 125 155 58 156 125 156 56 156 56 157 125 157 126 157 56 158 126 158 54 158 54 159 126 159 127 159 54 160 127 160 52 160 52 161 127 161 128 161 52 162 128 162 50 162 50 163 128 163 129 163 50 164 129 164 48 164 48 165 129 165 130 165 48 166 130 166 46 166 46 167 130 167 131 167 46 168 131 168 44 168 44 169 131 169 132 169 44 170 132 170 42 170 42 171 132 171 133 171 42 172 133 172 134 172 134 173 133 173 135 173 134 174 135 174 38 174 38 175 135 175 136 175 38 176 136 176 36 176 36 177 136 177 137 177 36 178 137 178 34 178 34 179 137 179 138 179 34 180 138 180 32 180 32 181 138 181 139 181 32 182 139 182 30 182 30 183 139 183 140 183 30 184 140 184 28 184 28 185 140 185 141 185 28 186 141 186 26 186 26 187 141 187 142 187 26 188 142 188 24 188 24 189 142 189 143 189 24 190 143 190 22 190 22 191 143 191 144 191 22 192 144 192 20 192 20 193 144 193 145 193 20 194 145 194 18 194 18 195 145 195 146 195 18 196 146 196 16 196 16 197 146 197 147 197 16 198 147 198 14 198 14 199 147 199 148 199 14 200 148 200 12 200 12 201 148 201 149 201 12 202 149 202 10 202 10 203 149 203 150 203 10 204 150 204 8 204 8 205 150 205 151 205 8 206 151 206 6 206 6 207 151 207 152 207 6 208 152 208 4 208 4 209 152 209 153 209 4 210 153 210 2 210 2 211 153 211 154 211 2 212 154 212 0 212 0 213 154 213 116 213 85 214 84 214 155 214 155 215 84 215 83 215 155 216 83 216 156 216 156 214 83 214 82 214 156 214 82 214 157 214 157 214 82 214 81 214 157 214 81 214 158 214 158 214 81 214 80 214 158 217 80 217 159 217 159 218 80 218 79 218 159 219 79 219 160 219 160 214 79 214 78 214 160 220 78 220 161 220 161 221 78 221 77 221 161 222 77 222 162 222 162 223 77 223 76 223 162 224 76 224 163 224 163 214 76 214 115 214 163 214 115 214 164 214 164 214 115 214 113 214 164 224 113 224 165 224 165 223 113 223 112 223 165 225 112 225 166 225 166 221 112 221 111 221 166 226 111 226 167 226 167 214 111 214 110 214 167 219 110 219 168 219 168 218 110 218 109 218 168 227 109 227 169 227 169 214 109 214 108 214 169 214 108 214 170 214 170 214 108 214 107 214 170 214 107 214 171 214 171 214 107 214 106 214 171 228 106 228 172 228 172 215 106 215 105 215 172 214 105 214 173 214 173 214 105 214 104 214 173 229 104 229 174 229 174 214 104 214 103 214 174 214 103 214 175 214 175 214 103 214 102 214 175 214 102 214 176 214 176 230 102 230 101 230 176 214 101 214 177 214 177 214 101 214 100 214 177 231 100 231 178 231 178 214 100 214 99 214 178 214 99 214 179 214 179 232 99 232 98 232 179 214 98 214 180 214 180 214 98 214 97 214 180 214 97 214 181 214 181 214 97 214 96 214 181 214 96 214 182 214 182 214 96 214 183 214 182 214 183 214 184 214 184 214 183 214 93 214 184 214 93 214 185 214 185 214 93 214 92 214 185 214 92 214 186 214 186 214 92 214 91 214 186 214 91 214 187 214 187 233 91 233 90 233 187 234 90 234 188 234 188 214 90 214 89 214 188 235 89 235 189 235 189 236 89 236 88 236 189 214 88 214 190 214 190 237 88 237 87 237 190 214 87 214 191 214 191 214 87 214 86 214 191 214 86 214 192 214 192 238 86 238 85 238 192 214 85 214 155 214 145 239 144 239 193 239 193 240 144 240 143 240 193 241 143 241 194 241 194 239 143 239 142 239 194 239 142 239 195 239 195 239 142 239 141 239 195 239 141 239 196 239 196 239 141 239 140 239 196 242 140 242 197 242 197 243 140 243 139 243 197 244 139 244 198 244 198 239 139 239 138 239 198 245 138 245 199 245 199 246 138 246 137 246 199 247 137 247 200 247 200 248 137 248 136 248 200 249 136 249 201 249 201 239 136 239 202 239 201 239 202 239 203 239 203 239 202 239 133 239 203 249 133 249 204 249 204 248 133 248 132 248 204 250 132 250 205 250 205 246 132 246 131 246 205 251 131 251 206 251 206 239 131 239 130 239 206 244 130 244 207 244 207 243 130 243 129 243 207 252 129 252 208 252 208 239 129 239 128 239 208 239 128 239 209 239 209 239 128 239 127 239 209 239 127 239 210 239 210 239 127 239 126 239 210 253 126 253 211 253 211 240 126 240 125 240 211 239 125 239 212 239 212 239 125 239 124 239 212 254 124 254 213 254 213 239 124 239 123 239 213 239 123 239 214 239 214 239 123 239 122 239 214 239 122 239 215 239 215 255 122 255 121 255 215 239 121 239 216 239 216 239 121 239 120 239 216 256 120 256 217 256 217 239 120 239 119 239 217 239 119 239 218 239 218 257 119 257 118 257 218 239 118 239 219 239 219 239 118 239 117 239 219 239 117 239 220 239 220 239 117 239 116 239 220 239 116 239 221 239 221 239 116 239 154 239 221 239 154 239 222 239 222 239 154 239 153 239 222 239 153 239 223 239 223 239 153 239 152 239 223 239 152 239 224 239 224 239 152 239 151 239 224 239 151 239 225 239 225 258 151 258 150 258 225 259 150 259 226 259 226 239 150 239 149 239 226 260 149 260 227 260 227 261 149 261 148 261 227 239 148 239 228 239 228 262 148 262 147 262 228 239 147 239 229 239 229 239 147 239 146 239 229 239 146 239 230 239 230 263 146 263 145 263 230 239 145 239 193 239 222 264 231 264 221 264 221 265 231 265 184 265 221 265 184 265 220 265 220 266 184 266 185 266 220 266 185 266 219 266 219 267 185 267 186 267 219 268 186 268 218 268 218 269 186 269 187 269 218 270 187 270 217 270 217 271 187 271 188 271 217 272 188 272 216 272 216 273 188 273 189 273 216 273 189 273 215 273 215 274 189 274 190 274 215 275 190 275 214 275 214 276 190 276 191 276 214 277 191 277 213 277 213 278 191 278 192 278 213 279 192 279 212 279 212 280 192 280 155 280 212 280 155 280 211 280 211 281 155 281 156 281 211 281 156 281 210 281 210 282 156 282 157 282 210 283 157 283 209 283 209 284 157 284 158 284 209 284 158 284 208 284 208 285 158 285 159 285 208 286 159 286 207 286 207 287 159 287 160 287 207 288 160 288 206 288 206 289 160 289 161 289 206 290 161 290 205 290 205 291 161 291 162 291 205 291 162 291 204 291 204 292 162 292 163 292 204 293 163 293 203 293 203 294 163 294 164 294 203 294 164 294 201 294 201 295 164 295 165 295 201 296 165 296 200 296 200 297 165 297 166 297 200 297 166 297 199 297 199 298 166 298 167 298 199 299 167 299 198 299 198 300 167 300 168 300 198 301 168 301 197 301 197 302 168 302 169 302 197 303 169 303 196 303 196 304 169 304 170 304 196 304 170 304 195 304 195 305 170 305 171 305 195 306 171 306 194 306 194 307 171 307 172 307 194 307 172 307 193 307 193 308 172 308 173 308 193 308 173 308 230 308 230 309 173 309 174 309 230 310 174 310 229 310 229 311 174 311 175 311 229 312 175 312 228 312 228 313 175 313 176 313 228 314 176 314 227 314 227 315 176 315 177 315 227 315 177 315 226 315 226 316 177 316 178 316 226 317 178 317 225 317 225 318 178 318 179 318 225 319 179 319 224 319 224 320 179 320 180 320 224 321 180 321 223 321 223 322 180 322 181 322 223 322 181 322 222 322 222 264 181 264 231 264</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.481132 0.7276763 0.3054208 -0.6141704 -6.50764 0 0.8953956 0.4452714 5.343665 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Lamp" name="Lamp" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Lamp-light"/>
      </node>
      <node id="ring_small" name="ring_small" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#ring_small-mesh" name="ring_small">
          <bind_material>
            <technique_common>
              <instance_material symbol="Material_001-material" target="#Material_001-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>