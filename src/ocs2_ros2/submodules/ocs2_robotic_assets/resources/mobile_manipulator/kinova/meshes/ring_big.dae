<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-03-19T18:21:07</created>
    <modified>2019-03-19T18:21:07</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">49.13434</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <YF_dofdist sid="YF_dofdist" type="float">0</YF_dofdist>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="Lamp-light" name="Lamp">
      <technique_common>
        <point>
          <color sid="color">1 1 1</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">8192</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <halo_intensity sid="blnder_halo_intensity" type="float">1</halo_intensity>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">1.000799</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <compressthresh sid="compressthresh" type="float">0.04999995</compressthresh>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <filtertype sid="filtertype" type="int">0</filtertype>
          <bufflag sid="bufflag" type="int">0</bufflag>
          <buftype sid="buftype" type="int">2</buftype>
          <ray_samp sid="ray_samp" type="int">1</ray_samp>
          <ray_sampy sid="ray_sampy" type="int">1</ray_sampy>
          <ray_sampz sid="ray_sampz" type="int">1</ray_sampz>
          <ray_samp_type sid="ray_samp_type" type="int">0</ray_samp_type>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
          <adapt_thresh sid="adapt_thresh" type="float">0.000999987</adapt_thresh>
          <ray_samp_method sid="ray_samp_method" type="int">1</ray_samp_method>
          <shadhalostep sid="shadhalostep" type="int">0</shadhalostep>
          <sun_effect_type sid="sun_effect_type" type="int">0</sun_effect_type>
          <skyblendtype sid="skyblendtype" type="int">1</skyblendtype>
          <horizon_brightness sid="horizon_brightness" type="float">1</horizon_brightness>
          <spread sid="spread" type="float">1</spread>
          <sun_brightness sid="sun_brightness" type="float">1</sun_brightness>
          <sun_size sid="sun_size" type="float">1</sun_size>
          <backscattered_light sid="backscattered_light" type="float">1</backscattered_light>
          <sun_intensity sid="sun_intensity" type="float">1</sun_intensity>
          <atm_turbidity sid="atm_turbidity" type="float">2</atm_turbidity>
          <atm_extinction_factor sid="atm_extinction_factor" type="float">1</atm_extinction_factor>
          <atm_distance_factor sid="atm_distance_factor" type="float">1</atm_distance_factor>
          <skyblendfac sid="skyblendfac" type="float">1</skyblendfac>
          <sky_exposure sid="sky_exposure" type="float">1</sky_exposure>
          <sky_colorspace sid="sky_colorspace" type="int">0</sky_colorspace>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_images/>
  <library_effects>
    <effect id="Material_001-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0.1 0.1 0.1 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.627451 0.627451 0.627451 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="Material_001-material" name="Material_001">
      <instance_effect url="#Material_001-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="ring_big-mesh" name="ring big">
      <mesh>
        <source id="ring_big-mesh-positions">
          <float_array id="ring_big-mesh-positions-array" count="696">-0.007340908 0.04399168 -0.009749948 0 0.04459995 0.009749948 0 0.04459995 -0.009749948 0.007340908 0.04399168 0.009749948 0.007340908 0.04399168 -0.009749948 0.01448154 0.04218339 0.009749948 0.01448154 0.04218339 -0.009749948 0.02122724 0.0392245 0.009749948 0.02122724 0.0392245 -0.009749948 0.02739387 0.03519564 0.009749948 0.02739387 0.03519564 -0.009749948 0.03281325 0.03020673 0.009749948 0.03281325 0.03020673 -0.009749948 0.0373376 0.02439385 0.009749948 0.0373376 0.02439385 -0.009749948 0.04084348 0.0179156 0.009749948 0.04084348 0.0179156 -0.009749948 0.04323524 0.01094859 0.009749948 0.04323524 0.01094859 -0.009749948 0.04444766 0.00368303 0.009749948 0.04444766 0.00368303 -0.009749948 0.04444766 -0.00368303 0.009749948 0.04444766 -0.00368303 -0.009749948 0.04323524 -0.01094859 0.009749948 0.04323524 -0.01094859 -0.009749948 0.04084348 -0.0179156 0.009749948 0.04084348 -0.0179156 -0.009749948 0.0373376 -0.02439385 0.009749948 0.0373376 -0.02439385 -0.009749948 0.03281325 -0.03020673 0.009749948 0.03281325 -0.03020673 -0.009749948 0.02739387 -0.03519564 0.009749948 0.02739387 -0.03519564 -0.009749948 0.02122724 -0.0392245 0.009749948 0.02122724 -0.0392245 -0.009749948 0.01448154 -0.04218339 0.009749948 0.01448154 -0.04218339 -0.009749948 0.007340908 -0.04399168 0.009749948 0.007340908 -0.04399168 -0.009749948 0 -0.04459995 0.009749948 0 -0.04459995 -0.009749948 -0.007340908 -0.04399168 0.009749948 -0.007340908 -0.04399168 -0.009749948 -0.01448154 -0.04218339 0.009749948 -0.01448154 -0.04218339 -0.009749948 -0.02122724 -0.0392245 0.009749948 -0.02122724 -0.0392245 -0.009749948 -0.02739387 -0.03519564 0.009749948 -0.02739387 -0.03519564 -0.009749948 -0.03281325 -0.03020673 0.009749948 -0.03281325 -0.03020673 -0.009749948 -0.0373376 -0.02439385 0.009749948 -0.0373376 -0.02439385 -0.009749948 -0.04084348 -0.0179156 0.009749948 -0.04084348 -0.0179156 -0.009749948 -0.04323524 -0.01094859 0.009749948 -0.04323524 -0.01094859 -0.009749948 -0.04444766 -0.00368303 0.009749948 -0.04444766 -0.00368303 -0.009749948 -0.04444766 0.00368303 0.009749948 -0.04444766 0.00368303 -0.009749948 -0.04323524 0.01094859 0.009749948 -0.04323524 0.01094859 -0.009749948 -0.04084348 0.0179156 0.009749948 -0.04084348 0.0179156 -0.009749948 -0.0373376 0.02439385 0.009749948 -0.0373376 0.02439385 -0.009749948 -0.03281325 0.03020673 0.009749948 -0.03281325 0.03020673 -0.009749948 -0.02739387 0.03519564 0.009749948 -0.02739387 0.03519564 -0.009749948 -0.02122724 0.0392245 0.009749948 -0.02122724 0.0392245 -0.009749948 -0.01448154 0.04218339 0.009749948 -0.01448154 0.04218339 -0.009749948 -0.007340908 0.04399168 0.009749948 -0.007052838 -0.04226553 0.0115 -0.01391333 -0.04052823 0.0115 -0.02039432 -0.03768545 0.0115 -0.02631896 -0.03381466 0.0115 -0.03152573 -0.0290215 0.0115 -0.03587257 -0.02343672 0.0115 -0.03924083 -0.01721262 0.0115 -0.04153877 -0.01051902 0.0115 -0.04270362 -0.003538489 0.0115 -0.04270362 0.003538489 0.0115 -0.04153877 0.01051902 0.0115 -0.03924083 0.01721262 0.0115 -0.03587257 0.02343672 0.0115 -0.03152573 0.0290215 0.0115 -0.02631896 0.03381466 0.0115 -0.02039432 0.03768545 0.0115 -0.01391333 0.04052823 0.0115 -0.007052838 0.04226553 0.0115 0 0.04459995 0.009749948 0 0.04284995 0.0115 0.007052838 0.04226553 0.0115 0.01391333 0.04052823 0.0115 0.02039432 0.03768545 0.0115 0.02631896 0.03381466 0.0115 0.03152573 0.0290215 0.0115 0.03587257 0.02343672 0.0115 0.03924083 0.01721262 0.0115 0.04153877 0.01051902 0.0115 0.04270362 0.003538489 0.0115 0.04270362 -0.003538489 0.0115 0.04153877 -0.01051902 0.0115 0.03924083 -0.01721262 0.0115 0.03587257 -0.02343672 0.0115 0.03152573 -0.0290215 0.0115 0.02631896 -0.03381466 0.0115 0.02039432 -0.03768545 0.0115 0.01391333 -0.04052823 0.0115 0.007052838 -0.04226553 0.0115 0 -0.04459995 0.009749948 0 -0.04284995 0.0115 -0.007052838 0.04226553 -0.0115 -0.01391333 0.04052823 -0.0115 -0.02039432 0.03768545 -0.0115 -0.02631896 0.03381466 -0.0115 -0.03152573 0.0290215 -0.0115 -0.03587257 0.02343672 -0.0115 -0.03924083 0.01721262 -0.0115 -0.04153877 0.01051902 -0.0115 -0.04270362 0.003538489 -0.0115 -0.04270362 -0.003538489 -0.0115 -0.04153877 -0.01051902 -0.0115 -0.03924083 -0.01721262 -0.0115 -0.03587257 -0.02343672 -0.0115 -0.03152573 -0.0290215 -0.0115 -0.02631896 -0.03381466 -0.0115 -0.02039432 -0.03768545 -0.0115 -0.01391333 -0.04052823 -0.0115 -0.007052838 -0.04226553 -0.0115 0 -0.04459995 -0.009749948 0 -0.04284995 -0.0115 0.007052838 -0.04226553 -0.0115 0.01391333 -0.04052823 -0.0115 0.02039432 -0.03768545 -0.0115 0.02631896 -0.03381466 -0.0115 0.03152573 -0.0290215 -0.0115 0.03587257 -0.02343672 -0.0115 0.03924083 -0.01721262 -0.0115 0.04153877 -0.01051902 -0.0115 0.04270362 -0.003538489 -0.0115 0.04270362 0.003538489 -0.0115 0.04153877 0.01051902 -0.0115 0.03924083 0.01721262 -0.0115 0.03587257 0.02343672 -0.0115 0.03152573 0.0290215 -0.0115 0.02631896 0.03381466 -0.0115 0.02039432 0.03768545 -0.0115 0.01391333 0.04052823 -0.0115 0.007052838 0.04226553 -0.0115 0 0.04284995 -0.0115 -0.04014939 -0.0122956 0.0115 -0.03749394 -0.01890403 0.0115 -0.03375983 -0.02496862 0.0115 -0.02905446 -0.03031492 0.0115 -0.02351331 -0.03478908 0.0115 -0.01729571 -0.03826248 0.0115 -0.01058053 -0.0406351 0.0115 -0.00356096 -0.0418387 0.0115 0.00356096 -0.0418387 0.0115 0.01058053 -0.0406351 0.0115 0.01729571 -0.03826248 0.0115 0.02351331 -0.03478908 0.0115 0.02905446 -0.03031492 0.0115 0.03375983 -0.02496862 0.0115 0.03749394 -0.01890403 0.0115 0.04014939 -0.0122956 0.0115 0.04164987 -0.005333423 0.0115 0.04195213 0.001782059 0.0115 0.04104751 0.008846402 0.0115 0.038962 0.01565623 0.0115 0.03575569 0.02201563 0.0115 0.03152072 0.02774173 0.0115 0.02637892 0.03266972 0.0115 0.0204783 0.03665781 0.0115 0.01398849 0.03959137 0.0115 0.00709629 0.041386 0.0115 0 0.04198998 0.0115 0 0.04284995 0.0115 -0.00709629 0.041386 0.0115 -0.01398849 0.03959137 0.0115 -0.0204783 0.03665781 0.0115 -0.02637892 0.03266972 0.0115 -0.03152072 0.02774173 0.0115 -0.03575569 0.02201563 0.0115 -0.038962 0.01565623 0.0115 -0.04104751 0.008846402 0.0115 -0.04195213 0.001782059 0.0115 -0.04164987 -0.005333423 0.0115 0.04014939 -0.0122956 -0.0115 0.03749394 -0.01890403 -0.0115 0.03375983 -0.02496862 -0.0115 0.02905446 -0.03031492 -0.0115 0.02351331 -0.03478908 -0.0115 0.01729571 -0.03826248 -0.0115 0.01058053 -0.0406351 -0.0115 0.00356096 -0.0418387 -0.0115 0 -0.04284995 -0.0115 -0.00356096 -0.0418387 -0.0115 -0.01058053 -0.0406351 -0.0115 -0.01729571 -0.03826248 -0.0115 -0.02351331 -0.03478908 -0.0115 -0.02905446 -0.03031492 -0.0115 -0.03375983 -0.02496862 -0.0115 -0.03749394 -0.01890403 -0.0115 -0.04014939 -0.0122956 -0.0115 -0.04164987 -0.005333423 -0.0115 -0.04195213 0.001782059 -0.0115 -0.04104751 0.008846402 -0.0115 -0.038962 0.01565623 -0.0115 -0.03575569 0.02201563 -0.0115 -0.03152072 0.02774173 -0.0115 -0.02637892 0.03266972 -0.0115 -0.0204783 0.03665781 -0.0115 -0.01398849 0.03959137 -0.0115 -0.00709629 0.041386 -0.0115 0 0.04198998 -0.0115 0.00709629 0.041386 -0.0115 0.01398849 0.03959137 -0.0115 0.0204783 0.03665781 -0.0115 0.02637892 0.03266972 -0.0115 0.03152072 0.02774173 -0.0115 0.03575569 0.02201563 -0.0115 0.038962 0.01565623 -0.0115 0.04104751 0.008846402 -0.0115 0.04195213 0.001782059 -0.0115 0.04164987 -0.005333423 -0.0115 0 0.04198998 0.0115</float_array>
          <technique_common>
            <accessor source="#ring_big-mesh-positions-array" count="232" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="ring_big-mesh-normals">
          <float_array id="ring_big-mesh-normals-array" count="939">-0.08257907 0.9965845 0 0.08257907 0.9965845 0 0.2454857 0.9694002 0 0.4016954 0.9157735 0 0.4016957 0.9157732 0 0.5469478 0.8371667 0 0.5469481 0.8371665 0 0.6772814 0.7357241 0 0.7891408 0.6142125 0 0.8794733 0.4759483 0 0.879474 0.475947 0 0.9458173 0.3246995 0 0.9863613 0.1645944 0 1 0 0 0.9863613 -0.1645944 0 0.9458173 -0.3246995 0 0.879474 -0.475947 0 0.8794733 -0.4759483 0 0.7891408 -0.6142125 0 0.6772814 -0.7357241 0 0.5469481 -0.8371665 0 0.5469478 -0.8371667 0 0.4016957 -0.9157732 0 0.4016954 -0.9157735 0 0.2454857 -0.9694002 0 0.08257907 -0.9965845 0 0.08257907 -0.9965845 0 -0.08257907 -0.9965845 0 -0.2454857 -0.9694002 0 -0.4016954 -0.9157735 0 -0.4016957 -0.9157732 0 -0.5469478 -0.8371667 0 -0.5469481 -0.8371665 0 -0.6772814 -0.7357241 0 -0.7891408 -0.6142125 0 -0.8794733 -0.4759483 0 -0.879474 -0.475947 0 -0.9458173 -0.3246995 0 -0.9863613 -0.1645944 0 -1 0 0 -0.9863613 0.1645944 0 -0.9458173 0.3246995 0 -0.879474 0.475947 0 -0.8794733 0.4759483 0 -0.7891408 0.6142125 0 -0.6772814 0.7357241 0 -0.5469481 0.8371665 0 -0.5469478 0.8371667 0 -0.4016957 0.9157732 0 -0.4016954 0.9157735 0 -0.2454857 0.9694002 0 -0.08257907 0.9965845 0 -0.173882 -0.6866428 0.7058943 -0.1738808 -0.68664 0.7058973 -0.2845274 -0.6486565 0.7058959 -0.2845268 -0.6486567 0.7058959 -0.3874121 -0.5929781 0.7058959 -0.3874115 -0.5929775 0.7058966 -0.4797292 -0.5211248 0.7058959 -0.4797279 -0.5211238 0.7058974 -0.5589595 -0.4350551 0.7058976 -0.5589595 -0.4350568 0.7058965 -0.622945 -0.3371204 0.7058962 -0.6229438 -0.3371207 0.7058971 -0.6699368 -0.2299892 0.7058963 -0.6699364 -0.2299901 0.7058964 -0.6986546 -0.116584 0.7058966 -0.6986554 -0.1165857 0.7058956 -0.7083159 0 0.7058956 -0.708316 0 0.7058956 -0.6986557 0.1165848 0.7058953 -0.6986546 0.1165847 0.7058964 -0.6699369 0.22999 0.705896 -0.6699367 0.2299894 0.7058964 -0.6229448 0.3371203 0.7058964 -0.6229441 0.337121 0.7058967 -0.55896 0.4350559 0.7058966 -0.5589593 0.4350562 0.705897 -0.4797285 0.5211246 0.7058965 -0.4797292 0.521124 0.7058964 -0.3874119 0.5929781 0.705896 -0.3874115 0.5929775 0.7058966 -0.2845275 0.6486563 0.705896 -0.2845267 0.648657 0.7058957 -0.1738817 0.6866413 0.7058959 -0.1738812 0.6866416 0.7058957 -0.05849218 0.7058979 0.7058943 -0.05849194 0.7058966 0.7058957 0.05849218 0.7058979 0.7058943 0.05849194 0.7058966 0.7058957 0.173882 0.6866428 0.7058943 0.1738808 0.68664 0.7058973 0.2845274 0.6486565 0.7058959 0.2845268 0.6486567 0.7058959 0.3874121 0.5929781 0.7058959 0.3874115 0.5929775 0.7058966 0.4797292 0.5211248 0.7058959 0.4797279 0.5211238 0.7058974 0.5589595 0.4350551 0.7058976 0.5589595 0.4350568 0.7058965 0.622945 0.3371204 0.7058962 0.6229438 0.3371207 0.7058971 0.6699368 0.2299892 0.7058963 0.6699364 0.2299901 0.7058964 0.6986546 0.116584 0.7058966 0.6986554 0.1165857 0.7058956 0.7083159 0 0.7058956 0.708316 0 0.7058956 0.6986557 -0.1165848 0.7058953 0.6986546 -0.1165847 0.7058964 0.6699369 -0.22999 0.705896 0.6699367 -0.2299894 0.7058964 0.6229448 -0.3371203 0.7058964 0.6229441 -0.337121 0.7058967 0.55896 -0.4350559 0.7058966 0.5589593 -0.4350562 0.705897 0.4797285 -0.5211246 0.7058965 0.4797292 -0.521124 0.7058964 0.3874119 -0.5929781 0.705896 0.3874115 -0.5929775 0.7058966 0.2845275 -0.6486563 0.705896 0.2845267 -0.648657 0.7058957 0.1738817 -0.6866413 0.7058959 0.1738812 -0.6866416 0.7058957 0.05849218 -0.7058979 0.7058943 0.05849194 -0.7058966 0.7058957 -0.05849218 -0.7058979 0.7058943 -0.05849194 -0.7058966 0.7058957 -0.1738818 0.6866427 -0.7058944 -0.173881 0.6866397 -0.7058975 -0.2845273 0.6486563 -0.7058962 -0.284527 0.6486567 -0.7058959 -0.3874118 0.5929779 -0.7058961 -0.3874112 0.5929774 -0.7058969 -0.4797289 0.5211246 -0.7058961 -0.4797284 0.5211234 -0.7058974 -0.5589593 0.4350551 -0.7058977 -0.5589599 0.4350563 -0.7058965 -0.6229445 0.3371206 -0.7058965 -0.6229448 0.3371204 -0.7058964 -0.6699364 0.2299894 -0.7058967 -0.6699365 0.2299893 -0.7058966 -0.6986542 0.1165848 -0.7058969 -0.6986558 0.1165856 -0.7058952 -0.7083156 0 -0.705896 -0.708316 0 -0.7058956 -0.6986553 -0.1165856 -0.7058956 -0.6986546 -0.1165847 -0.7058964 -0.6699366 -0.2299894 -0.7058965 -0.6699362 -0.2299895 -0.7058967 -0.6229448 -0.3371203 -0.7058964 -0.6229447 -0.3371208 -0.7058963 -0.5589596 -0.4350557 -0.7058972 -0.5589596 -0.4350562 -0.7058969 -0.4797287 -0.521124 -0.7058968 -0.4797286 -0.5211241 -0.7058967 -0.387412 -0.5929778 -0.7058961 -0.3874112 -0.5929774 -0.7058969 -0.2845268 -0.6486564 -0.7058963 -0.2845273 -0.6486566 -0.7058958 -0.1738813 -0.6866411 -0.7058961 -0.1738812 -0.6866415 -0.7058958 -0.05849224 -0.7058978 -0.7058945 -0.05849188 -0.7058964 -0.7058959 0.05849212 -0.7058978 -0.7058945 0.058492 -0.7058964 -0.7058959 0.1738818 -0.6866427 -0.7058944 0.173881 -0.6866397 -0.7058975 0.2845273 -0.6486563 -0.7058962 0.284527 -0.6486567 -0.7058959 0.3874118 -0.5929779 -0.7058961 0.3874112 -0.5929774 -0.7058969 0.4797289 -0.5211246 -0.7058961 0.4797284 -0.5211234 -0.7058974 0.5589593 -0.4350551 -0.7058977 0.5589599 -0.4350563 -0.7058965 0.6229445 -0.3371206 -0.7058965 0.6229448 -0.3371204 -0.7058964 0.6699364 -0.2299894 -0.7058967 0.6699365 -0.2299893 -0.7058966 0.6986542 -0.1165848 -0.7058969 0.6986558 -0.1165856 -0.7058952 0.7083156 0 -0.705896 0.708316 0 -0.7058956 0.6986553 0.1165856 -0.7058956 0.6986546 0.1165847 -0.7058964 0.6699366 0.2299894 -0.7058965 0.6699362 0.2299895 -0.7058967 0.6229448 0.3371203 -0.7058964 0.6229447 0.3371208 -0.7058963 0.5589596 0.4350557 -0.7058972 0.5589596 0.4350562 -0.7058969 0.4797287 0.521124 -0.7058968 0.4797286 0.5211241 -0.7058967 0.387412 0.5929778 -0.7058961 0.3874112 0.5929774 -0.7058969 0.2845268 0.6486564 -0.7058963 0.2845273 0.6486566 -0.7058958 0.1738813 0.6866411 -0.7058961 0.1738812 0.6866415 -0.7058958 0.05849224 0.7058978 -0.7058945 0.05849188 0.7058964 -0.7058959 -0.05849212 0.7058978 -0.7058945 -0.058492 0.7058964 -0.7058959 -9.53721e-7 0 1 0 0 1 1.02586e-6 0 1 -5.10517e-7 0 1 1.42371e-6 0 1 -7.62835e-7 0 1 5.07047e-7 0 1 -1.43538e-6 0 1 0 0 1 1.43538e-6 0 1 1.01711e-6 0 1 -1.42371e-6 0 1 -1.02586e-6 0 1 1.08801e-6 0 1 -2.87724e-7 0 1 0 0 1 1.21586e-6 0 1 -5.77773e-7 0 1 1.08797e-6 0 1 -2.13065e-6 0 1 2.74581e-6 0 1 -2.08544e-6 0 1 -4.76861e-7 0 -1 0 0 -1 -5.1581e-7 0 -1 -2.81149e-6 0 -1 -5.12928e-7 0 -1 -2.53523e-7 0 -1 0 0 -1 1.26332e-7 0 -1 2.81149e-6 0 -1 -1.03835e-6 0 -1 2.76397e-6 0 -1 2.73621e-6 0 -1 2.70603e-6 0 -1 -1.07576e-6 0 -1 1.24205e-6 0 -1 -2.87724e-7 0 -1 -1.47213e-7 0 -1 -1.13314e-6 0 -1 -1.27011e-6 0 -1 -1.08797e-6 0 -1 -1.99569e-6 0 -1 -3.02138e-6 0 -1 1.95133e-6 0 -1 -0.08480602 -0.9963976 0 0.08480602 -0.9963976 0 0.251978 -0.967733 0 0.4119014 -0.9112284 0 0.4119012 -0.9112285 0 0.5599744 -0.8285099 0 0.5599746 -0.8285098 0 0.6919387 -0.7219563 0 0.6919391 -0.721956 0 0.8039973 -0.594633 0 0.8929259 -0.4502037 0 0.9561668 -0.2928228 0 0.9919006 -0.1270173 0 0.9919004 -0.1270188 0 0.999099 0.04244023 0 0.999099 0.04244166 0 0.9775553 0.2106791 0 0.9775552 0.2106798 0 0.9278888 0.372857 0 0.9278891 0.3728564 0 0.8515291 0.5243073 0 0.8515294 0.5243068 0 0.7506726 0.6606745 0 0.7506722 0.6606749 0 0.6282199 0.7780359 0 0.6282199 0.7780359 0 0.4876946 0.8730143 0 0.487695 0.8730142 0 0.3331398 0.9428774 0 0.3331399 0.9428774 0 0.1690011 0.985616 0 0.1690011 0.985616 0 0 1 0 -0.1690011 0.985616 0 -0.1690011 0.985616 0 -0.3331399 0.9428774 0 -0.3331398 0.9428774 0 -0.487695 0.873014 0 -0.4876946 0.8730143 0 -0.6282199 0.7780359 0 -0.6282199 0.7780359 0 -0.7506722 0.6606749 0 -0.7506725 0.6606746 0 -0.8515294 0.5243068 0 -0.8515291 0.5243073 0 -0.9278891 0.3728564 0 -0.9278888 0.3728571 0 -0.9775552 0.2106797 0 -0.9775553 0.2106791 0 -0.999099 0.04244166 0 -0.999099 0.04244023 0 -0.9919003 -0.1270188 0 -0.9919005 -0.1270173 0 -0.9561668 -0.2928228 0 -0.8929259 -0.4502037 0 -0.8039973 -0.594633 0 -0.6919391 -0.721956 0 -0.6919387 -0.7219563 0 -0.5599746 -0.8285099 0 -0.5599745 -0.8285099 0 -0.4119012 -0.9112285 0 -0.4119014 -0.9112285 0 -0.251978 -0.967733 0 -0.08480602 -0.9963976 0</float_array>
          <technique_common>
            <accessor source="#ring_big-mesh-normals-array" count="313" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="ring_big-mesh-vertices">
          <input semantic="POSITION" source="#ring_big-mesh-positions"/>
        </vertices>
        <triangles material="Material_001-material" count="452">
          <input semantic="VERTEX" source="#ring_big-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#ring_big-mesh-normals" offset="1"/>
          <p>0 0 1 0 2 0 2 1 1 1 3 1 2 1 3 1 4 1 4 2 3 2 5 2 4 2 5 2 6 2 6 3 5 3 7 3 6 4 7 4 8 4 8 5 7 5 9 5 8 6 9 6 10 6 10 7 9 7 11 7 10 7 11 7 12 7 12 8 11 8 13 8 12 8 13 8 14 8 14 9 13 9 15 9 14 10 15 10 16 10 16 11 15 11 17 11 16 11 17 11 18 11 18 12 17 12 19 12 18 12 19 12 20 12 20 13 19 13 21 13 20 13 21 13 22 13 22 14 21 14 23 14 22 14 23 14 24 14 24 15 23 15 25 15 24 15 25 15 26 15 26 16 25 16 27 16 26 17 27 17 28 17 28 18 27 18 29 18 28 18 29 18 30 18 30 19 29 19 31 19 30 19 31 19 32 19 32 20 31 20 33 20 32 21 33 21 34 21 34 22 33 22 35 22 34 23 35 23 36 23 36 24 35 24 37 24 36 24 37 24 38 24 38 25 37 25 39 25 38 26 39 26 40 26 40 27 39 27 41 27 40 27 41 27 42 27 42 28 41 28 43 28 42 28 43 28 44 28 44 29 43 29 45 29 44 30 45 30 46 30 46 31 45 31 47 31 46 32 47 32 48 32 48 33 47 33 49 33 48 33 49 33 50 33 50 34 49 34 51 34 50 34 51 34 52 34 52 35 51 35 53 35 52 36 53 36 54 36 54 37 53 37 55 37 54 37 55 37 56 37 56 38 55 38 57 38 56 38 57 38 58 38 58 39 57 39 59 39 58 39 59 39 60 39 60 40 59 40 61 40 60 40 61 40 62 40 62 41 61 41 63 41 62 41 63 41 64 41 64 42 63 42 65 42 64 43 65 43 66 43 66 44 65 44 67 44 66 44 67 44 68 44 68 45 67 45 69 45 68 45 69 45 70 45 70 46 69 46 71 46 70 47 71 47 72 47 72 48 71 48 73 48 72 49 73 49 74 49 74 50 73 50 75 50 74 50 75 50 0 50 0 51 75 51 1 51 41 52 76 52 43 52 43 53 76 53 77 53 43 54 77 54 45 54 45 55 77 55 78 55 45 56 78 56 47 56 47 57 78 57 79 57 47 58 79 58 49 58 49 59 79 59 80 59 49 60 80 60 51 60 51 61 80 61 81 61 51 62 81 62 53 62 53 63 81 63 82 63 53 64 82 64 55 64 55 65 82 65 83 65 55 66 83 66 57 66 57 67 83 67 84 67 57 68 84 68 59 68 59 69 84 69 85 69 59 70 85 70 61 70 61 71 85 71 86 71 61 72 86 72 63 72 63 73 86 73 87 73 63 74 87 74 65 74 65 75 87 75 88 75 65 76 88 76 67 76 67 77 88 77 89 77 67 78 89 78 69 78 69 79 89 79 90 79 69 80 90 80 71 80 71 81 90 81 91 81 71 82 91 82 73 82 73 83 91 83 92 83 73 84 92 84 75 84 75 85 92 85 93 85 75 86 93 86 94 86 94 87 93 87 95 87 94 88 95 88 3 88 3 89 95 89 96 89 3 90 96 90 5 90 5 91 96 91 97 91 5 92 97 92 7 92 7 93 97 93 98 93 7 94 98 94 9 94 9 95 98 95 99 95 9 96 99 96 11 96 11 97 99 97 100 97 11 98 100 98 13 98 13 99 100 99 101 99 13 100 101 100 15 100 15 101 101 101 102 101 15 102 102 102 17 102 17 103 102 103 103 103 17 104 103 104 19 104 19 105 103 105 104 105 19 106 104 106 21 106 21 107 104 107 105 107 21 108 105 108 23 108 23 109 105 109 106 109 23 110 106 110 25 110 25 111 106 111 107 111 25 112 107 112 27 112 27 113 107 113 108 113 27 114 108 114 29 114 29 115 108 115 109 115 29 116 109 116 31 116 31 117 109 117 110 117 31 118 110 118 33 118 33 119 110 119 111 119 33 120 111 120 35 120 35 121 111 121 112 121 35 122 112 122 37 122 37 123 112 123 113 123 37 124 113 124 114 124 114 125 113 125 115 125 114 126 115 126 41 126 41 127 115 127 76 127 0 128 116 128 74 128 74 129 116 129 117 129 74 130 117 130 72 130 72 131 117 131 118 131 72 132 118 132 70 132 70 133 118 133 119 133 70 134 119 134 68 134 68 135 119 135 120 135 68 136 120 136 66 136 66 137 120 137 121 137 66 138 121 138 64 138 64 139 121 139 122 139 64 140 122 140 62 140 62 141 122 141 123 141 62 142 123 142 60 142 60 143 123 143 124 143 60 144 124 144 58 144 58 145 124 145 125 145 58 146 125 146 56 146 56 147 125 147 126 147 56 148 126 148 54 148 54 149 126 149 127 149 54 150 127 150 52 150 52 151 127 151 128 151 52 152 128 152 50 152 50 153 128 153 129 153 50 154 129 154 48 154 48 155 129 155 130 155 48 156 130 156 46 156 46 157 130 157 131 157 46 158 131 158 44 158 44 159 131 159 132 159 44 160 132 160 42 160 42 161 132 161 133 161 42 162 133 162 134 162 134 163 133 163 135 163 134 164 135 164 38 164 38 165 135 165 136 165 38 166 136 166 36 166 36 167 136 167 137 167 36 168 137 168 34 168 34 169 137 169 138 169 34 170 138 170 32 170 32 171 138 171 139 171 32 172 139 172 30 172 30 173 139 173 140 173 30 174 140 174 28 174 28 175 140 175 141 175 28 176 141 176 26 176 26 177 141 177 142 177 26 178 142 178 24 178 24 179 142 179 143 179 24 180 143 180 22 180 22 181 143 181 144 181 22 182 144 182 20 182 20 183 144 183 145 183 20 184 145 184 18 184 18 185 145 185 146 185 18 186 146 186 16 186 16 187 146 187 147 187 16 188 147 188 14 188 14 189 147 189 148 189 14 190 148 190 12 190 12 191 148 191 149 191 12 192 149 192 10 192 10 193 149 193 150 193 10 194 150 194 8 194 8 195 150 195 151 195 8 196 151 196 6 196 6 197 151 197 152 197 6 198 152 198 4 198 4 199 152 199 153 199 4 200 153 200 2 200 2 201 153 201 154 201 2 202 154 202 0 202 0 203 154 203 116 203 84 204 83 204 155 204 155 205 83 205 82 205 155 205 82 205 156 205 156 205 82 205 81 205 156 206 81 206 157 206 157 205 81 205 80 205 157 207 80 207 158 207 158 208 80 208 79 208 158 209 79 209 159 209 159 205 79 205 78 205 159 210 78 210 160 210 160 211 78 211 77 211 160 212 77 212 161 212 161 205 77 205 76 205 161 205 76 205 162 205 162 205 76 205 115 205 162 205 115 205 163 205 163 205 115 205 113 205 163 205 113 205 164 205 164 205 113 205 112 205 164 205 112 205 165 205 165 213 112 213 111 213 165 205 111 205 166 205 166 205 111 205 110 205 166 214 110 214 167 214 167 215 110 215 109 215 167 205 109 205 168 205 168 205 109 205 108 205 168 216 108 216 169 216 169 205 108 205 107 205 169 205 107 205 170 205 170 205 107 205 106 205 170 205 106 205 171 205 171 205 106 205 105 205 171 205 105 205 172 205 172 205 105 205 104 205 172 205 104 205 173 205 173 205 104 205 103 205 173 205 103 205 174 205 174 205 103 205 102 205 174 205 102 205 175 205 175 205 102 205 101 205 175 217 101 217 176 217 176 205 101 205 100 205 176 205 100 205 177 205 177 205 100 205 99 205 177 205 99 205 178 205 178 205 99 205 98 205 178 205 98 205 179 205 179 205 98 205 97 205 179 218 97 218 180 218 180 205 97 205 96 205 180 219 96 219 181 219 181 205 96 205 182 205 181 205 182 205 183 205 183 205 182 205 93 205 183 220 93 220 184 220 184 221 93 221 92 221 184 205 92 205 185 205 185 205 92 205 91 205 185 205 91 205 186 205 186 205 91 205 90 205 186 205 90 205 187 205 187 222 90 222 89 222 187 205 89 205 188 205 188 223 89 223 88 223 188 224 88 224 189 224 189 225 88 225 87 225 189 205 87 205 190 205 190 205 87 205 86 205 190 205 86 205 191 205 191 205 86 205 85 205 191 205 85 205 192 205 192 205 85 205 84 205 192 205 84 205 155 205 144 226 143 226 193 226 193 227 143 227 142 227 193 228 142 228 194 228 194 229 142 229 141 229 194 230 141 230 195 230 195 227 141 227 140 227 195 227 140 227 196 227 196 227 140 227 139 227 196 227 139 227 197 227 197 227 139 227 138 227 197 231 138 231 198 231 198 227 138 227 137 227 198 232 137 232 199 232 199 227 137 227 136 227 199 233 136 233 200 233 200 227 136 227 201 227 200 227 201 227 202 227 202 227 201 227 133 227 202 227 133 227 203 227 203 227 133 227 132 227 203 227 132 227 204 227 204 227 132 227 131 227 204 227 131 227 205 227 205 227 131 227 130 227 205 227 130 227 206 227 206 227 130 227 129 227 206 227 129 227 207 227 207 227 129 227 128 227 207 227 128 227 208 227 208 234 128 234 127 234 208 227 127 227 209 227 209 227 127 227 126 227 209 235 126 235 210 235 210 236 126 236 125 236 210 227 125 227 211 227 211 237 125 237 124 237 211 227 124 227 212 227 212 238 124 238 123 238 212 227 123 227 213 227 213 227 123 227 122 227 213 239 122 239 214 239 214 227 122 227 121 227 214 227 121 227 215 227 215 227 121 227 120 227 215 227 120 227 216 227 216 227 120 227 119 227 216 227 119 227 217 227 217 227 119 227 118 227 217 227 118 227 218 227 218 240 118 240 117 240 218 241 117 241 219 241 219 227 117 227 116 227 219 227 116 227 220 227 220 227 116 227 154 227 220 227 154 227 221 227 221 242 154 242 153 242 221 227 153 227 222 227 222 227 153 227 152 227 222 227 152 227 223 227 223 243 152 243 151 243 223 244 151 244 224 244 224 227 151 227 150 227 224 227 150 227 225 227 225 245 150 245 149 245 225 227 149 227 226 227 226 227 149 227 148 227 226 227 148 227 227 227 227 227 148 227 147 227 227 227 147 227 228 227 228 227 147 227 146 227 228 227 146 227 229 227 229 246 146 246 145 246 229 247 145 247 230 247 230 248 145 248 144 248 230 227 144 227 193 227 221 249 231 249 220 249 220 250 231 250 183 250 220 250 183 250 219 250 219 251 183 251 184 251 219 251 184 251 218 251 218 252 184 252 185 252 218 253 185 253 217 253 217 254 185 254 186 254 217 255 186 255 216 255 216 256 186 256 187 256 216 257 187 257 215 257 215 258 187 258 188 258 215 258 188 258 214 258 214 259 188 259 189 259 214 259 189 259 213 259 213 260 189 260 190 260 213 260 190 260 212 260 212 261 190 261 191 261 212 262 191 262 211 262 211 263 191 263 192 263 211 264 192 264 210 264 210 265 192 265 155 265 210 266 155 266 209 266 209 267 155 267 156 267 209 268 156 268 208 268 208 269 156 269 157 269 208 270 157 270 207 270 207 271 157 271 158 271 207 272 158 272 206 272 206 273 158 273 159 273 206 274 159 274 205 274 205 275 159 275 160 275 205 276 160 276 204 276 204 277 160 277 161 277 204 278 161 278 203 278 203 279 161 279 162 279 203 280 162 280 202 280 202 281 162 281 163 281 202 281 163 281 200 281 200 282 163 282 164 282 200 283 164 283 199 283 199 284 164 284 165 284 199 285 165 285 198 285 198 286 165 286 166 286 198 287 166 287 197 287 197 288 166 288 167 288 197 289 167 289 196 289 196 290 167 290 168 290 196 291 168 291 195 291 195 292 168 292 169 292 195 293 169 293 194 293 194 294 169 294 170 294 194 295 170 295 193 295 193 296 170 296 171 296 193 297 171 297 230 297 230 298 171 298 172 298 230 299 172 299 229 299 229 300 172 300 173 300 229 301 173 301 228 301 228 302 173 302 174 302 228 302 174 302 227 302 227 303 174 303 175 303 227 303 175 303 226 303 226 304 175 304 176 304 226 304 176 304 225 304 225 305 176 305 177 305 225 306 177 306 224 306 224 307 177 307 178 307 224 308 178 308 223 308 223 309 178 309 179 309 223 310 179 310 222 310 222 311 179 311 180 311 222 311 180 311 221 311 221 312 180 312 231 312</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.481132 0.7276763 0.3054208 -0.6141704 -6.50764 0 0.8953956 0.4452714 5.343665 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Lamp" name="Lamp" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Lamp-light"/>
      </node>
      <node id="ring_big" name="ring_big" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#ring_big-mesh" name="ring_big">
          <bind_material>
            <technique_common>
              <instance_material symbol="Material_001-material" target="#Material_001-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>