<?xml version="1.0" ?>
<!-- ================================================================================================================= -->
<!-- |    This document was autogenerated by xacro from kinova-ros/ocs2_robotic_assets/resources/mobile_manipulator/kinova/urdf/j2n6s300_standalone.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                                               | -->
<!-- ================================================================================================================= -->
<!-- j2n6s300 refers to jaco v2 6DOF non-spherical 3fingers -->
<robot name="j2n6s300" xmlns:xi="http://www.w3.org/2001/XInclude">
  <!-- links      		mesh_no
   base           		0
   shoulder       		1
   arm            		2
   forearm        		3
   wrist          		4
   arm_mico       		5
   arm_half1 (7dof)		6
   arm_half2 (7dof)		7
   wrist_spherical_1  8
   wrist_spherical_2  9

   hand 3 finger  		55
   hand_2finger   		56
   finger_proximal		57
   finger_distal      58
-->
  <!-- links      		mesh_no
   base           		0
   shoulder       		1
   arm            		2
   forearm        		3
   wrist          		4
   arm_mico       		5
   arm_half1 (7dof)		6
   arm_half2 (7dof)		7
   wrist_spherical_1  8
   wrist_spherical_2  9

   hand 3 finger  		55
   hand_2finger   		56
   finger_proximal		57
   finger_distal      58
-->
  <!-- Root link -->
  <link name="root"/>
  <joint name="root_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="root"/>
    <child link="j2n6s300_link_base"/>
  </joint>
  <!-- Robot arm -->
  <link name="j2n6s300_link_base">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/base.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/base.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.46784"/>
      <origin rpy="0 0 0" xyz="0 0 0.1255"/>
      <inertia ixx="0.000951270861568" ixy="0" ixz="0" iyy="0.000951270861568" iyz="0" izz="0.00037427200000000004"/>
    </inertial>
  </link>
  <link name="j2n6s300_link_1">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/shoulder.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/shoulder.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.7477"/>
      <origin xyz="0 -0.002 -0.0605"/>
      <inertia ixx="0.0015203172520400004" ixy="0" ixz="0" iyy="0.0015203172520400004" iyz="0" izz="0.00059816"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_1" type="revolute">
    <parent link="j2n6s300_link_base"/>
    <child link="j2n6s300_link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="40" velocity="0.6283185307179586" lower="-300" upper="300"/>
    <origin rpy="0 3.141592653589793 0" xyz="0 0 0.15675"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_link_2">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/arm.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.99"/>
      <origin xyz="0 -0.2065 -0.01"/>
      <inertia ixx="0.010502207990999999" ixy="0" ixz="0" iyy="0.0007920000000000001" iyz="0" izz="0.010502207990999999"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_2" type="revolute">
    <parent link="j2n6s300_link_1"/>
    <child link="j2n6s300_link_2"/>
    <axis xyz="0 0 1"/>
    <limit effort="80" lower="0.8203047484373349" upper="5.462880558742252" velocity="0.6283185307179586"/>
    <origin rpy="-1.5707963267948966 0 3.141592653589793" xyz="0 0.0016 -0.11875"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_link_3">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/forearm.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.6763"/>
      <origin xyz="0 0.081 -0.0086"/>
      <inertia ixx="0.0014202243190800001" ixy="0" ixz="0" iyy="0.000304335" iyz="0" izz="0.0014202243190800001"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_3" type="revolute">
    <parent link="j2n6s300_link_2"/>
    <child link="j2n6s300_link_3"/>
    <axis xyz="0 0 1"/>
    <limit effort="40" lower="0.33161255787892263" upper="5.951572749300664" velocity="0.6283185307179586"/>
    <origin rpy="0 3.141592653589793 0" xyz="0 -0.410 0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_link_4">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.426367"/>
      <origin xyz="0 -0.037 -0.0642"/>
      <inertia ixx="7.734969059999999e-05" ixy="0" ixz="0" iyy="7.734969059999999e-05" iyz="0" izz="0.0001428"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_4" type="revolute">
    <parent link="j2n6s300_link_3"/>
    <child link="j2n6s300_link_4"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781" lower="-300" upper="300"/>
    <origin rpy="-1.5707963267948966 0 3.141592653589793" xyz="0 0.2073 -0.0114"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_link_5">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/wrist.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.426367"/>
      <origin xyz="0 -0.037 -0.0642"/>
      <inertia ixx="7.734969059999999e-05" ixy="0" ixz="0" iyy="7.734969059999999e-05" iyz="0" izz="0.0001428"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_5" type="revolute">
    <parent link="j2n6s300_link_4"/>
    <child link="j2n6s300_link_5"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781" lower="-300" upper="300"/>
    <origin rpy="1.0471975511965976 0 3.141592653589793" xyz="0 -0.03703 -0.06414"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_link_6">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_3finger.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/hand_3finger.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.99"/>
      <origin xyz="0 0 -0.06"/>
      <inertia ixx="0.00034532361869999995" ixy="0" ixz="0" iyy="0.00034532361869999995" iyz="0" izz="0.0005815999999999999"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_6" type="revolute">
    <parent link="j2n6s300_link_5"/>
    <child link="j2n6s300_link_6"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781" lower="-300" upper="300"/>
    <origin rpy="1.0471975511965976 0 3.141592653589793" xyz="0 -0.03703 -0.06414"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n6s300_end_effector"/>
  <joint name="j2n6s300_joint_end_effector" type="fixed">
    <parent link="j2n6s300_link_6"/>
    <child link="j2n6s300_end_effector"/>
    <axis xyz="0 0 0"/>
    <origin rpy="3.141592653589793 0 1.5707963267948966" xyz="0 0 -0.1600"/>
  </joint>
  <link name="j2n6s300_link_finger_1">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_1" type="revolute">
    <parent link="j2n6s300_link_6"/>
    <child link="j2n6s300_link_finger_1"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.7047873384941834 0.6476144647144773 1.67317415161155" xyz="0.00279 0.03126 -0.11467"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <link name="j2n6s300_link_finger_tip_1">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_tip_1" type="revolute">
    <parent link="j2n6s300_link_finger_1"/>
    <child link="j2n6s300_link_finger_tip_1"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
  <link name="j2n6s300_link_finger_2">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_2" type="revolute">
    <parent link="j2n6s300_link_6"/>
    <child link="j2n6s300_link_finger_2"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.570796327 .649262481663582 -1.38614049188413" xyz="0.02226 -0.02707 -0.11482"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <link name="j2n6s300_link_finger_tip_2">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_tip_2" type="revolute">
    <parent link="j2n6s300_link_finger_2"/>
    <child link="j2n6s300_link_finger_tip_2"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
  <link name="j2n6s300_link_finger_3">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_3" type="revolute">
    <parent link="j2n6s300_link_6"/>
    <child link="j2n6s300_link_finger_3"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.570796327 .649262481663582 -1.75545216211587" xyz="-0.02226 -0.02707 -0.11482"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <link name="j2n6s300_link_finger_tip_3">
    <visual>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/kinova/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n6s300_joint_finger_tip_3" type="revolute">
    <parent link="j2n6s300_link_finger_3"/>
    <child link="j2n6s300_link_finger_tip_3"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
</robot>
