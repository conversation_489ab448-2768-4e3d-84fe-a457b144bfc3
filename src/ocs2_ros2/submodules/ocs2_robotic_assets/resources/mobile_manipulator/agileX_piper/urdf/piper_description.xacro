<?xml version="1.0" encoding="utf-8"?>
<robot
  name="piper"
  xmlns:xacro="http://ros.org/wiki/xacro">
<!-- world -->
  <link name="world"/>
  <joint name="fixed_base_joint" type="fixed">
      <parent link="world"/>
      <child link="base_link"/>
  </joint>
<!-- piper -->
<link
    name="base_link">
    <inertial>
      <origin
        xyz="-0.00473641164191482 2.56829134630247E-05 0.041451518036016"
        rpy="0 0 0" />
      <mass
        value="1.02" />
      <inertia
        ixx="0.00267433" 
        ixy="-0.00000073" 
        ixz="-0.00017389" 
        iyy="0.00282612" 
        iyz="0.0000004" 
        izz="0.00089624" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="link1">
    <inertial>
      <origin
        xyz="0.000121504734057468 0.000104632162460536 -0.00438597309559853"
        rpy="0 0 0" />
      <mass
        value="0.71" />
      <inertia
        ixx="0.00048916" 
        ixy="-0.00000036" 
        ixz="-0.00000224" 
        iyy="0.00040472" 
        iyz="-0.00000242" 
        izz="0.00043982" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint1"
    type="revolute">
    <origin
      xyz="0 0 0.123"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="link1" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.618"
      upper="2.168"
      effort="100"
      velocity="5" />
  </joint>
  <link
    name="link2">
    <inertial>
      <origin
        xyz="0.198666145229743 -0.010926924140076 0.00142121714502687"
        rpy="0 0 0" />
      <mass
        value="1.17" />
      <inertia
        ixx="0.00116918" 
        ixy="-0.00180037" 
        ixz="0.00025146" 
        iyy="0.06785384" 
        iyz="-0.00000455" 
        izz="0.06774489" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint2"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 -0.10095 -3.1416" />
    <parent
      link="link1" />
    <child
      link="link2" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="3.14"
      effort="100"
      velocity="5" />
  </joint>
  <link
    name="link3">
    <inertial>
      <origin
        xyz="-0.0202737662122021 -0.133914995944595 -0.000458682652737356"
        rpy="0 0 0" />
      <mass
        value="0.5" />
      <inertia
        ixx="0.01361711" 
        ixy="0.00165794" 
        ixz="-0.00000048" 
        iyy="0.00045024" 
        iyz="-0.00000045" 
        izz="0.01380322" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint3"
    type="revolute">
    <origin
      xyz="0.28503 0 0"
      rpy="0 0 -1.759" />
    <parent
      link="link2" />
    <child
      link="link3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.967"
      upper="0"
      effort="100"
      velocity="5" />
  </joint>
  <link
    name="link4">
    <inertial>
      <origin
        xyz="-9.66635791618542E-05 0.000876064475651083 -0.00496880904640868"
        rpy="0 0 0" />
      <mass
        value="0.38" />
      <inertia
        ixx="0.00018501" 
        ixy="0.00000054" 
        ixz="0.00000120" 
        iyy="0.00018965" 
        iyz="-0.00000841" 
        izz="0.00015484" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint4"
    type="revolute">
    <origin
      xyz="-0.021984 -0.25075 0"
      rpy="1.5708 0 0" />
    <parent
      link="link3" />
    <child
      link="link4" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.745"
      upper="1.745"
      effort="100"
      velocity="5" />
  </joint>
  <link
    name="link5">
    <inertial>
      <origin
        xyz="-4.10554118924211E-05 -0.0566486692356075 -0.0037205791677906"
        rpy="0 0 0" />
      <mass
        value="0.383" />
      <inertia
        ixx="0.00166169" 
        ixy="0.00000006" 
        ixz="-0.00000007" 
        iyy="0.00018510" 
        iyz="0.00001026" 
        izz="0.00164321" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint5"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="link4" />
    <child
      link="link5" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.22"
      upper="1.22"
      effort="100"
      velocity="5" />
  </joint>
  <link
    name="link6">
    <inertial>
      <origin
        xyz="-8.82590762930069E-05 9.0598378529832E-06 -0.002"
        rpy="0 0 0" />
      <mass
        value="0.00699089613564366" />
      <inertia
        ixx="5.73015540542155E-07" 
        ixy="-1.98305403089247E-22"
        ixz="-7.2791893904596E-23"
        iyy="5.73015540542155E-07" 
        iyz="-3.4146026640245E-24"
        izz="1.06738869138926E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint6"
    type="revolute">
    <origin
      xyz="8.8259E-05 -0.091 0"
      rpy="1.5708 0 0" />
    <parent
      link="link5" />
    <child
      link="link6" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.0944"
      upper="2.0944"
      effort="100"
      velocity="3" />
  </joint>
  <link
    name="gripper_base">
    <inertial>
      <origin
        xyz="-0.000183807162235591 8.05033155577911E-05 0.0321436689908876"
        rpy="0 0 0" />
      <mass
        value="0.45" />
      <inertia
        ixx="0.00092934"
        ixy="0.00000034"
        ixz="-0.00000738"
        iyy="0.00071447"
        iyz="0.00000005"
        izz="0.00039442" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/gripper_base.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/gripper_base.STL" />
      </geometry>
    </collision>
  </link> 
  <joint
    name="joint6_to_gripper_base"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="link6" />
    <child
      link="gripper_base" />
  </joint>
  <link
    name="link7">
    <inertial>
      <origin
        xyz="0.00065123185041968 -0.0491929869131989 0.00972258769184025"
        rpy="0 0 0" />
      <mass
        value="0.025" />
      <inertia
        ixx="0.00007371"
        ixy="-0.00000113"
        ixz="0.00000021"
        iyy="0.00000781"
        iyz="-0.00001372"
        izz="0.0000747" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint7"
    type="prismatic">
    <origin
      xyz="0 0 0.1358"
      rpy="1.5708 0 0" />
    <parent
      link="gripper_base" />
    <child
      link="link7" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="0.035"
      effort="10"
      velocity="1" />
  </joint>
  <link
    name="link8">
    <inertial>
      <origin
        xyz="0.000651231850419722 -0.0491929869131991 0.00972258769184024"
        rpy="0 0 0" />
      <mass
        value="0.025" />
      <inertia
        ixx="0.00007371"
        ixy="-0.00000113"
        ixz="0.00000021"
        iyy="0.00000781"
        iyz="-0.00001372"
        izz="0.0000747" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link8.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/mobile_manipulator/agileX_piper/meshes/link8.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint8"
    type="prismatic">
    <origin
      xyz="0 0 0.1358"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="gripper_base" />
    <child
      link="link8" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-0.035"
      upper="0"
      effort="10"
      velocity="1" />
  </joint>

<!-- controller -->
  <xacro:macro name="transmission_block" params="tran_name joint_name motor_name">
    <transmission name="${tran_name}">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="${joint_name}">
            <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
        </joint>
        <actuator name="$motor_name">
            <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
  </xacro:macro>

  <xacro:transmission_block tran_name="tran1" joint_name="joint1" motor_name="motor1"/>
  <xacro:transmission_block tran_name="tran2" joint_name="joint2" motor_name="motor2"/>
  <xacro:transmission_block tran_name="tran3" joint_name="joint3" motor_name="motor3"/>
  <xacro:transmission_block tran_name="tran4" joint_name="joint4" motor_name="motor4"/>
  <xacro:transmission_block tran_name="tran5" joint_name="joint5" motor_name="motor5"/>
  <xacro:transmission_block tran_name="tran6" joint_name="joint6" motor_name="motor6"/>
  <xacro:transmission_block tran_name="tran7" joint_name="joint7" motor_name="motor7"/>
  <xacro:transmission_block tran_name="tran8" joint_name="joint8" motor_name="motor8"/>
<!-- gazebo -->
  <gazebo>
    <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
        <robotNamespace>/piper</robotNamespace>
        <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>
 
  <!-- <gazebo reference="base_link">
    <material>Gazebo/Black</material>
  </gazebo>

  <gazebo reference="link1">
    <material>Gazebo/White</material>
  </gazebo>
 
  <gazebo reference="link2">
    <material>Gazebo/White</material>
  </gazebo>

  <gazebo reference="link3">
    <material>Gazebo/White</material>
  </gazebo>
 
  <gazebo reference="link4">
    <material>Gazebo/Black</material>
  </gazebo>
 
  <gazebo reference="link5">
    <material>Gazebo/White</material>
  </gazebo>
 
  <gazebo reference="link6">
    <material>Gazebo/White</material>
  </gazebo>
 
  <gazebo reference="link7">
    <material>Gazebo/Orange</material>
  </gazebo>
 
  <gazebo reference="link8">
    <material>Gazebo/Orange</material>
  </gazebo> -->
</robot>