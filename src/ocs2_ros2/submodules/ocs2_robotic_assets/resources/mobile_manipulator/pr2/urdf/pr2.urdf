<?xml version="1.0" ?>
<!-- ===================================================================================================== -->
<!-- |    This document was autogenerated by xacro from pr2_common/ocs2_robotic_assets/resources/mobile_manipulator/pr2/robots/pr2.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                                   | -->
<!-- ===================================================================================================== -->
<robot name="pr2">
  <!--TODO Define and give source-->
  <!-- ============================   Shoulder   ============================ -->
  <!-- ============================   Upper Arm   ============================ -->
  <!-- ============================   Forearm   ============================ -->
  <!-- DATA SOURCES -->
  <!-- all link offsets, CG, limits are obtained from Function Engineering spreadsheet 090224_link_data.xls unless stated otherwise -->
  <!-- all link geometry sizes are obtained from Function provided CAD model unless stated otherwise -->
  <!-- all simplified collision geometry are hand approximated from CAD model, sometimes from respective bounding boxes -->
  <!-- This is the 'effective' wheel radius. Wheel radius for uncompressed wheel is 0.079.  mp 20080801 -->
  <material name="Blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="Green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="Grey">
    <color rgba="0.7 0.7 0.7 1.0"/>
  </material>
  <material name="Grey2">
    <color rgba="0.9 0.9 0.9 1.0"/>
  </material>
  <material name="Red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="White">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <material name="Black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>
  <material name="LightGrey">
    <color rgba="0.6 0.6 0.6 1.0"/>
  </material>
  <material name="Caster">
    <texture filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_caster_texture.png"/>
  </material>
  <material name="Wheel_l">
    <texture filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_wheel_left.png"/>
  </material>
  <material name="Wheel_r">
    <texture filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_wheel_right.png"/>
  </material>
  <material name="RollLinks">
    <texture filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/materials/textures/pr2_wheel_left.png"/>
  </material>
  <!-- Now we can start using the macros included above to define the actual PR2 -->
  <link name="base_link">
    <inertial>
      <mass value="116.0"/>
      <origin xyz="-0.061 0.0 0.1465"/>
      <inertia ixx="5.652232699207" ixy="-0.009719934438" ixz="1.293988226423" iyy="5.669473158652" iyz="-0.007379583694" izz="3.683196351726"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base.dae"/>
      </geometry>
      <material name="White"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/base_L.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- base_footprint is a fictitious link(frame) that is on the ground right below base_link origin,
         navigation stack dedpends on this frame -->
  <link name="base_footprint">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <material name="White"/>
    </visual>
    <collision>
      <!-- represent base collision with a simple rectangular model, positioned by base_size_z s.t. top
             surface of the collision box matches the top surface of the PR2 base -->
      <origin rpy="0 0 0" xyz="0 0 0.071"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <joint name="base_footprint_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.051"/>
    <child link="base_link"/>
    <parent link="base_footprint"/>
  </joint>
  <!-- visualize bellow -->
  <link name="base_bellow_link">
    <inertial>
      <mass value="1.0"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.05 0.37 0.3"/>
      </geometry>
      <material name="Black"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.05 0.37 0.3"/>
      </geometry>
    </collision>
  </link>
  <joint name="base_bellow_joint" type="fixed">
    <origin rpy="0 0 0" xyz="-0.29 0 0.8"/>
    <parent link="base_link"/>
    <child link="base_bellow_link"/>
  </joint>
  <joint name="base_laser_joint" type="fixed">
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0.275 0.0 0.252"/>
    <parent link="base_link"/>
    <child link="base_laser_link"/>
  </joint>
  <link name="base_laser_link" type="laser">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <joint name="fl_caster_rotation_joint" type="continuous">
    <axis xyz="0 0 1"/>
    <limit effort="6.5" velocity="10"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="10"/>
    <calibration rising="-0.7853981633974483"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0.2246 0.2246 0.0282"/>
    <parent link="base_link"/>
    <child link="fl_caster_rotation_link"/>
  </joint>
  <link name="fl_caster_rotation_link">
    <inertial>
      <mass value="3.473082"/>
      <origin xyz="0 0 0.07"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster.stl"/>
      </geometry>
      <material name="Caster"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="fl_caster_l_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 0.049 0"/>
    <parent link="fl_caster_rotation_link"/>
    <child link="fl_caster_l_wheel_link"/>
  </joint>
  <link name="fl_caster_l_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_l"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="fl_caster_r_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 -0.049 0"/>
    <parent link="fl_caster_rotation_link"/>
    <child link="fl_caster_r_wheel_link"/>
  </joint>
  <link name="fl_caster_r_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_r"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="fr_caster_rotation_joint" type="continuous">
    <axis xyz="0 0 1"/>
    <limit effort="6.5" velocity="10"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="10"/>
    <calibration rising="-0.7853981633974483"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0.2246 -0.2246 0.0282"/>
    <parent link="base_link"/>
    <child link="fr_caster_rotation_link"/>
  </joint>
  <link name="fr_caster_rotation_link">
    <inertial>
      <mass value="3.473082"/>
      <origin xyz="0 0 0.07"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster.stl"/>
      </geometry>
      <material name="Caster"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="fr_caster_l_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 0.049 0"/>
    <parent link="fr_caster_rotation_link"/>
    <child link="fr_caster_l_wheel_link"/>
  </joint>
  <link name="fr_caster_l_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_l"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="fr_caster_r_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 -0.049 0"/>
    <parent link="fr_caster_rotation_link"/>
    <child link="fr_caster_r_wheel_link"/>
  </joint>
  <link name="fr_caster_r_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_r"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="bl_caster_rotation_joint" type="continuous">
    <axis xyz="0 0 1"/>
    <limit effort="6.5" velocity="10"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="10"/>
    <calibration rising="2.356194490192345"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="-0.2246 0.2246 0.0282"/>
    <parent link="base_link"/>
    <child link="bl_caster_rotation_link"/>
  </joint>
  <link name="bl_caster_rotation_link">
    <inertial>
      <mass value="3.473082"/>
      <origin xyz="0 0 0.07"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster.stl"/>
      </geometry>
      <material name="Caster"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="bl_caster_l_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 0.049 0"/>
    <parent link="bl_caster_rotation_link"/>
    <child link="bl_caster_l_wheel_link"/>
  </joint>
  <link name="bl_caster_l_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_l"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="bl_caster_r_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 -0.049 0"/>
    <parent link="bl_caster_rotation_link"/>
    <child link="bl_caster_r_wheel_link"/>
  </joint>
  <link name="bl_caster_r_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_r"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="br_caster_rotation_joint" type="continuous">
    <axis xyz="0 0 1"/>
    <limit effort="6.5" velocity="10"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="10"/>
    <calibration rising="2.356194490192345"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="-0.2246 -0.2246 0.0282"/>
    <parent link="base_link"/>
    <child link="br_caster_rotation_link"/>
  </joint>
  <link name="br_caster_rotation_link">
    <inertial>
      <mass value="3.473082"/>
      <origin xyz="0 0 0.07"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster.stl"/>
      </geometry>
      <material name="Caster"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/caster_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="br_caster_l_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 0.049 0"/>
    <parent link="br_caster_rotation_link"/>
    <child link="br_caster_l_wheel_link"/>
  </joint>
  <link name="br_caster_l_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_l"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="br_caster_r_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <limit effort="7" velocity="15"/>
    <!-- alpha tested effort and velocity limits -->
    <safety_controller k_velocity="10"/>
    <dynamics damping="1.0" friction="0.0"/>
    <origin rpy="0 0 0" xyz="0 -0.049 0"/>
    <parent link="br_caster_rotation_link"/>
    <child link="br_caster_r_wheel_link"/>
  </joint>
  <link name="br_caster_r_wheel_link">
    <inertial>
      <mass value="0.44036"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/base_v0/wheel.dae"/>
      </geometry>
      <material name="Wheel_r"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <!-- rotation because cyl. geom primitive has symmetry axis in +x direction -->
      <geometry>
        <cylinder length="0.034" radius="0.074792"/>
      </geometry>
    </collision>
  </link>
  <joint name="torso_lift_joint" type="prismatic">
    <axis xyz="0 0 1"/>
    <limit effort="10000" lower="0.0" upper="0.33" velocity="0.013"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="2000000" soft_lower_limit="0.0115" soft_upper_limit="0.325"/>
    <calibration falling="0.00475"/>
    <dynamics damping="20000.0"/>
    <origin rpy="0 0 0" xyz="-0.05 0 0.739675"/>
    <parent link="base_link"/>
    <child link="torso_lift_link"/>
  </joint>
  <link name="torso_lift_link">
    <inertial>
      <mass value="36.248046"/>
      <origin xyz="-0.1 0 -0.0885"/>
      <inertia ixx="2.771653750257" ixy="0.004284522609" ixz="-0.160418504506" iyy="2.510019507959" iyz="0.029664468704" izz="0.526432355569"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift.dae"/>
      </geometry>
      <material name="Grey2"/>
    </visual>
    <collision name="torso_lift_collision">
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/torso_v0/torso_lift_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="l_torso_lift_side_plate_joint" type="fixed">
    <origin xyz="0.0535 0.209285 0.176625"/>
    <!-- location of bottom front bolt hole location -->
    <parent link="torso_lift_link"/>
    <child link="l_torso_lift_side_plate_link"/>
  </joint>
  <link name="l_torso_lift_side_plate_link">
    <inertial>
      <mass value="0.1"/>
      <origin xyz="-0.0625 0.0 0.05"/>
      <!-- center of the 12.5cm by 10cm bolt hole pattern -->
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="r_torso_lift_side_plate_joint" type="fixed">
    <origin xyz="0.0535  -0.209285 0.176625"/>
    <!-- location of bottom front bolt hole location -->
    <parent link="torso_lift_link"/>
    <child link="r_torso_lift_side_plate_link"/>
  </joint>
  <link name="r_torso_lift_side_plate_link">
    <inertial>
      <mass value="0.1"/>
      <origin xyz="-0.0625 0.0 0.05"/>
      <!-- center of the 12.5cm by 10cm bolt hole pattern -->
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <!-- actuated motor screw joint -->
  <link name="torso_lift_motor_screw_link">
    <inertial>
      <mass value="1.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <!-- for debugging only
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.5 0.7 0.01" />
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.5 0.7 0.01" />
        </geometry>
      </collision>
      -->
  </link>
  <joint name="torso_lift_motor_screw_joint" type="fixed">
    <origin xyz="-0.15 0.0 0.7"/>
    <axis xyz="0 0 1"/>
    <parent link="base_link"/>
    <child link="torso_lift_motor_screw_link"/>
    <dynamics damping="0.0001"/>
  </joint>
  <joint name="imu_joint" type="fixed">
    <axis xyz="0 1 0"/>
    <origin rpy="0 3.141592653589793 0" xyz="-0.02977 -0.1497 0.164"/>
    <parent link="torso_lift_link"/>
    <child link="imu_link"/>
  </joint>
  <link name="imu_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <joint name="head_pan_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="2.645" lower="-3.007" upper="3.007" velocity="6"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="1.5" soft_lower_limit="-2.857" soft_upper_limit="2.857"/>
    <calibration rising="0.0"/>
    <dynamics damping="0.5"/>
    <origin rpy="0.0 0.0 0.0" xyz="-0.01707 0.0 0.38145"/>
    <parent link="torso_lift_link"/>
    <child link="head_pan_link"/>
  </joint>
  <link name="head_pan_link">
    <inertial>
      <mass value="6.339"/>
      <!-- mass/cog/moi updated per 100505_link_data.xls -->
      <origin rpy="0 0 0" xyz="0.010907 0.031693 0.090507"/>
      <inertia ixx="0.032497592" ixy="0.00063604088" ixz="0.0025851534" iyy="0.046545627" iyz="-0.0024534295" izz="0.057652724"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0 " xyz="0 0 0.0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan.dae"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_pan_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="head_tilt_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="18" lower="-0.471238" upper="1.39626" velocity="5"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="3.0" soft_lower_limit="-0.3712" soft_upper_limit="1.29626"/>
    <calibration falling="0.0"/>
    <dynamics damping="10.0"/>
    <origin rpy="0 0 0" xyz="0.068 0 0"/>
    <parent link="head_pan_link"/>
    <child link="head_tilt_link"/>
  </joint>
  <link name="head_tilt_link">
    <inertial>
      <mass value="4.479"/>
      <!-- mass/cog/moi updated per 100505_link_data.xls -->
      <origin rpy="0 0 0" xyz="0.001716 -0.019556 0.055002"/>
      <inertia ixx="0.024223222" ixy="0.00062063507" ixz="-0.000096909696" iyy="0.054723086" iyz="0.00279702400" izz="0.067306377"/>
    </inertial>
    <visual>
      <origin rpy="0.0 0.0 0.0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/head_v0/head_tilt_L.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Head plate frame -->
  <joint name="head_plate_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0232 0 0.0645"/>
    <parent link="head_tilt_link"/>
    <child link="head_plate_frame"/>
  </joint>
  <link name="head_plate_frame">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
    </collision>
  </link>
  <joint name="sensor_mount_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
    <parent link="head_plate_frame"/>
    <child link="sensor_mount_link"/>
  </joint>
  <link name="sensor_mount_link">
    <inertial>
      <!-- Needs verification with CAD -->
      <mass value="0.05"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  <joint name="high_def_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.046457 -0.110 0.054600"/>
    <parent link="sensor_mount_link"/>
    <child link="high_def_frame"/>
  </joint>
  <link name="high_def_frame">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="high_def_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0.0            0.0         0.0"/>
    <parent link="high_def_frame"/>
    <child link="high_def_optical_frame"/>
  </joint>
  <link name="high_def_optical_frame">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <!-- Define link to stereo cameras, set origin relative to that -->
  <joint name="double_stereo_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
    <parent link="sensor_mount_link"/>
    <child link="double_stereo_link"/>
  </joint>
  <link name="double_stereo_link">
    <inertial>
      <!-- Needs verification with CAD -->
      <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  <joint name="wide_stereo_frame_joint" type="fixed">
    <origin rpy="0.0   0.0   0.0" xyz="0.045 0.03 0.0501"/>
    <parent link="double_stereo_link"/>
    <child link="wide_stereo_link"/>
  </joint>
  <!-- camera link is at center of the optical frame, but in x-forward notation -->
  <link name="wide_stereo_link">
    <inertial>
      <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
      <!-- this inertia is made up for now. -->
    </inertial>
  </link>
  <!-- attach optical frame to the camera link -->
  <joint name="wide_stereo_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <!-- rotate frame from x-forward to z-forward camera coords -->
    <parent link="wide_stereo_link"/>
    <child link="wide_stereo_optical_frame"/>
  </joint>
  <!-- optical frame for the stereo camera, with z-forward notation, this is the frame stereo camera images users should refer to -->
  <link name="wide_stereo_optical_frame" type="camera"/>
  <joint name="wide_stereo_l_stereo_camera_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="wide_stereo_link"/>
    <child link="wide_stereo_l_stereo_camera_frame"/>
  </joint>
  <link name="wide_stereo_l_stereo_camera_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="wide_stereo_l_stereo_camera_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="wide_stereo_l_stereo_camera_frame"/>
    <child link="wide_stereo_l_stereo_camera_optical_frame"/>
  </joint>
  <link name="wide_stereo_l_stereo_camera_optical_frame"/>
  <joint name="wide_stereo_r_stereo_camera_frame_joint" type="fixed">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 -0.09 0.0"/>
    <parent link="wide_stereo_l_stereo_camera_frame"/>
    <child link="wide_stereo_r_stereo_camera_frame"/>
  </joint>
  <link name="wide_stereo_r_stereo_camera_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="wide_stereo_r_stereo_camera_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="wide_stereo_r_stereo_camera_frame"/>
    <child link="wide_stereo_r_stereo_camera_optical_frame"/>
  </joint>
  <link name="wide_stereo_r_stereo_camera_optical_frame"/>
  <joint name="narrow_stereo_frame_joint" type="fixed">
    <origin rpy="0.0   0.0   0.0" xyz="0.045 0.06 0.0501"/>
    <parent link="double_stereo_link"/>
    <child link="narrow_stereo_link"/>
  </joint>
  <!-- camera link is at center of the optical frame, but in x-forward notation -->
  <link name="narrow_stereo_link">
    <inertial>
      <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
      <!-- this inertia is made up for now. -->
    </inertial>
  </link>
  <!-- attach optical frame to the camera link -->
  <joint name="narrow_stereo_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <!-- rotate frame from x-forward to z-forward camera coords -->
    <parent link="narrow_stereo_link"/>
    <child link="narrow_stereo_optical_frame"/>
  </joint>
  <!-- optical frame for the stereo camera, with z-forward notation, this is the frame stereo camera images users should refer to -->
  <link name="narrow_stereo_optical_frame" type="camera"/>
  <joint name="narrow_stereo_l_stereo_camera_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="narrow_stereo_link"/>
    <child link="narrow_stereo_l_stereo_camera_frame"/>
  </joint>
  <link name="narrow_stereo_l_stereo_camera_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="narrow_stereo_l_stereo_camera_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="narrow_stereo_l_stereo_camera_frame"/>
    <child link="narrow_stereo_l_stereo_camera_optical_frame"/>
  </joint>
  <link name="narrow_stereo_l_stereo_camera_optical_frame"/>
  <joint name="narrow_stereo_r_stereo_camera_frame_joint" type="fixed">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 -0.09 0.0"/>
    <parent link="narrow_stereo_l_stereo_camera_frame"/>
    <child link="narrow_stereo_r_stereo_camera_frame"/>
  </joint>
  <link name="narrow_stereo_r_stereo_camera_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="narrow_stereo_r_stereo_camera_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="narrow_stereo_r_stereo_camera_frame"/>
    <child link="narrow_stereo_r_stereo_camera_optical_frame"/>
  </joint>
  <link name="narrow_stereo_r_stereo_camera_optical_frame"/>
  <joint name="projector_wg6802418_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.110 0.0546"/>
    <parent link="head_plate_frame"/>
    <child link="projector_wg6802418_frame"/>
  </joint>
  <link name="projector_wg6802418_frame">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="projector_wg6802418_child_frame_joint" type="fixed">
    <origin rpy="0 -1.5707963267948966 0" xyz="0 0 0"/>
    <parent link="projector_wg6802418_frame"/>
    <child link="projector_wg6802418_child_frame"/>
  </joint>
  <link name="projector_wg6802418_child_frame">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="laser_tilt_mount_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="0.65" lower="-0.7854" upper="1.48353" velocity="10.0"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="0.05" soft_lower_limit="-0.7353999999999999" soft_upper_limit="1.43353"/>
    <calibration falling="0.0"/>
    <dynamics damping="0.008"/>
    <origin rpy="0 0 0" xyz="0.09893 0 0.227"/>
    <parent link="torso_lift_link"/>
    <child link="laser_tilt_mount_link"/>
  </joint>
  <link name="laser_tilt_mount_link">
    <inertial>
      <mass value="0.591"/>
      <origin rpy="0 0 0" xyz="-0.001136 0.00167 -0.00713"/>
      <inertia ixx="0.001195273" ixy="0.000023087" ixz="0.000037467" iyy="0.001083956" iyz="0.000034906" izz="0.000795014"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo.dae"/>
      </geometry>
      <material name="Red"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/tilting_laser_v0/tilting_hokuyo_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="laser_tilt_joint" type="fixed">
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0.03"/>
    <parent link="laser_tilt_mount_link"/>
    <child link="laser_tilt_link"/>
  </joint>
  <link name="laser_tilt_link" type="laser">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <!-- Shoulder pan -->
  <joint name="r_shoulder_pan_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.0 -0.188 0.0"/>
    <!-- transform from parent link to this joint frame -->
    <parent link="torso_lift_link"/>
    <child link="r_shoulder_pan_link"/>
    <limit effort="30" lower="-2.2853981633974483" upper="0.7146018366025517" velocity="2.088"/>
    <!-- alpha tested velocity and effort limits -->
    <dynamics damping="10.0"/>
    <safety_controller k_position="100" k_velocity="10" soft_lower_limit="-2.1353981633974484" soft_upper_limit="0.5646018366025517"/>
    <calibration rising="-0.7853981633974483"/>
  </joint>
  <link name="r_shoulder_pan_link">
    <inertial>
      <mass value="25.799322"/>
      <origin rpy="0 0 0" xyz="-0.001201 0.024513 -0.098231"/>
      <inertia ixx="0.866179142480" ixy="-0.06086507933" ixz="-0.12118061183" iyy="0.87421714893" iyz="-0.05886609911" izz="0.27353821674"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.dae"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0 0 0.0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Shoulder lift -->
  <joint name="r_shoulder_lift_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <!-- Limits updated from Function's CAD values as of 2009_02_24 (link_data.xls) -->
    <limit effort="30" lower="-0.5236" upper="1.3963" velocity="2.082"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="10" soft_lower_limit="-0.3535999999999999" soft_upper_limit="1.2963"/>
    <calibration falling="0.0"/>
    <dynamics damping="10.0"/>
    <origin rpy="0 0 0" xyz="0.1 0 0"/>
    <parent link="r_shoulder_pan_link"/>
    <child link="r_shoulder_lift_link"/>
  </joint>
  <link name="r_shoulder_lift_link">
    <inertial>
      <mass value="2.74988"/>
      <origin rpy="0 0 0" xyz="0.02195 -0.02664 -0.03127"/>
      <inertia ixx="0.02105584615" ixy="0.00496704022" ixz="-0.00194808955" iyy="0.02127223737" iyz="0.00110425490" izz="0.01975753814"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_upper_arm_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_shoulder_lift_link"/>
    <child link="r_upper_arm_roll_link"/>
    <limit effort="30" lower="-3.9000000000000004" upper="0.8" velocity="3.27"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-3.7500000000000004" soft_upper_limit="0.65"/>
    <calibration rising="-1.5707963267948966"/>
    <dynamics damping="0.1"/>
  </joint>
  <link name="r_upper_arm_roll_link">
    <inertial>
      <!-- dummy mass, to be removed -->
      <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0.0 0 0"/>
      <inertia ixx="0.01" ixy="0.00" ixz="0.00" iyy="0.01" iyz="0.00" izz="0.01"/>
    </inertial>
    <visual>
      <!-- TODO: This component doesn't actually have a mesh -->
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_upper_arm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_upper_arm_roll_link"/>
    <child link="r_upper_arm_link"/>
  </joint>
  <link name="r_upper_arm_link">
    <inertial>
      <!-- NOTE:reflect==-1 for right side, reflect==1 for the left side -->
      <mass value="6.01769"/>
      <origin xyz="0.21398 -0.01621 -0.0002"/>
      <inertia ixx="0.01537748957" ixy="0.00375711247" ixz="-0.00070852914" iyy="0.0747367044" iyz="-0.0001793645" izz="0.07608763307"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Has upperarm link -->
  <!-- No transmission, since this a fixed joint w/o actuator -->
  <!-- forearm_roll_link is a fictitious link internal to elbow_flex_link, provides an attachment point for the actual forearm -->
  <joint name="r_forearm_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="30" velocity="3.5999999999999996" upper="300" lower="-300"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="1"/>
    <calibration rising="0.0"/>
    <dynamics damping="0.1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_elbow_flex_link"/>
    <child link="r_forearm_roll_link"/>
  </joint>
  <!-- TODO: inertial tag should be optional -->
  <link name="r_forearm_roll_link">
    <inertial>
      <!-- dummy masses, to be removed -->
      <mass value="0.1"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0.00" ixz="0.00" iyy="0.01" iyz="0.00" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <!-- TODO: collision tag should be optional -->
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Elbow flex -->
  <joint name="r_elbow_flex_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <!-- Note: Overtravel limits are 140, -7 degrees instead of 133, 0 -->
    <limit effort="30" lower="-2.3213" upper="0.00" velocity="3.3"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="3" soft_lower_limit="-2.1212999999999997" soft_upper_limit="-0.15"/>
    <calibration falling="-1.1606"/>
    <dynamics damping="1.0"/>
    <origin rpy="0 0 0" xyz="0.4 0 0"/>
    <parent link="r_upper_arm_link"/>
    <child link="r_elbow_flex_link"/>
  </joint>
  <link name="r_elbow_flex_link">
    <inertial>
      <mass value="1.90327"/>
      <origin xyz="0.01014 0.00032 -0.01211"/>
      <inertia ixx="0.00346541989" ixy="0.00004066825" ixz="0.00043171614" iyy="0.00441606455" iyz="-0.00003968914" izz="0.00359156824"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_forearm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <!-- transform from parent link to this joint frame -->
    <parent link="r_forearm_roll_link"/>
    <child link="r_forearm_link"/>
  </joint>
  <link name="r_forearm_link">
    <inertial>
      <mass value="2.57968"/>
      <origin xyz="0.18791 -0.00017 -0.00912"/>
      <inertia ixx="0.00364857222" ixy="0.00005215877" ixz="0.00071534842" iyy="0.01507736897" iyz="-0.00001310770" izz="0.01659310749"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Wrist flex -->
  <joint name="r_wrist_flex_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="10" lower="-2.18" upper="0.0" velocity="3.078"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="20" k_velocity="4" soft_lower_limit="-2.0" soft_upper_limit="-0.1"/>
    <dynamics damping="0.1"/>
    <calibration falling="-0.5410521"/>
    <origin rpy="0 0 0" xyz="0.321 0 0"/>
    <parent link="r_forearm_link"/>
    <child link="r_wrist_flex_link"/>
  </joint>
  <link name="r_wrist_flex_link">
    <inertial>
      <mass value="0.61402"/>
      <origin xyz="-0.00157 0.0 -0.00075"/>
      <inertia ixx="0.00065165722" ixy="0.00000028864" ixz="0.00000303477" iyy="0.00019824443" iyz="-0.00000022645" izz="0.00064450498"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Wrist roll -->
  <joint name="r_wrist_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="10" velocity="3.5999999999999996" upper="300" lower="-300"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="2"/>
    <dynamics damping="0.1"/>
    <calibration rising="-1.5707963267948966"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_wrist_flex_link"/>
    <child link="r_wrist_roll_link"/>
  </joint>
  <link name="r_wrist_roll_link">
    <inertial>
      <!-- dummy masses, to be removed.  wrist roll masses are on "gripper_palm" -->
      <mass value="0.1"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_gripper_palm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_wrist_roll_link"/>
    <child link="r_gripper_palm_link"/>
  </joint>
  <link name="r_gripper_palm_link">
    <inertial>
      <mass value="0.58007"/>
      <origin rpy="0 0 0" xyz="0.06623 0.00053 -0.00119"/>
      <inertia ixx="0.00035223921" ixy="-0.00001580476" ixz="-0.00000091750" iyy="0.00067741312" iyz="-0.00000059554" izz="0.00086563316"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.dae"/>
      </geometry>
      <material name="Red"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_gripper_led_joint" type="fixed">
    <!--  Need to check if we need a positive or negative Z term -->
    <origin xyz="0.0513 0.0 .0244"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_led_frame"/>
  </joint>
  <link name="r_gripper_led_frame"/>
  <joint name="r_gripper_motor_accelerometer_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_motor_accelerometer_link"/>
  </joint>
  <link name="r_gripper_motor_accelerometer_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <joint name="r_gripper_tool_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.18 0 0"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_tool_frame"/>
  </joint>
  <link name="r_gripper_tool_frame"/>
  <!-- actuated motor screw joint -->
  <link name="r_gripper_motor_slider_link">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <!-- for debugging only
      <visual>
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <geometry>
          <cylinder length="0.002" radius="0.025"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <geometry>
          <cylinder length="0.002" radius="0.025"/>
        </geometry>
      </collision>
      -->
  </link>
  <joint name="r_gripper_motor_slider_joint" type="prismatic">
    <origin rpy="0 0 0" xyz="0.16828 0 0"/>
    <axis xyz="1 0 0"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_motor_slider_link"/>
    <limit effort="1000.0" lower="-0.1" upper="0.1" velocity="0.2"/>
  </joint>
  <link name="r_gripper_motor_screw_link">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
    </inertial>
    <!-- for debugging only
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.05 0.001 0.05" />
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.05 0.001 0.05" />
        </geometry>
      </collision>
      -->
  </link>
  <joint name="r_gripper_motor_screw_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0 0"/>
    <axis xyz="0 1 0"/>
    <parent link="r_gripper_motor_slider_link"/>
    <child link="r_gripper_motor_screw_link"/>
    <dynamics damping="0.0001"/>
  </joint>
  <!-- Finger proximal digit -->
  <joint name="r_gripper_l_finger_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.02"/>
    <origin rpy="0 0 0" xyz="0.07691 0.01 0"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_l_finger_link"/>
  </joint>
  <link name="r_gripper_l_finger_link">
    <inertial>
      <mass value="0.17126"/>
      <origin rpy="0 0 0" xyz="0.03598 0.01730 -0.00164"/>
      <inertia ixx="0.00007756198" ixy="0.00000149095" ixz="-0.00000983385" iyy="0.00019708305" iyz="-0.00000306125" izz="0.00018105446"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger proximal digit -->
  <joint name="r_gripper_r_finger_joint" type="revolute">
    <axis xyz="0 0 -1"/>
    <origin rpy="0 0 0" xyz="0.07691 -0.01 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.02"/>
    <mimic joint="r_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="r_gripper_palm_link"/>
    <child link="r_gripper_r_finger_link"/>
  </joint>
  <link name="r_gripper_r_finger_link">
    <inertial>
      <mass value="0.17389"/>
      <origin rpy="0 0 0" xyz="0.03576 -0.01736 -0.00095"/>
      <inertia ixx="0.00007738410" ixy="-0.00000209309" ixz="-0.00000836228" iyy="0.00019847383" iyz="0.00000246110" izz="0.00018106988"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger tip -->
  <joint name="r_gripper_l_finger_tip_joint" type="revolute">
    <axis xyz="0 0 -1"/>
    <origin rpy="0 0 0" xyz="0.09137 0.00495 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.001"/>
    <mimic joint="r_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="r_gripper_l_finger_link"/>
    <child link="r_gripper_l_finger_tip_link"/>
  </joint>
  <link name="r_gripper_l_finger_tip_link">
    <inertial>
      <mass value="0.04419"/>
      <origin rpy="0 0 0" xyz="0.00423 0.00284 0.0"/>
      <inertia ixx="0.00000837047" ixy="0.00000583632" ixz="0.0" iyy="0.00000987067" iyz="0.0" izz="0.00001541768"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger tip -->
  <joint name="r_gripper_r_finger_tip_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.09137 -0.00495 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.001"/>
    <mimic joint="r_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="r_gripper_r_finger_link"/>
    <child link="r_gripper_r_finger_tip_link"/>
  </joint>
  <link name="r_gripper_r_finger_tip_link">
    <inertial>
      <mass value="0.04419"/>
      <origin rpy="0 0 0" xyz="0.00423 -0.00284 0.0"/>
      <inertia ixx="0.00000837047" ixy="-0.00000583632" ixz="0.0" iyy="0.00000987067" iyz="0.0" izz="0.00001541768"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- fictitous joint that represents the gripper gap -->
  <!-- effort is the linear force at the gripper gap
         velocity limit is the linear velocity limit at the gripper gap
         try and introduce a very stiff spring
         The velocity limits are alpha tested.
         The effort limits are somewhat inflated.
         k_velocity was recently raised from 500.0 to 5000.0.  Tested on beta
    -->
  <joint name="r_gripper_joint" type="prismatic">
    <parent link="r_gripper_r_finger_tip_link"/>
    <child link="r_gripper_l_finger_tip_frame"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="10.0"/>
    <limit effort="1000.0" lower="0.0" upper="0.09" velocity="0.2"/>
    <safety_controller k_position="20.0" k_velocity="5000.0" soft_lower_limit="-0.01" soft_upper_limit="0.088"/>
  </joint>
  <!-- This link is the same as the l_finger_tip_link,
	 but because the urdf does not support graph structures,
	 this link exists twice -->
  <link name="r_gripper_l_finger_tip_frame"/>
  <!-- Shoulder pan -->
  <joint name="l_shoulder_pan_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.0 0.188 0.0"/>
    <!-- transform from parent link to this joint frame -->
    <parent link="torso_lift_link"/>
    <child link="l_shoulder_pan_link"/>
    <limit effort="30" lower="-0.7146018366025517" upper="2.2853981633974483" velocity="2.088"/>
    <!-- alpha tested velocity and effort limits -->
    <dynamics damping="10.0"/>
    <safety_controller k_position="100" k_velocity="10" soft_lower_limit="-0.5646018366025517" soft_upper_limit="2.1353981633974484"/>
    <calibration rising="0.7853981633974483"/>
  </joint>
  <link name="l_shoulder_pan_link">
    <inertial>
      <mass value="25.799322"/>
      <origin rpy="0 0 0" xyz="-0.001201 0.024513 -0.098231"/>
      <inertia ixx="0.866179142480" ixy="-0.06086507933" ixz="-0.12118061183" iyy="0.87421714893" iyz="-0.05886609911" izz="0.27353821674"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.dae"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0 0 0.0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_pan.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Shoulder lift -->
  <joint name="l_shoulder_lift_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <!-- Limits updated from Function's CAD values as of 2009_02_24 (link_data.xls) -->
    <limit effort="30" lower="-0.5236" upper="1.3963" velocity="2.082"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="10" soft_lower_limit="-0.3535999999999999" soft_upper_limit="1.2963"/>
    <calibration falling="0.0"/>
    <dynamics damping="10.0"/>
    <origin rpy="0 0 0" xyz="0.1 0 0"/>
    <parent link="l_shoulder_pan_link"/>
    <child link="l_shoulder_lift_link"/>
  </joint>
  <link name="l_shoulder_lift_link">
    <inertial>
      <mass value="2.74988"/>
      <origin rpy="0 0 0" xyz="0.02195 -0.02664 -0.03127"/>
      <inertia ixx="0.02105584615" ixy="0.00496704022" ixz="-0.00194808955" iyy="0.02127223737" iyz="0.00110425490" izz="0.01975753814"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/shoulder_lift.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="l_upper_arm_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_shoulder_lift_link"/>
    <child link="l_upper_arm_roll_link"/>
    <limit effort="30" lower="-0.8" upper="3.9000000000000004" velocity="3.27"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-0.65" soft_upper_limit="3.7500000000000004"/>
    <calibration rising="1.5707963267948966"/>
    <dynamics damping="0.1"/>
  </joint>
  <link name="l_upper_arm_roll_link">
    <inertial>
      <!-- dummy mass, to be removed -->
      <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0.0 0 0"/>
      <inertia ixx="0.01" ixy="0.00" ixz="0.00" iyy="0.01" iyz="0.00" izz="0.01"/>
    </inertial>
    <visual>
      <!-- TODO: This component doesn't actually have a mesh -->
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/shoulder_v0/upper_arm_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="l_upper_arm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_upper_arm_roll_link"/>
    <child link="l_upper_arm_link"/>
  </joint>
  <link name="l_upper_arm_link">
    <inertial>
      <!-- NOTE:reflect==-1 for right side, reflect==1 for the left side -->
      <mass value="6.01769"/>
      <origin xyz="0.21405 0.01658 -0.00057"/>
      <inertia ixx="0.01530603856" ixy="-0.00339324862" ixz="0.00060765455" iyy="0.07473694455" iyz="-0.00019953729" izz="0.07601594191"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/upper_arm.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Has upperarm link -->
  <!-- No transmission, since this a fixed joint w/o actuator -->
  <!-- forearm_roll_link is a fictitious link internal to elbow_flex_link, provides an attachment point for the actual forearm -->
  <joint name="l_forearm_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="30" velocity="3.5999999999999996" upper="300" lower="-300"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="1"/>
    <calibration rising="0.0"/>
    <dynamics damping="0.1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_elbow_flex_link"/>
    <child link="l_forearm_roll_link"/>
  </joint>
  <!-- TODO: inertial tag should be optional -->
  <link name="l_forearm_roll_link">
    <inertial>
      <!-- dummy masses, to be removed -->
      <mass value="0.1"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0.00" ixz="0.00" iyy="0.01" iyz="0.00" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <!-- TODO: collision tag should be optional -->
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/forearm_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Elbow flex -->
  <joint name="l_elbow_flex_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <!-- Note: Overtravel limits are 140, -7 degrees instead of 133, 0 -->
    <limit effort="30" lower="-2.3213" upper="0.00" velocity="3.3"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="100" k_velocity="3" soft_lower_limit="-2.1212999999999997" soft_upper_limit="-0.15"/>
    <calibration falling="-1.1606"/>
    <dynamics damping="1.0"/>
    <origin rpy="0 0 0" xyz="0.4 0 0"/>
    <parent link="l_upper_arm_link"/>
    <child link="l_elbow_flex_link"/>
  </joint>
  <link name="l_elbow_flex_link">
    <inertial>
      <mass value="1.90327"/>
      <origin xyz="0.01014 0.00032 -0.01211"/>
      <inertia ixx="0.00346541989" ixy="0.00004066825" ixz="0.00043171614" iyy="0.00441606455" iyz="-0.00003968914" izz="0.00359156824"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/upper_arm_v0/elbow_flex.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Elbow flex -->
  <joint name="l_forearm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <!-- transform from parent link to this joint frame -->
    <parent link="l_forearm_roll_link"/>
    <child link="l_forearm_link"/>
  </joint>
  <link name="l_forearm_link">
    <inertial>
      <mass value="2.57968"/>
      <origin xyz="0.18791 -0.00017 -0.00912"/>
      <inertia ixx="0.00364857222" ixy="0.00005215877" ixz="0.00071534842" iyy="0.01507736897" iyz="-0.00001310770" izz="0.01659310749"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/forearm.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Wrist flex -->
  <joint name="l_wrist_flex_joint" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="10" lower="-2.18" upper="0.0" velocity="3.078"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_position="20" k_velocity="4" soft_lower_limit="-2.0" soft_upper_limit="-0.1"/>
    <dynamics damping="0.1"/>
    <calibration falling="-0.5410521"/>
    <origin rpy="0 0 0" xyz="0.321 0 0"/>
    <parent link="l_forearm_link"/>
    <child link="l_wrist_flex_link"/>
  </joint>
  <link name="l_wrist_flex_link">
    <inertial>
      <mass value="0.61402"/>
      <origin xyz="-0.00157 0.0 -0.00075"/>
      <inertia ixx="0.00065165722" ixy="0.00000028864" ixz="0.00000303477" iyy="0.00019824443" iyz="-0.00000022645" izz="0.00064450498"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_flex.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Wrist roll -->
  <joint name="l_wrist_roll_joint" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="10" velocity="3.5999999999999996" upper="300" lower="-300"/>
    <!-- alpha tested velocity and effort limits -->
    <safety_controller k_velocity="2"/>
    <dynamics damping="0.1"/>
    <calibration rising="-1.5707963267948966"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_wrist_flex_link"/>
    <child link="l_wrist_roll_link"/>
  </joint>
  <link name="l_wrist_roll_link">
    <inertial>
      <!-- dummy masses, to be removed.  wrist roll masses are on "gripper_palm" -->
      <mass value="0.1"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll.stl"/>
      </geometry>
      <material name="RollLinks"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/forearm_v0/wrist_roll_L.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Wrist flex , Wrist roll -->
  <joint name="l_gripper_palm_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_wrist_roll_link"/>
    <child link="l_gripper_palm_link"/>
  </joint>
  <link name="l_gripper_palm_link">
    <inertial>
      <mass value="0.58007"/>
      <origin rpy="0 0 0" xyz="0.06623 0.00053 -0.00119"/>
      <inertia ixx="0.00035223921" ixy="-0.00001580476" ixz="-0.00000091750" iyy="0.00067741312" iyz="-0.00000059554" izz="0.00086563316"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.dae"/>
      </geometry>
      <material name="Red"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/gripper_palm.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="l_gripper_led_joint" type="fixed">
    <!--  Need to check if we need a positive or negative Z term -->
    <origin xyz="0.0513 0.0 .0244"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_led_frame"/>
  </joint>
  <link name="l_gripper_led_frame"/>
  <joint name="l_gripper_motor_accelerometer_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_motor_accelerometer_link"/>
  </joint>
  <link name="l_gripper_motor_accelerometer_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <joint name="l_gripper_tool_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.18 0 0"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_tool_frame"/>
  </joint>
  <link name="l_gripper_tool_frame"/>
  <!-- actuated motor screw joint -->
  <link name="l_gripper_motor_slider_link">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
    <!-- for debugging only
      <visual>
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <geometry>
          <cylinder length="0.002" radius="0.025"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <geometry>
          <cylinder length="0.002" radius="0.025"/>
        </geometry>
      </collision>
      -->
  </link>
  <joint name="l_gripper_motor_slider_joint" type="prismatic">
    <origin rpy="0 0 0" xyz="0.16828 0 0"/>
    <axis xyz="1 0 0"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_motor_slider_link"/>
    <limit effort="1000.0" lower="-0.1" upper="0.1" velocity="0.2"/>
  </joint>
  <link name="l_gripper_motor_screw_link">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
    </inertial>
    <!-- for debugging only
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.05 0.001 0.05" />
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.05 0.001 0.05" />
        </geometry>
      </collision>
      -->
  </link>
  <joint name="l_gripper_motor_screw_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.0 0 0"/>
    <axis xyz="0 1 0"/>
    <parent link="l_gripper_motor_slider_link"/>
    <child link="l_gripper_motor_screw_link"/>
    <dynamics damping="0.0001"/>
  </joint>
  <!-- Finger proximal digit -->
  <joint name="l_gripper_l_finger_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.02"/>
    <origin rpy="0 0 0" xyz="0.07691 0.01 0"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_l_finger_link"/>
  </joint>
  <link name="l_gripper_l_finger_link">
    <inertial>
      <mass value="0.17126"/>
      <origin rpy="0 0 0" xyz="0.03598 0.01730 -0.00164"/>
      <inertia ixx="0.00007756198" ixy="0.00000149095" ixz="-0.00000983385" iyy="0.00019708305" iyz="-0.00000306125" izz="0.00018105446"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger proximal digit -->
  <joint name="l_gripper_r_finger_joint" type="revolute">
    <axis xyz="0 0 -1"/>
    <origin rpy="0 0 0" xyz="0.07691 -0.01 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.02"/>
    <mimic joint="l_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="l_gripper_palm_link"/>
    <child link="l_gripper_r_finger_link"/>
  </joint>
  <link name="l_gripper_r_finger_link">
    <inertial>
      <mass value="0.17389"/>
      <origin rpy="0 0 0" xyz="0.03576 -0.01736 -0.00095"/>
      <inertia ixx="0.00007738410" ixy="-0.00000209309" ixz="-0.00000836228" iyy="0.00019847383" iyz="0.00000246110" izz="0.00018106988"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.dae"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger tip -->
  <joint name="l_gripper_l_finger_tip_joint" type="revolute">
    <axis xyz="0 0 -1"/>
    <origin rpy="0 0 0" xyz="0.09137 0.00495 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.001"/>
    <mimic joint="l_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="l_gripper_l_finger_link"/>
    <child link="l_gripper_l_finger_tip_link"/>
  </joint>
  <link name="l_gripper_l_finger_tip_link">
    <inertial>
      <mass value="0.04419"/>
      <origin rpy="0 0 0" xyz="0.00423 0.00284 0.0"/>
      <inertia ixx="0.00000837047" ixy="0.00000583632" ixz="0.0" iyy="0.00000987067" iyz="0.0" izz="0.00001541768"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- Finger tip -->
  <joint name="l_gripper_r_finger_tip_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.09137 -0.00495 0"/>
    <!-- limits on passive finger and finger top joints without
         transmission are not enforced by safety controllers.
         The lower/upper limits and are enforced in PR2 simulation and
         effort and velocity limits are ignored. This is also needed because
         these joints are declared revolute rather than revolute.-->
    <limit effort="1000.0" lower="0.0" upper="0.548" velocity="0.5"/>
    <dynamics damping="0.001"/>
    <mimic joint="l_gripper_l_finger_joint" multiplier="1" offset="0"/>
    <parent link="l_gripper_r_finger_link"/>
    <child link="l_gripper_r_finger_tip_link"/>
  </joint>
  <link name="l_gripper_r_finger_tip_link">
    <inertial>
      <mass value="0.04419"/>
      <origin rpy="0 0 0" xyz="0.00423 -0.00284 0.0"/>
      <inertia ixx="0.00000837047" ixy="-0.00000583632" ixz="0.0" iyy="0.00000987067" iyz="0.0" izz="0.00001541768"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.dae"/>
      </geometry>
      <material name="Green"/>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/pr2/meshes/gripper_v0/l_finger_tip.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- fictitous joint that represents the gripper gap -->
  <!-- effort is the linear force at the gripper gap
         velocity limit is the linear velocity limit at the gripper gap
         try and introduce a very stiff spring
         The velocity limits are alpha tested.
         The effort limits are somewhat inflated.
         k_velocity was recently raised from 500.0 to 5000.0.  Tested on beta
    -->
  <joint name="l_gripper_joint" type="prismatic">
    <parent link="l_gripper_r_finger_tip_link"/>
    <child link="l_gripper_l_finger_tip_frame"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="10.0"/>
    <limit effort="1000.0" lower="0.0" upper="0.09" velocity="0.2"/>
    <safety_controller k_position="20.0" k_velocity="5000.0" soft_lower_limit="-0.01" soft_upper_limit="0.088"/>
  </joint>
  <!-- This link is the same as the l_finger_tip_link,
	 but because the urdf does not support graph structures,
	 this link exists twice -->
  <link name="l_gripper_l_finger_tip_frame"/>
  <joint name="l_forearm_cam_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 -0.5628686837681712 0" xyz=".135 0 .044"/>
    <parent link="l_forearm_roll_link"/>
    <child link="l_forearm_cam_frame"/>
  </joint>
  <link name="l_forearm_cam_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="l_forearm_cam_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="l_forearm_cam_frame"/>
    <child link="l_forearm_cam_optical_frame"/>
  </joint>
  <link name="l_forearm_cam_optical_frame"/>
  <joint name="r_forearm_cam_frame_joint" type="fixed">
    <origin rpy="1.5707963267948966 -0.5628686837681712 0" xyz=".135 0 .044"/>
    <parent link="r_forearm_roll_link"/>
    <child link="r_forearm_cam_frame"/>
  </joint>
  <link name="r_forearm_cam_frame">
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="r_forearm_cam_optical_frame_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0.0 -1.5707963267948966" xyz="0 0 0"/>
    <parent link="r_forearm_cam_frame"/>
    <child link="r_forearm_cam_optical_frame"/>
  </joint>
  <link name="r_forearm_cam_optical_frame"/>
</robot>
