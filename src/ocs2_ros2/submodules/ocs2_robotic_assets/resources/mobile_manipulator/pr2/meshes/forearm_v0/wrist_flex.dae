<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>WakiMudi</author>
      <authoring_tool>OpenCOLLADA2009 x64</authoring_tool>
      <comments>
			ColladaMaya export options: 
			bakeTransforms=0;relativePaths=0;copyTextures=0;exportTriangles=0;exportCgfxFileReferences=1;
			isSampling=0;curveConstrainSampling=0;removeStaticCurves=1;exportPolygonMeshes=1;exportLights=1;
			exportCameras=1;exportJointsAndSkin=1;exportAnimations=1;exportInvisibleNodes=0;exportDefaultCameras=0;
			exportTexCoords=1;exportNormals=1;exportNormalsPerVertex=1;exportVertexColors=1;exportVertexColorsPerVertex=1;
			exportTexTangents=0;exportTangents=0;exportReferencedMaterials=0;exportMaterialsOnly=0;
			exportXRefs=1;dereferenceXRefs=1;exportCameraAsLookat=0;cameraXFov=0;cameraYFov=1;doublePrecision=0
		</comments>
      <source_data>file:///C:/Users/<USER>/Documents/maya/projects/willow_textures/scenes/wrist_flex1b_med.mb</source_data>
    </contributor>
    <created>2010-04-30T15:40:15</created>
    <modified>2010-04-30T15:40:15</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Y_UP</up_axis>
  </asset>
  <library_materials>
    <material id="lambert2" name="lambert2">
      <instance_effect url="#lambert2-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="lambert2-fx">
      <profile_COMMON>
        <newparam sid="file2-surface">
          <surface type="2D">
            <init_from>file2</init_from>
          </surface>
        </newparam>
        <newparam sid="file2-sampler">
          <sampler2D>
            <source>file2-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="file1-surface">
          <surface type="2D">
            <init_from>file1</init_from>
          </surface>
        </newparam>
        <newparam sid="file1-sampler">
          <sampler2D>
            <source>file1-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="file2-sampler" texcoord="TEX0">
                <extra>
                  <technique profile="OpenCOLLADAMaya">
                    <blend_mode>NONE</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <transparent opaque="RGB_ZERO">
              <color>0 0 0 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </lambert>
          <extra>
            <technique profile="OpenCOLLADAMaya">
              <bump>
                <texture texture="file1-sampler" texcoord="TEX1">
                  <extra>
                    <technique profile="OpenCOLLADA3dsMax">
                      <amount>1</amount>
                      <bumpInterp>1</bumpInterp>
                    </technique>
                    <technique profile="OpenCOLLADAMaya">
                      <blend_mode>NONE</blend_mode>
                    </technique>
                  </extra>
                </texture>
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="file2" name="file2">
      <init_from>wrist_color.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file2</originalMayaNodeId>
        </technique>
      </extra>
    </image>
    <image id="file1" name="file1">
      <init_from>wrist_normals.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file1</originalMayaNodeId>
        </technique>
      </extra>
    </image>
  </library_images>
  <library_geometries>
    <geometry id="wrist_flex_MShape" name="wrist_flex_MShape">
      <mesh>
        <source id="wrist_flex_MShape-positions" name="wrist_flex_MShape-positions">
          <float_array id="wrist_flex_MShape-positions-array" count="1503">-0.3702255 0.1925103 0 -0.3700209 0.1873432 -0.002070012 -0.3700001 0.1923976 -0.006456409 -0.3697768 -0.1469366 -0.01285007 -0.3697768 -0.09022539 -0.01285007 -0.3697768 -0.09 -0.01285007 -0.3697768 -0.05100238 -0.01285007 -0.3697768 0 -0.01285007 -0.3697768 0.05100238 -0.01285007 -0.3697768 0.09 -0.01285007 -0.3697768 0.09022539 -0.01285007 -0.3697768 0.1382853 -0.01285007 -0.3697746 -0.192285 0.01291283 -0.3697746 -0.1922849 -0.01291281 -0.3697746 0.1922849 -0.01291281 -0.3697746 0.192285 0.01291283 -0.3697746 -0.1925103 -0.01291281 -0.3697703 -0.003407641 0.02174653 -0.3691118 0.1469789 0.02577388 -0.3690049 0.1920416 -0.02759305 -0.3681524 -0.01076812 -0.03813692 -0.3680947 -0.1918716 0.0375 -0.3680947 0.1918716 0.0375 -0.3678315 -0.1918512 0.04 -0.3678315 0.1918515 0.04000001 -0.3677628 -0.2027425 0 -0.3677627 0.2027427 0 -0.3675766 -0.1951699 0.04000041 -0.3675766 0.1951699 0.04000041 -0.3673333 -0.1540254 0.04447736 -0.3672425 -0.1913852 -0.04836736 -0.36716 -0.2036821 -0.01611452 -0.3671431 0.2032132 -0.01965453 -0.3664338 -0.2003413 0.03870241 -0.3660311 0.2013064 0.03909201 -0.3656329 -0.1992288 -0.05253543 -0.3653613 0.1997133 -0.05327102 -0.3647272 0.1914735 -0.06230771 -0.3642376 -0.2077892 -2.63269e-6 -0.3636846 -0.2082945 0.0167301 -0.3635674 -0.1913621 0.06984976 -0.3629616 0.2070279 0.03811614 -0.3626761 -0.2070878 0.03914431 -0.3624062 0.2095146 0.01442627 -0.3618848 0.1900801 0.07884005 -0.3615235 0.2075956 0.0375 -0.3610253 0.2010229 0.07252439 -0.3592082 -0.2053101 0.06835431 -0.3585149 0.2117702 -0.0128597 -0.3576172 0.2099765 -0.05132858 -0.3576081 -0.2055298 -0.07498891 -0.356782 -0.2118857 0.03818383 -0.3564307 -0.1909233 -0.09972262 -0.356193 0.2135378 0.003279271 -0.3560078 -0.2131499 -0.01353696 -0.355456 -0.2124147 -0.04235696 -0.3553476 -0.1908035 0.1035193 -0.3544728 0.1898052 -0.1063742 -0.3527555 -0.2123832 0.05676534 -0.3516091 0.1969918 0.1134333 -0.3514981 0.2125334 0.05808702 -0.3514104 0.2136882 0.03753995 -0.3513845 0.2070323 -0.09425599 -0.3512759 0.1905944 0.1170197 -0.3508236 -0.1963711 -0.1164269 -0.3506968 -0.1963874 0.1164907 -0.3495078 0.2136118 -0.04918234 -0.3493813 0.1989901 -0.1185739 -0.3477255 -0.215 0 -0.3477255 0.215 0 -0.3472883 -0.2147815 0.01252025 -0.3472883 0.2147815 0.01252025 -0.3472873 0.2147815 -0.01252022 -0.3472871 -0.2147815 -0.01252021 -0.3471595 -0.2050827 0.1134838 -0.3468618 0.2146257 0.02144146 -0.3461738 0.2061663 0.1144476 -0.345834 -0.1903413 0.1318291 -0.3455131 -0.2143454 0.0375 -0.3455131 0.2143454 0.0375 -0.3452343 -0.2143018 0.03999986 -0.3452342 0.2143018 0.03999986 -0.3438833 -0.1902509 -0.1367337 -0.3430297 -0.2113557 -0.1023458 -0.3430224 -0.2045509 -0.1264542 -0.3422088 0.2122048 -0.0965064 -0.3384218 0.1900309 -0.1498588 -0.3381485 -0.198644 0.1472362 -0.3372633 0.1985604 0.1493019 -0.3369388 0.2119928 0.1142003 -0.3347911 0.1899149 0.1577599 -0.3342953 -0.2122375 0.117035 -0.3340519 0.2083338 -0.1379841 -0.3315877 -0.2088441 0.142112 -0.3307501 -0.2141215 0.05032944 -0.3301933 -0.1955323 -0.1665623 -0.330191 0.1955377 -0.1665616 -0.3296307 -0.1864731 -0.1668435 -0.3282263 -0.1896968 0.1714961 -0.3273755 -0.1898876 -0.1731322 -0.3260204 -0.2116112 -0.1411739 -0.3247875 0.204229 -0.1669388 -0.3247558 -0.2042872 -0.1669255 -0.3240757 0.213058 -0.1228186 -0.3222091 0.2116168 -0.1475531 -0.3215695 -0.1990019 0.1797451 -0.3201847 0.2041066 0.1756999 -0.3169998 0.2142764 0.04145848 -0.3149484 0.2127687 0.1428646 -0.3149437 0.210783 0.1662768 -0.3136699 0.1846078 -0.1951834 -0.3129986 -0.1847047 -0.195919 -0.3127306 0.1886124 -0.1989855 -0.3112622 -0.2122578 -0.160923 -0.3107828 0.1970553 0.1994308 -0.3106334 0.1910063 0.2016558 -0.3078812 -0.2078895 0.1879866 -0.3078629 -0.1982032 0.2026862 -0.3054624 -0.1974817 -0.2067373 -0.3048613 0.1982105 -0.2069664 -0.3041311 -0.2122559 0.1662963 -0.3027217 -0.1890173 0.2131945 -0.3009588 0.2071266 -0.2009814 -0.2999234 -0.2110623 0.1869826 -0.2980338 -0.2105652 -0.1939612 -0.290705 -0.1844137 -0.2278004 -0.2902027 -0.2062393 -0.2173912 -0.290004 0.2036589 0.2215481 -0.2898422 -0.2056867 0.2187637 -0.2896761 -0.1884704 -0.2308458 -0.2886328 0.1842227 -0.2303522 -0.2875256 0.2096801 0.2126388 -0.2849482 -0.215 0 -0.28245 0.1939837 -0.2384311 -0.2820349 -0.1937911 -0.238984 -0.2819238 0.1877275 0.2401429 -0.2802779 0.188457 -0.2417839 -0.2802364 -0.198115 0.2394366 -0.2766279 0.1958052 0.2443946 -0.2748313 0.2025374 -0.2412518 -0.2721242 -0.202427 -0.2441064 -0.26951 -0.1883556 0.2539084 -0.2685513 0.2100384 -0.2330339 -0.263624 0.2113329 -0.2256896 -0.2635938 -0.1835185 -0.2585085 -0.2549223 0.2062645 0.2566821 -0.2521736 -0.2100638 0.2472869 -0.2513527 -0.1882359 -0.2721273 -0.2512544 -0.2070207 0.258602 -0.2502954 0.1934598 -0.2720363 -0.2483566 -0.1965336 0.2729695 -0.2481289 0.1972858 0.2726505 -0.2479342 0.2029927 -0.2677701 -0.247742 -0.2099907 -0.2527952 -0.2460378 -0.1963038 -0.2752521 -0.2438522 0.1879425 0.2784503 -0.2430502 0.1880456 -0.2794353 -0.2365412 -0.2063403 -0.2730195 -0.2330648 -0.1842497 -0.2866615 -0.2311488 0.2100673 0.2661407 -0.2296252 -0.2108449 0.2577822 -0.2274435 -0.1877238 0.2922014 -0.2268413 0.1827111 -0.2917072 -0.2250217 0.2105103 0.2646569 -0.223595 0.2096054 -0.2744977 -0.217775 0.2055832 -0.2886752 -0.2149595 -0.2031821 0.294127 -0.2138196 0.1875659 0.3022428 -0.2125814 0.2048985 0.2939638 -0.2108415 0.1958582 -0.30273 -0.2071572 -0.2020792 -0.300797 -0.2066388 0.1878203 -0.306924 -0.2051814 -0.2086387 -0.2911746 -0.2046293 0.1955302 0.3074835 -0.203897 -0.2086917 0.2916879 -0.1991639 -0.2099237 -0.2869782 -0.1985185 -0.1874063 -0.3129474 -0.1964677 -0.1960551 0.3122178 -0.1938081 -0.1873382 0.3154051 -0.1880245 -0.1929386 -0.3184897 -0.1822755 -0.183647 -0.3217325 -0.1819135 0.1876599 -0.3223871 -0.1813915 0.187217 0.3226537 -0.1758295 0.1956805 -0.324171 -0.1752426 0.2059467 -0.3154319 -0.1717662 -0.2082396 0.3122661 -0.1707376 -0.2013205 -0.3233266 -0.1677044 -0.2015259 0.3244954 -0.1676555 0.2015307 0.3245174 -0.1635401 0.2085982 0.315491 -0.1625462 0.2090068 -0.313692 -0.1598649 -0.2078762 -0.3194521 -0.1542558 -0.2096414 0.3128924 -0.1540256 -0.1869929 0.3365961 -0.1511641 0.1869625 0.3377972 -0.1472275 0.004908981 -0.3394071 -0.1432757 0.2057121 0.3311954 -0.1399539 0.1941783 0.3417648 -0.1398427 -0.1941711 0.3418144 -0.1370481 -0.192726 -0.3431997 -0.1370438 0.1927667 -0.3431857 -0.134091 0.2011519 -0.3401587 -0.1322873 0.186491 -0.3458937 -0.1302599 -0.1865423 -0.3465109 -0.1291214 -0.201414 -0.3416308 -0.1286152 -0.202607 0.3404171 -0.1156124 0.1867477 0.351686 -0.1146266 -0.207922 0.337359 -0.1117448 -0.1867482 0.3530256 -0.1081669 0.2095975 0.3274784 -0.1043899 0.2010588 0.3501066 -0.1037371 0.2092146 -0.331741 -0.1015347 -0.1954472 0.3544363 -0.09842638 0.215 0 -0.0976236 0.2082055 -0.3410866 -0.09756299 -0.1964201 -0.3552967 -0.0944865 0.186629 -0.3577601 -0.09406924 0.1944592 -0.3567082 -0.09213556 -0.1866209 -0.3584466 -0.09161074 -0.2076232 -0.3446624 -0.09088573 0.05033117 -0.3588162 -0.08976492 0.2049936 -0.3501036 -0.08274565 0.118375 0.3607304 -0.08128654 0.1950217 0.3599258 -0.07856728 -0.1174141 0.36162 -0.07736104 -0.1865671 0.3618481 -0.07365659 0.2081312 0.3482978 -0.07059937 -2.53512e-4 -0.3632034 -0.07059911 0.05048281 -0.3632021 -0.07059911 -0.1865424 -0.3632021 -0.07059911 0.1865424 -0.3632021 -0.07029366 -0.1952886 -0.3617442 -0.07003996 0.1982001 -0.3604561 -0.06905807 -0.206068 -0.3530806 -0.06879913 -0.1955465 0.3622488 -0.06742604 -0.2046439 0.3556939 -0.06630636 -0.2090389 -0.3415093 -0.06630635 0.2090389 -0.3415093 -0.06290996 0.2072217 -0.3512886 -0.05993868 0.1865257 0.3652727 -0.0580623 0.2049257 0.3565167 -0.05547872 -0.215 0 -0.0378498 -0.1874164 0.3685617 -0.03700967 -0.2089621 0.3459609 -0.03697252 -0.2073522 -0.3560471 -0.03658878 0.1915472 0.3679904 -0.03595465 -0.207747 0.3541697 -0.03464968 0.2075887 -0.3556795 -0.03180196 0.1995702 0.3648267 -0.02888556 -0.1951758 0.3676273 -0.02770565 -0.186396 -0.3715252 -0.02430235 0.1948477 -0.370863 -0.02135762 -0.1998654 -0.3684697 -0.02045245 0.186447 0.3694861 -0.004620749 -0.1601595 0.3700017 -0.004018139 -0.1740933 0.369985 -1.76703e-4 0.1864323 0.37 -1.76703e-4 -0.1864322 0.37 -2.01898e-5 0.2089275 0.3478938 -1.98651e-5 -0.2089275 0.3478941 7.48661e-4 -0.1864322 0.3699993 7.48661e-4 0.1864322 0.3699992 0.001861027 0.1068421 0.3700117 0.003781133 -0.1996804 0.3663711 0.004863047 0.1959609 0.3682782 0.005500569 0.1728214 0.3699729 0.005681344 0.2084142 -0.3600651 0.01042735 -0.05136451 0.3699003 0.01083478 0.2069219 0.3581267 0.01143309 0.2038795 -0.3707652 0.01388326 -0.2080487 -0.3627477 0.01905475 -0.2076847 0.3554987 0.02762255 0.1945747 -0.3809547 0.0295718 -0.1912935 0.3685576 0.03577712 -0.2040919 -0.3751662 0.03718943 0.188183 0.368417 0.04058135 0.1935566 0.3672594 0.04232905 -0.1999281 0.3635351 0.04893441 -0.1913627 -0.3859234 0.04900004 -0.1881198 0.3671539 0.05917051 -0.1861021 -0.3884268 0.05917051 0.1861021 -0.3884268 0.05961975 0.2011074 0.3602059 0.06626091 0.195074 -0.3881507 0.06703675 -0.1973576 -0.3870012 0.06978237 -0.1950616 0.3620573 0.07711252 0.1865761 0.3620837 0.08199726 -0.2050062 0.3516138 0.08504176 0.2070374 0.3482667 0.08741303 -0.2038074 -0.3853966 0.08866344 0.2064646 -0.3817542 0.09018183 -0.1943367 -0.3931337 0.09117713 0.196175 0.3568664 0.09311664 -0.2076356 -0.3786469 0.09973842 -0.1866703 0.3564675 0.1037683 -0.2081045 0.3407778 0.1132893 -0.1964982 -0.396933 0.1175541 -0.1955242 0.3499044 0.1199998 -0.136688 -0.400252 0.12 -0.1858957 -0.4002509 0.12 0.1858957 -0.4002509 0.1205227 0.197489 -0.3977888 0.1226474 -0.2067062 -0.386995 0.1226644 0.2067788 -0.38692 0.1242129 0.1867974 0.3488087 0.1242932 -0.2083923 -0.3785569 0.1242932 0.2083923 -0.3785569 0.1242973 0.2057522 0.3382307 0.1313508 -0.2097303 0.317625 0.1326839 0.1931516 0.3447667 0.134331 0.2098212 0.3174332 0.1369326 -0.2009465 0.339296 0.1408998 0.1856726 -0.4035957 0.1411626 0.2009999 0.3373522 0.1424855 -0.1939031 -0.4026327 0.1432863 0.03937378 -0.4038334 0.1486251 -0.1873221 0.3393066 0.1569654 0.2088912 0.317068 0.1576924 -0.1858303 -0.4043724 0.1577601 -0.2043467 -0.3955951 0.1579374 0.1858291 -0.4043751 0.158799 0.1942013 -0.4037987 0.1592391 0.2041398 -0.3960281 0.1595952 -0.2083274 -0.3822731 0.1595952 0.2083274 -0.3822731 0.1607325 0.1858301 -0.4043794 0.1607665 -0.18583 -0.4043792 0.1652564 -0.01837213 -0.404402 0.1665164 0.02769212 0.3306482 0.1707446 -0.1409503 -0.4040606 0.1713609 -0.2056794 0.3175962 0.1726375 -0.1939985 -0.4029499 0.1736038 0.1453394 -0.403792 0.1769811 0.1851008 0.3258241 0.1817335 -0.1858551 -0.4027736 0.1832442 0.1171233 -0.4025637 0.1847032 0.1874235 -0.4022473 0.1849461 0.2053341 0.3106181 0.1873858 -0.1950244 0.3183341 0.1874502 -0.1872762 0.319178 0.18843 0.1955294 0.3176536 0.193129 -0.2058784 -0.3892145 0.194515 0.1948447 -0.3985475 0.1946129 -0.06913187 -0.4000354 0.1957408 0.2059281 -0.3882571 0.1993357 0.143524 -0.3986305 0.2006129 -0.187445 -0.3982362 0.2009399 -0.208695 -0.3727186 0.2057726 -0.208963 0.2900807 0.207438 0.2087693 0.2896775 0.208124 -0.03153513 -0.3952403 0.2091873 -0.1998078 -0.3902111 0.2178282 -0.2054854 0.2892416 0.2190594 0.1891202 -0.3899849 0.2198773 -0.1876322 0.2979588 0.2209202 0.0531832 -0.3885895 0.2213666 -0.1858746 -0.388279 0.221541 0.2041042 0.287773 0.2230618 0.1859271 0.2957763 0.2260384 0.2002088 -0.379688 0.2267817 0.2078988 -0.3647047 0.2275837 0.1917135 -0.3829813 0.2285482 -0.1941429 -0.381784 0.2331322 -0.2074679 -0.3601672 0.2335713 -0.1959408 0.2856521 0.2366214 0.1969589 0.2826975 0.2388317 0.2105871 0.2528124 0.2388326 -0.2105872 0.2528113 0.2390235 -0.1868608 -0.3727963 0.2403649 -0.201591 -0.3633889 0.2432928 -0.0265986 -0.3680122 0.2436687 0.2093865 -0.3229117 0.2439259 0.1874744 -0.3673675 0.2469649 0.208242 0.2602105 0.2474679 0.1961215 -0.3594493 0.2475426 -0.207138 0.2617785 0.2491552 -0.1924424 -0.3583795 0.2498857 0.2065382 -0.3356039 0.2508097 0.2101328 -0.2795878 0.2524041 -0.2101049 -0.2838437 0.2530085 -0.197099 0.2675799 0.2530175 0.1971169 0.267573 0.2531961 -0.2106927 -0.2467662 0.2531961 0.2106927 -0.2467662 0.2534068 0.2135496 0.08766675 0.2534267 -0.2085976 -0.3119252 0.2536768 0.2111734 0.219226 0.253683 -0.2111749 0.219138 0.254295 -0.1880906 0.2687639 0.254295 0.1880906 0.2687639 0.2543562 -0.05285565 0.2688285 0.2545402 -0.2118159 -0.1825529 0.2546896 0.2120075 0.1706507 0.2553303 -0.2128316 0.1246505 0.2558343 0.02281929 -0.347965 0.2560821 -0.2130192 -0.1115391 0.2562732 -0.214101 -0.05161656 0.2564487 -0.1863119 -0.3474682 0.2566782 0.2093079 -0.286826 0.2572596 0.2138175 -0.06723618 0.2575034 -0.215 0 0.2575034 0.215 0 0.2582636 0.2085503 0.2462129 0.259142 -0.2005527 -0.3298001 0.259523 0.2107579 -0.2054951 0.2603102 0.2124356 0.1184337 0.2604846 -0.2117837 0.14915 0.2610176 -0.205933 0.2473104 0.2610313 0.2118063 -0.1407178 0.2613515 0.1869725 -0.3355717 0.2616632 -0.2092054 -0.2499842 0.261668 -0.2129796 0.08130819 0.2618327 -0.2098607 0.2194543 0.2618974 -0.2095579 0.2329696 0.2619276 0.2090962 -0.2477923 0.2623739 0.2104024 0.194379 0.2626656 0.1947897 -0.3291678 0.2626692 -0.2104353 -0.180909 0.2627702 -0.2118123 -0.1198291 0.263454 0.2107354 0.1635914 0.2636301 -0.2122464 -0.08994154 0.2646976 -0.1920679 -0.3249131 0.2649952 0.2122447 0.07842782 0.264997 0.2033707 -0.3006347 0.2650917 0.07095778 0.2565459 0.2651006 -0.2033622 -0.3011026 0.2652945 0.2128313 0.04624716 0.2654879 0.0359363 -0.323312 0.2655611 -0.2131992 0.03087977 0.2658596 0.2128399 -0.03321136 0.2661661 -0.2134721 -0.004403017 0.266251 -0.2084773 0.186116 0.2672274 0.2097061 -0.1170068 0.2678387 0.206939 -0.2043984 0.2683635 0.2128778 -1.88105e-4 0.2684052 -0.1879197 0.2509287 0.2684462 -0.1871493 -0.3119473 0.2691689 -0.2057793 -0.2057206 0.2695936 0.1870212 0.2486906 0.2705389 0.2028587 -0.2481203 0.2706854 0.2027469 0.228819 0.2708107 -0.2099053 -0.03298769 0.2708425 -0.20242 -0.2469221 0.2709979 0.2059564 0.1418601 0.2710765 0.1873501 -0.2993206 0.2712445 -0.2085558 0.06508113 0.271472 -0.2057672 -0.1379605 0.2718762 -0.2024817 0.213799 0.2718806 -0.2072376 -0.08044802 0.2719845 -0.1974839 0.2389373 0.2721495 0.2028012 0.2190648 0.2728355 0.1962218 0.2376451 0.2728516 -0.2046807 0.1264377 0.2729567 -0.1956574 -0.2786198 0.2729806 -0.2028642 0.1689073 0.2730831 0.2021412 0.1819666 0.2731584 0.1953611 -0.2774206 0.2733608 0.2055819 -0.08607492 0.2734131 -0.00710512 0.2374217 0.2734131 0.1675248 0.2374217 0.2736612 -0.2009131 -0.180662 0.2737723 -0.1872756 -0.2820574 0.2741035 0.2047536 0.0829606 0.274376 0.2070318 0.02405372 0.2744737 0.1877775 -0.2724346 0.2745564 0.1885151 0.2329268 0.2745567 0.07625653 0.2329294 0.2747782 -0.2059433 0.03635483 0.2752625 -0.1946488 0.2192262 0.275358 0.1979167 -0.184395 0.2756478 -0.2060938 -0.002476956 0.2756927 -0.1881961 -0.2467662 0.2756927 0.1881961 -0.2467662 0.2756959 0.1885979 0.2260775 0.2761749 -0.1886784 0.2191376 0.2761749 0.1886784 0.2191376 0.276184 -0.195334 -0.1746613 0.276227 -0.09433918 0.2191402 0.2762543 0.1979212 0.1451007 0.276372 0.1237792 0.2191528 0.2766864 0.1903809 0.1866215 0.2767427 -0.1955843 0.1497901 0.2770631 -0.1981355 -0.1084356 0.2770886 0.1906505 -0.1647234 0.277108 0.1960963 -0.1278513 0.277247 -0.1984453 0.09341364 0.2773334 -0.1994303 -0.07529848 0.2774064 -0.1898814 0.148612 0.2778293 0.1911105 -0.1216992 0.2778658 -0.1897982 -0.1225873 0.2779811 -0.198551 0.06022317 0.2780938 -0.1998232 -0.03543688 0.2782585 0.1967211 0.06571445 0.2782806 0.2015149 -0.007037647 0.2784575 0.1976207 -0.04869732 0.2785507 -0.1994507 0.020068 0.2788758 0.1913837 0.06370441 0.2790418 -0.1915471 0.05478436 0.28 -0.1925034 0 0.28 -0.1235732 0 0.28 0.1925034 0</float_array>
          <technique_common>
            <accessor source="#wrist_flex_MShape-positions-array" count="501" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wrist_flex_MShape-normals" name="wrist_flex_MShape-normals">
          <float_array id="wrist_flex_MShape-normals-array" count="1503">-0.9926072 0.1212634 -0.005117067 -0.9998366 -0.01513356 -0.00988698 -0.9949352 0.09370561 -0.03637738 -0.9993659 0 -0.03560333 -0.9994286 -1.95049e-4 -0.03379816 -0.9994887 0 -0.03197572 -0.9994887 0 -0.03197626 -0.9994887 0 -0.03197626 -0.9994887 0 -0.03197626 -0.9994887 0 -0.03197578 -0.9995143 -3.89778e-4 -0.03116202 -0.9990435 -2.50443e-4 -0.04372903 -0.9924468 -0.1188478 0.03040581 -0.9859525 -0.04218035 -0.1616119 -0.996124 0.07801996 -0.04061724 -0.9874588 0.1473755 0.05661887 -0.9791895 -0.1082548 -0.1716649 -0.9988244 3.42452e-4 0.04847346 -0.9968545 0.001209457 0.07924384 -0.9935234 0.07976969 -0.08092036 -0.9972197 1.61199e-4 -0.07451683 -0.9931608 -0.07265077 0.09139701 -0.9935231 0.1021584 0.0497551 -0.9690532 -0.1522924 0.1942753 -0.9553435 0.0908944 0.2811709 -0.9230818 -0.3839484 0.02244334 -0.8510471 0.5248722 0.01509505 -0.7285395 -0.2049883 0.6536131 -0.5958837 0.165541 0.7858236 -0.991769 0.001261338 0.1280341 -0.9921318 -0.05811555 -0.1108916 -0.8813773 -0.4522527 -0.1365341 -0.9129016 0.4050488 -0.05046044 -0.9386774 -0.3385915 -0.06511877 -0.9270955 0.3736762 0.02932752 -0.912969 -0.3758481 -0.1588269 -0.9245542 0.3424506 -0.167114 -0.9841909 0.04027753 -0.1724699 -0.6345564 -0.7728412 0.007404964 -0.7003185 -0.7127514 0.03923546 -0.9683099 -0.1633981 0.1888834 -0.5859511 0.7565002 0.2904629 -0.6431556 -0.6567321 0.3937688 -0.6753705 0.7351942 0.05800049 -0.9757197 0.06812115 0.2081596 -0.6522536 0.6822762 -0.3302493 -0.9045734 0.3835573 0.1860929 -0.8353825 -0.5238593 0.1664554 -0.5108963 0.8591368 -0.02947777 -0.6036423 0.7910634 -0.09917048 -0.7645204 -0.6180503 -0.1830911 -0.4512024 -0.8919595 0.0287142 -0.9656932 -0.0577899 -0.253174 -0.3882293 0.9215589 0.002705292 -0.4190621 -0.907908 -0.009492348 -0.5264984 -0.8467686 -0.07604136 -0.9591885 -0.05836956 0.2766773 -0.9553665 0.07616469 -0.2854362 -0.4731013 -0.8752165 0.1008527 -0.914507 0.2839409 0.2881919 -0.3555107 0.9321733 0.06830187 -0.3058767 0.9494455 0.07065851 -0.6958888 0.6910942 -0.1952632 -0.9427624 0.05971907 0.3280746 -0.9108203 -0.2868448 -0.2968609 -0.9180384 -0.2723624 0.2881393 -0.2463848 0.9680586 -0.04644205 -0.8702391 0.3823781 -0.310598 -0.2272639 -0.9736555 0.01860431 -0.06833949 0.9976564 0.003387826 -0.1923727 -0.9812424 0.01249086 -0.1549541 0.9871225 0.03972913 -0.1264867 0.9915651 -0.02827821 -0.09969069 -0.9945967 -0.02896989 -0.7664132 -0.5963801 0.2386243 -0.05835995 0.9980701 0.02121433 -0.674039 0.7013074 0.2320332 -0.9294692 -0.05298615 0.3650745 -0.08486658 -0.9960753 0.02513354 -0.05535017 0.9982061 0.02282633 -0.1575627 -0.9765691 0.146584 -0.1673458 0.9767699 0.1338501 -0.9268355 -0.01211283 -0.3752721 -0.3638023 -0.9239084 -0.1184957 -0.7247148 -0.6284782 -0.2824953 -0.3485613 0.9302883 -0.1143181 -0.9095867 -0.003666342 -0.4154979 -0.8484951 -0.3756117 0.3727893 -0.8596961 0.3340154 0.3864665 -0.3228459 0.9379331 0.1266971 -0.8990637 0.07760922 0.4308841 -0.2414729 -0.9651091 0.1012694 -0.5630046 0.7886634 -0.2470544 -0.5487383 -0.7983997 0.2478795 0 -0.9998595 0.01677077 -0.8594652 -0.2938994 -0.4182617 -0.856069 0.2733397 -0.4386698 -0.8843584 0.09053192 -0.4579456 -0.8776352 -0.08537906 0.4716638 -0.872589 0.02756285 -0.4876769 -0.3829282 -0.9043653 -0.1883862 -0.7123231 0.5946845 -0.3727547 -0.6388937 -0.6832361 -0.353558 -0.06701221 0.9969457 -0.04010515 -0.2425222 0.960221 -0.1384141 -0.7681977 -0.4775271 0.4264273 -0.6429152 0.6763425 0.3594728 0 0.9998603 0.01671204 -0.06506117 0.9968106 0.0462147 -0.3691269 0.9076884 0.1996167 -0.8454008 -0.09374845 -0.5258411 -0.8416588 0.08486138 -0.5333001 -0.8366981 -0.0311017 -0.5467804 -0.07813963 -0.9954255 -0.05497534 -0.7758477 0.3807044 0.5031147 -0.8401112 0.07141498 0.5376922 -0.5374041 -0.7733122 0.3364299 -0.7639349 -0.3967132 0.5089422 -0.7686518 -0.36015 -0.5286458 -0.7474792 0.4257528 -0.5099113 -0.03821685 -0.9985965 0.03666488 -0.8082016 -0.07453797 0.5841697 -0.5083789 0.790093 -0.3424967 -0.2214943 -0.9624139 0.1571613 -0.3268284 -0.9190357 -0.2203554 -0.7888132 0.1019056 -0.6061262 -0.4993534 -0.7726878 -0.3919181 -0.605246 0.6410306 0.4719715 -0.4805972 -0.7903019 0.3800646 -0.7887099 0.03650484 -0.6136808 -0.7650963 -0.1403941 -0.6284243 -0.3441522 0.9035669 0.2551984 0 -1 -1.75365e-4 -0.7442988 0.2377553 -0.6240927 -0.7249202 -0.2592951 -0.6381667 -0.7540327 0.08618718 0.6511577 -0.748748 -0.06034505 -0.6601022 -0.6916119 -0.4093653 0.5950572 -0.6959142 0.376644 0.6114267 -0.6012947 0.5946294 -0.5337232 -0.6062357 -0.5768677 -0.5474502 -0.7207382 -0.05702289 0.6908581 -0.2891558 0.9192595 -0.2671157 -0.039563 0.9979881 -0.04954128 -0.7159062 0.1200758 -0.6877936 -0.4582728 0.7574041 0.4651076 -0.2004445 -0.9588456 0.2010901 -0.6811814 0.005650144 -0.732093 -0.4186654 -0.7921124 0.4441591 -0.6599152 0.2786577 -0.6977547 -0.6086078 -0.3855102 0.6935261 -0.6231102 0.3713722 0.6883433 -0.4816474 0.6887605 -0.5418716 -0.2034906 -0.9557269 -0.212551 -0.6022149 -0.3704953 -0.7071567 -0.6575316 0.08497334 0.7486199 -0.6594221 -0.01171427 -0.7516817 -0.3665664 -0.8326687 -0.4150805 -0.61374 0.1297919 -0.7787665 -0.1720602 0.9607281 0.217708 -0.05243925 -0.9955195 0.07868271 -0.616612 -0.07527848 0.7836599 -0.5938258 -0.001140439 -0.8045927 -0.02996694 0.9983414 0.04915706 -0.1782157 0.95534 -0.2357214 -0.3789384 0.7656677 -0.5197678 -0.4053369 -0.7071364 0.5793618 -0.5744013 0.05078272 0.8169971 -0.3614118 0.7689052 0.5274149 -0.5302915 0.3813674 -0.7571986 -0.4177196 -0.6628817 -0.6213679 -0.5727729 0.05250482 -0.8180309 -0.2202065 -0.9206889 -0.3222438 -0.5189748 0.3253554 0.7904486 -0.2038229 -0.9350065 0.2902052 -0.06116436 -0.993524 -0.09575597 -0.5448393 -0.02055587 -0.8382885 -0.4954945 -0.3300317 0.8034703 -0.5210795 -0.06144045 0.8512938 -0.4859493 -0.2404451 -0.8402616 -0.5125909 0.04007393 -0.8576973 -0.4789712 0.08153468 -0.874036 -0.4806212 0.09452244 0.8718192 -0.4308138 0.3490134 -0.8322194 -0.270535 0.812776 -0.5159515 -0.1824007 -0.9219419 0.3416915 -0.3638422 -0.5949746 -0.7166758 -0.3276071 -0.6857738 0.6499138 -0.3675157 0.6042455 0.7069792 -0.1258062 0.9598118 0.250867 -0.1029538 0.9690639 -0.2243118 -0.1772097 -0.9134586 -0.3663195 -0.03186119 -0.9951147 0.09344252 -0.4269818 -0.07766969 0.9009184 -0.3965448 0.05026131 0.9166385 -0.4240185 -8.65133e-4 -0.9056531 -0.2443386 0.7827286 0.5723937 -0.3626593 0.3470044 0.8649082 -0.3654117 -0.3281691 0.8710794 -0.3672753 -0.2901353 -0.8837026 -0.3547844 0.2727001 -0.8942945 -0.2907935 0.5972654 -0.7474712 -0.3610072 0.06252453 -0.9304646 -0.3718838 -0.05083911 -0.9268861 -0.2605908 -0.6659274 -0.6990232 -0.2452381 -0.6859935 0.6850336 -0.2940349 0.06109603 0.95384 -0.1356678 -0.9132006 0.3842641 -0.3022491 -0.04874805 0.9519817 -0.02292526 0.9963887 0.08175603 -0.2122025 0.6609132 0.7198359 -0.02252 0.9968967 -0.07543114 -0.2508038 -0.3916638 0.8852666 1.57873e-4 0.9999987 0.001601913 -0.09037541 0.9520084 -0.2924252 -0.2444592 -0.4476357 -0.8601523 -0.2428294 0.1041056 -0.9644666 -0.255851 0.3470587 -0.9022697 -0.2433035 -0.08280145 -0.9664095 -0.1271943 -0.8827161 -0.4523648 -0.2507277 1.19435e-4 -0.9680577 -0.167433 0.7758335 -0.6083161 -0.2366275 0.003691177 0.9715935 -0.2022275 0.3700064 0.9067521 -0.206479 -5.49284e-4 0.978451 -0.212617 -0.08556286 0.9733822 -0.07659522 0.9387008 0.336116 -0.2019015 0 -0.9794059 -0.1990255 0 -0.9799944 -0.2020634 -0.08425026 -0.9757419 -0.2056824 0.104362 -0.9730383 -0.1868605 -0.3541994 -0.9163111 -0.1735698 0.484268 -0.8575302 -0.1183437 -0.8183934 -0.5623407 -0.1674028 -0.3569909 0.9189853 -0.1236434 -0.7270115 0.675401 -0.03186242 -0.9883348 -0.1489265 -0.0197636 0.9937341 -0.1100084 -0.08109345 0.8949029 -0.4388311 -0.1736339 0.02846018 0.984399 -0.1042089 0.7530887 0.6496136 1.79049e-4 -0.9999993 -0.001213664 -0.08714507 -0.07594578 0.9932966 -0.01203814 -0.9920173 0.1255267 -0.07806809 -0.9046515 -0.4189402 -0.09508572 0.2410072 0.9658542 -0.03260824 -0.9397221 0.3403807 -0.05895642 0.9442825 -0.3238128 -0.07096662 0.584612 0.8082032 -0.06924561 -0.4185577 0.9055466 -0.1887784 -0.1437759 -0.9714377 -0.1776166 0.3514186 -0.9192156 -0.1480207 -0.5940996 -0.7906551 -0.04626865 0.04336236 0.9979874 -0.01813277 -6.13533e-4 0.9998354 -0.02123128 -0.003398744 0.9997688 -0.008647939 0.07350242 0.9972575 -0.01668848 -0.137596 0.9903479 0.002097146 0.9980862 0.06180288 0.002593517 -0.9978635 0.06528263 0.01745242 -0.06680369 0.9976135 0.02343782 0.1084622 0.9938242 0.007897277 -6.59723e-4 0.9999686 -0.001798184 -0.5069577 0.8619691 0.006079475 0.3938225 0.9191664 0.02477827 0.002361765 0.9996903 -0.03872331 0.9749834 -0.2188789 0.02243762 -6.29746e-4 0.999748 0.008502749 0.8594908 0.5110804 -0.1211761 0.750094 -0.6501349 -0.05262661 -0.9549807 -0.2919631 0.02448117 -0.9222166 0.3858978 -0.1734256 0.3923846 -0.903304 0.07407791 -0.2685002 0.960427 -0.1133217 -0.7919188 -0.6000192 0.07904565 0.04491885 0.9958584 0.1084255 0.2937244 0.949721 0.09070603 -0.6177407 0.7811331 -0.1902876 -0.2409938 -0.9516894 0.1266167 -0.05158318 0.9906096 -0.1905123 -0.0479585 -0.9805126 -0.1884132 0.1106051 -0.9758417 0.1068374 0.6452504 0.756464 -0.1703816 0.4352369 -0.8840469 -0.1649148 -0.5131878 -0.8422834 0.1751662 -0.3776275 0.9092383 0.2086743 0.08157498 0.9745771 0.1498908 -0.74133 0.6541885 0.09755654 0.9064038 0.4109924 -0.1178469 -0.7700818 -0.6269658 -0.09448488 0.8606151 -0.500414 -0.1752527 -0.3998161 -0.8996853 0.2291667 0.4492843 0.8634965 -0.05644425 -0.951031 -0.3038981 0.2654703 -0.05871479 0.9623294 0.09985794 -0.9448346 0.3119551 -0.1468863 -0.4518846 -0.8799005 0.2805401 -0.3786682 0.8819907 -0.1581876 -8.08484e-4 -0.9874088 -0.1595702 -0.1166281 -0.980273 -0.1778218 0.06705398 -0.9817755 -0.1332103 0.524758 -0.840764 -0.05875778 -0.9074303 -0.4160742 -0.05436752 0.9163048 -0.3967742 0.3391762 0.05587957 0.9390618 -0.01006664 -0.9964118 -0.08403655 -0.0114498 0.9963943 -0.08406833 0.2047892 0.8018784 0.5612953 0.02339374 -0.9974148 0.06794496 0.3601993 0.2725129 0.8921845 0.02987081 0.9955298 0.08960044 0.3011341 -0.6084106 0.7342716 -0.1070593 0.1074989 -0.9884242 0.3070523 0.5970365 0.7411251 -0.08432116 -0.327886 -0.9409467 -0.08724053 -8.4609e-4 -0.9961869 0.399143 -0.1160813 0.9095109 0.1238275 0.9528772 0.276933 -0.06089721 -0.04758947 -0.9970089 -0.01812259 -0.788091 -0.6152919 -0.01365658 0.02148995 -0.9996758 0.01085242 0.2955632 -0.9552616 0.01158541 0.7922701 -0.6100606 0.003737431 -0.9803642 -0.1971601 0.009072999 0.9866878 -0.162373 0.04184358 0.03886493 -0.998368 0.0371522 -0.06049427 -0.9974769 0.05248286 1.31142e-4 -0.9986218 0.4303006 -2.23078e-4 0.9026857 0.05793714 -2.02348e-4 -0.9983203 0.2821658 -0.7985056 0.5317624 0.1018211 -0.373082 -0.9221943 0.07577632 -1.22923e-4 -0.9971248 0.4634094 0.05621764 0.8843592 0.1488387 -0.04143598 -0.9879931 0.1730014 0 -0.9849216 0.1938494 0.06611389 -0.978801 0.3034665 0.7830601 0.5428855 0.4705808 -0.3781779 0.7972047 0.5032727 -0.0334585 0.8634797 0.4693684 0.3934155 0.7905173 0.1293086 -0.876263 -0.4641576 0.242894 0.4691494 -0.8490591 0.2697367 -2.21598e-4 -0.9629341 0.1652228 0.8571763 -0.4878013 0.3331463 0.0056849 -0.9428581 0.3383959 -0.1695748 -0.9255986 0.03544444 -0.9955233 -0.08761884 0.1633515 -0.9533317 0.2539192 0.2118511 0.9605055 0.1804118 0.400564 3.51602e-4 -0.9162688 0.3486122 -0.6179025 -0.7047454 0.399386 -0.7247974 0.5613909 0.4905908 0.112695 -0.864072 0.6012949 -0.07026508 0.7959317 0.5621169 4.41486e-4 -0.8270577 0.5619822 -0.05287357 -0.8254577 0.4271808 0.8218477 0.3769391 0.5963886 0.05739776 0.800641 0.4808084 0.6256282 -0.6143393 0.2292167 0.943521 -0.2392233 0.6554061 0.2304327 -0.7192659 0.5929264 -0.3267575 -0.7359807 0.2888333 -0.9204938 -0.2631849 0.5789715 -0.3933118 0.7142112 0.5899622 0.5473585 0.5935851 0.07641494 0.9936997 0.08198632 0.1030132 -0.9888495 0.1075408 0.7487475 -0.05500361 -0.6605694 0.588578 -0.6445961 -0.4879259 0.762156 1.12386e-4 -0.6473933 0.09682981 0.9938638 -0.05346579 0.7732933 0.03201988 -0.6332394 0.3799239 0.833338 0.4015042 0.7593008 0.4061599 -0.5084253 0.3908772 -0.8300233 0.3978397 0.8067932 -0.3004982 -0.5087097 0.5059237 0.8249949 -0.251842 0.0517903 0.9983897 -0.02314677 0.09063296 -0.9953541 -0.03249529 0.692713 -0.4176731 0.5879608 0.6956192 0.4351283 0.5716445 0.08339766 -0.9963469 -0.01837507 0.1013629 0.9946315 -0.02082576 0.06681667 0.9975682 0.01982609 0.3698243 -0.9214709 -0.1188339 0.1859328 0.9815518 0.04455276 0.0653837 -0.9975821 0.02355846 0.7074899 -0.0898882 0.7009837 0.7219875 0.0823846 0.686984 0.7120553 -9.28474e-4 0.7021227 0.1107025 -0.9936586 -0.01968399 0.09187724 0.9956145 0.01761803 0.06561647 -0.9976715 0.0186005 0.8901601 0.001012547 -0.4556469 0.09269203 -0.995523 -0.01849327 0.157202 -0.9873255 -0.02181753 0.8954948 -0.02627457 -0.4442959 0.2699966 0.9611074 -0.05808945 0.09112251 0.9956636 -0.01872679 0.1015815 -0.9948183 0.004216388 0.1180942 0.9930024 3.2396e-4 0.4886498 0.8397317 0.2367956 0.7010255 -0.6482535 -0.297205 0.2846772 0.9583905 -0.02113306 0.1109471 0.9935594 0.02303067 0.2370662 -0.9712721 0.02073831 0.6148044 -0.691609 0.379068 0.287109 0.9576965 -0.01964199 0.9225326 0.05779931 -0.3815664 0.3759754 -0.9259086 -0.03654953 0.283556 -0.9586743 0.02322931 0.3966968 -0.917591 0.02565772 0.4211695 -0.8975885 0.1301965 0.4157913 0.9087217 -0.03663917 0.4024528 0.9146594 0.03781292 0.8827273 0.3729181 -0.285875 0.3961181 -0.9179589 -0.02102447 0.3571916 -0.9337509 -0.02287588 0.3499762 0.9363371 0.02809896 0.3400317 -0.9402182 -0.01918663 0.9181195 -0.2691475 -0.290889 0.4243248 0.9052568 0.02141594 0.6836538 0.7182842 -0.1291714 0.8596516 -5.6197e-4 0.5108804 0.7244503 -0.6752193 -0.1387469 0.3369603 0.9412113 0.02406486 0.9511924 1.27371e-4 -0.3085984 0.4530325 -0.8913124 0.01798994 0.3605331 0.9323899 -0.02578738 0.3926709 -0.9196153 -0.01083064 0.5616877 -0.8269068 0.02705741 0.4556617 0.8898945 -0.02145282 0.5886889 0.8080108 -0.02374948 0.5217034 0.8530999 -0.006793457 0.8937877 -0.06379382 0.44393 0.9704586 -0.04824965 -0.2363939 0.7327766 -0.6793401 -0.03918291 0.8872707 0.09578308 0.4511942 0.7981265 0.6011995 -0.03941052 0.7198273 0.6715742 0.175604 0.5845767 -0.8108907 -0.02694679 0.8143278 -0.5791894 -0.03754698 0.7091455 0.7045994 0.02554032 0.9784034 0.06187881 -0.1972253 0.6932922 -0.7201834 0.02611296 0.7197095 -0.693899 -0.02285506 0.7513659 -0.657534 0.05566173 0.6939512 -0.7196048 -0.02450696 0.8705441 -0.408454 0.2744415 0.7648622 0.6412187 0.06184226 0.9024828 0.3436252 0.2597049 0.8109218 -0.5846815 0.02352517 0.9446744 -0.3064407 -0.1169798 0.8328591 -0.5529588 0.02412981 0.7950183 0.6060454 0.02559473 0.9453373 0.3076763 -0.1080403 0.7718403 0.635316 -0.02522355 0.9589338 -0.002519514 0.2836187 0.9515065 -1.45222e-4 0.3076287 0.8847483 -0.4637626 -0.04631008 0.9913969 -0.05735717 -0.1176534 0.7949346 0.6062026 0.02443914 0.7405584 0.6716765 0.0205873 0.9956841 0.04579422 -0.08072204 0.9754003 0.02524643 0.2189906 0.9827183 -9.82061e-4 0.1851045 0.7845189 -0.6196219 0.02447239 0.9578855 -0.2820478 0.05389218 0.9186903 0.3941712 -0.02524471 0.8031405 -0.5957829 -0.002864674 0.9955508 -0.089539 -0.02934958 0.9860232 0.1637645 -0.03065112 0.9886916 0.09317046 0.1175075 0.9902186 -0.04672002 0.13147 0.9852965 0.1635224 0.04950828 0.9516923 -0.3047341 -0.03766353 0.9934555 -0.002691727 0.1141882 0.9331035 0.3587505 0.02481695 0.9979339 5.7957e-4 0.06424475 0.9839571 0.1766835 0.02472824 0.9513004 -0.3074722 0.02209912 0.9382375 -0.3453195 -0.02156524 0.9954836 0.09280224 -0.02000591 0.9652681 0.2604567 -0.02049036 0.9437426 -0.3300501 0.02041873 0.9192983 -0.3930072 -0.02088011 0.9970365 -0.07467584 0.01848398 0.9980369 0.05991543 -0.01823296 0.9951048 -0.09716245 -0.01804868 0.9279916 -0.3715515 0.02794756 0.9149953 -0.4029606 -0.02015334 0.9502903 0.3107244 0.01997069 0.918247 0.3959901 -0.003772144 0.9650757 0.2610262 -0.02223016 0.9525076 -0.3043672 0.009482597 0.994976 0.0985199 0.01779264 0.9953616 -0.09429865 0.01905842 0.9881819 -0.1532363 -0.003886867 0.9999985 0 0.001757688 0.9964561 0.0841085 9.62576e-4</float_array>
          <technique_common>
            <accessor source="#wrist_flex_MShape-normals-array" count="501" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wrist_flex_MShape-map1" name="wrist_flex_MShape-map1">
          <float_array id="wrist_flex_MShape-map1-array" count="1336">0.03131936 0.2601633 0.01375731 0.2433842 0.01350382 0.2361247 0.0140046 0.248557 0.008594132 0.2380261 0.1580537 0.2361247 0.01478666 0.2578681 0.01136725 0.2578913 0.01375789 0.2288651 0.004991611 0.2444894 0.03250883 0.3189611 0.01494834 0.2593176 0.007247839 0.2286683 0.001885748 0.2361247 0.0272166 0.1649113 0.08465058 0.3895794 0.005503391 0.2578681 0.01247044 0.2076075 0.06226798 0.1052642 0.2151121 0.4378422 0.04840926 0.359418 0.0113164 0.269805 0.004669574 0.2582254 0.002889798 0.2587912 0.002245014 0.2247285 0.007768384 0.2063631 0.3642894 0.1971394 0.0282989 0.1505696 0.01670257 0.1801678 0.3620555 0.286956 0.1524059 0.4260049 0.08109795 0.3904397 0.03251152 0.3325361 0.01975825 0.3023408 0.3644308 0.2361247 0.1766777 0.03810897 0.05941103 0.1010058 0.3627993 0.3350722 0.1202992 0.4190543 0.1724159 0.4380765 0.06731346 0.3849554 0.02947265 0.3379999 0.01440356 0.3024842 0.003278129 0.2052368 0.01138225 0.1814727 0.3692759 0.2168679 0.218418 0.02734987 0.1549744 0.0437729 0.02143213 0.156118 0.04062033 0.1195906 0.0854778 0.076964 0.3689483 0.2629399 0.3678811 0.330979 0.362212 0.3632374 0.2214061 0.4437755 0.09186377 0.4065723 0.04697222 0.3645838 0.03492416 0.3517597 0.005792268 0.2781761 0.370069 0.1682812 0.3707277 0.2360156 0.1950331 0.02989274 0.2871921 0.01662785 0.1585192 0.03835408 0.05576972 0.09624081 0.07136534 0.08086484 0.3687747 0.2815991 0.3660582 0.3047955 0.3672548 0.3488305 0.3536044 0.3827116 0.132049 0.4281601 0.1814578 0.442842 0.2644332 0.4380584 0.07125247 0.3942143 0.05472802 0.3778308 0.01957008 0.3226936 0.01254376 0.1673725 0.3664764 0.154533 0.3736253 0.1862163 0.178647 0.03243866 0.221753 0.02114566 0.3619333 0.09304343 0.2665331 0.01477402 0.1208754 0.05423816 0.02680384 0.1393294 0.0383576 0.1161203 0.08885238 0.0687435 0.3742139 0.2500716 0.3734643 0.3416335 0.2930123 0.4201804 0.3648716 0.3788851 0.372923 0.363144 0.1545959 0.4391253 0.1966842 0.4476604 0.2496929 0.4449811 0.0964746 0.4144113 0.1179129 0.424288 0.3704234 0.1176094 0.3764779 0.232044 0.3605496 0.07401263 0.2862477 0.01177871 0.1135137 0.05322932 0.1630759 0.0331258 0.09287262 0.06059423 0.3740559 0.2842273 0.3722552 0.3183787 0.3354016 0.4040869 0.3583203 0.3870012 0.133975 0.4342885 0.2179435 0.4496616 0.1679918 0.4448187 0.3061363 0.4199687 0.2679906 0.4430448 0.3656018 0.1169735 0.3747833 0.1292078 0.1745129 0.02712312 0.2535436 0.01106515 0.3564091 0.04889233 0.3639523 0.06981575 0.3076611 0.01447309 0.2850059 0.005476707 0.375303 0.3202577 0.3618298 0.3912702 0.372074 0.3687997 0.2871945 0.4322394 0.2386539 0.4490709 0.366996 0.09244847 0.371989 0.09225828 0.2010327 0.02108898 0.2311401 0.01523752 0.3074546 0.006497645 0.1373745 0.03889209 0.1605801 0.02929625 0.1131735 0.04816216 0.3523228 0.4000398 0.3223602 0.4162288 0.3733207 0.3739173 0.2969734 0.4317299 0.3600138 0.04153308 0.3466176 0.02465966 0.3687758 0.0618091 0.3286192 0.01100346 0.3071995 0.001992014 0.3435788 0.4029827 0.3279085 0.005036788 0.3243803 0.4203082 0.3735079 0.07526925 0.3461866 0.01597203 0.3674239 0.04526488 0.3586119 0.0277069 0.8379475 0.8432504 0.716688 0.8429623 0.8455532 0.828956 0.8092938 0.8548743 0.7162026 0.8223665 0.9331277 0.8360644 0.7163697 0.8576482 0.9334187 0.8545004 0.7133505 0.8298843 0.9333466 0.8150451 0.7169416 0.8666255 0.7131334 0.8492889 0.8555533 0.8668055 0.7112693 0.8030423 0.7161293 0.8050355 0.9376603 0.8323513 0.9384326 0.8499093 0.7121475 0.8628595 0.934373 0.8676146 0.7084309 0.832718 0.7068019 0.8160784 0.9335945 0.7994559 0.9379917 0.8023469 0.9358767 0.8635538 0.7078289 0.8521935 0.7155955 0.7845728 0.7073482 0.7846631 0.9426358 0.8158071 0.9408025 0.8616441 0.7044212 0.8503255 0.7037663 0.8223536 0.7034138 0.7864387 0.9338373 0.7845728 0.7114568 0.7427645 0.7054003 0.7607735 0.9352603 0.7370023 0.942339 0.7853579 0.7146666 0.7125707 0.7027007 0.7463871 0.9394735 0.7484084 0.9355271 0.7120558 0.708222 0.7462439 0.7098324 0.7043652 0.7530655 0.6414915 0.7054074 0.7214845 0.938418 0.7156229 0.7090817 0.6851515 0.713098 0.6414915 0.7019023 0.7109715 0.943918 0.6913999 0.9393018 0.6697274 0.9363347 0.6414915 0.7088538 0.6620387 0.7136526 0.6097262 0.7045548 0.6881373 0.8964867 0.5144212 0.705218 0.6429277 0.7146184 0.5553225 0.7090698 0.6298556 0.7016506 0.6936418 0.9415597 0.6455721 0.7700161 0.5144285 0.9356855 0.6045541 0.7030079 0.6606186 0.7096528 0.587328 0.7095915 0.6065726 0.8689318 0.5064332 0.935104 0.5332837 0.7009398 0.6440445 0.7053053 0.6204121 0.7153159 0.51443 0.7113117 0.5546394 0.9387802 0.6033886 0.8205966 0.5038284 0.9340222 0.5064347 0.9394761 0.5573585 0.9341168 0.51443 0.7010981 0.6235867 0.7157558 0.4959967 0.7118541 0.5143787 0.7037905 0.6037559 0.7060373 0.5681797 0.9447587 0.6275446 0.9218515 0.5038284 0.9340702 0.5104061 0.941923 0.5359827 0.9423056 0.5144722 0.7102103 0.5029497 0.7070906 0.5435548 0.9434376 0.5933889 0.8658594 0.4927398 0.933156 0.4972945 0.9384907 0.5036989 0.7104334 0.486342 0.7073125 0.5175255 0.703836 0.5335768 0.7940693 0.485618 0.9422742 0.5088165 0.7053112 0.4980947 0.7156567 0.4856555 0.7032095 0.5064099 0.9337761 0.4856555 0.9390097 0.4863459 0.7046126 0.4897058 0.711105 0.4758633 0.7030339 0.5142464 0.9325216 0.469993 0.7159225 0.4687275 0.9389181 0.4775764 0.7055708 0.473782 0.7116363 0.4569134 0.9380893 0.457308 0.7161289 0.4564241 0.9320425 0.4525706 0.4233129 0.1067344 0.4640736 0.06568301 0.4208732 0.1187288 0.4509995 0.07176845 0.5650482 0.2151516 0.4033833 0.1472918 0.4789915 0.04602361 0.4291583 0.0883069 0.5077747 0.03372873 0.4319959 0.2151516 0.4049532 0.1327515 0.3970406 0.1919587 0.4515325 0.06520769 0.4976217 0.03409187 0.4186988 0.1061522 0.5972046 0.01343384 0.4054388 0.1859693 0.6692844 0.4346484 0.3926795 0.1822376 0.4725771 0.04460935 0.434728 0.07632025 0.4187094 0.09762906 0.5307527 0.01954234 0.5757569 0.01455477 0.7443078 0.08808985 0.3968789 0.1934081 0.713726 0.4312632 0.4817359 0.3815488 0.410762 0.1109309 0.3959243 0.1493509 0.3869273 0.1924548 0.4532127 0.05687704 0.4999768 0.02700097 0.5763686 0.009795125 0.6082645 0.009024536 0.745263 0.1428761 0.6733765 0.03098468 0.3958496 0.207892 0.6897534 0.4368032 0.7435663 0.3797313 0.5587699 0.4131673 0.4167384 0.3084588 0.4011491 0.1297803 0.3889381 0.1755181 0.3903449 0.1930117 0.4832991 0.03411986 0.5226417 0.01776918 0.5581208 0.008911326 0.6573836 0.0175601 0.7482516 0.1286707 0.746523 0.2151516 0.7356972 0.06856528 0.3955961 0.2151516 0.7091972 0.440828 0.6683301 0.439541 0.732392 0.4239856 0.7440255 0.3582329 0.5045224 0.400378 0.605266 0.4254818 0.453569 0.3617286 0.3958503 0.2224112 0.5161318 0.01695903 0.5994084 0.002720449 0.6447602 0.01127704 0.7490333 0.08790646 0.7489378 0.1680071 0.7458097 0.2450802 0.7165282 0.04695553 0.7490708 0.08006998 0.3863426 0.2054511 0.6886894 0.4445276 0.6512074 0.4347006 0.7441592 0.3960137 0.7448049 0.3210003 0.748935 0.3600987 0.5440978 0.4149955 0.4782467 0.3839819 0.5757784 0.4215967 0.4244086 0.3276152 0.4600635 0.3734552 0.3983188 0.2744943 0.5383437 0.009640535 0.5804675 0.001992041 0.6217595 0.004364826 0.676613 0.01841922 0.7515951 0.1072369 0.7511951 0.1972467 0.7456989 0.2798248 0.7515459 0.2177046 0.6965754 0.03100133 0.7407475 0.06336585 0.3860219 0.2151532 0.3907938 0.2230007 0.3847485 0.192711 0.7185081 0.4414058 0.6973156 0.4487921 0.6629041 0.4453033 0.6479003 0.4386143 0.7474732 0.406378 0.5571745 0.4198766 0.5223482 0.4132377 0.4771011 0.3895612 0.6179606 0.4326824 0.4289493 0.3412005 0.4081813 0.2970078 0.3911138 0.2397112 0.5573246 0.00511063 0.6376777 0.005221647 0.6653769 0.01226822 0.7544904 0.1774159 0.7500755 0.267302 0.7542388 0.2342787 0.7235184 0.04744205 0.7485606 0.0717548 0.3839779 0.2151516 0.7365857 0.4258536 0.6798327 0.4486082 0.750928 0.3897384 0.7495769 0.2846316 0.7495182 0.3200472 0.5406466 0.4211616 0.4982181 0.4026245 0.5848324 0.4287995 0.4089145 0.3119392 0.4394316 0.3566906 0.3983231 0.288473 0.3898659 0.2586321 0.7058671 0.03057348 0.3843275 0.2244952 0.7297341 0.4365196 0.6495057 0.4431005 0.5564581 0.4248999 0.4545572 0.3747497 0.6360856 0.4395446 0.4201013 0.3350231 0.3852129 0.2456129 0.7326466 0.04952332 0.6853384 0.5507799 0.690251 0.5690722 0.6904093 0.5991797 0.6905406 0.5466688 0.6855937 0.6260237 0.6852187 0.5155095 0.6923531 0.6256994 0.6067029 0.6260237 0.6919408 0.5152065 0.6856439 0.6398742 0.606615 0.6377863 0.5772847 0.6260238 0.6850894 0.5033913 0.6901841 0.6396322 0.4693546 0.6011529 0.6002617 0.5020076 0.6900345 0.4930128 0.6855639 0.661792 0.4692242 0.638511 0.4981766 0.5155097 0.4692698 0.6260237 0.6851801 0.4935125 0.6892027 0.6645499 0.4692698 0.6606165 0.469525 0.5507799 0.4641985 0.6258465 0.5667791 0.4892687 0.4696826 0.4936545 0.6851807 0.4918918 0.6940647 0.6628378 0.6908922 0.6870388 0.580278 0.6704547 0.4635424 0.641658 0.4696447 0.5155095 0.4664748 0.5567151 0.4615448 0.5974723 0.6617032 0.4844287 0.6861046 0.4779929 0.6862416 0.6905666 0.4709486 0.6907764 0.4656844 0.6645525 0.4629988 0.5462189 0.4957052 0.4860865 0.6453428 0.478839 0.4634971 0.5194005 0.6904076 0.4723039 0.6909952 0.7073398 0.4687688 0.7001945 0.4655611 0.6941099 0.4606468 0.6599564 0.4647504 0.5327989 0.4696828 0.4918721 0.4696683 0.4797149 0.4650018 0.5024719 0.6870884 0.4580724 0.6863347 0.7049028 0.4705991 0.7202254 0.460701 0.6840865 0.4649464 0.484989 0.5373473 0.472247 0.6606506 0.4695086 0.6935178 0.4540258 0.6864653 0.7260152 0.4636098 0.7277475 0.4682878 0.7308292 0.6833722 0.7166168 0.4602611 0.7052035 0.4687464 0.4687681 0.6082687 0.4569934 0.6896046 0.7302161 0.4710231 0.7379269 0.5591468 0.464413 0.6867038 0.7476007 0.695132 0.728847 0.4650667 0.7486194 0.4681518 0.75305 0.4615782 0.4637965 0.4696569 0.4567346 0.6899084 0.7488602 0.6842487 0.752445 0.6948681 0.7444426 0.4600594 0.742873 0.470504 0.7536466 0.4648628 0.4525706 0.6867939 0.7664176 0.4629269 0.7622033 0.4703353 0.766573 0.6923591 0.7618547 0.684472 0.7669622 0.4673301 0.7749091 0.6908094 0.7765416 0.4640571 0.7765428 0.221278 0.5809316 0.2215177 0.5951879 0.2166067 0.5958851 0.3307938 0.5860537 0.2215295 0.5966375 0.2119952 0.583145 0.2804547 0.5659937 0.2434618 0.5992336 0.2196052 0.5966377 0.2152144 0.5734445 0.247572 0.5659937 0.2805854 0.5659937 0.221813 0.6139451 0.2126949 0.5961413 0.2211473 0.5659573 0.221278 0.5659573 0.2217996 0.5453998 0.3031971 0.5659937 0.326526 0.5513317 0.4429829 0.6191579 0.2137256 0.613078 0.2146696 0.5641009 0.3327696 0.5659937 0.2221369 0.6334676 0.44401 0.5966375 0.2138575 0.6392452 0.4437908 0.5373169 0.3623421 0.5659937 0.2188993 0.6409887 0.443281 0.6412955 0.4179916 0.5883888 0.4493278 0.6154959 0.2172518 0.5429831 0.2220675 0.5156228 0.4441202 0.5574453 0.3849539 0.5659937 0.2224049 0.6498823 0.4469904 0.6392159 0.4440216 0.5951879 0.4442613 0.5809316 0.4528095 0.5955452 0.2175908 0.6588157 0.4428234 0.511766 0.4485685 0.5425566 0.4129509 0.5659937 0.3850846 0.5659937 0.4478999 0.6600134 0.442887 0.6649176 0.4413959 0.5722442 0.45231 0.6398041 0.2189088 0.5059372 0.2224574 0.4941628 0.4505978 0.5620483 0.4442612 0.5659573 0.2227787 0.6728823 0.4443919 0.5734445 0.2135983 0.529964 0.4481491 0.5046924 0.4429543 0.4865526 0.2173833 0.6776652 0.4470273 0.6890795 0.4435199 0.6903695 0.4503249 0.5734445 0.4443266 0.5697009 0.2141659 0.5001231 0.2193951 0.4768675 0.4528121 0.5187925 0.2246478 0.4767044 0.2178464 0.690967 0.4511157 0.6753197 0.2231726 0.69706 0.4542515 0.5818092 0.222668 0.4730581 0.4461473 0.4768678 0.4398098 0.4602723 0.4416187 0.7126853 0.2143187 0.4766569 0.4511867 0.4766492 0.4421318 0.4580677 0.2256732 0.4598457 0.2178975 0.7122758 0.4463024 0.7151507 0.2235563 0.7206669 0.2182648 0.453573 0.447697 0.4534401 0.4508562 0.7019036 0.4471609 0.7315341 0.4417434 0.734897 0.2188145 0.731719 0.2239227 0.7428702 0.4461429 0.7517312 0.441525 0.7486925 0.2190919 0.7544762 0.2241462 0.7563242 0.2149595 0.7439867 0.4413227 0.7605271 0.1925567 0.7728484 0.1974684 0.769164 0.2231531 0.7738321 0.1734018 0.767411 0.2297914 0.7677854 0.2170854 0.8651017 0.2023846 0.7646134 0.1652468 0.7729768 0.2292233 0.9897457 0.2067115 0.9897723 0.1783658 0.9893944 0.1440651 0.7689293 0.1489464 0.9902348 0.155104 0.7645511 0.1420983 0.772045 0.229186 0.9942383 0.1886958 0.9945281 0.1609966 0.9942599 0.1209691 0.7730601 0.1999319 0.997672 0.126581 0.9109406 0.1376815 0.992075 0.1233547 0.7675352 0.1237244 0.7809521 0.121614 0.8192084 0.1209691 0.9892563 0.1450784 0.9970816 0.1204325 0.7730601 0.1086761 0.7730516 0.1178557 0.9740227 0.1227274 0.9969379 0.1182051 0.9821019 0.1204325 0.9892563 0.09931988 0.7700944 0.09858869 0.9898269 0.08578102 0.773006 0.1020954 0.7654425 0.1037864 0.9943261 0.073403 0.7680797 0.07497969 0.9492379 0.0806435 0.994541 0.05349997 0.7728773 0.0756791 0.9893346 0.07255697 0.8125215 0.03938619 0.7685687 0.0557425 0.9894395 0.0616626 0.9944834 0.03288624 0.7727526 0.06000709 0.7645793 0.01535959 0.7726051 0.03945064 0.9937435 0.03122707 0.9895813 0.001885748 0.7677848 0.0233241 0.7643057 0.008160141 0.9897817 0.006618069 0.9948359 0.02329573 0.998008 0.2383049 0.7800118 0.2363808 0.7800115 0.2363924 0.7785619 0.241863 0.779485 0.2366321 0.7643057 0.2466223 0.7651832 0.2734278 0.7711812 0.2761038 0.7643057 0.2732801 0.7731052 0.274324 0.7676231 0.2825049 0.7709994 0.2846837 0.7670876 0.2881013 0.7643057 0.2557674 0.7710013 0.2529854 0.7744188 0.2571682 0.7643056 0.2670445 0.7731403 0.2642031 0.7662299 0.2640553 0.7643056 0.2980332 0.7679523 0.2971994 0.7676231 0.2954196 0.7643057</float_array>
          <technique_common>
            <accessor source="#wrist_flex_MShape-map1-array" count="668" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="wrist_flex_MShape-vertices" name="wrist_flex_MShape-vertices">
          <input semantic="POSITION" source="#wrist_flex_MShape-positions"/>
          <input semantic="NORMAL" source="#wrist_flex_MShape-normals"/>
        </vertices>
        <triangles material="lambert2SG" count="998">
          <input semantic="VERTEX" source="#wrist_flex_MShape-vertices" offset="0"/>
          <input semantic="TEXCOORD" source="#wrist_flex_MShape-map1" offset="1" set="0"/>
          <p>107 0 69 2 71 1 404 113 433 97 414 126 394 150 427 152 397 151 114 553 138 574 127 578 376 161 369 174 362 167 297 372 287 327 311 348 85 28 49 25 66 17 123 264 120 266 160 265 85 28 103 14 104 27 281 401 251 403 272 402 308 300 367 315 348 331 12 493 33 495 21 494 304 589 333 591 309 590 28 646 22 648 24 647 494 200 484 195 457 199 346 468 343 460 350 472 324 119 360 139 344 141 81 11 61 7 41 22 176 448 180 440 203 424 398 118 423 140 377 138 306 62 290 82 266 46 54 356 31 392 55 369 186 385 191 321 170 365 246 297 192 272 243 287 50 390 84 389 83 344 204 364 215 384 219 339 100 368 84 389 102 387 474 220 468 229 487 207 129 476 144 471 147 465 157 343 140 388 154 396 406 313 412 328 387 288 446 194 417 188 437 184 355 469 345 461 353 455 72 8 69 2 213 5 160 265 241 268 192 272 499 193 477 211 487 207 57 535 52 526 37 519 382 320 410 338 391 337 98 547 77 529 87 534 284 397 291 394 289 361 207 286 192 272 246 297 170 365 191 321 172 340 291 451 278 435 299 434 459 233 424 240 458 223 196 70 168 55 188 96 317 111 288 72 307 124 72 8 48 12 53 4 173 641 197 633 188 642 261 607 256 616 265 612 493 210 496 212 500 201 314 454 318 428 331 459 73 324 113 305 132 273 435 228 390 246 388 249 241 268 259 279 192 272 463 87 422 66 462 104 485 214 445 230 490 215 434 60 463 87 493 98 393 299 406 313 387 288 479 205 465 224 466 216 424 240 435 228 458 223 444 159 456 172 464 171 246 297 243 287 271 298 360 139 324 119 371 117 52 526 82 544 64 543 327 427 335 444 334 453 283 116 272 129 269 80 146 267 174 270 148 276 117 561 98 547 105 552 215 433 203 424 218 419 453 163 425 170 421 158 111 487 110 489 125 483 137 573 121 563 117 561 327 427 332 437 335 444 285 371 277 347 287 327 497 203 498 197 499 193 262 613 275 603 265 612 12 493 17 496 4 499 397 151 376 161 368 156 234 370 235 311 249 346 64 543 95 558 84 557 312 413 301 409 321 417 12 493 16 507 25 502 223 110 248 93 240 71 258 19 392 37 384 29 26 13 53 4 48 12 122 49 101 84 92 48 283 404 301 409 300 406 177 309 166 283 187 296 197 108 210 92 196 70 232 407 216 410 217 414 413 250 447 244 449 237 128 271 137 284 117 285 368 156 376 161 362 167 404 113 414 126 383 81 166 587 177 585 150 581 359 462 353 455 342 446 107 0 81 11 108 10 212 345 235 311 234 370 112 571 110 567 86 551 377 138 416 148 374 149 133 479 139 481 149 470 391 337 418 382 395 351 106 562 88 539 114 553 183 431 202 418 181 439 70 301 51 308 39 333 418 382 420 374 395 351 257 621 255 620 242 623 418 198 446 194 448 204 138 74 145 40 127 56 331 459 346 468 351 477 321 142 322 130 342 144 148 276 150 295 137 284 168 55 145 40 151 73 273 610 263 619 277 615 311 348 330 353 338 391 416 165 423 177 456 172 261 607 275 603 276 600 31 392 35 399 55 369 142 36 152 65 139 64 49 25 48 12 66 17 131 20 108 10 109 32 80 275 58 282 42 294 148 276 137 284 128 271 270 322 293 335 305 281 212 345 205 310 235 311 241 268 379 303 382 320 445 373 411 329 428 350 461 164 453 163 436 154 106 41 76 42 88 75 492 222 496 212 493 210 381 122 402 90 373 107 91 269 120 266 123 264 450 236 480 217 455 235 471 175 476 183 442 176 108 10 60 21 89 33 288 72 268 54 282 94 249 346 235 311 263 326 397 151 368 156 370 153 73 324 55 369 83 344 19 527 14 546 32 545 159 31 131 20 145 40 232 115 238 79 269 80 496 212 478 225 480 217 19 527 20 511 11 537 142 36 139 64 122 49 63 522 90 540 88 539 364 400 338 391 352 376 71 1 43 9 61 7 189 38 159 31 168 55 322 130 321 142 301 120 20 511 19 527 37 519 132 273 94 280 78 289 212 635 225 631 208 634 253 617 242 623 254 618 109 32 108 10 89 33 99 490 129 476 118 486 226 39 189 38 196 70 143 18 103 14 213 5 494 200 457 199 493 210 280 425 298 420 299 434 130 480 110 489 112 485 166 283 150 295 148 276 217 414 216 410 202 418 176 448 203 424 179 449 256 616 262 613 265 612 75 3 71 1 61 7 300 406 227 412 228 408 24 517 46 524 41 533 307 124 288 72 292 112 334 453 326 452 329 443 23 497 40 505 29 500 242 623 239 624 224 628 164 50 165 86 152 65 54 356 68 316 38 355 76 42 41 22 46 58 202 418 195 432 181 439 93 274 91 269 123 264 144 471 125 483 130 480 407 248 449 237 380 243 81 11 41 22 60 21 305 281 323 302 347 290 276 600 292 592 282 602 4 499 30 509 3 503 239 624 242 623 253 617 30 509 52 526 35 525 401 34 429 45 399 26 233 363 244 341 236 304 446 194 486 196 448 204 188 96 168 55 173 95 375 253 364 254 352 259 410 181 437 184 417 188 73 324 83 344 113 305 204 364 191 321 186 385 458 223 435 228 477 211 44 512 63 522 59 530 175 291 191 321 236 304 377 138 423 140 416 148 469 189 483 185 472 182 357 143 340 145 365 134 461 164 444 159 464 171 450 91 402 90 440 123 44 512 46 524 24 517 477 211 499 193 479 205 480 217 479 205 496 212 337 135 340 145 357 143 399 26 143 18 213 5 303 100 306 62 324 119 93 274 74 293 58 282 314 454 296 445 318 428 211 47 164 50 143 18 437 184 410 181 442 176 498 197 495 208 470 206 82 544 97 560 99 565 342 144 322 130 344 141 497 203 490 215 495 208 482 192 489 187 486 196 302 318 305 281 293 335 153 323 157 343 175 291 460 191 482 192 446 194 395 351 420 374 396 330 382 320 391 337 241 268 476 183 460 191 437 184 361 173 374 166 372 157 432 59 457 78 433 97 424 240 438 241 389 251 229 421 227 412 250 415 387 288 308 300 259 279 14 546 11 537 1 541 365 258 389 251 381 252 311 348 295 312 330 353 19 527 11 537 14 546 10 538 17 496 1 541 156 463 169 447 171 456 79 6 81 11 107 0 128 271 123 264 146 267 311 608 338 604 316 598 340 593 313 595 309 590 426 51 463 87 434 60 308 300 387 288 367 315 187 296 205 310 198 325 334 453 329 443 327 427 335 444 345 461 343 460 315 416 312 413 320 422 78 289 51 308 70 301 436 154 421 158 397 151 65 521 40 505 74 518 279 601 260 614 273 610 347 290 341 317 363 319 28 652 24 654 41 653 372 157 374 166 409 155 23 497 29 500 21 494 145 40 131 20 127 56 375 253 407 248 380 243 420 374 441 375 396 330 464 171 456 172 472 182 99 565 95 558 82 544 380 243 388 249 364 254 280 425 278 435 250 415 347 290 241 268 305 281 372 157 409 155 394 150 236 304 305 281 175 291 343 460 346 468 334 453 315 416 320 422 327 427 260 614 255 620 257 621 349 106 310 89 317 111 375 253 380 243 364 254 27 501 23 497 33 495 167 584 182 588 173 583 24 517 22 531 18 523 241 268 393 299 387 288 188 96 197 108 196 70 478 225 455 235 480 217 398 118 371 117 378 99 468 229 474 220 449 237 476 183 437 184 442 176 305 281 302 318 323 302 366 69 386 53 258 19 275 603 279 601 286 596 333 591 328 594 339 597 194 636 208 634 206 630 262 613 267 609 275 603 253 617 267 609 262 613 56 516 63 522 44 512 499 193 500 201 479 205 3 503 30 509 13 508 132 273 113 305 175 291 152 65 142 36 164 50 436 154 444 159 461 164 297 605 311 608 316 598 368 156 356 160 370 153 353 168 372 157 355 162 431 349 412 328 406 313 471 175 499 193 489 187 25 378 38 355 39 333 271 298 243 287 259 279 500 201 496 212 479 205 30 509 4 499 20 511 154 464 134 475 147 465 161 582 141 575 150 581 118 398 140 388 126 367 43 9 34 23 45 16 2 556 14 546 1 541 260 614 257 621 263 619 256 616 261 607 264 611 73 324 54 356 55 369 413 250 412 255 447 244 36 536 37 519 19 527 258 19 310 89 366 69 473 234 465 224 479 205 438 241 459 233 465 224 458 223 477 211 466 216 47 513 23 497 42 506 158 457 180 440 176 448 465 224 459 233 466 216 241 268 132 273 305 281 11 537 10 538 1 541 146 267 123 264 160 265 373 107 366 69 349 106 93 274 116 278 105 292 449 237 474 220 435 228 2 556 1 541 0 548 244 341 270 322 236 304 15 532 26 555 0 548 39 498 12 493 25 502 25 378 31 392 38 355 207 286 246 297 235 311 12 493 13 508 16 507 431 349 406 313 411 329 295 312 308 300 348 331 338 260 354 257 339 262 499 193 488 190 500 201 417 383 418 382 391 337 87 306 74 293 93 274 270 322 289 361 293 335 198 639 177 644 187 645 117 285 105 292 116 278 398 118 414 126 423 140 127 56 131 20 114 57 117 561 121 563 98 547 292 112 288 72 282 94 147 465 176 448 154 464 303 100 322 130 301 120 185 277 192 272 207 286 476 183 471 175 489 187 139 481 152 474 149 470 238 79 221 102 214 63 250 415 252 436 231 426 136 473 130 480 112 485 250 415 227 412 300 406 151 73 145 40 138 74 386 53 415 68 392 37 285 371 287 327 297 372 271 298 295 312 287 327 313 137 307 124 292 112 491 202 441 213 448 204 294 599 279 601 285 606 426 51 422 66 463 87 455 88 419 52 415 68 463 232 492 222 493 210 462 239 478 225 492 222 447 244 431 245 454 238 452 231 431 245 445 230 454 238 431 245 452 231 352 376 338 391 330 353 238 79 232 115 221 102 237 35 143 18 399 26 113 305 83 344 100 368 379 303 363 319 385 336 497 203 485 214 490 215 369 379 351 358 362 393 226 39 210 92 240 71 264 109 282 94 268 54 365 134 349 106 357 143 349 106 337 135 357 143 296 360 319 334 302 318 143 18 142 36 104 27 348 331 352 376 330 353 340 145 337 135 313 137 190 83 214 63 184 101 311 348 287 327 295 312 157 343 172 340 175 291 176 448 179 449 154 464 233 363 215 384 231 395 344 141 359 147 342 144 31 514 13 508 30 509 365 134 373 107 349 106 235 311 246 297 263 326 238 79 214 63 237 35 122 49 139 64 119 85 183 133 169 103 184 101 48 12 49 25 32 24 422 66 419 52 443 105 221 102 217 132 201 131 124 342 102 387 126 367 32 24 49 25 36 43 100 368 102 387 124 342 126 367 140 388 157 343 221 102 201 131 184 101 85 28 104 27 92 48 285 606 279 601 273 610 323 302 341 317 347 290 154 464 179 449 170 467 390 246 358 256 354 257 358 256 390 246 389 251 91 269 58 282 80 275 91 269 80 275 120 266 58 282 74 293 47 307 337 135 317 111 307 124 187 296 207 286 205 310 408 77 404 113 383 81 105 292 87 306 93 274 400 314 430 352 428 350 209 30 258 19 163 15 116 278 123 264 128 271 166 283 185 277 187 296 492 222 478 225 496 212 184 101 169 103 165 86 201 131 183 133 184 101 126 367 102 387 118 398 122 49 119 85 101 84 268 54 226 39 240 71 392 37 419 52 384 29 106 41 109 32 76 42 174 270 185 277 166 283 310 89 288 72 317 111 281 401 272 402 283 404 96 566 112 571 86 551 400 314 411 329 393 299 60 21 41 22 76 42 221 102 232 115 217 132 278 435 284 442 250 415 201 430 217 414 200 423 42 294 58 282 47 307 247 61 266 46 269 80 480 217 450 236 475 226 36 536 62 559 67 550 35 525 64 543 50 549 292 592 286 596 304 589 452 231 485 214 481 221 107 0 71 1 75 3 192 272 259 279 243 287 34 649 22 648 28 646 33 656 51 658 42 657 396 330 400 314 241 268 408 77 432 59 433 97 38 355 31 392 54 356 460 191 446 194 437 184 469 189 457 199 484 195 441 213 470 206 430 218 366 69 310 89 349 106 384 29 426 51 401 34 68 316 39 333 38 355 353 168 359 178 361 173 78 289 80 275 51 308 487 207 468 229 481 221 51 659 80 661 42 660 219 339 233 363 236 304 432 59 408 77 399 26 163 15 159 31 189 38 292 592 309 590 313 595 415 68 419 52 392 37 270 322 244 341 274 366 374 166 416 165 409 155 419 52 405 67 384 29 268 54 258 19 226 39 399 26 429 45 432 59 397 151 421 158 376 161 405 67 422 66 384 29 462 104 443 105 478 121 227 412 220 411 228 408 67 76 62 44 92 48 432 59 429 45 457 78 198 639 193 640 177 644 182 638 197 633 173 641 240 71 248 93 268 54 263 326 271 298 277 347 16 507 13 508 31 514 88 539 90 540 114 553 315 416 327 427 318 428 422 66 426 51 384 29 113 305 124 342 153 323 124 342 126 367 153 323 491 202 498 197 470 206 42 662 23 664 27 663 277 615 285 606 273 610 367 315 413 332 375 354 23 497 21 494 33 495 94 280 80 275 78 289 336 438 342 446 353 455 44 512 59 530 46 524 364 254 354 257 338 260 96 566 86 551 67 550 64 543 82 544 95 558 290 82 269 80 266 46 220 411 216 410 230 405 232 407 251 403 230 405 278 435 291 451 284 442 363 319 351 358 369 379 321 417 342 446 336 438 179 449 203 424 199 441 200 423 202 418 183 431 377 138 359 147 360 139 232 407 230 405 216 410 331 359 319 334 314 380 198 639 212 635 208 634 209 30 189 38 226 39 206 630 223 627 197 633 138 574 135 568 151 579 385 336 425 381 410 338 47 513 40 505 23 497 381 122 373 107 365 134 36 536 19 527 32 545 31 514 30 509 35 525 183 431 181 439 169 447 239 624 245 622 223 627 177 585 161 582 150 581 193 640 182 638 178 643 412 255 431 245 447 244 331 459 334 453 346 468 5 504 20 511 4 499 423 140 439 127 456 146 215 433 218 419 231 426 398 118 377 138 371 117 284 397 289 361 274 366 252 386 284 397 274 366 269 80 272 129 251 128 303 100 290 82 306 62 292 592 304 589 309 590 314 380 319 334 296 360 289 361 296 360 302 318 20 511 9 528 10 538 333 591 340 593 309 590 425 170 453 163 442 176 341 317 319 334 331 359 77 529 65 521 87 534 103 14 66 17 72 8 385 180 403 169 425 170 5 504 4 499 17 496 118 486 129 476 134 475 133 479 112 485 119 488 149 470 136 473 133 479 63 522 88 539 59 530 10 538 9 528 17 496 45 665 34 667 41 666 326 452 331 459 318 428 320 422 321 417 325 429 213 5 107 0 108 10 486 196 489 187 491 202 491 202 489 187 498 197 6 510 20 511 5 504 414 126 398 118 383 81 276 600 286 596 292 592 65 521 74 518 87 534 198 325 205 310 212 345 264 109 276 125 282 94 173 95 168 55 151 73 59 530 88 539 76 542 248 625 223 627 245 622 259 279 295 312 271 298 394 150 409 155 427 152 220 411 202 418 216 410 258 19 268 54 288 72 478 121 443 105 455 88 405 67 419 52 422 66 495 208 467 219 470 206 467 219 445 230 428 227 445 373 431 349 411 329 490 215 445 230 467 219 481 221 454 238 452 231 172 340 191 321 175 291 387 288 259 279 241 268 487 207 485 214 497 203 497 203 495 208 498 197 146 267 160 265 174 270 21 494 17 496 12 493 450 236 451 242 475 226 298 420 318 428 299 434 315 416 300 406 312 413 372 157 370 153 355 162 98 547 90 540 77 529 267 609 260 614 279 601 279 601 294 599 286 596 167 584 161 582 178 586 56 516 77 529 63 522 44 512 24 517 17 496 224 628 222 632 208 634 267 609 279 601 275 603 444 159 416 165 456 172 325 429 336 438 332 437 53 4 26 13 43 9 449 237 435 228 380 243 438 241 451 242 381 252 239 624 253 617 245 622 368 156 362 167 356 160 453 163 421 158 436 154 230 405 281 401 228 408 251 403 281 401 230 405 487 207 497 203 499 193 353 168 361 173 372 157 300 406 315 416 298 420 213 5 384 29 401 34 223 627 206 630 239 624 18 523 17 496 24 517 396 330 430 352 400 314 155 580 173 583 151 579 57 535 86 551 82 544 375 354 352 376 348 331 146 267 148 276 128 271 246 297 271 298 263 326 185 277 207 286 187 296 91 269 93 274 58 282 277 347 271 298 287 327 205 310 207 286 235 311 93 274 123 264 116 278 148 276 174 270 166 283 196 70 210 92 226 39 330 353 295 312 348 331 159 31 145 40 168 55 307 124 313 137 337 135 89 33 60 21 76 42 109 32 89 33 76 42 131 20 109 32 106 41 147 465 158 457 176 448 57 535 37 519 36 536 69 2 107 0 213 5 162 466 171 456 180 440 154 464 140 482 134 475 92 48 101 84 67 76 118 486 134 475 140 482 170 365 157 343 154 396 165 86 169 103 152 65 119 488 139 481 133 479 453 163 461 164 471 175 304 589 294 599 316 598 110 567 97 560 86 551 250 415 231 426 229 421 29 500 17 496 21 494 390 246 424 240 389 251 390 246 354 257 388 249 339 262 354 257 358 256 98 547 87 534 105 552 135 568 115 554 121 563 17 496 8 520 7 515 194 636 206 630 197 633 211 47 214 63 190 83 158 457 162 466 180 440 156 463 144 471 130 480 180 440 195 432 203 424 483 185 484 195 488 190 144 471 156 463 158 457 68 316 132 273 70 301 132 273 68 316 73 324 306 62 383 81 378 99 132 273 175 291 305 281 120 266 94 280 132 273 306 62 399 26 383 81 395 351 396 330 241 268 408 77 383 81 399 26 399 26 213 5 401 34 143 18 164 50 142 36 488 190 494 200 500 201 471 175 488 190 499 193 489 187 499 193 498 197 338 604 339 597 316 598 22 531 15 532 18 523 393 299 241 268 400 314 320 422 325 429 332 437 329 443 326 452 318 428 16 507 31 514 25 502 479 205 466 216 477 211 475 226 473 234 479 205 83 344 55 369 50 390 83 344 84 389 100 368 126 367 157 343 153 323 122 49 92 48 104 27 49 25 85 28 62 44 219 339 191 321 204 364 184 101 165 86 164 50 62 44 85 28 92 48 214 63 221 102 184 101 170 365 172 340 157 343 367 315 387 288 413 332 316 598 294 599 297 605 163 15 108 10 131 20 477 211 435 228 474 220 459 233 458 223 466 216 258 19 384 29 213 5 390 246 435 228 424 240 440 123 402 90 451 136 159 31 163 15 131 20 213 5 163 15 258 19 483 185 471 175 472 182 129 476 111 487 125 483 489 187 482 192 476 183 370 153 394 150 397 151 450 91 455 88 415 68 443 105 419 52 455 88 422 66 443 105 462 104 485 214 452 231 445 230 470 206 428 227 430 218 470 206 467 219 428 227 449 237 407 248 413 250 450 91 415 68 386 53 370 153 356 160 355 162 136 473 156 463 130 480 491 202 470 206 441 213 448 204 441 213 420 209 482 192 486 196 446 194 476 183 482 192 460 191 418 198 448 204 420 209 446 194 418 198 417 188 486 196 491 202 448 204 479 205 480 217 475 226 401 34 426 51 434 60 490 215 467 219 495 208 50 390 55 369 35 399 36 43 49 25 62 44 248 93 264 109 268 54 385 336 410 338 379 303 0 548 1 541 15 532 351 358 341 317 331 359 77 529 56 516 65 521 210 92 223 110 240 71 428 350 411 329 400 314 131 20 106 41 114 57 403 169 369 174 376 161 383 81 398 118 378 99 117 285 116 278 128 271 300 406 301 409 312 413 141 575 121 563 137 573 209 30 163 15 189 38 171 456 162 466 156 463 133 479 136 473 112 485 147 465 134 475 129 476 192 272 185 277 160 265 99 490 111 487 129 476 156 463 136 473 149 470 67 550 86 551 57 535 411 329 406 313 393 299 296 445 299 434 318 428 249 626 242 623 234 629 377 138 374 149 359 147 160 265 185 277 174 270 118 576 102 569 95 558 101 570 119 577 96 566 152 474 169 447 149 470 347 290 363 319 379 303 218 419 227 412 229 421 218 419 229 421 231 426 164 50 211 47 190 83 184 101 164 50 190 83 296 360 289 361 291 394 303 100 301 120 290 82 284 442 252 436 250 415 233 363 252 386 244 341 290 82 283 116 269 80 231 395 252 386 233 363 244 341 252 386 274 366 269 80 238 79 247 61 251 128 232 115 269 80 302 318 293 335 289 361 301 120 283 116 290 82 363 179 369 174 403 169 191 321 219 339 236 304 421 158 403 169 376 161 204 450 199 441 215 433 99 565 97 560 111 572 234 629 225 631 212 635 500 201 494 200 493 210 266 46 247 61 237 35 335 444 336 438 345 461 322 130 303 100 324 119 302 318 319 334 323 302 356 478 362 484 346 468 433 97 439 127 414 126 469 114 439 127 433 97 433 97 457 78 469 114 493 98 457 78 434 60 457 78 429 45 434 60 391 337 395 351 241 268 142 36 122 49 104 27 363 319 341 317 351 358 360 139 359 147 344 141 211 47 143 18 237 35 281 401 283 404 300 406 299 434 278 435 280 425 12 493 4 499 3 503 143 18 104 27 103 14 488 190 471 175 483 185 472 182 456 172 439 186 453 163 471 175 442 176 319 334 341 317 323 302 344 141 322 130 324 119 261 607 265 612 275 603 333 263 358 256 340 261 276 600 275 603 286 596 358 256 389 251 365 258 364 254 388 249 354 257 363 319 403 362 385 336 410 181 425 170 442 176 113 305 100 368 124 342 438 241 424 240 459 233 380 243 435 228 388 249 475 226 451 242 473 234 389 251 438 241 381 252 299 434 296 445 291 451 247 61 238 79 237 35 473 234 451 242 465 224 465 224 451 242 438 241 371 117 306 62 378 99 325 429 321 417 336 438 371 117 377 138 360 139 149 470 169 447 156 463 423 140 414 126 439 127 208 634 194 636 193 640 85 28 66 17 103 14 94 280 120 266 80 275 410 338 417 383 391 337 359 178 374 166 361 173 119 488 112 485 96 491 99 490 118 486 95 492 321 417 320 422 312 413 36 536 67 550 57 535 35 525 52 526 64 543 331 459 326 452 334 453 135 568 155 580 151 579 135 568 114 553 115 554 79 6 61 7 81 11 177 585 178 586 161 582 253 617 256 616 264 611 177 644 193 640 178 643 225 631 224 628 208 634 222 632 239 624 206 630 234 629 242 623 225 631 155 580 167 584 173 583 255 620 254 618 242 623 182 638 194 636 197 633 198 639 208 634 193 640 263 619 257 621 242 623 135 568 138 574 114 553 481 221 485 214 487 207 53 4 43 9 71 1 12 493 39 498 33 495 217 414 202 418 200 423 215 433 199 441 203 424 215 384 233 363 219 339 132 273 78 289 70 301 32 24 26 13 48 12 53 4 71 1 69 2 450 236 440 247 451 242 402 90 381 122 451 136 153 323 175 291 113 305 468 229 449 237 447 244 349 106 317 111 337 135 125 483 144 471 129 476 218 419 220 411 227 412 259 279 308 300 295 312 310 89 258 19 288 72 297 605 294 599 285 606 436 154 427 152 444 159 144 471 158 457 147 465 261 607 276 600 264 611 263 619 273 610 260 614 68 316 70 301 39 333 270 322 274 366 289 361 236 304 270 322 305 281 61 7 45 16 41 22 318 428 327 427 329 443 43 651 15 650 34 649 59 530 76 542 46 524 95 558 102 569 84 557 67 550 101 570 96 566 65 521 56 516 40 505 171 456 169 447 181 439 468 229 447 244 454 238 468 229 454 238 481 221 54 356 73 324 68 316 74 518 40 505 47 513 69 2 72 8 53 4 179 449 186 458 170 467 494 200 488 190 484 195 45 16 61 7 43 9 237 35 214 63 211 47 81 11 60 21 108 10 434 60 429 45 401 34 17 496 15 532 1 541 48 12 72 8 66 17 348 331 367 315 375 354 320 422 332 437 327 427 11 537 20 511 10 538 160 265 120 266 241 268 213 5 108 10 163 15 150 581 141 575 137 573 397 151 427 152 436 154 14 546 2 556 32 545 32 545 0 548 26 555 27 501 33 495 42 506 132 273 241 268 120 266 464 171 472 182 471 175 50 549 64 543 84 557 228 408 220 411 230 405 196 70 189 38 168 55 2 556 0 548 32 545 408 77 433 97 404 113 264 611 248 625 245 622 41 653 34 655 28 652 43 564 26 555 15 532 179 449 199 441 186 458 199 441 204 450 186 458 201 430 200 423 183 431 15 650 22 648 34 649 345 461 336 438 353 455 33 357 39 333 51 308 258 19 209 30 226 39 461 164 464 171 471 175 75 3 79 6 107 0 334 453 335 444 343 460 340 261 358 256 365 258 335 444 332 437 336 438 298 420 315 416 318 428 386 53 402 90 450 91 61 7 79 6 75 3 346 468 362 484 351 477 396 330 441 375 430 352 103 14 72 8 213 5 463 232 462 239 492 222 409 155 416 165 444 159 474 220 487 207 477 211 386 53 392 37 258 19 114 553 90 540 115 554 399 26 266 46 237 35 484 195 483 185 469 189 242 623 224 628 225 631 197 633 223 627 210 637 306 62 371 117 324 119 402 90 386 53 366 69 373 107 402 90 366 69 350 472 343 460 345 461 375 354 413 332 407 377 439 186 469 189 472 182 409 155 444 159 427 152 379 303 241 268 347 290 8 520 20 511 7 515 379 303 410 338 382 320 9 528 20 511 8 520 7 515 20 511 6 510 281 401 300 406 228 408 300 406 298 420 250 415 280 425 250 415 298 420 29 500 40 505 17 496 12 493 3 503 13 508 370 153 372 157 394 150 306 62 266 46 399 26 193 640 194 636 182 638 218 419 203 424 220 411 161 582 155 580 141 575 339 597 328 594 316 598 333 591 304 589 328 594 262 613 256 616 253 617 260 614 267 609 254 618 203 424 202 418 220 411 333 263 339 262 358 256 206 630 208 634 222 632 328 594 304 589 316 598 286 596 294 599 304 589 239 624 222 632 224 628 253 617 254 618 267 609 161 582 167 584 155 580 141 575 155 580 135 568 141 575 135 568 121 563 90 540 63 522 77 529 182 588 167 584 178 586 56 516 44 512 40 505 98 547 115 554 90 540 44 512 17 496 40 505 98 547 121 563 115 554 8 520 17 496 9 528 17 496 6 510 5 504 346 468 350 472 356 478 18 523 15 532 17 496 6 510 17 496 7 515 413 332 387 288 412 328 20 511 37 519 30 509 260 614 254 618 255 620 195 432 202 418 203 424 30 509 37 519 52 526 421 158 425 170 403 169 350 472 345 461 355 469 180 440 181 439 195 432 57 535 82 544 52 526 253 617 264 611 245 622 180 440 171 456 181 439 86 551 97 560 82 544 249 626 263 619 242 623 356 478 350 472 355 469 111 572 97 560 110 567 156 463 162 466 158 457 110 489 130 480 125 483</p>
        </triangles>
      </mesh>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <originalMayaNodeId>wrist_flex_MShape</originalMayaNodeId>
          <double_sided>1</double_sided>
        </technique>
      </extra>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="VisualSceneNode" name="wrist_flex1b_med">
      <node id="wrist_flex_M" name="wrist_flex_M" type="NODE">
        <translate sid="translate">0 0 0</translate>
        <rotate sid="rotateZ">0 0 1 0</rotate>
        <rotate sid="rotateY">0 1 0 0</rotate>
        <rotate sid="rotateX">1 0 0 0</rotate>
        <scale sid="scale">0.101 0.101 0.101</scale>
        <instance_geometry url="#wrist_flex_MShape">
          <bind_material>
            <technique_common>
              <instance_material symbol="lambert2SG" target="#lambert2">
                <bind_vertex_input semantic="TEX0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="TEX1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADAMaya">
            <originalMayaNodeId>wrist_flex_M</originalMayaNodeId>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#VisualSceneNode"/>
  </scene>
</COLLADA>
