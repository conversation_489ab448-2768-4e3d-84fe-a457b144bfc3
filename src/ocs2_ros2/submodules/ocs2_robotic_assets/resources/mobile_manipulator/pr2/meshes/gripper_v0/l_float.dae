<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>WakiMudi</author>
      <authoring_tool>OpenCOLLADA2009 x64</authoring_tool>
      <comments>
			ColladaMaya export options: 
			bakeTransforms=0;relativePaths=0;copyTextures=0;exportTriangles=0;exportCgfxFileReferences=1;
			isSampling=0;curveConstrainSampling=0;removeStaticCurves=1;exportPolygonMeshes=1;exportLights=1;
			exportCameras=1;exportJointsAndSkin=1;exportAnimations=1;exportInvisibleNodes=0;exportDefaultCameras=0;
			exportTexCoords=1;exportNormals=1;exportNormalsPerVertex=1;exportVertexColors=1;exportVertexColorsPerVertex=1;
			exportTexTangents=0;exportTangents=0;exportReferencedMaterials=0;exportMaterialsOnly=0;
			exportXRefs=1;dereferenceXRefs=1;exportCameraAsLookat=0;cameraXFov=0;cameraYFov=1;doublePrecision=0
		</comments>
      <source_data>file:///C:/Users/<USER>/Documents/maya/projects/willow_textures/scenes/float2a_med2_hard.mb</source_data>
    </contributor>
    <created>2010-04-30T15:30:23</created>
    <modified>2010-04-30T15:30:23</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Y_UP</up_axis>
  </asset>
  <library_materials>
    <material id="lambert4" name="lambert4">
      <instance_effect url="#lambert4-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="lambert4-fx">
      <profile_COMMON>
        <newparam sid="file6-surface">
          <surface type="2D">
            <init_from>file6</init_from>
          </surface>
        </newparam>
        <newparam sid="file6-sampler">
          <sampler2D>
            <source>file6-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="file5-surface">
          <surface type="2D">
            <init_from>file5</init_from>
          </surface>
        </newparam>
        <newparam sid="file5-sampler">
          <sampler2D>
            <source>file5-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="file6-sampler" texcoord="TEX0">
                <extra>
                  <technique profile="OpenCOLLADAMaya">
                    <blend_mode>NONE</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <transparent opaque="RGB_ZERO">
              <color>0 0 0 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </lambert>
          <extra>
            <technique profile="OpenCOLLADAMaya">
              <bump>
                <texture texture="file5-sampler" texcoord="TEX1">
                  <extra>
                    <technique profile="OpenCOLLADA3dsMax">
                      <amount>1</amount>
                      <bumpInterp>1</bumpInterp>
                    </technique>
                    <technique profile="OpenCOLLADAMaya">
                      <blend_mode>NONE</blend_mode>
                    </technique>
                  </extra>
                </texture>
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="file6" name="file6">
      <init_from>l_float_color.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file6</originalMayaNodeId>
        </technique>
      </extra>
    </image>
    <image id="file5" name="file5">
      <init_from>l_float_normals.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file5</originalMayaNodeId>
        </technique>
      </extra>
    </image>
  </library_images>
  <library_geometries>
    <geometry id="float_MShape" name="float_MShape">
      <mesh>
        <source id="float_MShape-positions" name="float_MShape-positions">
          <float_array id="float_MShape-positions-array" count="1506">-0.02751475 -1.69065e-4 0.01689573 -0.0275 0 -0.0915 -0.0275 0 0.0915 -0.0271918 -0.004533265 -0.0277604 -0.02704087 0.005663494 3.85838e-4 -0.02692083 -0.006257891 0.02788499 -0.0266204 0.007283397 -0.09150006 -0.0266204 0.007283405 0.09150014 -0.02626347 -0.008266798 0.09149991 -0.026005 -0.00921524 -0.09149952 -0.02513386 -0.01142378 -0.01675652 -0.02388843 0.01391518 -0.0750259 -0.02266942 0.01571442 0.0914985 -0.02052745 -0.01868599 -0.09150062 -0.01914877 -0.02012698 0.01380606 -0.01908379 -0.01999225 0.09150296 -0.01828574 0.02196686 -0.09149879 -0.01746902 0.0214011 0.03733867 -0.01623015 0.02247811 -0.08108905 -0.01337129 0.02423923 0.09149546 -0.01176653 -0.02500216 -0.02079004 -0.01148125 -0.02498862 0.03822031 -0.01108055 -0.02509269 0.09149987 -0.01042235 0.0254885 -0.08057584 -0.009356776 0.02614118 0.08547336 -0.006892176 0.02671485 0.004220192 -0.006869795 -0.02669593 -0.09149893 -0.004004715 -0.02720577 0.0915 -0.003576191 -0.02756543 0.08852302 -0.001621969 0.02745213 -0.0915 -0.001621969 0.02745213 0.0915 -0.001616859 0.02744953 -0.0508375 -0.00154305 0.02746157 0.02653738 -0.001330639 -0.02762406 -0.08589033 2.48305e-4 -0.02784327 -0.001646448 0.001510921 0.02785884 -0.008565506 0.004707708 0.02888057 -0.0915 0.004707708 0.02888057 0.0915 0.004711619 0.02888209 0.05096849 0.004724717 0.02888751 -0.03556418 0.006152664 -0.02704152 0.09150206 0.006709563 -0.0268216 -0.09149775 0.01045818 0.002210309 -0.0915 0.01055191 -0.02568455 0.03527806 0.01165371 -0.02517018 -0.03705889 0.01288628 -0.0244301 9.97351e-4 0.01794171 -0.02084095 -0.0915 0.01794171 -0.02084095 0.0915 0.01801239 -0.02080624 0.02590523 0.02348093 -0.01746127 -0.0915 0.02348093 -0.01746127 -0.02747153 0.02348093 -0.01746127 0.03655694 0.02348093 -0.01746127 0.0915 0.03519615 0.01965276 -0.0915 0.03519615 0.01965276 0.135 0.03519615 0.01965276 0.1309405 0.03519615 0.01965277 -0.135 0.03519615 0.01965277 0.0915 0.03562357 0.0197602 0.1388796 0.03864338 0.02051736 0.1466943 0.04045949 0.02097466 -0.1488836 0.04526243 0.02218302 0.0915 0.04613224 0.02240218 0.152999 0.04646008 -0.00815234 -0.0915 0.04646008 -0.00815234 0.002067938 0.04646008 -0.00815234 0.0915 0.05163043 0.02378368 -0.1547402 0.05321844 -0.0248354 -0.135 0.05321844 -0.0248354 0.1350002 0.05321844 -0.0248354 -0.05564733 0.05321845 -0.0248354 0.1221429 0.05321845 -0.0248354 0.0225 0.05365602 -0.02465779 -0.1390351 0.05399467 -0.0245852 0.1410363 0.05478069 0.02457553 -0.155 0.05478069 0.02457553 0.155 0.05582307 -0.02593813 0.06629493 0.05582308 -0.02593816 -0.135 0.05582308 -0.02593816 0.1350002 0.05582308 -0.02593813 -0.08156252 0.05582308 -0.02593813 -0.01245524 0.0558626 -0.02592212 0.1362385 0.05644717 -0.02358453 -0.1465824 0.05819767 -0.02497779 -0.145081 0.05860356 -0.02481175 -0.1225183 0.05860356 -0.02481175 -0.05934479 0.05860356 -0.02481175 -0.02814092 0.05860356 -0.02481175 -0.002105783 0.05860356 -0.02481175 0.02852379 0.05860356 -0.02481175 0.04517861 0.05860356 -0.02481175 0.06279062 0.05860356 -0.02481175 0.0819341 0.05860356 -0.02481175 0.1079692 0.05860356 -0.02481175 0.1225183 0.06012039 -0.0241993 0.1326252 0.06041255 -0.02407888 0.1476699 0.06052683 -0.02245903 0.1511717 0.06128338 -0.02373163 -0.1361049 0.06236385 0.05223726 0.0915 0.06365959 -0.02091613 -0.1530405 0.0651903 -0.03709254 -0.1305109 0.06673984 -0.02151572 -0.1437214 0.06673984 -0.02151572 0.1437214 0.06952443 -0.04927947 -0.1215831 0.06952443 -0.04927947 -0.08778776 0.06952443 -0.04927947 -0.0551923 0.06952443 -0.04927947 -0.01384807 0.06952443 -0.04927947 0.05419245 0.06952443 -0.04927947 0.1215831 0.07009061 -0.02015831 0.1528113 0.07054745 -0.04896863 0.1299978 0.07175518 -0.01732611 -0.155 0.07175518 -0.01732611 0.155 0.07233718 -0.05403011 -0.01867555 0.07242828 -0.05414118 -0.1242444 0.07250611 -0.01917979 -0.153 0.07250611 -0.01917978 0.153 0.07306175 -0.04792421 -0.1363921 0.07327137 -0.05516705 0.05850438 0.07352854 -0.05366399 0.1304542 0.07555975 -0.04661008 0.1407991 0.07571838 -0.05428419 -0.1347005 0.0765068 -0.01756244 -0.1506967 0.07723461 -0.01726611 0.1509568 0.07796832 -0.05895126 0.1224915 0.07884194 -0.01661312 -0.153 0.0791195 -0.05928748 -0.123515 0.08011653 -0.05987759 -0.02485906 0.08080835 0.05970918 -0.0915 0.0808112 -0.01581536 0.153 0.08087528 -0.05125929 0.1429345 0.08235842 -0.01518859 -0.1521836 0.0824489 -0.06060084 0.0975327 0.08334064 -0.0126328 -0.155 0.08376739 -0.04294211 -0.1483672 0.08426204 -0.05948816 0.1331363 0.08438527 -0.05906692 -0.1347677 0.08573828 -0.0496707 -0.1463996 0.08582816 -0.061081 -0.0426941 0.08622728 -0.06089622 -0.1258672 0.08639172 -0.01355468 0.1525 0.08639173 -0.01355468 -0.1525 0.0868812 -0.06110727 -0.1215832 0.08711349 -0.06111646 -0.1215832 0.08711689 -0.06111657 0.1215831 0.0876944 -0.06114458 -0.1171852 0.08776628 -0.01299784 -0.153 0.08797845 -0.04098799 0.1502485 0.09045474 -0.01190874 0.153 0.09095505 -0.06101374 -0.1107358 0.0915618 -0.0609861 0.03796055 0.09265771 -0.04832208 0.1489237 0.09282844 -0.01094715 -0.1525 0.09291366 -0.06065249 0.118487 0.09305599 -0.01085497 0.1525 0.09420364 -0.05958175 -0.1320456 0.09506457 -0.01004128 -0.153 0.09660021 -0.05782939 0.1366768 0.0967425 -0.03674493 -0.1516137 0.0967425 -0.03674493 0.1516137 0.09796197 -0.05834661 -0.1215832 0.09796197 -0.05834661 0.1215832 0.09796209 -0.05834683 0.03679491 0.09796543 -0.05834534 -0.01159852 0.09796766 -0.05834458 0.1119903 0.09821109 -0.0581995 -0.1240111 0.0982117 -0.05819914 0.124014 0.09862029 -0.05339562 -0.1433617 0.1006759 -0.04368292 -0.1497175 0.1009988 -0.00763731 0.153 0.1013103 -0.04483181 0.1492283 0.1016994 -0.007353507 -0.153 0.1034084 -0.05517196 -0.1309824 0.1034101 -0.055171 0.130983 0.1040088 -0.0495204 -0.1446707 0.105744 -0.05253401 0.1391797 0.1061721 -0.05302124 -0.1358926 0.1061905 -0.005534136 0.1525 0.1063355 -0.05350138 -0.1316278 0.106339 -0.05349938 0.1316259 0.1066174 -0.05332305 -0.01514327 0.1071451 -0.002989524 -0.155 0.1079897 -0.004805255 -0.1525 0.1090459 -0.00221953 0.155 0.1095627 -0.004168063 0.153 0.1136088 -0.04945891 0.1316627 0.113642 -0.002515508 -0.153 0.1159064 -0.00159818 0.1525 0.1164371 -0.0479229 -0.1316767 0.1196835 -6.80948e-5 0.153 0.1205845 2.9691e-4 -0.1525 0.1229579 0.001258398 -0.153 0.1233616 0.04181403 -0.155 0.1263421 0.002629328 0.1525 0.1271966 -0.0281916 -0.1503761 0.1290423 -0.03605736 -0.144842 0.1292209 0.003795537 -0.1525 0.1326831 0.005198086 0.153 0.1330141 -0.03699167 0.1411707 0.1332145 -0.03869237 -0.1364893 0.1343866 0.005888203 -0.153 0.1349784 0.006127955 0.1525 0.135119 -0.03826122 -0.1317404 0.1354485 -0.02385543 0.1505534 0.1364638 -0.03753082 0.1320905 0.1393672 0.01006373 0.155 0.1394577 0.0101004 -0.155 0.1400163 0.008168821 -0.1525 0.1435418 0.009597002 0.153 0.1445969 -0.03374728 -0.04591804 0.1446944 0.01006391 -0.153 0.1490126 0.01181322 0.1525 0.1522512 0.01312521 -0.1525 0.1529709 0.01341676 -0.153 0.1560234 -0.02374394 0.1445048 0.1572614 0.01515483 0.153 0.159568 -0.02716612 0.007409309 0.1613759 -0.005510215 -0.1517772 0.1613759 -0.005510214 0.1517772 0.1616073 0.01691539 -0.1525 0.1625736 -0.02590836 0.1317874 0.1625987 -0.02588372 -0.02738857 0.1625987 -0.02588372 0.03695892 0.1630706 -0.01010419 -0.1509777 0.1632942 -0.02560957 -0.1317875 0.1637664 0.01779005 0.1525 0.1656374 -0.02462015 -0.02326972 0.1656374 -0.02462015 0.07268733 0.1666452 0.01895626 -0.153 0.1671587 -0.02399306 -0.07356486 0.1673529 -0.02287956 -0.1373617 0.1674092 -0.02297513 0.1373113 0.1684438 0.01968489 0.153 0.1686229 -0.02340011 -0.1317894 0.1686229 -0.02340011 0.1317894 0.1686838 -0.02337544 0.01235525 0.1686839 -0.02337543 0.1186104 0.1695075 0.02227366 0.155 0.1701619 -0.02277948 -0.1317893 0.1703761 -0.02269351 0.1317893 0.171738 -0.02214965 -0.009061517 0.171738 -0.02214965 0.06033247 0.1719498 -0.02206531 0.1202544 0.1720874 -0.01779387 -0.1435694 0.1724028 0.02128867 -0.1525 0.1732669 -0.02154151 -0.05866177 0.1747996 -0.02094287 0.03273788 0.1747996 -0.02094287 0.1186036 0.1748145 -0.02098091 -0.1317864 0.1748336 -0.02092958 -0.1317865 0.1752816 0.02245488 0.1525 0.1753341 0.02247617 0.153 0.1754034 -0.0207235 -0.1317871 0.1754812 0.02469364 -0.155 0.1755636 -0.02063768 -0.1284912 0.1776523 -0.01983813 -0.1317851 0.1777244 -0.01981044 -0.131785 0.1778337 -0.01977775 -0.1317851 0.1778687 -0.01975511 -0.09430936 0.1778687 -0.01975511 -0.03034909 0.1778687 -0.01975511 0.05363592 0.1778724 -0.01976463 -0.1317849 0.1780244 -0.01969543 -0.1317859 0.1788801 0.02391264 -0.153 0.1796183 -0.01908457 0.1317842 0.1808004 -0.01864064 -0.1317831 0.1809451 -0.01858639 -0.04550924 0.1809451 -0.01858639 0.02933712 0.1840287 -0.01743679 -0.08403597 0.1840287 -0.01743679 -8.2774e-4 0.1840287 -0.01743679 0.128476 0.1842778 0.02609929 0.1525 0.1855723 -0.01686678 0.07118335 0.1857172 0.02668239 -0.1525 0.1864644 -0.006816845 -0.1483986 0.1871194 -0.01630636 -0.04843955 0.1871194 -0.01630636 0.01801785 0.1871224 -0.01631433 -0.1317757 0.1879207 0.02757504 0.153 0.1892264 0.0583698 0.0915 0.1892787 0.02812515 -0.153 0.1902167 -0.01519381 0.1317701 0.1902169 -0.01519514 -0.08915135 0.1902169 -0.01519514 9.91504e-5 0.1902169 -0.01519514 0.06464363 0.190972 1.94581e-4 0.1509061 0.192614 -0.00721414 0.1467246 0.1933214 -0.01410314 -0.04714943 0.1933214 -0.01410314 0.03628226 0.1933214 -0.01410314 0.1251697 0.1948746 -0.01355997 -0.1317621 0.1953938 0.03276027 0.155 0.1954332 0.03061835 -0.1525 0.1954332 0.03061835 0.1525 0.1964326 -0.01303043 -0.08708421 0.1964326 -0.01303043 0.06855008 0.1964326 -0.01303043 -0.003708715 0.1984725 0.03184961 0.153 0.1995504 -0.01197707 0.06085308 0.2008064 0.008633132 -0.1517294 0.2008426 -0.01153963 0.1317477 0.2016324 0.03312968 -0.153 0.2040695 0.03411698 -0.1525 0.2040695 0.03411698 0.1525 0.2063269 0.03503145 0.153 0.207089 -0.008401978 0.1382373 0.2103702 0.03666939 -0.153 0.2109815 -0.007290633 -0.1381434 0.2127059 0.0376156 0.1525 0.2136541 0.03799972 -0.1525 0.2153238 -0.006892177 -0.1316936 0.2157368 0.03884342 0.153 0.2165561 0.0391753 -0.153 0.2194698 0.04251354 -0.155 0.2208169 0.04090139 -0.1525 0.2208169 0.04090139 0.1525 0.2210781 -0.00511466 0.002039531 0.2248502 0.0425353 0.1521836 0.2261455 0.06764976 -0.155 0.2261455 0.06764976 0.155 0.2274837 0.04360214 -0.153 0.2285393 0.04402975 0.153 0.2295247 0.01704555 -0.1516137 0.2295247 0.01704555 0.1516137 0.2318533 0.04537109 -0.1503219 0.2318645 0.008060088 0.1492285 0.2320317 0.007413979 -0.1487168 0.2331349 0.04584393 0.1496935 0.2337096 7.67442e-4 -0.1416041 0.2339516 0.04838018 -0.155 0.2339516 0.04838018 0.155 0.2339918 1.23374e-4 0.1404907 0.2342952 -0.001663989 -0.1316259 0.2342986 -0.001663106 0.1316278 0.2347025 0.04652651 0.153 0.2347025 0.0465265 -0.153 0.2347562 0.06981465 -0.1531057 0.2369782 0.02474929 0.1506726 0.237118 0.04750503 -0.1528113 0.2375608 -8.26032e-4 -0.1309833 0.2375633 -8.25413e-4 0.1309824 0.2384519 0.1235711 -0.0915 0.2385308 0.01080779 -0.1493127 0.240443 0.0511719 -0.1539862 0.2404688 0.04886244 -0.1437214 0.2404688 0.04886244 0.1437214 0.2416758 0.07155319 -0.1473603 0.2418987 0.01439582 0.149198 0.2421386 0.02125131 -0.1485318 0.2426061 0.05177672 0.1528662 0.2429776 0.0718669 0.1468941 0.2434016 6.17749e-4 -0.124014 0.2434023 6.17913e-4 0.1240111 0.2436741 6.82599e-4 0.02878421 0.2436791 6.84179e-4 -0.05119582 0.2436835 6.85665e-4 -0.1215832 0.2436835 6.85665e-4 0.1215832 0.2437631 7.33931e-4 0.0863308 0.2447589 0.00205923 -0.1355881 0.2448517 0.007806957 0.1441457 0.2452935 0.07246181 -0.1398887 0.2454227 0.05085396 0.1371778 0.24573 0.07257253 -0.135 0.24573 0.07257253 -0.0915 0.24573 0.07257253 0.03307344 0.24573 0.07257253 0.0915 0.24573 0.07257253 0.135 0.2467965 0.05142593 -0.1476689 0.246978 0.01323544 -0.1457204 0.2470894 0.05154324 -0.1326253 0.2479204 0.003260314 0.1337786 0.2484877 0.002340386 -0.1210366 0.2486051 0.05215847 -0.1225183 0.2486051 0.05215847 -0.02124926 0.2486051 0.05215847 0.06596126 0.2486051 0.05215847 0.1102664 0.2486051 0.05215847 0.1225183 0.248848 0.0522541 0.1450249 0.2490043 0.00265595 0.05096789 0.249499 0.002904271 -0.03559514 0.2496733 0.002978838 0.008528702 0.2497239 0.00304555 -0.08935748 0.2501688 0.05475486 -0.1454572 0.2511749 0.05540858 0.1436506 0.2512752 0.02492861 0.1411024 0.2513461 0.05326883 -0.1362385 0.2513856 0.05328485 -0.1350014 0.2513856 0.05328486 -0.06649664 0.2513856 0.05328486 -0.01677544 0.2513856 0.05328486 0.03395022 0.2513856 0.05328486 0.09271162 0.2513856 0.05328486 0.1349998 0.2514709 0.02468222 -0.1411278 0.2524883 0.05588948 -0.09126725 0.2524883 0.05588948 -0.03557517 0.2524883 0.05588949 0.02413836 0.2524883 0.05588949 0.09018434 0.2524883 0.05588949 -0.135 0.2524883 0.05588949 0.135 0.2532199 0.005791913 0.09598684 0.2533987 0.006244733 -0.1215831 0.2534011 0.006247182 0.1215832 0.2535615 0.006415454 0.1215832 0.2537762 0.006395991 0.05292521 0.2541294 0.00724449 0.1254904 0.2547021 0.007716634 0.1187751 0.2547584 0.007723826 -0.05141915 0.2549569 0.01540452 -0.1356784 0.2554744 0.009011617 -0.129758 0.2560555 0.009341162 8.45528e-4 0.2561476 0.009442734 -0.08977946 0.2565995 0.01306777 0.1316125 0.2568427 0.02650314 -0.1299845 0.2569504 0.02660988 0.1295193 0.2576785 0.01255726 0.1219565 0.2577886 0.02699955 0.04768682 0.2577887 0.02699914 -0.00990518 0.2577926 0.02698856 -0.1215831 0.2577926 0.02698856 0.1215831 0.2579193 0.02663768 -0.06600362 0.2581456 0.01380783 0.07053776 0.2582253 0.01402357 0.02757355 0.2582489 0.01441457 -0.1204421 0.258304 0.01431635 -0.04432146 0.2586574 0.02404932 -0.03782295 0.2590051 0.02013076 -0.1215831 0.2590051 0.02013076 0.1215832 0.2590077 0.02024302 -0.07910684 0.2590187 0.0219534 0.01589075 0.2989169 0.0941187 -0.0915 0.2989169 0.0941187 0.0915 0.3057052 0.09668282 0.003519459 0.3148085 0.0994528 -0.09149989 0.3159935 0.1188308 -0.0915 0.3185269 0.1003265 -0.03760693 0.3203103 0.1008352 0.09150002 0.3410678 0.1030206 -0.09150002 0.3428104 0.1658501 0.02461187 0.3428114 0.1658474 -0.0915 0.3428114 0.1658474 0.0915 0.3428114 0.1658474 -0.0665686 0.3454135 0.1667365 -0.0915 0.3457182 0.1030342 0.09150013 0.3480814 0.1673334 0.0915 0.3491542 0.167634 -0.003811536 0.3519813 0.1677265 -0.09150001 0.3534844 0.1676656 0.09150002 0.3568992 0.1673889 -0.004437531 0.3586377 0.1020137 0.08503246 0.3588248 0.1667803 0.0915 0.3590731 0.1667816 -0.09150002 0.3614173 0.1659126 0.0915 0.3614404 0.1659053 0.03703572 0.3639143 0.164761 -0.0915 0.3639143 0.164761 0.0915 0.3639144 0.1647611 0.02675558 0.3647483 0.1011506 -0.09149973 0.3724237 0.09962764 0.09149946 0.3848897 0.0958404 -0.09150074 0.3891383 0.09424534 0.008485302 0.4002111 0.08890409 -0.0915 0.4002111 0.08890409 0.0915 0.419986 0.07841308 -0.0915 0.419986 0.07841308 0.0915 0.4215961 0.07757568 0.008147961 0.4243671 0.07652039 0.04900608 0.4259067 0.07601617 -0.02869482 0.4294115 0.0754241 0.09149949 0.4314654 0.07532558 0.01426744 0.4320827 0.07513481 -0.0915005 0.4398998 0.07664258 0.09149482 0.4416248 0.07739559 0.002511892 0.4434187 0.1225822 -0.0915 0.4434187 0.1225822 0.0915 0.4437078 0.1224269 0.01362012 0.4437712 0.1223962 -0.03600347 0.4445984 0.07908243 -0.0915 0.4483946 0.08181927 -0.001140707 0.4485677 0.119152 0.02289824 0.4487148 0.1190291 -0.007006993 0.4487632 0.1189805 -0.0312808 0.4491142 0.1186613 -0.07313195 0.449649 0.08311368 0.09150003 0.4504971 0.1175032 0.06684556 0.4522277 0.08585585 -0.09150144 0.4525977 0.1144107 0.007512986 0.4532116 0.1140766 0.09150204 0.4532161 0.0875723 -0.01950558 0.4537833 0.08866577 0.09150012 0.4542884 0.1114992 -0.06268198 0.4544581 0.08958005 0.03562349 0.4544684 0.1116413 -0.0914999 0.4559973 0.1069332 0.02141523 0.4560539 0.1067121 0.06995835 0.4560819 0.09423599 0.001698636 0.4561113 0.1064359 -0.01429625 0.4561205 0.09447077 -0.03761075 0.4562935 0.09599859 -0.0915 0.4567023 0.1004976 -0.0915 0.4567023 0.1004976 0.0915 0.4567174 0.1008662 -0.01450372 0.4567265 0.09885258 0.03645722</float_array>
          <technique_common>
            <accessor source="#float_MShape-positions-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="float_MShape-normals" name="float_MShape-normals">
          <float_array id="float_MShape-normals-array" count="1506">-0.9998016 -0.01991894 4.25344e-4 -0.9692 -0.007462935 -0.2461619 -0.9692236 -0.01057821 0.2459549 -0.9835314 -0.1807329 -0.001301714 -0.9735777 0.228356 2.50585e-4 -0.9814388 -0.1917748 6.14754e-4 -0.7740573 0.2745191 -0.5705037 -0.6754745 0.1936899 0.7114903 -0.660269 -0.23531 0.7132139 -0.4830501 -0.1619555 -0.8604841 -0.8987364 -0.4384888 6.48036e-4 -0.8316887 0.5551738 0.008721609 -0.6570685 0.4455597 0.6080605 -0.6296742 -0.6259546 -0.4600992 -0.6728472 -0.7397807 0.001157122 -0.2359468 -0.2370942 0.9423988 -0.483291 0.6236374 -0.6144153 -0.6332969 0.7739089 -3.82723e-4 -0.5522353 0.8332785 0.02613447 -0.3812387 0.7144302 0.5867252 -0.3711437 -0.928575 -9.95077e-4 -0.4537379 -0.8911332 0.00188997 -0.3130383 -0.757859 0.572413 -0.3329985 0.9429047 -0.006536927 -0.3225714 0.9463487 0.01928786 -0.266791 0.9637544 -4.0061e-4 -0.2202277 -0.9030682 -0.3687377 -0.1170634 -0.8462901 0.5197012 -0.1301174 -0.9911391 0.02670062 -0.2055931 0.7949787 -0.5707369 -0.1924822 0.8611323 0.4705336 -0.1951527 0.9807727 3.62158e-4 -0.1671679 0.9859283 -6.5601e-4 -0.01958221 -0.9994702 -0.02599793 0.07650765 -0.9970681 -0.001304446 -0.2186812 0.9757962 5.48108e-4 -0.1927627 0.67925 -0.7081398 -0.210935 0.6737059 0.7082561 -0.2833889 0.9590045 -0.00104866 -0.3161489 0.9487086 0.001393351 0.1264603 -0.6726756 0.7290509 0.1130781 -0.5642888 -0.8177966 0 0 -1 0.3506147 -0.9365196 -6.9011e-4 0.3265656 -0.9451737 0.001230885 0.4480115 -0.8940277 -3.31374e-4 0.491096 -0.7864646 -0.3745641 0.4591447 -0.7658552 0.4501688 0.5407428 -0.8411871 0.001219414 0.3174198 -0.6305425 -0.7082803 0.449903 -0.8930775 0 0.4499519 -0.8930528 0 0.3174464 -0.6306014 0.7082159 -0.2780594 0.1411818 -0.950132 -0.99305 0.1050438 0.05308056 -0.8916508 0.452724 0 -0.8174077 0.5704262 -0.08036472 -0.3574266 0.1814774 0.9161398 -0.7173346 0.6876515 0.1121008 -0.8229635 0.02591286 0.5675029 -0.7829662 -0.02411819 -0.6215966 -0.2180406 0.8674435 0.4472135 -0.4490421 0.435525 0.7801789 -0.6034764 -0.6849449 -0.4082482 -0.3898791 -0.9208661 0 -0.6759619 -0.6495115 0.348153 -0.3373619 0.4741954 -0.8132194 -0.7119777 -0.701284 -0.03589589 -0.7819684 -0.6214287 0.04849528 -0.7125991 -0.7015715 0 -0.6522456 -0.7580077 0 -0.7125989 -0.7015716 0 -0.6852221 -0.6844063 -0.2491158 -0.6390169 -0.6961882 0.3270772 -0.1993833 0.6772515 -0.708221 -0.1852618 0.5961243 0.7812259 0.1290441 -0.9916389 0 0.0131869 -0.9990078 -0.04253794 0.05117918 -0.9986824 0.003748356 -0.007798019 -0.9999695 0 0.0952427 -0.995454 0 0.03174911 -0.9977619 0.05884988 -0.5717185 -0.6190945 -0.5383864 0.06393602 -0.9766723 -0.2049965 -0.5833501 -0.810327 -0.0554323 -0.191076 -0.9815753 0 -0.5428574 -0.8398249 0 -0.3737485 -0.9275302 0 -0.373748 -0.9275302 0 -0.3737484 -0.9275302 0 -0.3737484 -0.9275302 0 -0.5428574 -0.8398249 0 -0.1910738 -0.9815757 0 -0.5388858 -0.8387756 0.0778305 -0.3154099 -0.90971 0.2700822 0.08583909 -0.9720067 0.2187116 -0.3450482 -0.5778687 0.739601 -0.3127426 -0.9076203 -0.2800314 -0.2884291 0.7120136 0.6401916 -0.2056 -0.5579109 -0.8040299 -0.8788074 -0.4133048 -0.2384888 -0.1660877 -0.8521519 -0.4962378 0.05677101 -0.9506046 0.3051687 -0.885923 -0.4394501 -0.1484049 -0.8823529 -0.4705882 0 -0.8969198 -0.4421931 0 -0.8969197 -0.4421935 0 -0.8912584 -0.4534957 2.91838e-4 -0.8623721 -0.5050502 0.0351943 0.2717431 -0.8985774 0.3445494 -0.8578171 -0.4387606 0.2676544 0.06196409 -0.3296294 -0.9420748 0.07463227 -0.4664751 0.8813802 -0.7754116 -0.6314561 0 -0.7829996 -0.6151562 -0.09216493 0.3050536 -0.8997142 -0.3121805 0.3339823 -0.8613113 0.3828819 -0.7648831 -0.4148468 -0.4928042 -0.6845728 -0.7289445 -1.80768e-4 -0.7009433 -0.6423451 0.3099535 -0.6660606 -0.3603007 0.6531054 -0.66752 -0.5990637 -0.4421987 0.1181037 -0.8608786 -0.4949136 0.0765477 -0.8494574 0.5220752 -0.5040363 -0.8612067 0.06534826 0.3468646 -0.8561254 -0.383059 -0.3778113 -0.9251722 -0.03626434 -0.3744917 -0.9272295 0.001143497 -0.2654933 0.6553726 -0.7071069 0.3575366 -0.8825501 0.3054064 -0.4601925 -0.5627098 0.6867172 0.1629063 -0.7185743 -0.6761009 -0.2087896 -0.9779508 -0.004386595 0.1897786 -0.4684693 -0.862856 -0.3739614 -0.2957847 -0.8790133 -0.2352738 -0.8948315 0.3793717 -0.1941257 -0.8970762 -0.3969503 -0.2406002 -0.5724531 -0.7838426 -0.125978 -0.992033 -3.67708e-4 -0.08421981 -0.991816 -0.09595814 0.2080241 -0.6929919 0.6902812 0.2469991 -0.6767668 -0.693526 -0.1358227 -0.9907171 -0.005651316 0.1054033 -0.9940795 -0.02638236 -0.03192794 -0.9968832 0.07214233 -0.02995881 -0.999368 0.01913447 0.3632796 -0.8967486 -0.2527245 -0.2861666 -0.2119382 0.9344468 0.346881 -0.8562874 0.3826819 0.08570108 -0.9962124 0.01470394 0.1682206 -0.9857488 0.00109386 -0.04353961 -0.556756 0.8295342 0.3370183 -0.8319201 -0.440826 0.2593155 -0.9654508 0.02569779 0.314645 -0.7767065 0.5456424 0.2846369 -0.9438537 -0.1676967 0.3338411 -0.8240793 -0.4576498 0.2518375 -0.9108282 0.3270626 0.003046851 -0.1101779 -0.9939073 0.0167212 -0.1225571 0.9923205 0.369572 -0.929048 -0.01692656 0.4581179 -0.8888841 0.003597924 0.4551367 -0.8904215 0 0.4368896 -0.8995151 -2.06952e-4 0.4439005 -0.8960758 -5.97613e-4 0.4345051 -0.9002319 -0.02806906 0.4226384 -0.9053376 0.04172149 0.18923 -0.7728603 -0.6057053 0.1064109 -0.4359528 -0.8936566 0.3575612 -0.8826456 0.3051012 0.2362464 -0.5606643 0.7936265 0.3575639 -0.8826456 -0.3050981 0.4714034 -0.8785243 -0.07729097 0.4828026 -0.8743702 0.04876789 0.3556216 -0.6902866 -0.6301093 0.4298161 -0.8474115 0.3116919 0.4458424 -0.8578072 -0.2557174 0.3146414 -0.7767083 0.5456419 0.480536 -0.8743134 -0.06827299 0.4770892 -0.8707343 0.119196 0.4935487 -0.8697181 0 0.2021029 -0.4988936 -0.842769 0.3146439 -0.7767079 -0.5456408 0.2110475 -0.5209724 0.8270712 0.3575583 -0.8826472 0.3051002 0.470742 -0.8813177 0.04100133 0.3575628 -0.8826451 -0.3051007 0.2700848 -0.6667115 0.6946582 0.473303 -0.8803154 -0.03207802 0.357565 -0.8826438 0.3051018 0.3146413 -0.7767069 -0.5456439 0.3468818 -0.856286 -0.3826843 -0.1209473 0.4811727 -0.8682422 0.3146463 -0.7767048 0.545644 0.1723505 -0.3687721 -0.9134016 0.3355358 -0.6859697 -0.645648 0.3146403 -0.7767066 -0.5456448 0.3468843 -0.8562852 0.3826838 0.3885669 -0.8039545 0.450192 0.4120799 -0.8599437 -0.3011431 0.3575623 -0.8826447 -0.3051028 0.3146459 -0.7767051 0.5456438 0.4375543 -0.8975319 -0.05461285 0.156589 -0.3554798 0.9214739 0.426732 -0.9003966 0.08476934 0.171833 -0.4241694 0.8891309 0.2110472 -0.5209734 -0.8270707 0.2700872 -0.6667094 -0.6946592 0.3468842 -0.8562854 0.3826835 0.4202603 -0.9074034 5.85886e-4 0.3575646 -0.8826439 -0.3051018 0.3146467 -0.7767026 0.5456468 0.3146508 -0.7767033 -0.5456435 0.3575661 -0.8826441 -0.3050998 0.304704 -0.7109786 0.6337704 0.3575605 -0.8826439 0.305107 0.3991742 -0.9168751 -3.64058e-4 0.0239306 -0.05828657 -0.9980131 0.03072305 -0.07615236 0.9966227 0.3146513 -0.7767047 -0.5456412 0.3909706 -0.9192172 0.04670899 0.390207 -0.9207271 2.48996e-4 0.3920183 -0.9199573 -3.0113e-4 0.1071285 -0.2765885 -0.9549986 0.3943692 -0.9174446 -0.05261661 0.3146447 -0.7767025 0.5456483 0.3817939 -0.9242476 0 0.3816198 -0.9243193 0 0.3468845 -0.856286 -0.3826819 0.3782501 -0.9257035 0 0.3652143 -0.9106857 -0.1930545 0.369664 -0.9015529 0.224835 0.3575585 -0.8826457 0.3051041 0.3754117 -0.9250042 -0.05859297 0.3772995 -0.9229231 0.07653651 0.3755271 -0.9268115 0 0.3763439 -0.9264801 0 0.1897785 -0.4684685 0.8628564 0.3671999 -0.9265261 -0.0819376 0.3684674 -0.9283242 0.04945713 0.3703606 -0.9288881 0 0.3695244 -0.929221 0 0.3692735 -0.9293208 -1.24324e-4 0.3126979 -0.8032498 -0.5069613 0.3146442 -0.7767059 -0.5456436 0.3666012 -0.9303782 0 0.3640823 -0.9313669 0 0.3639827 -0.9314057 -4.03418e-4 0.1698762 -0.4312402 -0.8861004 0.04670376 -0.1214971 0.9914924 0.270083 -0.6667125 0.694658 0.3575618 -0.8826456 0.3051004 0.3491216 -0.8906102 0.2914234 0.1897788 -0.4684698 -0.8628557 0.3641573 -0.9313325 0.00305605 0.1153553 -0.3289796 0.9372649 0.05616099 -0.1558695 -0.9861798 0.1087582 -0.2837666 0.9527057 0.3578107 -0.9337941 0 0.358315 -0.9336007 0 0.3568411 -0.9341651 0 0.2596446 -0.6623546 -0.7027597 0.2502634 -0.6466164 -0.7205938 0.3575619 -0.8826451 -0.3051017 0.3524597 -0.9347172 0.04556045 0.3509269 -0.9355758 -0.03934683 0.3522543 -0.9359044 0 0.3517295 -0.9361016 0 0.3459964 -0.9382359 0 0.3462559 -0.9381401 0 0.3475117 -0.9376742 -0.001648387 0.314649 -0.7767072 0.5456391 0.3429919 -0.9393383 0 0.3146452 -0.7767071 -0.5456414 0.2139991 -0.5748944 -0.7897472 0.3406104 -0.9402045 0 0.3400927 -0.9403918 0 0.3393154 -0.9398395 -0.0395834 0.3468853 -0.8562848 0.3826838 -0.1723762 0.6857743 0.7071069 0.3468825 -0.856287 -0.3826814 0.3357167 -0.9414511 0.03104844 0.3349282 -0.9422436 1.1319e-4 0.334746 -0.9423084 0 0.3350034 -0.942217 0 0.1058677 -0.3015243 0.9475628 0.2192387 -0.6449913 0.732066 0.3286204 -0.9444622 0 0.3288907 -0.944368 0 0.3274464 -0.9448696 -6.9426e-4 0.3241947 -0.9452249 -0.03804657 0.2021027 -0.4988925 0.8427697 0.2699852 -0.6666198 -0.6947849 0.3146445 -0.7767051 0.5456446 0.3148486 -0.949142 0 0.3229784 -0.9464064 0 0.3182474 -0.9480076 1.29259e-4 0.35756 -0.8826447 0.3051049 0.3125994 -0.9498851 2.7825e-4 0.04394073 -0.1216292 -0.9916024 0.3090401 -0.9499217 0.04629292 0.3575623 -0.882644 -0.3051047 0.2701423 -0.6665899 -0.6947526 0.3146401 -0.7767081 0.5456429 0.3575588 -0.8826464 0.3051018 0.305546 -0.9093615 0.2823176 0.3575639 -0.8826461 -0.3050964 0.3037599 -0.9311432 -0.2017482 0.3146379 -0.7767082 0.5456441 0.3146485 -0.7767099 -0.5456356 0.2916434 -0.955038 -0.05335413 0.3468833 -0.8562844 0.3826863 0.3575653 -0.8826453 -0.3050969 0.1897782 -0.4684706 -0.8628555 0.3606761 -0.7596371 -0.5411692 0.3108571 -0.6536853 0.6899735 0.2765161 -0.9610093 -1.01616e-4 0.3792613 -0.622035 0.6850061 -0.03725526 0.4670373 -0.8834524 -0.06204683 0.653836 0.754088 0.3468633 -0.8562409 -0.3828019 0.3625412 -0.8959298 0.2566585 0.1378254 -0.06587137 -0.9882637 0.06119772 -0.08114251 0.994822 0.4850408 -0.6837796 -0.5451431 0.1958938 -0.4776599 0.8564267 0.1523797 -0.4582366 -0.875671 0.5801054 -0.547455 0.6031342 0.2869561 -0.8036586 -0.521334 0.2381084 -0.4385048 -0.866613 0.2525015 -0.4434597 0.8599921 0.2904734 -0.8538768 0.4318792 0.2612341 -0.9603606 -0.09728405 0.2712854 -0.9575325 0.09765159 0.4135912 -0.8527825 0.3189113 0.3596753 -0.8509818 -0.3827056 0.3599098 0.4771893 -0.8017203 0.2970986 0.05135779 0.9534646 0.4212732 -0.8347245 -0.3546322 0.2740081 -0.9596805 -0.06271173 0.2782921 -0.9567991 0.08419536 -0.2654928 0.6553684 -0.7071108 0.3356553 -0.4549732 -0.8248242 0.4622225 -0.2011506 -0.8636485 0.7076227 -0.4965866 -0.5026648 0.6644405 -0.636851 0.391075 0.03486289 0.9750921 -0.2190435 0.4079786 -0.1655187 0.8978624 0.4584244 0.06115858 -0.8866265 0.5565706 -0.2137262 0.8028389 0.5469293 0.5906334 0.5933132 0.3138081 -0.9485855 -0.04135276 0.3493403 -0.9360864 0.04127164 0.3110336 -0.9503989 -1.54875e-4 0.311193 -0.9503467 -1.92239e-4 0.2918877 -0.9564508 -0.001829798 0.4035675 -0.9149101 0.008527626 0.3241557 -0.9459993 0.002919071 0.431924 -0.8443028 -0.3171664 0.564135 -0.5019869 0.6555614 0.742788 0.6261258 -0.2371338 0.8426311 -0.4425187 0.306839 0.598368 0.8008672 -0.02381872 0.5065171 0.6245641 -0.594441 0.9969008 -0.07866908 0 0.5468162 0.5537763 0.6279522 0.6734378 0.7345847 0.08286554 0.565522 -0.7844319 -0.2546599 0.5997543 -0.2509099 -0.7598282 0.8030178 -0.5504453 -0.2284129 0.5309401 -0.8212468 0.208941 0.4788028 -0.8761504 -0.05575386 0.9586912 -0.278865 -0.05608391 0.9139193 -0.405896 0 0.9139225 -0.4058888 0 0.9139229 -0.4058878 0 0.9104418 -0.4058358 0.07995626 0.5799202 -0.7901682 0.1983097 0.482369 -0.87596 0.003769086 0.5668914 -0.8237921 8.34654e-4 0.5242472 -0.8515649 -0.001526638 0.5472662 -0.8369582 7.97775e-4 0.8499417 -0.01239176 -0.526731 0.9299299 -0.04981779 0.3643467 0.729037 0.08894806 0.6786702 0.7211183 -0.6876368 -0.08452238 0.7016123 -0.7125455 -0.004388195 0.7015702 -0.7126004 0 0.7580072 -0.6522461 0 0.7015737 -0.712597 0 0.5975145 -0.8018581 0 0.7235078 -0.6890658 0.0415292 0.7675838 0.1455719 -0.6241987 0.9999695 -0.007800004 0 0.9999695 -0.007802648 0 0.9959122 -0.09032779 0 0.9999695 -0.007796728 0 0.995526 -0.005050065 -0.09435271 0.9971427 -0.008392763 0.07507352 0.6878086 -0.7258871 0.002689192 0.7424685 -0.6696487 -0.01764205 0.6415625 -0.7667613 0.02179068 0.7792178 -0.6263489 0.02250635 0.7677547 -0.6407431 9.34799e-4 0.7663585 -0.6337912 0.1048973 0.7935317 -0.6084214 0.01143569 0.774787 -0.6322222 5.87183e-4 0.8845897 -0.1239741 -0.4495904 0.852913 -0.4572881 -0.2518469 0.8049178 -0.5933862 -3.81865e-4 0.823442 -0.5673964 -0.002108977 0.8936788 -0.2881753 0.3439378 0.9531578 0.1541394 -0.2602522 0.9410335 0.2018145 0.2715269 0.8987342 -0.4362873 0.04393497 0.9610592 0.2763418 6.33434e-4 0.9566677 0.2911809 7.57395e-4 0.9693725 0.2405146 -0.0496958 0.9572315 0.2868079 0.03806709 0.9606045 0.2779147 -0.0015819 0.9244259 -0.3813612 6.97594e-4 0.9553354 -0.2955185 0.001732087 0.9494981 -0.3123846 -0.02948182 0.9528931 -0.3033059 3.48106e-4 0.9780316 0.208449 -0.001782689 0.9920764 -0.005825731 -0.125501 0.9981423 -0.001278988 0.06091274 0.9991639 0.04085117 -0.00164355 0.9972605 0.07396336 8.49519e-4 0.2469447 -0.6628245 -0.7068819 0.330651 -0.8678595 0.3707961 0.302147 -0.9532608 -9.02978e-4 0.1998505 -0.8077793 -0.5545741 0 0 -1 0.199835 -0.9798283 0.001580782 0.1757907 -0.8765916 0.4479786 0.04716176 -0.7050136 -0.7076239 -0.3340433 0.9425577 0 -0.129769 0.3478496 -0.9285261 -0.08787764 0.273203 0.9579341 -0.3289318 0.9443535 5.75807e-4 -0.2442747 0.9163053 -0.3173555 -0.04441406 -0.9008874 0.4317746 -0.09585983 0.9431863 0.3181359 -0.1724878 0.9850114 6.86857e-4 0.003500547 0.7040516 -0.7101402 0.02840558 0.5517884 0.8335002 0.1953097 0.9807413 -7.43797e-4 -0.1247766 -0.991681 -0.03161656 0.2204044 0.8012426 0.5562662 0.2329013 0.8618718 -0.4504818 0.329611 0.831179 0.4477701 0.371641 0.9283745 -0.001923588 0.2081691 0.4382742 -0.8744034 0.3139912 0.6334155 0.7072443 0.4195674 0.9077222 -0.00188638 -0.1303742 -0.8848991 -0.4471647 -0.2129495 -0.8379128 0.5025482 -0.2729319 -0.7858858 -0.5548799 -0.38084 -0.924637 -0.002701038 -0.3651057 -0.7479926 -0.5542606 -0.4091618 -0.8341644 0.3698056 -0.3482029 -0.8216308 -0.4513064 -0.3193489 -0.7657472 0.558254 -0.3553646 -0.9347275 5.73778e-4 -0.2714292 -0.9624583 -2.396e-4 -0.245676 -0.969352 2.37594e-4 -0.1034149 -0.8199069 0.5630789 -0.03603452 -0.9993504 5.52224e-4 0.01987881 -0.6956049 -0.7181495 0.2427297 -0.789654 0.563497 0.3975231 -0.9175921 1.81001e-4 0.3451854 0.4791593 -0.8070027 0.1631495 0.233953 0.9584615 0.5170738 0.8559404 9.23472e-4 0.5339367 0.8455243 -4.48359e-4 0.4843928 -0.8128121 -0.3235741 0.6906545 -0.7231833 0.001539923 0.6614538 0.7499859 0 0.6489168 0.7608594 0 0.7075117 0.7067007 -0.001175401 0.6982664 0.7157401 -0.01183514 0.609624 -0.6501763 0.4534637 0.7647463 0.6443307 0.001074904 0.5852565 -0.3829253 -0.7147329 0.8373434 0.5466772 0 0.8153422 0.470815 0.3369722 0.8714902 -0.4904128 -3.59022e-4 0.7685795 -0.3011051 0.5644655 0.8956869 0.444684 0.001002693 0.8895259 -0.4568749 0.002983781 0.8053968 0.485262 -0.3403773 0.9631268 0.2690394 0.002106639 0.962292 0.2720168 9.48932e-4 0.96995 -0.2433045 2.27132e-4 0.9672504 0.2538202 -0.001413479 0.9701954 -0.2423172 -0.001836657 0.8666124 -0.2083055 -0.4534222 0.8526046 0.06263761 -0.518789 0.8196239 0.02917944 0.5721584 0.9999951 0.003130141 1.50238e-4 0.9969172 -0.07837524 0.00364677</float_array>
          <technique_common>
            <accessor source="#float_MShape-normals-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="float_MShape-map1" name="float_MShape-map1">
          <float_array id="float_MShape-map1-array" count="1580">0.9151841 0.02354542 0.9122264 0.02354385 0.9122082 0.02213909 0.9151841 0.0732065 0.9138957 0.01167067 0.9122264 0.101335 0.9341287 0.02354542 0.934003 0.01799408 0.9101155 0.009159139 0.9151841 0.1364481 0.9341287 0.0729422 0.9329712 0.009509577 0.909827 0.001985467 0.9122264 0.1577963 0.930997 0.002985387 0.9056631 0.003319698 0.9341287 0.2144026 0.9151841 0.2042563 0.9122264 0.2153982 0.9151841 0.2792554 0.9341287 0.2807494 0.9122264 0.2821253 0.9341287 0.3301462 0.9122264 0.3301459 0.9151841 0.3301462 0.9146381 0.3399695 0.9333274 0.3436526 0.911056 0.34153 0.9105139 0.3504343 0.904552 0.3505863 0.02349834 0.5504214 0.01018341 0.5422684 0.05073042 0.54634 0.06041084 0.5357815 0.04115652 0.5508266 0.06405926 0.5224394 0.03288578 0.5518255 0.06568136 0.5152518 0.09220424 0.4497798 0.05520264 0.4806303 0.02362833 0.4678395 0.0580759 0.4691995 0.01305749 0.4939336 0.09916808 0.30572 0.009219659 0.5002238 0.2212152 0.1313153 0.002178565 0.5136109 0.1152961 0.2415569 0.004391577 0.5331802 0.1397631 0.1811601 0.001992039 0.5251452 0.14739 0.1568666 0.1498871 0.1280144 0.1460188 0.09768884 0.172085 0.01706984 0.1338416 0.06613454 0.2199815 0.1073517 0.1219284 0.04367909 0.2232798 0.1191954 0.1185342 0.03297584 0.2229026 0.1253309 0.2222746 0.1131311 0.1199179 0.02106572 0.2212892 0.1101872 0.1272662 0.009994958 0.1335709 0.005300173 0.1470067 0.001985473 0.1624264 0.005949393 0.2313385 0.1283605 0.2302143 0.1209023 0.2323482 0.1313153 0.2335819 0.1073517 0.2312873 0.1128492 0.3036916 0.1332953 0.3058152 0.1064046 0.2857383 0.1617686 0.3118452 0.08353294 0.3077432 0.1631142 0.2803555 0.2498215 0.3197218 0.06613458 0.3138004 0.1811601 0.3316349 0.04367906 0.3382673 0.2415568 0.3353576 0.02994253 0.3528743 0.4288351 0.2814783 0.01706982 0.3983608 0.4806303 0.3231833 0.007066693 0.387882 0.5152518 0.3065567 0.001985484 0.3308749 0.01573022 0.4181677 0.5087218 0.2939024 0.004522253 0.3116657 0.002449718 0.3895041 0.5224394 0.429935 0.4678395 0.395733 0.5413622 0.4405058 0.4939337 0.4311421 0.5501278 0.4443437 0.5002237 0.4124069 0.5508265 0.4511351 0.5129786 0.4206776 0.5518255 0.4418967 0.5439078 0.4509923 0.5283987 0.8144466 0.6846485 0.8004358 0.6806281 0.8036777 0.6675088 0.8099696 0.6985556 0.793289 0.6685403 0.8102603 0.663993 0.79659 0.6917545 0.8197951 0.6989015 0.7882473 0.6775824 0.7983769 0.6401982 0.8162765 0.6702692 0.8248298 0.6874908 0.7837005 0.6675087 0.7879553 0.5958792 0.815993 0.6675088 0.8221796 0.6781829 0.8087252 0.5725505 0.7905539 0.6893297 0.8557967 0.6897516 0.7848622 0.6770641 0.7957284 0.5012152 0.8159994 0.6566156 0.8255056 0.6789129 0.859714 0.6794405 0.881925 0.6935377 0.7837005 0.5909828 0.7868945 0.5082369 0.8022143 0.4809624 0.8159931 0.5712268 0.8337609 0.6789547 0.8948543 0.685369 0.7945962 0.3891856 0.8080362 0.403697 0.8159969 0.5162733 0.8258218 0.512248 0.8893632 0.6790964 0.8689497 0.4773014 0.9399131 0.6864206 0.7837005 0.5137188 0.786998 0.3883573 0.8369724 0.3799175 0.8893917 0.5714131 0.8962326 0.6790985 0.858187 0.3798451 0.8859502 0.5378578 0.9087184 0.6790927 0.9234759 0.6960584 0.7837005 0.46677 0.7837005 0.4297559 0.7907341 0.3764838 0.815993 0.3913793 0.8043335 0.3963734 0.8255016 0.379973 0.8928423 0.6119847 0.8963017 0.6641331 0.8982235 0.6790985 0.8901815 0.3797917 0.8893917 0.4983428 0.9207536 0.6790767 0.8585611 0.7004063 0.9704628 0.6889794 0.7837005 0.3913794 0.8005758 0.3764075 0.8034101 0.3913793 0.825316 0.3751301 0.8928423 0.5030199 0.8947904 0.3734618 0.9032466 0.6641254 0.9137267 0.675336 0.9328198 0.6790513 0.9680471 0.6989017 0.9708111 0.6789151 0.8021122 0.3631987 0.8026676 0.3865145 0.8221777 0.3807059 0.8036739 0.3913793 0.8117251 0.3794986 0.8560242 0.3744524 0.8963017 0.5434741 0.9000105 0.6659999 0.8945699 0.4459068 0.8962326 0.3797895 0.9067317 0.5903507 0.9154795 0.6102769 0.9242792 0.6715815 0.9827948 0.6931299 0.9745184 0.6781821 0.8167406 0.3666485 0.8162758 0.3886222 0.8512865 0.3649676 0.8997699 0.597955 0.9001666 0.3664126 0.8979801 0.3797896 0.9032466 0.5666198 0.9102251 0.5627581 0.9207538 0.6028507 0.9278121 0.6072867 0.9557984 0.53176 0.9862795 0.6813574 0.9815586 0.6274776 0.8190747 0.3594311 0.8228595 0.365162 0.8997699 0.5191542 0.9015061 0.4628302 0.9443333 0.3725742 0.9039322 0.3797922 0.9041141 0.3835348 0.9067317 0.4949809 0.9137267 0.5285041 0.9172363 0.5499044 0.9242792 0.5706447 0.9313525 0.5985463 0.9961351 0.6788977 0.9814683 0.6675088 0.9811488 0.6702659 0.9164926 0.3609288 0.9701422 0.3686444 0.9172398 0.3798051 0.9069085 0.3797935 0.9032634 0.3797929 0.9102251 0.4777657 0.9207538 0.5295566 0.9278121 0.5252326 0.9814575 0.5621302 0.9933301 0.6719457 0.9922974 0.6384427 0.8491906 0.3586832 0.9067317 0.4223503 0.9708073 0.3799752 0.9260428 0.3798205 0.9100608 0.3797967 0.9067358 0.3797946 0.9137267 0.4340163 0.9172363 0.4744381 0.9242792 0.4759031 0.9278121 0.4305549 0.9814633 0.4713082 0.9875103 0.5873211 0.9925031 0.6675088 0.9682369 0.3605675 0.9826894 0.3754759 0.9492642 0.3798983 0.9207538 0.4282075 0.98827 0.5391289 0.9926853 0.6675088 0.9929291 0.5895437 0.9939805 0.6643201 0.8899275 0.3580001 0.9756171 0.3598908 0.9745156 0.3807048 0.9814683 0.3913793 0.988072 0.4890237 0.9973603 0.6679327 0.9978908 0.6095437 0.9852092 0.3639701 0.9811481 0.3886189 0.9883275 0.4279734 0.9955173 0.5304042 0.9979813 0.5607554 0.9948575 0.3820963 0.9869236 0.3919999 0.9940444 0.4710546 0.9925004 0.3913793 0.9956219 0.4274943 0.9980081 0.3926751 0.7432656 0.5467442 0.7411959 0.666764 0.7342608 0.5600107 0.7489958 0.5828505 0.7340158 0.6087991 0.7345933 0.4783697 0.7489833 0.6667639 0.7489953 0.5174514 0.7325957 0.667188 0.7289436 0.5296595 0.7413233 0.4388689 0.7775652 0.6036022 0.7485533 0.675776 0.7456456 0.4857492 0.7775652 0.5045695 0.7331754 0.6781529 0.727107 0.4703099 0.7255992 0.588799 0.7347048 0.3919303 0.7775652 0.6539133 0.7775652 0.6678259 0.7485848 0.4537483 0.7265627 0.6712011 0.729059 0.4267494 0.7411959 0.3906346 0.7760839 0.6844727 0.7466441 0.6889293 0.7775652 0.3895727 0.7489833 0.3906347 0.7285694 0.3813515 0.7272015 0.6923851 0.7484321 0.3810943 0.7738224 0.6919034 0.7346835 0.6981223 0.7358289 0.3746285 0.7703947 0.698685 0.7464405 0.6997969 0.7768666 0.3780956 0.7333658 0.3632253 0.7463644 0.3684404 0.7738224 0.3654952 0.7424683 0.3600328 0.7698577 0.358 0.1494389 0.7833411 0.1577096 0.783341 0.1558701 0.8868065 0.1672835 0.7833428 0.1492469 0.8680586 0.1652404 0.972441 0.1494389 0.9911482 0.1423327 0.8555797 0.1737411 0.8448445 0.1577096 0.9911484 0.1442911 0.9187683 0.1400514 0.7833412 0.1749641 0.979326 0.1769639 0.7833463 0.1743835 0.9911469 0.1389744 0.9911477 0.1364665 0.9062727 0.1791237 0.7901847 0.1267365 0.7833378 0.1282199 0.9911489 0.1265835 0.8715671 0.1210629 0.8438433 0.1210475 0.910853 0.1209447 0.7833412 0.1191241 0.991147 0.8976569 0.02469508 0.8927783 0.0273933 0.897612 0.02329031 0.8944996 0.03887042 0.8924458 0.01031034 0.8976569 0.1024862 0.8852603 0.01479296 0.8944996 0.1538673 0.8814553 0.004470894 0.8976569 0.1589475 0.875477 0.007297794 0.8976569 0.2165495 0.8787124 0.004256607 0.8944996 0.2528999 0.8778597 0.001985484 0.870515 0.00425664 0.8976569 0.2832765 0.885231 0.003136696 0.8629444 0.004824379 0.8944996 0.303211 0.8614147 0.001985484 0.858106 0.00425664 0.8944996 0.3171237 0.8548107 0.004824379 0.8976569 0.3312972 0.8510816 0.00425664 0.8908858 0.3337704 0.8411593 0.00425664 0.8439268 0.004824379 0.8947755 0.3426813 0.827131 0.00425664 0.8852603 0.3412011 0.8341197 0.004824379 0.8114631 0.001985484 0.8787124 0.3517374 0.8230867 0.004824379 0.8153228 0.00425664 0.8769323 0.3479828 0.8014293 0.00425664 0.8717136 0.3517374 0.8079674 0.004824379 0.7705563 0.001985484 0.8778597 0.3540086 0.8675245 0.3508103 0.7957085 0.004824379 0.7859014 0.00425664 0.8571756 0.3517374 0.8876873 0.3515855 0.8629444 0.3511697 0.7765028 0.00425664 0.8340749 0.3540086 0.7850841 0.004824379 0.7647978 0.00425664 0.853734 0.3511697 0.8464902 0.3517374 0.7711906 0.004824379 0.7518198 0.00425664 0.8375711 0.3517374 0.7589318 0.004824379 0.7338635 0.001985484 0.8439268 0.3511697 0.8255889 0.3517374 0.7491246 0.004824379 0.741241 0.00425664 0.8341197 0.3511697 0.8046796 0.3540086 0.7276796 0.00425664 0.8214522 0.3511697 0.8112961 0.3517374 0.7348226 0.004824379 0.7201453 0.00425664 0.8034718 0.3517374 0.7068321 0.001985484 0.7176061 0.004824379 0.8112364 0.3511697 0.7907734 0.3517374 0.7118577 0.00425664 0.7981603 0.3511697 0.775194 0.3517374 0.7017236 0.00425664 0.7102968 0.004824379 0.7814064 0.3511697 0.7704535 0.3540086 0.6936762 0.001985484 0.7057168 0.005183732 0.7654698 0.3511697 0.7628633 0.3517374 0.6945289 0.00425664 0.6990719 0.006872137 0.7360219 0.3540086 0.6844831 0.004210623 0.7556627 0.3511697 0.7481015 0.3517374 0.6782808 0.01324913 0.6879809 0.01479296 0.7366087 0.3517374 0.676293 0.01154414 0.7438125 0.3511697 0.726884 0.3517374 0.6817848 0.02344193 0.6731235 0.02011461 0.7149106 0.3517374 0.7327794 0.3511697 0.6755843 0.02469665 0.6936762 0.3540086 0.7178645 0.3511697 0.6787417 0.03887042 0.6726266 0.02469665 0.7039598 0.3517374 0.7102968 0.3511697 0.6755843 0.08537802 0.6945289 0.3517374 0.6787417 0.1106076 0.6726266 0.1148063 0.6917859 0.3515232 0.6998984 0.3494173 0.6755843 0.1638534 0.6809257 0.3496613 0.6787417 0.1460414 0.6726266 0.2035471 0.6879809 0.3412011 0.6807959 0.3456848 0.6787417 0.1756058 0.6755843 0.2532788 0.673508 0.3381519 0.6787417 0.2103875 0.6726266 0.3166974 0.6756292 0.3327037 0.6787417 0.2293 0.6755843 0.3312976 0.6804641 0.3286007 0.6726266 0.3312976 0.6787417 0.2492995 0.6787417 0.3006024 0.6787417 0.271038 0.6787417 0.3171237 0.07955617 0.8007425 0.06042092 0.8445213 0.06935266 0.7980855 0.05394279 0.8781294 0.05200788 0.8426567 0.07654724 0.7866911 0.08569879 0.8623386 0.03311073 0.9075714 0.06906521 0.7833377 0.08830423 0.7922785 0.08983721 0.8521229 0.08040159 0.8754148 0.01847395 0.9515244 0.1066459 0.8106306 0.09496888 0.8394554 0.07361456 0.8921686 0.05838514 0.9297626 0.009290832 0.9463374 0.1029147 0.8198411 0.1085013 0.8060506 0.09894177 0.8296483 0.06715861 0.9081052 0.06318571 0.9179123 0.05391562 0.9407956 0.00532742 0.9561629 0.03323733 0.8842075 0.1122584 0.7966428 0.04787351 0.9557106 0.01365571 0.9614764 0.04480783 0.9632782 0.001992039 0.9695425 0.04059329 0.9736767 0.007271491 0.9755785 0.03576761 0.9855942 0.03272025 0.9931109 0.5151417 0.6489718 0.4208743 0.6299914 0.5151423 0.6174175 0.3073343 0.6348159 0.3073352 0.6174175 0.3073355 0.6576877 0.3073352 0.5949621 0.5077981 0.6646267 0.4204913 0.5931337 0.5151425 0.6792974 0.5151423 0.5949621 0.3786541 0.5882388 0.3073352 0.6845783 0.466888 0.589987 0.4274403 0.5819265 0.3073346 0.5812255 0.5151424 0.7081496 0.5151418 0.5842589 0.3685339 0.7101749 0.4140912 0.57039 0.3073353 0.7143973 0.4152353 0.7247345 0.5151365 0.5723488 0.3073352 0.5670131 0.5151423 0.7324431 0.5151424 0.5612779 0.4099434 0.5627024 0.3073352 0.7324431 0.3073336 0.5583496 0.3073352 0.7928399 0.4516913 0.555817 0.389089 0.5572273 0.4487956 0.7928399 0.5151425 0.5565832 0.5151423 0.7928399 0.6834475 0.456347 0.7112319 0.4854952 0.6834475 0.5336109 0.7112319 0.4665827 0.7112319 0.5202768 0.7112319 0.4465832 0.7112319 0.5498413 0.7112319 0.4248447 0.6834475 0.5805597 0.6834475 0.379821 0.7112319 0.5852751 0.7112319 0.3952803 0.7112319 0.6570122 0.7112319 0.378759 0.6834475 0.6175737 0.6838004 0.3702656 0.6834475 0.6559502 0.7119274 0.3672819 0.6972864 0.6660885 0.6864787 0.358 0.7124585 0.6724408 0.6849864 0.6727668 0.6784686 0.3697473 0.6777643 0.6708459 0.7149748 0.6810897 0.6906439 0.6863653 0.6779267 0.6589723 0.6830032 0.6841309 0.719464 0.6890106 0.9415821 0.2389155 0.9605267 0.2796284 0.9415821 0.3290252 0.9605267 0.1733765 0.992101 0.3290252 0.9415821 0.1501747 0.992101 0.2796284 0.9417838 0.3336072 0.9605267 0.07182121 0.9936021 0.3447908 0.9415821 0.03702445 0.9430025 0.3421777 0.9415821 0.02242417 0.9460326 0.3495112 0.992101 0.02703423 0.992101 0.0224244 0.992101 0.07182121 0.992223 0.01801886 0.9418662 0.01556988 0.9930828 0.009144903 0.9442806 0.004060547 0.9952231 0.001985471 0.5671867 0.112781 0.6660513 0.1183656 0.5664759 0.1215758 0.6660513 0.1103124 0.6660513 0.1258237 0.4582441 0.1227942 0.6660513 0.1048149 0.6377402 0.1287785 0.4582441 0.1166587 0.4582441 0.1287785 0.5317652 0.1048149 0.6660513 0.1287785 0.5341995 0.1287797 0.4582441 0.1105944 0.5200915 0.1076242 0.5466812 0.01420477 0.6660513 0.2472848 0.4582441 0.447243 0.4582441 0.1048149 0.6030317 0.01413273 0.4582441 0.1076505 0.5701045 0.008519048 0.6660513 0.01453305 0.4582441 0.01453305 0.6660513 0.4262982 0.5361454 0.008686138 0.5976689 0.008464116 0.6451932 0.008065498 0.4862406 0.006495151 0.6025329 0.5126956 0.5536162 0.00410987 0.6660511 0.001985484 0.4582418 0.003412666 0.50427 0.5127105 0.6660513 0.512715 0.6333267 0.002189873 0.4582441 0.512715 0.5718743 0.5163451 0.6198766 0.5198969 0.4582441 0.5199026 0.5320129 0.5198131 0.6660513 0.5199026 0.6536462 0.529896 0.4650877 0.528686 0.5573554 0.5258873 0.6660499 0.5388254 0.4582492 0.5332447 0.654229 0.5364912 0.5197474 0.5378979 0.2087584 0.8745724 0.2145079 0.8196276 0.2178142 0.8993381 0.2020151 0.786903 0.2093231 0.9151248 0.2146694 0.7869031 0.2024336 0.8743368 0.2213259 0.9667137 0.1951712 0.8480974 0.209072 0.9702484 0.200147 0.932206 0.1969061 0.786903 0.1949046 0.8927355 0.2174348 0.9947125 0.1873376 0.8686569 0.2020151 0.9947103 0.1896176 0.9312592 0.1853885 0.7869014 0.1885793 0.9947104 0.3601043 0.8940375 0.344571 0.9182925 0.3389427 0.904399 0.3955731 0.9674324 0.3346387 0.8937747 0.3495371 0.9305514 0.365321 0.892113 0.3296726 0.8815157 0.3535099 0.9403585 0.3440437 0.8492617 0.3858603 0.93285 0.3235478 0.8663964 0.3593037 0.9546605 0.3190782 0.8553635 0.3454281 0.8138037 0.3615881 0.865548 0.4034516 0.9629658 0.3662783 0.9718772 0.3151053 0.8455564 0.3344909 0.8166506 0.4026103 0.9821665 0.3692392 0.9791864 0.3415742 0.8064235 0.3710946 0.9837664 0.4102511 0.9799284 0.3106963 0.8346725 0.3388175 0.7968313 0.3737903 0.9904113 0.3074013 0.8265387 0.3297151 0.8023267 0.3023256 0.8140061 0.671629 0.7704594 0.6178267 0.7814049 0.5995211 0.7596509 0.6268421 0.7818903 0.6017933 0.7694717 0.671629 0.6069798 0.671629 0.7818903 0.6222323 0.7818903 0.6089528 0.7779757 0.5995211 0.5650562 0.671629 0.5428168 0.6222323 0.5428168 0.6087258 0.5459422 0.4511545 0.8036286 0.4498568 0.8004062 0.4498254 0.8003243 0.4510963 0.803607 0.4497733 0.8001562 0.4531969 0.808912 0.4484971 0.7968314 0.1692193 0.5562387 0.187244 0.5749307 0.1653623 0.5660663 0.1920328 0.5558168 0.147625 0.6098511 0.1357168 0.6392463 0.1383307 0.7695254 0.1218517 0.6734725 0.1079034 0.7079041 0.09074897 0.7502499 0.1358627 0.7793462 0.08492023 0.7630002 0.2285894 0.9342064 0.237566 0.9913281 0.2290444 0.9947084 0.2282654 0.8671967 0.2370794 0.9947085 0.241909 0.8889353 0.2486137 0.9947109 0.240116 0.7932715 0.2536093 0.9308653 0.2548605 0.8487225 0.233826 0.7869027 0.2562601 0.8919376 0.2620009 0.9947085 0.2492461 0.786904 0.2620811 0.9202219 0.2620009 0.7869015 0.268291 0.9323176 0.268291 0.8596095 0.268291 0.9947085 0.268291 0.7869015 0.2943851 0.8931533 0.2943851 0.9947085 0.2943851 0.7869015 0.4415033 0.7968314 0.4401434 0.8004062 0.440212 0.8002002 0.4401748 0.8003243 0.438904 0.803607 0.439138 0.80296 0.4368033 0.808912 0.2291702 0.5796334 0.2006266 0.5720527 0.2225082 0.5631884 0.2299646 0.6887697 0.219338 0.555817 0.2494058 0.6295849 0.1981681 0.5622748 0.2659771 0.6704916 0.2808416 0.7071845 0.291792 0.7342159 0.2971216 0.7473719 0.2495399 0.7666474 0.250439 0.7702247 0.3011982 0.7565649 0.2536288 0.7829099 0.5933858 0.7596509 0.5706746 0.7818902 0.5933858 0.6817732 0.5930908 0.7632281 0.5212778 0.5428168 0.5864403 0.7759134 0.5847104 0.5474206 0.5212778 0.7818902 0.5933858 0.5650562 0.5706746 0.5428168 0.5912346 0.5552782 0.576226 0.5433124 0.461482 0.8002002 0.4613885 0.7999837 0.4601908 0.7968314 0.462556 0.8029599 0.4270965 0.8580274 0.4173138 0.7968313 0.4288314 0.7968329</float_array>
          <technique_common>
            <accessor source="#float_MShape-map1-array" count="790" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="float_MShape-vertices" name="float_MShape-vertices">
          <input semantic="POSITION" source="#float_MShape-positions"/>
          <input semantic="NORMAL" source="#float_MShape-normals"/>
        </vertices>
        <triangles material="lambert4SG" count="1000">
          <input semantic="VERTEX" source="#float_MShape-vertices" offset="0"/>
          <input semantic="TEXCOORD" source="#float_MShape-map1" offset="1" set="0"/>
          <p>397 0 386 1 385 2 8 30 15 31 12 32 253 371 313 358 280 368 304 392 291 388 297 395 112 442 116 449 129 446 237 403 232 409 251 406 205 420 197 424 208 416 441 68 445 69 438 70 157 105 135 106 144 107 428 270 426 271 421 272 106 143 105 152 113 131 449 61 446 58 454 56 274 220 243 196 195 194 368 259 358 245 342 253 121 567 134 569 137 571 2 313 7 314 4 315 415 273 428 270 416 277 105 152 104 153 113 131 386 338 369 339 385 340 323 474 285 475 325 476 292 370 280 368 301 365 389 349 390 354 374 351 88 463 89 466 76 461 219 382 213 383 228 376 85 450 80 454 79 448 457 509 459 510 461 511 90 470 91 472 76 461 292 370 273 373 280 368 140 447 154 443 148 439 317 381 321 377 315 386 250 412 251 406 232 409 107 544 88 545 106 546 141 418 131 422 146 414 146 414 131 422 125 417 171 404 181 397 186 401 244 378 228 376 263 374 377 367 345 369 361 364 329 352 335 350 320 353 69 573 63 574 67 575 136 167 139 178 126 136 332 233 310 246 307 209 379 256 409 262 380 248 123 453 109 452 102 458 421 272 423 275 428 270 224 161 202 148 230 171 159 486 151 498 170 491 447 595 445 596 444 597 5 320 0 317 3 323 495 644 489 645 485 646 449 61 454 56 451 63 15 31 19 33 12 32 283 226 275 238 282 247 396 19 395 17 364 16 6 100 16 96 9 98 439 45 473 54 454 56 224 161 209 141 202 148 217 663 207 664 212 665 390 354 375 357 374 351 444 597 441 599 440 602 251 406 278 399 237 403 61 694 58 695 75 696 229 185 240 207 235 183 414 257 404 229 402 249 26 104 13 103 41 101 94 468 102 458 81 465 12 316 4 315 7 314 461 511 464 517 463 519 185 134 180 139 188 145 176 169 174 206 195 194 341 611 437 607 440 602 175 116 185 134 204 128 485 646 492 648 495 644 91 472 92 471 76 461 248 707 255 708 256 709 382 4 346 11 360 7 215 413 225 415 232 409 344 344 367 342 369 339 286 478 285 475 203 481 286 151 214 129 305 142 283 226 282 247 287 239 288 215 283 226 287 239 284 200 276 214 283 226 235 183 241 195 236 159 106 546 87 548 86 550 288 215 295 201 289 189 261 236 254 211 248 224 288 215 284 200 283 226 229 185 224 161 233 186 474 610 453 601 455 605 270 173 267 199 272 188 84 444 85 450 79 448 78 467 81 465 68 469 247 172 264 150 239 160 101 568 134 569 117 565 349 714 319 715 330 716 375 289 376 290 418 276 43 734 45 737 48 740 477 535 487 540 490 539 21 726 28 727 22 728 10 329 3 323 9 328 411 217 359 190 370 203 473 618 486 627 483 623 89 547 107 544 90 549 170 112 151 108 157 105 265 749 255 750 257 751 53 86 128 84 363 82 18 325 17 321 24 330 262 223 252 210 230 171 372 297 412 301 369 307 147 502 120 506 130 504 391 362 376 360 390 354 409 262 403 250 380 248 132 114 118 118 127 125 379 256 354 241 381 261 122 426 101 432 115 425 426 271 420 274 421 272 403 250 420 258 399 230 105 552 84 556 104 558 93 557 92 555 108 553 137 687 134 683 168 679 340 191 370 203 331 165 98 38 15 31 57 39 126 136 121 154 136 167 408 299 407 304 412 301 311 384 321 377 330 380 250 480 323 474 271 484 422 288 425 294 427 280 79 448 67 445 77 441 482 534 470 531 471 528 366 705 319 703 350 706 496 652 498 647 500 650 381 261 355 255 371 265 97 437 84 444 77 441 277 222 265 235 230 171 191 394 190 400 186 401 6 322 11 318 16 327 463 519 464 517 465 522 75 720 237 719 319 715 176 169 155 181 167 192 236 159 242 184 239 160 491 649 489 645 498 647 52 744 47 738 51 742 287 239 282 247 290 234 310 246 332 233 316 202 216 149 204 128 209 141 266 225 259 212 258 232 270 173 272 188 281 163 221 162 224 161 229 185 282 247 277 222 290 234 312 359 313 358 320 353 313 756 318 757 329 758 247 172 242 184 241 195 275 238 276 214 269 213 266 225 267 199 259 212 245 208 240 207 229 185 275 238 269 213 268 237 258 232 259 212 245 208 434 527 436 521 435 525 431 530 432 529 434 527 200 390 191 394 206 379 131 422 122 426 125 417 42 91 49 97 46 99 48 740 46 741 50 743 23 637 31 633 29 636 345 302 384 296 361 295 422 269 410 268 400 267 462 81 453 71 469 83 256 752 255 750 265 749 145 156 149 137 126 136 88 463 80 454 87 460 314 691 322 682 309 688 80 454 76 461 71 457 443 600 444 597 439 604 105 552 86 550 85 554 91 551 107 544 108 553 127 125 113 131 126 136 345 369 334 372 327 375 333 176 316 202 357 204 264 150 270 173 281 163 353 228 354 241 380 248 417 298 419 291 427 280 416 277 424 283 419 291 98 38 279 43 439 45 152 411 141 418 146 414 337 306 347 303 384 296 249 753 238 755 252 754 483 623 485 625 478 620 347 303 359 300 384 296 459 510 460 513 461 511 93 557 110 559 94 561 312 359 314 356 309 361 74 771 56 772 192 773 184 433 183 427 169 436 125 417 111 421 133 410 205 420 183 427 197 424 365 47 430 49 439 45 330 380 321 377 334 372 312 359 309 361 306 363 200 390 210 387 207 393 210 387 200 390 206 379 302 366 292 370 301 365 192 759 313 756 253 761 233 186 224 161 230 171 391 362 377 367 361 364 88 463 76 461 80 454 155 181 165 193 160 155 387 343 373 345 372 341 231 135 239 160 264 150 470 62 473 54 467 59 13 332 20 335 14 333 32 635 35 632 31 633 471 528 468 523 469 524 490 539 488 542 482 534 40 732 28 727 43 734 359 482 347 479 325 476 411 285 404 292 414 278 425 294 422 288 408 299 257 751 255 750 249 753 455 605 447 595 452 609 54 588 68 585 58 590 487 658 494 656 490 660 223 669 299 672 326 677 180 139 178 157 188 145 341 611 440 602 438 606 340 191 352 219 370 203 94 508 120 506 102 507 137 177 136 167 121 154 77 441 83 431 97 437 198 123 231 135 214 129 305 142 300 174 333 176 205 420 208 416 237 403 278 399 251 406 271 405 214 129 231 135 305 142 326 244 342 253 328 221 74 767 192 759 111 766 231 135 220 140 234 147 25 639 23 637 18 642 430 533 364 541 363 538 145 156 143 180 160 155 74 771 66 774 56 772 27 730 22 728 28 727 324 348 338 346 344 344 134 683 131 686 158 666 150 121 132 114 127 125 173 120 175 116 157 105 107 130 118 118 108 117 252 210 238 197 230 171 316 202 332 233 354 241 365 20 366 22 396 19 357 204 316 202 353 228 57 39 15 31 65 40 318 757 336 762 343 760 469 83 484 87 476 90 473 618 474 610 454 613 273 674 292 676 217 663 338 346 329 352 343 355 42 91 16 96 29 94 243 196 199 182 195 194 274 220 195 194 194 231 299 672 217 663 292 676 60 582 99 586 82 584 147 502 123 505 120 506 355 255 332 233 339 254 208 416 211 419 215 413 55 587 57 589 65 581 344 344 338 346 367 342 59 592 58 590 73 591 486 657 499 659 493 653 414 257 405 251 399 230 335 350 329 352 338 346 377 367 334 372 345 369 17 321 12 316 19 326 24 638 17 643 19 641 16 327 11 318 18 325 480 621 489 630 481 622 154 501 159 486 177 497 444 597 440 602 437 607 477 535 490 539 482 534 70 464 71 457 76 461 308 391 304 392 303 398 130 111 119 113 135 106 26 736 41 739 33 733 210 387 206 379 213 383 234 147 239 160 231 135 216 149 222 146 204 128 363 82 341 78 429 80 233 186 230 171 238 197 69 573 64 576 63 574 175 116 179 127 185 134 53 86 36 88 128 84 204 128 198 123 175 116 467 526 465 522 468 523 315 487 323 474 337 483 315 487 337 483 317 493 47 738 40 732 43 734 488 662 501 654 499 659 455 605 452 609 454 613 414 257 402 249 405 251 63 748 64 746 50 743 341 78 438 70 433 75 371 265 351 260 358 245 473 54 463 57 467 59 431 530 429 536 432 529 317 381 327 375 321 377 40 46 22 48 27 50 151 108 135 106 157 105 468 523 466 520 469 524 123 505 102 507 120 506 376 290 413 282 418 276 140 503 147 502 159 486 433 75 436 73 432 77 281 163 289 189 300 174 316 202 294 240 310 246 316 202 300 174 298 216 204 128 185 134 209 141 227 158 236 159 220 140 259 212 260 187 246 198 222 146 227 158 220 140 310 246 294 240 290 234 238 197 254 211 233 186 295 201 288 215 296 227 226 170 222 146 221 162 254 211 265 235 258 232 268 237 269 213 266 225 260 187 270 173 264 150 229 185 226 170 221 162 254 211 258 232 245 208 245 208 246 198 240 207 265 235 254 211 261 236 260 187 264 150 247 172 248 224 254 211 238 197 246 198 247 172 241 195 298 216 295 201 296 227 242 184 236 159 241 195 265 235 266 225 258 232 277 222 275 238 268 237 233 186 254 211 245 208 188 145 202 148 185 134 483 623 474 610 473 618 412 301 392 309 369 307 419 291 372 297 373 284 53 778 363 775 56 772 484 87 498 89 497 93 467 526 463 519 465 522 108 553 110 559 93 557 38 628 39 624 35 632 9 328 13 332 10 329 480 621 481 622 475 614 501 654 493 653 499 659 461 511 460 513 462 515 474 610 479 616 475 614 155 181 143 180 139 178 42 91 53 86 63 95 500 650 495 644 492 648 479 616 485 625 480 621 478 620 479 616 474 610 465 522 464 517 468 523 458 512 459 510 457 509 494 656 487 658 496 652 474 610 483 623 478 620 418 276 415 273 374 281 381 261 354 241 355 255 415 273 373 284 374 281 106 546 88 545 87 548 484 537 487 540 477 535 468 523 464 517 466 520 356 218 340 191 357 204 476 532 484 537 477 535 439 45 442 52 457 53 197 424 183 427 189 430 453 71 472 85 469 83 337 306 384 296 327 305 171 404 182 407 152 411 68 585 65 581 70 583 387 343 388 347 373 345 366 705 365 704 319 703 14 333 10 329 13 332 14 333 21 334 15 331 10 329 14 333 15 331 467 526 468 523 470 531 44 735 33 733 41 739 462 81 460 79 453 71 491 92 498 89 472 85 361 295 413 282 376 290 158 666 131 686 141 684 472 617 453 601 475 614 457 53 473 54 439 45 400 267 410 268 381 261 299 672 322 682 326 677 108 117 118 118 124 109 161 119 153 110 164 126 142 168 143 180 145 156 118 118 132 114 124 109 162 133 173 120 164 126 250 480 225 485 218 477 113 131 114 144 126 136 404 229 401 243 402 249 365 543 364 541 430 533 334 372 349 385 330 380 99 428 115 425 83 431 444 597 445 596 441 599 472 617 481 622 491 626 259 212 267 199 260 187 440 602 441 599 438 606 257 783 262 784 265 785 257 783 252 786 262 784 27 730 28 727 40 732 447 595 446 603 449 608 15 331 8 324 10 329 297 395 303 398 304 392 319 715 291 718 330 716 197 424 201 423 208 416 392 309 412 301 407 304 346 11 382 4 336 14 191 394 196 396 190 400 394 9 395 17 388 13 66 768 74 767 111 766 160 155 172 179 178 157 173 120 157 105 166 115 57 39 61 41 98 38 61 41 279 43 98 38 17 321 11 318 12 316 18 642 23 637 16 640 490 660 501 654 488 662 265 235 262 223 230 171 195 194 174 206 168 205 243 196 274 220 328 221 400 267 381 261 371 265 368 308 392 309 407 304 450 598 447 595 453 601 411 217 370 203 404 229 170 491 214 499 203 481 168 679 158 666 194 673 158 666 223 669 194 673 223 252 326 244 274 220 373 284 415 273 416 277 120 122 119 113 130 111 385 2 367 8 382 4 383 25 398 24 366 22 206 379 191 394 181 397 171 404 152 411 156 408 409 279 423 275 421 272 381 261 410 268 406 266 421 263 420 258 403 250 406 286 423 275 409 279 149 137 138 132 126 136 421 272 403 287 409 279 460 79 458 76 453 71 373 284 416 277 419 291 357 204 378 242 399 230 353 228 380 248 378 242 379 256 380 248 354 241 427 280 423 275 422 288 420 274 426 271 414 278 138 132 150 121 127 125 424 283 428 270 427 280 89 547 88 545 107 544 32 635 30 634 38 628 85 554 84 556 105 552 173 120 180 139 185 134 160 155 163 138 149 137 309 688 322 682 302 681 361 295 384 296 413 282 46 741 44 735 41 739 485 625 489 630 480 621 140 503 159 486 154 501 340 191 333 176 357 204 116 449 109 452 123 453 109 452 95 459 102 458 230 171 202 148 199 182 488 65 499 66 473 54 494 656 500 650 501 654 429 536 431 530 430 533 134 569 101 568 122 572 228 376 213 383 206 379 92 471 93 473 78 467 278 399 297 395 291 388 499 66 486 67 473 54 40 46 15 31 22 48 9 98 42 91 41 101 115 425 111 421 125 417 406 266 409 262 379 256 293 488 323 474 303 494 2 313 4 315 0 317 9 98 16 96 42 91 142 168 145 156 126 136 445 69 450 72 453 71 472 85 498 89 484 87 413 282 384 296 411 285 158 666 207 664 217 663 407 304 408 299 368 308 112 442 129 446 148 439 30 634 37 631 38 628 15 31 47 44 52 42 48 740 51 742 47 738 36 629 29 636 31 633 426 271 415 273 418 276 424 283 427 280 419 291 39 624 31 633 35 632 32 635 38 628 35 632 169 436 183 427 148 439 114 570 103 560 121 567 366 22 350 26 383 25 163 138 178 157 180 139 405 251 402 249 401 243 218 477 225 485 211 489 149 137 145 156 160 155 393 3 362 6 363 10 81 465 102 458 95 459 311 384 291 388 304 392 62 698 75 696 58 695 180 139 173 120 162 133 67 575 63 574 56 577 393 3 394 9 387 5 368 308 348 311 392 309 442 518 448 516 457 509 418 276 413 282 426 271 399 230 405 251 401 243 307 209 277 222 230 171 373 345 388 347 389 349 494 656 496 652 500 650 313 358 306 363 301 365 322 682 368 689 342 685 386 1 393 3 387 5 78 467 68 469 70 464 388 13 395 17 389 18 301 365 280 368 313 358 263 374 228 376 253 371 442 52 439 45 435 51 161 119 173 120 166 115 165 193 172 179 160 155 293 402 278 399 271 405 132 114 144 107 124 109 425 294 412 301 417 298 428 270 424 283 416 277 244 670 217 663 219 667 62 698 58 695 59 702 280 368 263 374 253 371 237 403 278 399 291 388 351 260 355 255 339 254 352 219 340 191 356 218 13 103 9 98 41 101 464 517 461 511 462 515 152 680 158 666 141 684 231 135 264 150 305 142 360 7 362 6 397 0 273 674 217 663 244 670 363 775 192 773 56 772 114 144 121 154 126 136 123 453 129 446 116 449 122 426 115 425 125 417 320 353 314 356 312 359 206 379 253 371 228 376 323 474 293 488 271 484 348 311 344 310 392 309 129 446 140 447 148 439 78 467 93 473 94 468 119 113 108 117 124 109 208 416 215 413 237 403 2 36 8 30 7 34 12 32 7 34 8 30 182 675 158 666 152 680 135 106 119 113 124 109 457 509 456 514 458 512 154 443 169 436 148 439 273 373 263 374 280 368 234 147 236 159 239 160 242 184 247 172 239 160 324 348 320 353 335 350 318 757 343 760 329 758 65 581 64 576 71 578 425 294 408 299 412 301 413 282 411 285 426 271 243 196 307 209 230 171 315 386 321 377 311 384 235 183 236 159 227 158 245 208 259 212 246 198 220 140 236 159 234 147 235 183 226 170 229 185 246 198 260 187 247 172 233 186 245 208 229 185 269 213 267 199 266 225 282 247 275 238 277 222 283 226 276 214 275 238 260 187 267 199 270 173 268 237 266 225 265 235 272 188 276 214 284 200 204 128 222 146 220 140 221 162 222 146 216 149 209 141 224 161 221 162 288 215 287 239 296 227 298 216 296 227 316 202 289 189 284 200 288 215 287 239 290 234 294 240 185 134 202 148 209 141 179 127 173 120 185 134 305 142 264 150 281 163 284 200 289 189 281 163 284 200 281 163 272 188 277 222 268 237 265 235 296 227 287 239 294 240 296 227 294 240 316 202 227 158 222 146 226 170 227 158 226 170 235 183 221 162 216 149 209 141 248 707 238 712 249 710 240 207 241 195 235 183 240 207 246 198 241 195 295 201 300 174 289 189 295 201 298 216 300 174 272 188 267 199 269 213 276 214 272 188 269 213 202 148 176 169 199 182 290 234 277 222 307 209 501 654 492 648 493 653 498 647 489 645 495 644 82 434 83 431 72 438 313 358 312 359 306 363 103 560 100 562 117 565 436 521 434 527 432 529 291 718 319 715 237 719 435 525 431 530 434 527 435 525 436 521 442 518 457 509 448 516 456 514 436 521 456 514 442 518 78 467 70 464 76 461 231 135 204 128 220 140 174 206 167 192 168 205 39 624 98 612 128 619 31 633 39 624 36 629 36 629 39 624 128 619 84 556 100 562 103 560 327 305 384 296 345 302 131 686 134 683 122 690 97 564 101 568 117 565 97 564 117 565 100 562 498 647 495 644 500 650 452 609 447 595 449 608 443 600 447 595 444 597 32 635 24 638 30 634 500 650 492 648 501 654 51 742 65 747 52 744 466 520 464 517 462 515 48 740 45 737 46 741 51 742 48 740 50 743 475 614 481 622 472 617 3 323 0 317 1 319 0 317 4 315 1 319 156 408 152 411 146 414 3 323 1 319 9 328 482 64 473 54 470 62 279 699 319 703 365 704 308 391 311 384 304 392 382 4 397 0 385 2 481 622 489 630 491 626 493 653 483 651 486 657 313 358 329 352 320 353 396 19 364 16 365 20 50 743 46 741 49 745 360 782 346 777 362 780 461 55 463 57 473 54 497 655 498 647 496 652 37 631 98 612 38 628 417 298 412 301 372 297 474 610 455 605 454 613 308 391 315 386 311 384 466 520 462 515 469 524 394 9 388 13 387 5 386 338 387 343 372 341 389 18 396 19 390 21 390 21 396 19 391 23 76 461 92 471 78 467 390 354 376 360 375 357 80 454 69 451 79 448 67 445 79 448 69 451 80 454 71 457 69 451 395 17 394 9 364 16 396 19 398 24 391 23 394 9 363 10 364 16 393 3 386 1 397 0 395 17 396 19 389 18 393 3 363 10 394 9 248 707 249 710 255 708 471 528 477 535 482 534 172 179 165 193 155 181 496 787 484 788 497 789 351 260 339 254 358 245 372 297 419 291 417 298 106 546 86 550 105 552 108 553 92 555 91 551 103 560 117 565 121 567 308 492 323 474 315 487 153 110 144 107 132 114 427 280 425 294 417 298 403 250 378 242 380 248 150 121 162 133 164 126 163 138 162 133 150 121 357 204 353 228 378 242 410 293 423 275 406 286 118 118 113 131 127 125 423 275 410 293 422 288 427 280 428 270 423 275 415 273 426 271 428 270 406 266 379 256 381 261 399 230 420 258 414 257 104 558 84 556 103 560 378 242 403 250 399 230 103 166 114 144 104 153 106 143 113 131 107 130 183 427 112 442 148 439 147 502 140 503 123 505 322 682 314 691 324 693 100 562 84 556 97 564 446 58 439 45 454 56 408 264 371 265 358 245 37 37 15 31 98 38 37 37 30 35 15 31 134 683 158 666 168 679 438 70 453 71 436 73 58 695 61 694 55 697 362 780 346 777 363 775 432 77 429 80 433 75 95 459 96 455 73 462 95 459 109 452 96 455 281 163 300 174 305 142 338 15 343 12 367 8 23 637 29 636 16 640 325 476 347 479 323 474 64 746 65 747 51 742 125 417 133 410 146 414 70 583 65 581 71 578 400 267 408 264 422 269 47 738 43 734 48 740 488 65 473 54 482 64 334 372 321 377 327 375 319 703 279 699 75 696 5 320 8 324 2 313 38 628 98 612 39 624 98 612 437 607 341 611 128 619 98 612 341 611 439 604 437 607 98 612 211 419 225 415 215 413 261 711 256 709 265 713 261 711 248 707 256 709 279 699 61 694 75 696 343 12 336 14 382 4 201 495 218 477 211 489 339 254 332 233 358 245 26 337 20 335 13 332 34 731 33 733 44 735 33 733 20 729 26 736 20 335 21 334 14 333 20 729 28 727 21 726 20 729 34 731 28 727 28 727 34 731 43 734 1 102 6 100 9 98 133 765 192 759 181 764 291 388 311 384 330 380 159 486 170 491 203 481 181 397 156 408 133 410 300 174 316 202 333 176 293 402 297 395 278 399 225 415 250 412 232 409 189 430 193 429 197 424 210 387 212 389 207 393 302 366 301 365 306 363 309 361 302 366 306 363 384 296 359 300 411 285 77 441 84 444 79 448 438 70 445 69 453 71 273 373 244 378 263 374 244 378 219 382 228 376 153 110 157 105 144 107 190 400 182 407 186 401 193 429 201 423 197 424 212 389 210 387 213 383 219 382 212 389 213 383 177 440 187 435 184 433 187 435 193 429 189 430 271 405 251 406 250 412 293 402 303 398 297 395 201 423 211 419 208 416 438 70 436 73 433 75 215 413 232 409 237 403 365 47 439 45 279 43 469 83 472 85 484 87 433 75 429 80 341 78 332 233 328 221 358 245 366 22 398 24 396 19 24 638 19 641 30 634 362 6 393 3 397 0 418 276 374 281 375 289 373 345 389 349 374 351 90 549 107 544 91 551 85 450 86 456 80 454 90 470 76 461 89 466 80 454 86 456 87 460 120 563 110 559 119 566 461 55 473 54 457 53 63 748 50 743 49 745 45 737 43 734 34 731 458 512 460 513 459 510 33 733 34 731 20 729 25 639 18 642 24 638 430 533 431 530 435 525 21 334 22 336 15 331 448 516 442 518 456 514 8 324 5 320 10 329 484 661 496 652 487 658 4 315 11 318 6 322 471 528 469 524 476 532 2 313 0 317 5 320 468 523 471 528 470 531 492 648 485 646 483 651 476 532 477 535 471 528 176 169 195 194 199 182 18 325 11 318 17 321 493 653 492 648 483 651 121 567 117 565 134 569 313 756 192 759 318 757 205 721 75 720 183 722 147 502 130 504 151 498 81 465 95 459 73 462 249 753 252 754 257 751 426 271 411 285 414 278 346 777 192 773 363 775 61 694 57 700 55 697 218 477 323 474 250 480 196 668 207 664 158 666 190 671 158 666 182 675 218 477 187 490 159 486 65 581 68 585 55 587 175 116 173 120 179 127 72 580 67 575 56 577 212 665 219 667 217 663 190 671 196 668 158 666 218 477 159 486 203 481 158 666 217 663 223 669 223 669 217 663 299 672 323 474 218 477 285 475 475 614 479 616 480 621 456 74 436 73 453 71 73 591 96 593 59 592 82 584 72 580 60 582 218 477 203 481 285 475 458 76 456 74 453 71 439 45 446 58 443 60 356 218 401 243 370 203 184 433 187 435 189 430 153 110 161 119 166 115 324 348 314 356 320 353 123 453 140 447 129 446 369 339 386 338 372 341 101 432 97 437 83 431 376 360 391 362 361 364 163 138 180 139 162 133 189 430 183 427 184 433 42 91 29 94 36 88 194 231 195 194 168 205 325 476 285 475 286 478 274 678 194 673 223 669 66 774 60 776 56 772 359 190 325 175 331 165 170 112 157 105 175 116 75 720 62 724 112 723 200 390 196 396 191 394 200 390 207 393 196 396 369 307 392 309 344 310 322 682 324 693 348 692 383 25 350 26 349 28 437 607 439 604 444 597 169 436 154 443 177 440 133 765 111 766 192 759 60 770 66 768 99 769 169 436 177 440 184 433 322 682 342 685 326 677 63 95 49 97 42 91 370 203 352 219 356 218 157 105 153 110 166 115 353 228 316 202 354 241 328 221 332 233 307 209 206 763 181 764 192 759 188 145 178 157 176 169 363 538 429 536 430 533 156 408 181 397 171 404 186 401 182 407 171 404 400 267 371 265 408 264 5 320 3 323 10 329 15 31 52 42 65 40 355 255 351 260 371 265 47 44 15 31 40 46 41 101 42 91 46 99 51 742 50 743 64 746 111 421 115 425 99 428 398 24 383 25 391 23 72 438 77 441 67 445 109 452 116 449 112 442 71 578 64 576 69 573 348 692 368 689 322 682 112 723 183 722 75 720 356 218 399 230 401 243 356 218 357 204 399 230 160 155 143 180 155 181 302 681 322 682 299 672 177 497 159 486 187 490 138 132 127 125 126 136 149 137 150 121 138 132 132 114 150 121 153 110 153 110 150 121 164 126 149 137 163 138 150 121 160 155 178 157 163 138 113 131 104 153 114 144 113 131 118 118 107 130 474 610 475 614 453 601 494 656 501 654 490 660 42 91 36 88 53 86 335 350 338 346 324 348 174 206 176 169 167 192 178 157 172 179 155 181 178 157 155 181 176 169 46 741 45 737 44 735 167 192 137 177 168 205 192 773 346 777 318 779 135 106 124 109 144 107 452 609 449 608 451 615 452 609 451 615 454 613 133 410 156 408 146 414 450 598 445 596 447 595 75 720 205 721 237 719 453 601 447 595 455 605 143 180 142 168 139 178 115 425 101 432 83 431 56 577 63 574 53 579 408 264 358 245 368 259 342 253 358 245 328 221 307 209 310 246 290 234 58 695 55 697 54 701 370 203 401 243 404 229 446 603 447 595 443 600 193 496 218 477 201 495 167 192 136 167 137 177 119 113 110 124 108 117 120 563 94 561 110 559 139 178 142 168 126 136 94 468 81 465 78 467 45 737 34 731 44 735 155 181 139 178 136 167 175 116 198 123 170 112 331 165 286 151 305 142 325 175 286 151 331 165 307 209 243 196 328 221 203 164 214 129 286 151 328 221 274 220 326 244 170 112 198 123 214 129 30 35 19 33 15 31 318 779 346 777 336 781 193 496 187 490 218 477 367 8 343 12 382 4 383 25 349 28 377 27 347 479 337 483 323 474 159 486 147 502 151 498 377 27 349 28 334 29 99 428 83 431 82 434 167 192 155 181 136 167 1 319 4 315 6 322 25 639 24 638 32 635 370 203 359 190 331 165 111 766 99 769 66 768 382 4 360 7 397 0 68 469 81 465 73 462 96 455 109 452 112 442 68 585 73 591 58 590 32 635 31 633 25 639 112 723 62 724 96 725 349 714 350 717 319 715 354 241 332 233 355 255 60 582 72 580 56 577 369 339 367 342 385 340 485 625 479 616 478 620 303 494 323 474 308 492 31 633 23 637 25 639 4 315 12 316 11 318 96 593 62 594 59 592 164 126 173 120 161 119 230 171 199 182 243 196 128 84 341 78 363 82 337 483 327 500 317 493 324 312 344 310 348 311 202 148 188 145 176 169 305 142 333 176 331 165 253 761 206 763 192 759 151 108 130 111 135 106 198 123 204 128 231 135 435 51 439 45 430 49 191 394 186 401 181 397 292 676 302 681 299 672 54 588 55 587 68 585 391 23 383 25 377 27 72 438 83 431 77 441 331 165 333 176 340 191</p>
        </triangles>
      </mesh>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <originalMayaNodeId>float_MShape</originalMayaNodeId>
          <double_sided>1</double_sided>
        </technique>
      </extra>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="VisualSceneNode" name="float2a_med2_hard">
      <node id="float_M" name="float_M" type="NODE">
        <translate sid="translate">0 0 0</translate>
        <rotate sid="rotateZ">0 0 1 0</rotate>
        <rotate sid="rotateY">0 1 0 0</rotate>
        <rotate sid="rotateX">1 0 0 0</rotate>
        <scale sid="scale">0.1 0.1 0.1</scale>
        <instance_geometry url="#float_MShape">
          <bind_material>
            <technique_common>
              <instance_material symbol="lambert4SG" target="#lambert4">
                <bind_vertex_input semantic="TEX0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="TEX1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADAMaya">
            <originalMayaNodeId>float_M</originalMayaNodeId>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#VisualSceneNode"/>
  </scene>
</COLLADA>
