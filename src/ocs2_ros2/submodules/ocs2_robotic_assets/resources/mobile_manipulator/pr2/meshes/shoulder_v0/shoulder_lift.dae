<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>WakiMudi</author>
      <authoring_tool>OpenCOLLADA2009 x64</authoring_tool>
      <comments>
			ColladaMaya export options: 
			bakeTransforms=0;relativePaths=0;copyTextures=0;exportTriangles=0;exportCgfxFileReferences=1;
			isSampling=0;curveConstrainSampling=0;removeStaticCurves=1;exportPolygonMeshes=1;exportLights=1;
			exportCameras=1;exportJointsAndSkin=1;exportAnimations=1;exportInvisibleNodes=0;exportDefaultCameras=0;
			exportTexCoords=1;exportNormals=1;exportNormalsPerVertex=1;exportVertexColors=1;exportVertexColorsPerVertex=1;
			exportTexTangents=0;exportTangents=0;exportReferencedMaterials=0;exportMaterialsOnly=0;
			exportXRefs=1;dereferenceXRefs=1;exportCameraAsLookat=0;cameraXFov=0;cameraYFov=1;doublePrecision=0
		</comments>
      <source_data>file:///C:/Users/<USER>/Documents/maya/projects/willow_textures/scenes/shoulder_lift1d.mb</source_data>
    </contributor>
    <created>2010-04-30T15:51:20</created>
    <modified>2010-04-30T15:51:20</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Y_UP</up_axis>
  </asset>
  <library_materials>
    <material id="lambert3" name="lambert3">
      <instance_effect url="#lambert3-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="lambert3-fx">
      <profile_COMMON>
        <newparam sid="file4-surface">
          <surface type="2D">
            <init_from>file4</init_from>
          </surface>
        </newparam>
        <newparam sid="file4-sampler">
          <sampler2D>
            <source>file4-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="file3-surface">
          <surface type="2D">
            <init_from>file3</init_from>
          </surface>
        </newparam>
        <newparam sid="file3-sampler">
          <sampler2D>
            <source>file3-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="file4-sampler" texcoord="TEX0">
                <extra>
                  <technique profile="OpenCOLLADAMaya">
                    <blend_mode>NONE</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <transparent opaque="RGB_ZERO">
              <color>0 0 0 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </lambert>
          <extra>
            <technique profile="OpenCOLLADAMaya">
              <bump>
                <texture texture="file3-sampler" texcoord="TEX1">
                  <extra>
                    <technique profile="OpenCOLLADA3dsMax">
                      <amount>1</amount>
                      <bumpInterp>1</bumpInterp>
                    </technique>
                    <technique profile="OpenCOLLADAMaya">
                      <blend_mode>NONE</blend_mode>
                    </technique>
                  </extra>
                </texture>
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="file4" name="file4">
      <init_from>shoulder_lift_color.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file4</originalMayaNodeId>
        </technique>
      </extra>
    </image>
    <image id="file3" name="file3">
      <init_from>shoulder_lift_normals.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file3</originalMayaNodeId>
        </technique>
      </extra>
    </image>
  </library_images>
  <library_geometries>
    <geometry id="shoulder_lift_MShape" name="shoulder_lift_MShape">
      <mesh>
        <source id="shoulder_lift_MShape-positions" name="shoulder_lift_MShape-positions">
          <float_array id="shoulder_lift_MShape-positions-array" count="1506">-1.270913 0.7510774 1.72322e-4 -1.270241 -0.7489206 0.03719981 -1.268546 -0.1091845 -0.08042635 -1.264158 0.05399705 0.1296602 -1.254353 -0.7489139 -0.2115261 -1.251535 0.7510734 -0.225574 -1.246729 -0.7489156 0.2452351 -1.245915 -0.3518365 -0.2490036 -1.244625 0.7510628 0.2691005 -1.243397 -0.748886 0.01296994 -1.241605 0.7510789 -0.01201384 -1.236031 -0.06202273 0.1192476 -1.233391 0.003306657 -0.1346303 -1.230445 0.1765935 0.3190639 -1.229276 -0.74892 -0.3190307 -1.226726 -0.73892 -0.3287002 -1.226726 -0.55892 -0.3287002 -1.2241 -0.54892 -0.3383492 -1.216027 0.7510465 -0.2515817 -1.215576 -0.74892 -0.244809 -1.212186 -0.4684893 -0.2617499 -1.204558 0.05422186 0.2979021 -1.200297 -0.74892 -0.3112659 -1.198503 -0.7459939 -0.3181029 -1.197748 -0.73892 -0.3209356 -1.197748 -0.55892 -0.3209356 -1.195121 -0.54892 -0.3305844 -1.194367 0.7510744 0.3656861 -1.188405 -0.7489145 0.3612523 -1.182773 -0.01418 0.4640416 -1.175124 -0.5489177 -0.4843133 -1.174135 -0.7488586 0.4922308 -1.16133 0.04829427 0.4367616 -1.154484 0.1720173 -0.4561132 -1.150429 0.7510699 -0.5482424 -1.14661 0.7510799 0.5478411 -1.102042 0.1483377 0.570621 -1.101467 -0.54892 -0.6321944 -1.098148 -0.5513518 -0.637942 -1.081214 0.7510639 -0.611634 -1.080005 0.01291024 -0.610649 -1.078112 -0.54892 -0.6125969 -1.074939 0.1584264 -0.6773194 -1.074815 -0.5513518 -0.6183629 -1.072503 -0.4672858 -0.6807282 -1.06939 -0.04908497 0.6877135 -1.066307 -0.7488612 0.6366916 -1.059047 -0.4151166 -0.6451755 -1.054041 0.7510799 0.6547417 -1.049906 -0.6294863 -0.715565 -1.041118 0.7510447 0.7314823 -1.034559 -0.7489155 0.7388864 -1.022681 -0.6359285 -0.7024621 -0.9739888 -0.7374242 -0.8150128 -0.9730083 -0.739791 -0.8161832 -0.9728764 -0.74392 -0.8163403 -0.9728764 -0.7412084 -0.8163403 -0.9708564 -0.02402744 0.8196369 -0.969655 -0.74892 -0.8201642 -0.9672836 -0.3124685 -0.8240707 -0.9633929 0.7510566 -0.8305785 -0.96162 -0.739177 -0.8064189 -0.9611072 0.09874965 0.7855291 -0.9603334 -0.7273448 -0.8310622 -0.9510075 -0.7374242 -0.7957291 -0.9500269 -0.739791 -0.7968995 -0.9498951 -0.74392 -0.7970567 -0.9498951 -0.7412084 -0.7970567 -0.9489531 -0.7474555 -0.7981779 -0.9466734 -0.74892 -0.8008804 -0.9335511 -0.7447099 -0.81614 -0.916356 -0.7489165 0.8369205 -0.9013396 -0.748913 0.8953768 -0.9004109 0.7510429 -0.8546018 -0.8971437 -0.7489169 -0.9000565 -0.8936546 -0.7489204 -0.8600061 -0.8799725 0.7510638 0.875397 -0.8569249 0.7510484 0.9392321 -0.8223994 0.01739253 -0.9290376 -0.7778873 0.1052099 0.9665575 -0.769309 0.01997748 -1.011788 -0.7501856 -0.02387207 1.026287 -0.7421917 -0.7489122 -0.9968634 -0.7139404 0.7510548 -1.056262 -0.6911261 -0.7489185 -1.070552 -0.6846751 -0.7489036 1.041834 -0.6840704 0.7510576 -1.036586 -0.6788915 -0.2840418 1.039182 -0.6731342 -0.04276202 -1.042878 -0.6650617 -0.7489118 1.090967 -0.6604109 -0.1329386 -1.085954 -0.6432395 0.7510732 1.096212 -0.6409203 0.7510804 1.06323 -0.5879223 -0.01048516 1.127144 -0.5600901 0.03612808 -1.106873 -0.532787 -0.04639101 -1.153309 -0.5303351 0.1725857 1.121973 -0.5268198 -0.7489074 -1.124153 -0.4750294 -0.04149775 1.178647 -0.4419534 0.7510733 -1.191617 -0.4337625 0.07501405 -1.162815 -0.4044165 0.7510732 1.206752 -0.4018479 0.7510821 -1.175814 -0.3913497 0.07064719 1.177812 -0.3559379 -0.7489196 1.189794 -0.3477571 0.04168039 -1.221989 -0.3439466 -0.7489168 1.224497 -0.3277087 0.7510535 1.200588 -0.3236548 -0.7488921 -1.232284 -0.3004681 -0.7489197 -1.204923 -0.2329065 0.7510763 -1.249442 -0.2202794 0.00776572 1.251354 -0.1642419 -0.7489204 1.229721 -0.1352382 0.7510778 -1.233671 -0.1146993 -0.7489139 1.265968 -0.1133222 -0.08599967 1.235922 -0.1017818 0.06391327 -1.266912 -0.06689748 0.75108 1.26823 -0.001879119 0.06853438 1.270031 0 -0.74892 -1.27 0 -0.74892 1.27 0 0.75108 -1.27 0 0.75108 1.27 0 -0.74892 1.24 0 -0.74892 -1.24 0 0.75108 -1.24 0 0.75108 1.24 0.08451895 0.75108 1.267173 0.1330208 0.7510815 -1.263667 0.1401283 -0.7489133 1.262953 0.171789 0.5387549 -1.231525 0.1804078 0.7510816 -1.228322 0.1954786 -0.7489215 -1.228133 0.2195399 -0.7489066 -1.251433 0.2493001 -0.7489208 1.217041 0.2515548 0.7510795 1.245921 0.2621724 0.7510812 1.216006 0.3392003 0.4492524 -1.192704 0.3850711 -0.7489108 1.213446 0.3871094 0.7510856 -1.211465 0.3991682 -0.7489211 -1.208453 0.4620234 -0.7489196 -1.15167 0.4949033 0.7510828 -1.140331 0.5007355 0.751077 1.172118 0.5163715 0.001087935 1.127369 0.5245607 0.751073 1.125343 0.5288628 0.00107575 1.154645 0.540942 -0.7488997 1.119204 0.5455211 0.00108 1.151011 0.5455212 0.00108 1.121011 0.5570198 -0.7310629 -1.141328 0.5570198 -0.1378709 -1.141328 0.5570198 0.3156726 -1.141328 0.5714693 0.09292185 1.105956 0.5737167 -0.4196789 -1.133027 0.5737167 0.04739697 -1.133027 0.5737168 0.6796515 -1.133027 0.582052 -0.7489201 -1.128813 0.5836635 -0.08411872 1.106655 0.588512 0.7510802 -1.125574 0.59029 -0.5558397 -1.124481 0.59029 -0.1534959 -1.124481 0.59029 0.3515265 -1.124481 0.597761 -0.1540996 1.087854 0.5999197 -0.1480153 1.1204 0.6038827 -0.1168214 1.125962 0.6066475 0.6908122 -1.115746 0.6067357 -0.2405494 -1.115693 0.6067357 0.1827206 -1.115693 0.6558146 -0.7489137 1.093416 0.6600758 0.1671495 1.098752 0.6782252 -0.2299161 1.03808 0.6782252 0.2320761 1.03808 0.67848 -0.2293733 1.037917 0.6880927 -0.7489181 -1.03444 0.6946338 -0.2299161 1.063195 0.6946338 0.2320761 1.063195 0.704519 0.1766382 1.05279 0.7165098 0.2040754 1.068117 0.7165099 0.183076 1.046692 0.7165103 -0.2019157 1.068116 0.7165104 -0.1809162 1.046692 0.7387271 0.7510779 0.9984958 0.7477351 -0.7489187 0.9899809 0.7553135 -0.7489236 -1.024504 0.7558537 0.7510803 -0.984129 0.7567165 0.7510825 -1.022926 0.7721361 0.7510764 1.011278 0.7833115 0.3211258 0.9628188 0.8223397 0.3245845 0.9707732 0.843576 0.1533277 1.072448 0.8440453 0.3102225 0.9237338 0.8596872 -0.3475094 0.9361872 0.8674275 -0.3757893 0.8878078 0.8824645 -0.3083731 0.9119433 0.902189 0.006604421 -0.8532836 0.9046129 -0.338446 0.9226634 0.9077973 0.3677876 0.8987126 0.9150127 0.7510796 -0.8819147 0.9190338 0.3349212 0.8812138 0.9263082 0.7510744 0.8281134 0.9432662 -0.4439637 0.8048906 0.9465954 -0.7489147 0.8063293 0.9523104 0.4544135 0.7941694 0.9557452 -0.7489212 0.8428485 0.9760621 -0.4528933 0.8125287 0.9765302 0.4554743 0.8119661 0.9795512 -0.7489207 -0.8152477 0.9844051 0.7510804 -0.7605486 0.9891115 -0.03530478 1.118502 0.9919796 -0.4894698 0.7440272 0.998458 0.4978535 0.7353106 0.9985509 0.08320817 1.111991 1.002347 0.00108 -0.73 1.004403 -0.5014357 0.7271692 1.007323 -0.4936262 0.7240977 1.008599 0.001080298 -0.7717693 1.010818 0.7510686 -0.7700964 1.012431 -0.1529086 -0.717122 1.017035 0.4302642 0.8082417 1.020334 -0.4722866 0.7193492 1.021049 0.4707764 0.7199571 1.022154 -0.7489188 -0.7105957 1.022367 0.2241068 -0.7033576 1.02421 -0.08150118 1.140788 1.024996 0.00108 1.151011 1.025 0.00108 1.121011 1.025 0.1830763 1.046691 1.025001 -0.1014091 1.099958 1.025006 -0.06530894 1.112392 1.025007 -0.06196201 1.113252 1.025007 -0.05835044 1.114127 1.025581 -0.1809164 1.046691 1.026818 0.06737595 1.144074 1.028804 0.2820285 0.9423532 1.029304 0.1119183 1.128994 1.030102 -0.4690923 0.759501 1.032424 0.001079991 -0.76 1.033034 0.1576 1.105681 1.035547 -0.2186652 -0.7364781 1.038248 -0.3129276 0.9072284 1.039317 0.2040757 1.068116 1.042192 -0.2019159 1.068116 1.042994 0.2635904 -0.7268438 1.04647 0.4049681 0.7879015 1.047304 0.7510757 0.7334586 1.053159 0.5300111 0.7125626 1.054821 0.4397329 0.7378073 1.055108 0.2104206 -0.7306001 1.056222 -0.1834891 -0.742098 1.061103 -0.4431197 0.7294991 1.065987 0.4533395 0.7168641 1.06687 -0.3655434 -0.6341129 1.070341 -0.4546449 0.7116456 1.071134 0.5578864 0.6271226 1.072211 -0.05128064 1.112977 1.077395 -0.5637073 0.617299 1.079645 0.3516733 0.9093169 1.079735 0.3416681 0.8684261 1.082315 0.1011446 1.12463 1.082552 -0.7489183 0.6078856 1.091558 -0.5680301 0.6528063 1.093777 -0.3471417 0.8555083 1.098897 -0.05570987 1.137245 1.100552 -0.3950489 0.8535622 1.101218 -0.1653348 1.034219 1.101654 0.4593369 -0.5706493 1.103447 0.188274 1.069661 1.106619 0.3809508 -0.663853 1.109167 0.08633065 1.093627 1.109204 0.5490048 0.6329217 1.110045 0.4785105 0.7335844 1.11272 -0.1811904 1.069364 1.114803 -0.4784138 -0.6101185 1.118186 0.4914478 -0.603189 1.121875 0.5266651 0.6560596 1.123734 0.529093 0.6519415 1.125199 -0.4323674 -0.6262884 1.13202 0.6072679 0.5757009 1.133855 0.5783994 0.5101758 1.134304 0.5336487 0.5608192 1.134475 -0.5316923 0.5621391 1.13489 -0.5370899 0.6344198 1.136423 0.1499979 1.01295 1.143376 -0.6071349 0.4798861 1.143376 0.6083958 0.4798861 1.143377 -0.74892 -0.4798853 1.143377 -0.74892 0.4798853 1.143377 -0.5490201 -0.4798853 1.143377 0.5511801 -0.4798853 1.143377 0.75108 -0.4798853 1.143377 0.75108 0.4798853 1.14507 0.609285 0.4757969 1.146656 0.6357048 0.4719331 1.147044 -0.3313812 0.9160315 1.147374 0.1411636 1.003938 1.14789 0.5873399 0.4777719 1.149689 0.3267357 0.8143388 1.151613 0.3716243 0.8677724 1.154312 -0.556895 0.4995914 1.158141 0.568296 0.4784486 1.158339 -0.5983948 0.5409759 1.158655 -0.5662183 0.4787159 1.159734 0.5662112 0.4787203 1.159933 -0.5645733 0.4789474 1.161429 -0.3778185 0.7324552 1.163084 -0.04850679 1.046868 1.16655 0.5937975 0.5413758 1.167075 0.5779311 0.4473732 1.167464 -0.5766906 0.4461851 1.167493 -0.5233304 -0.5500852 1.1685 -0.6025126 0.4110106 1.168644 -0.1460741 1.039774 1.169649 0.1150583 0.9772028 1.169746 -0.594328 0.5355779 1.171039 -0.6484545 0.4914965 1.171039 0.6506145 0.4914965 1.171039 -0.6080049 -0.4914957 1.171039 0.6101649 -0.4914957 1.171039 -0.74892 -0.4914954 1.171039 -0.74892 0.4914954 1.171039 0.75108 -0.4914954 1.171039 0.75108 0.4914954 1.17152 0.6282023 0.3868719 1.172802 -0.5164914 0.5172825 1.172998 -0.1246991 0.9720116 1.173552 -0.6285514 0.3755787 1.175119 -0.6307986 0.3655536 1.175202 0.6332887 0.3649823 1.175357 0.4913215 0.5456747 1.177122 0.04414328 1.076794 1.179999 -0.1481396 0.7176485 1.18 -0.74892 -0.2979933 1.18 -0.74892 -0.2523919 1.18 -0.74892 -0.1797056 1.18 -0.74892 0.1797056 1.18 -0.74892 0.2979933 1.18 -0.7459909 -0.1726347 1.18 -0.7459909 0.1726347 1.18 -0.73892 -0.1697056 1.18 -0.73892 0.1697056 1.18 -0.7167681 -0.1697056 1.18 -0.7167681 0.1697056 1.18 -0.7106195 -0.171827 1.18 -0.7106184 0.1718278 1.18 -0.7070674 -0.1772772 1.18 -0.7070674 0.1772772 1.18 -0.6888429 -0.2385498 1.18 -0.6653283 -0.2979933 1.18 -0.6653283 0.2979933 1.18 -0.5486233 -0.5248107 1.18 -0.3821432 0.6213211 1.18 -0.2692403 0.7443772 1.18 -0.07282548 0.9364117 1.18 0.00108 -0.76 1.18 0.00108 0.73 1.18 0.001080001 0.9660106 1.18 0.02594073 0.9630251 1.18 0.06592196 0.7271117 1.18 0.07457878 0.9359968 1.18 0.3719886 0.6287502 1.18 0.5507834 -0.5248106 1.18 0.6674883 -0.2979933 1.18 0.6674883 0.2979933 1.18 0.7092274 -0.1772772 1.18 0.7092274 0.1772772 1.18 0.7092841 -0.2375917 1.18 0.7127785 -0.1718278 1.18 0.7189281 -0.1697056 1.18 0.7189281 0.1697056 1.18 0.74108 -0.1697056 1.18 0.74108 0.1697056 1.18 0.7481509 -0.1726347 1.18 0.7481509 0.1726347 1.18 0.75108 -0.2979933 1.18 0.75108 -0.1797056 1.18 0.75108 0.1797056 1.18 0.75108 0.2979933 1.180003 0.2358656 0.6947322 1.180024 0.1892685 0.8210516 1.183361 -0.5627578 -0.5099083 1.183573 0.6270696 0.4856984 1.183692 0.5663102 -0.5084296 1.183918 -0.6245707 0.4856841 1.184207 -0.2940187 -0.703277 1.185446 -0.4659052 -0.6008993 1.186441 0.1329615 1.017973 1.186663 -0.1300534 1.020443 1.188042 0.1651032 -0.7457355 1.188624 0.5906014 -0.4827166 1.189351 0.5902387 -0.4827217 1.189352 -0.5880786 -0.4827214 1.19014 -0.6407503 0.4600948 1.190153 0.3994862 -0.6503038 1.19116 0.1276775 1.011189 1.191661 0.6006644 -0.4712682 1.195132 0.5742204 0.5300539 1.196797 0.4882014 0.6386926 1.197151 -0.6823226 0.4141108 1.197316 0.616304 0.4764536 1.198563 0.6652123 0.422226 1.199973 -0.5028388 0.6182843 1.200153 0.2052764 0.9415424 1.200601 -0.542698 -0.5191538 1.201213 0.5444278 -0.5187431 1.202475 -0.608545 0.4709669 1.203141 0.6587308 -0.4062128 1.203451 -0.2352477 0.9123032 1.204429 0.5824844 -0.4747894 1.20534 -0.007279536 1.019564 1.206245 0.6035277 0.46494 1.207267 0.7076604 -0.3501964 1.207626 -0.714118 0.3466599 1.207638 -0.7077392 0.3495329 1.208006 0.5368629 -0.5115209 1.209322 -0.518597 -0.5218697 1.209758 0.4558989 0.6153106 1.209819 -0.5540775 -0.4819167 1.209836 0.5556932 -0.4821638 1.209951 -0.5281582 -0.5052725 1.20998 0.5072972 0.5413768 1.209998 0.5848095 0.4418667 1.21 -0.5726607 -0.4513646 1.21 0.4483333 0.5795625 1.21 0.5748323 -0.4513542 1.21 -0.74892 -0.2979915 1.21 -0.74892 -0.1797056 1.21 -0.74892 0.1797056 1.21 -0.74892 0.2979929 1.21 -0.7459909 -0.1726347 1.21 -0.7459909 0.1726347 1.21 -0.73892 -0.1697056 1.21 -0.73892 0.1697056 1.21 -0.7328448 0.2979929 1.21 -0.7305627 -0.2979915 1.21 -0.7167681 -0.1697056 1.21 -0.7167681 0.1697056 1.21 -0.7106196 0.171827 1.21 -0.7106185 -0.1718278 1.21 -0.7070674 -0.1772772 1.21 -0.7070674 0.1772772 1.21 -0.6829089 -0.3502864 1.21 -0.6661892 -0.2960507 1.21 -0.5856303 0.4509792 1.21 -0.5680937 -0.4623946 1.21 -0.5584048 -0.4689019 1.21 -0.5376055 -0.4925315 1.21 -0.5369971 -0.4936278 1.21 -0.5269244 -0.5040948 1.21 -0.4719267 0.5970673 1.21 -0.4406807 0.6384526 1.21 -0.2090713 0.8693208 1.21 -0.1448913 0.7209964 1.21 -0.0844532 0.9482748 1.21 -0.003600728 0.9659885 1.21 0.00108 -0.73 1.21 0.00108 0.73 1.21 0.001290609 0.9660053 1.21 0.005560828 0.9064993 1.21 0.0846006 0.9462215 1.21 0.201447 0.86661 1.21 0.4470276 0.615976 1.21 0.45077 0.6112779 1.21 0.5290844 -0.5040947 1.21 0.5397655 -0.4925315 1.21 0.5605648 -0.4689019 1.21 0.5703122 -0.4623378 1.21 0.5873111 0.4497698 1.21 0.6127989 0.4232421 1.21 0.6384486 0.396871 1.21 0.6683492 -0.2960507 1.21 0.6740788 0.3578568 1.21 0.68514 -0.3502129 1.21 0.6894255 0.2430434 1.21 0.6917526 0.3371321 1.21 0.7092274 -0.1772772 1.21 0.7092274 0.1772772 1.21 0.7127785 0.1718278 1.21 0.7127795 -0.171827 1.21 0.7189281 -0.1697056 1.21 0.7189281 0.1697056 1.21 0.7327228 -0.2979915 1.21 0.7350046 0.2979916 1.21 0.74108 -0.1697056 1.21 0.74108 0.1697056 1.21 0.7481509 -0.1726347 1.21 0.7481509 0.1726347 1.21 0.75108 -0.2979915 1.21 0.75108 -0.2388486 1.21 0.75108 -0.1797056 1.21 0.75108 0.1797056 1.21 0.75108 0.2979916 1.210001 0.1919994 0.7112734 1.210001 -0.4390299 0.5880288 1.210002 0.3859597 0.689766 1.210006 -0.6442903 0.3547906 1.210041 0.6225661 -0.4117683 1.210043 -0.6202159 -0.4119477 1.210342 0.3925474 -0.6207531 1.21175 -0.1827892 -0.7086005 1.211786 0.1849396 -0.7085783 1.211787 -0.411268 -0.6076146</float_array>
          <technique_common>
            <accessor source="#shoulder_lift_MShape-positions-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="shoulder_lift_MShape-normals" name="shoulder_lift_MShape-normals">
          <float_array id="shoulder_lift_MShape-normals-array" count="1506">-0.7049342 0.7092186 0.008764541 -0.8923875 -0.4508559 0.0193222 -0.9960743 0 -0.08852123 -0.9922519 -2.10822e-4 0.1242426 -0.79052 -0.5977278 -0.1334157 -0.9052876 0.3751855 -0.1992242 -0.6924605 -0.7089394 0.1338027 -0.9787408 0.002109923 -0.2050902 -0.8679028 0.4511549 0.2078555 0.70709 -0.7070932 -0.00656136 0.8956084 0.4448414 -0.001416078 0.9904697 0.001331295 -0.1377248 0.9885365 -0.004344274 0.1509197 -0.9672504 -0.001123134 0.2538213 -0.2012706 -0.9211813 -0.3330391 -0.5633096 -0.2557452 -0.7856697 -0.2528508 -0.3367546 -0.9070076 -0.7678041 -0.4802794 -0.4240385 0.6884586 0.7061803 0.1653309 0.7838985 -0.6035453 0.1457261 0.9725394 -0.008194023 0.2325935 0.9714854 0.004782333 -0.2370514 0.7210697 -0.691821 0.03797715 0.4663965 -0.6735525 -0.5734121 0.9236142 -0.08247962 -0.3743446 0.8677348 -0.2616021 -0.4226117 0.8320588 -0.5546809 0.002651518 0.7451025 0.6275142 -0.2259382 0.7728664 -0.599401 -0.2083169 -0.9427868 0.004153106 0.3333706 -0.6608498 -0.7072641 -0.2511076 -0.7884879 -0.5173658 0.3325951 0.9315948 0.005226444 -0.3634608 0.9252358 -0.001724117 0.3793885 -0.7105203 0.6039042 -0.3611931 -0.7239429 0.6027488 0.33556 0.9002171 -9.56022e-4 -0.4354405 -0.7744819 -0.5660751 -0.282377 -0.8095605 -0.580333 -0.08846132 0.7403848 0.5130739 0.4342645 0.8728906 8.19134e-4 0.4879153 0.6797745 -0.5966719 0.4264849 -0.8419633 0.003162029 -0.5395255 0.1010809 -0.7940081 0.5994446 -0.8537923 1.91472e-4 -0.5206138 -0.845534 -0.001220091 0.5339202 0.743774 -0.5137423 -0.4276321 0.8551965 0 0.518304 0.6780258 0.5984777 -0.4267381 -0.8777462 -0.4609646 -0.1306646 -0.5700583 0.708843 0.4154216 -0.5734037 -0.7095126 0.4096341 0.5758529 -0.3500275 0.738833 -0.891583 -0.451562 -0.03422591 -0.9687511 -0.2447032 0.04051453 -0.9064036 -0.2527355 0.3384631 -0.9579874 -0.08887357 0.2726936 -0.7690158 6.55034e-4 0.6392295 -0.5197243 -0.8459156 -0.1196386 -0.7660096 -0.001082594 -0.6428282 -0.6612296 0.5185055 -0.542151 -0.5972233 -0.3698139 0.7117317 0.7874923 -0.001783489 -0.616322 -0.7554674 -0.002082291 -0.6551828 0.2989797 -0.4413559 0.8460592 0.1193913 -0.2475472 0.9614916 0.1041692 -0.1367716 0.9851103 -0.0534245 -0.06529847 0.9964346 -0.1887671 -0.6703804 0.7176052 0.630197 -0.4148532 0.6563144 0.7508628 -0.01345433 0.6603212 0.5789996 -0.599467 -0.5526289 -0.5549185 -0.6027499 0.5733743 0.5792378 0.5999141 0.5518935 -0.6273446 -0.4496924 -0.6357794 0.6124215 -0.5218473 0.5938141 0.5677907 0.5987064 -0.5649463 -0.5434498 0.6031917 0.5837998 0.6443869 -0.001867514 0.7646975 0.621234 -0.003766191 -0.7836161 -0.612699 3.33653e-4 -0.7903163 -0.5875541 0.001896199 0.8091826 0.4214472 -0.7078428 0.5668693 -0.4442096 0.6288397 -0.6381524 -0.4134681 -0.6040596 -0.68129 0.4363897 -0.51673 -0.7365828 0.4915372 0.445599 0.7482195 0.5354593 -0.005715061 -0.8445418 0.5420676 5.50139e-4 0.8403346 -0.3946073 -0.6033349 0.6930166 -0.5159518 6.67819e-4 -0.8566173 -0.4273376 0.5581478 0.7112337 0.3707349 0.7063499 -0.6030136 -0.4620417 0.003927008 0.8868495 0.4479924 -0.002875545 0.8940328 -0.4297185 -0.002018649 -0.9029607 0.4305631 -0.002429711 -0.9025572 0.3496002 -0.5997467 0.7197801 -0.3893284 -0.002650816 0.9210952 -0.3032883 0.4507732 -0.8395354 0.3668354 0.001230906 0.9302851 -0.2718804 0.5197926 0.8098745 0.2446753 0.7074578 0.6630517 0.3741112 0.004393313 -0.9273735 0.2237463 -0.7075675 -0.6702879 -0.2686176 0.004834282 -0.9632348 -0.2263696 -0.6036656 0.7644243 0.2372774 0.445866 -0.8630777 -0.2176628 -0.5592604 -0.7999067 0.2207832 -0.4468877 0.8669177 -0.1518558 0.6032308 -0.7829766 -0.1761357 0.003391569 0.9843601 0.09902807 -0.7071666 -0.7000778 0.08840621 0.708141 0.7005146 -0.0733334 -0.6012108 0.7957184 0.09282263 -0.002327915 -0.9956799 -0.09486679 -0.001178734 -0.9954893 -0.07170966 0.603038 0.7944828 0.005368526 -2.77574e-4 0.9999856 -0.002847034 -0.6028788 -0.7978277 0.005335469 -0.7074361 0.7067571 -0.01413853 0.8010631 -0.5984129 0.002464559 0.7072778 0.7069315 -0.01495256 -0.7076243 -0.7064306 0.01055465 -0.6001068 0.7998503 -0.01867387 0.5527826 0.8331163 0.01287132 0.80026 -0.599515 0.04771648 0.7074803 0.7051203 0.09250326 0.5556156 -0.8262774 0.08355746 -0.6010898 0.7948014 -0.1535736 -0.009549011 0.9880911 -0.07502665 0.894676 0.44037 -0.160622 -0.7081594 0.6875396 0.1439508 -0.5553098 -0.8190904 -0.1734343 -0.7090942 -0.6834515 0.1689018 0.5560632 0.8137972 -0.1477324 0.7099366 -0.6885967 -0.2720424 -0.009786741 0.9622355 0.2939814 -0.5183298 0.8030623 0.2744721 0.5188613 -0.8095975 0.3066896 -0.5257324 -0.79344 -0.2045699 -0.8331209 0.5138683 -0.2942617 0.5537413 0.7789613 0.2982685 0.7093074 0.6386852 -0.2091701 -0.01563346 -0.9777544 -0.3679364 0.5557816 -0.7454727 0.2262659 0.1151768 0.9672322 -0.3643525 -0.6006683 -0.7116495 0.06275167 -0.008875432 0.9979896 -0.05751394 0.02123694 -0.9981188 0.4208102 -0.02069514 -0.9069126 0.4238217 0 -0.9057456 0.4110952 8.85796e-4 -0.911592 0.09623672 -0.4369605 -0.8943176 0.4523756 0 -0.8918275 0.451746 0 -0.8921466 0.4273939 0.003496171 -0.9040588 0.3608928 -0.611508 -0.7041409 -0.06420286 0.3085039 -0.949054 0.4106795 0.4533311 -0.7910962 0.484434 -0.001101802 -0.8748271 0.464799 0 -0.8854163 0.4644096 -1.21977e-4 -0.8856205 -0.3394232 0.1387186 -0.9303489 0.317003 -0.1253825 0.9401002 0.07589184 -0.3443066 0.9357849 0.4986773 0.00431324 -0.8667769 0.5018652 -5.21463e-4 -0.8649458 0.4934039 0 -0.8698003 0.4366783 -0.6045491 0.6662076 -0.01524885 0.4274154 0.9039267 -0.3655927 -0.01497036 -0.9306545 -0.3503097 -0.2494373 -0.902809 -0.01564951 -0.06432124 -0.9978065 -0.4138151 -0.7072523 0.573194 0.4449767 -0.1954012 0.8739646 0.3097278 0.2550134 0.9159896 -0.1058481 -0.4916315 -0.8643464 0.2312166 0.5077224 0.8299137 -0.1360071 -0.5984487 -0.7895324 0.21432 -0.4617417 0.8607331 -0.07513423 0.4138387 -0.9072444 -0.4179613 0.7073728 -0.570028 -0.4342786 -0.7067286 -0.558513 0.5408534 -0.374218 -0.7532851 -0.5207798 0.554062 0.6494641 0.4312713 0.627368 -0.6483937 0.4264073 0.7113652 0.5586917 -0.6207821 -0.06650263 -0.7811574 0.5826921 0.1635313 0.79607 -0.003270585 -0.5872921 -0.8093685 -0.4644077 -0.3561544 -0.8108511 0.5515409 -0.2296147 0.8019225 -0.6044081 0.172063 -0.7778721 -0.2527747 0.6140332 -0.7477086 -0.7405708 0.002584711 0.6719733 0.2405327 -0.6268777 0.7410589 0.5115525 0.2983697 0.8057851 0.6470765 0.4484757 -0.6165723 -0.1150908 -0.7127859 -0.6918744 -0.6091616 0.5975223 -0.52143 -0.7546409 0.03394071 -0.6552595 -0.7403485 -0.3153373 -0.5936721 -0.7032776 -0.1076676 -0.7027149 0.5330425 -0.7182847 0.4471385 0.7154149 -0.1233207 0.6877307 0.6413431 0.2497673 0.7254622 0.6801997 -0.5222446 -0.5143819 -0.5047941 0.7806612 0.3684439 4.65534e-4 0.1601771 -0.9870881 -0.7936831 -0.001574834 -0.6083296 -0.7719848 -0.1358031 -0.6209645 -0.1042024 -0.3158388 -0.9430736 -0.4309304 -0.01082213 0.9023202 -0.8084108 0.03494731 -0.5875803 -0.7621801 0.1259769 -0.6349893 0.6086661 -0.01298834 -0.7933202 0.6723558 0.5579045 -0.486498 -0.4533755 0.1040885 0.885221 0.2409684 0.6745536 0.6977904 -0.5791435 0.4268068 -0.6945709 -0.4815483 -0.4887965 -0.7274539 -0.7081077 -0.514394 0.4837172 -0.3897888 -0.185155 0.9020988 0.05987832 -0.298042 0.9526728 0.08624797 -0.01217472 0.9961993 -0.1005083 -0.0076936 -0.9949065 -0.1588647 -0.645151 -0.7473568 -0.1251391 0.4540605 -0.8821391 0.1493687 0.2512324 -0.9563321 0.4002989 0.2211582 -0.8892974 0.21686 0.172841 -0.9607798 -0.1168798 0.6305987 -0.7672576 0.2476266 0.2130768 0.9451346 -0.03121639 -0.7516969 -0.6587695 0.408894 0.3307269 0.8505443 0.5940974 -0.3806486 0.708629 0.1751411 0.05401858 -0.9830604 0.2301777 0.5139204 0.82638 0.4793234 -0.187972 -0.8572722 -0.0517816 0.7565788 -0.6518489 0.07252904 0.6724935 0.7365406 0.08061606 -0.6206473 0.7799347 0.5292583 0.1633513 -0.8325876 -0.02567869 -0.8000931 -0.599326 0.6880485 0.5178764 0.5083237 0.8235866 0.0319953 0.5662874 -0.1527408 -0.7695503 -0.6200504 0.1716688 0.2053284 -0.9635197 0.1338548 -0.2135134 -0.9677267 -0.1594164 0.7577701 -0.6327487 -0.3667407 -0.6885195 -0.6256534 -0.4007694 0.3405461 0.8505364 -0.2825883 0.7670317 -0.5760262 -0.7440205 -0.3487738 -0.5699037 -0.2627425 0.176554 -0.9485753 -0.8186363 0.2102772 -0.5344324 0.08320712 0.7561945 0.6490351 -0.2822956 -0.739729 -0.6108273 0.3432191 0.2938426 0.8921082 -0.4800931 -0.8325856 -0.2762461 0.8186998 -0.1164641 0.562287 -0.3826751 0.7078477 -0.5937268 0.3670209 -0.1646119 0.9155318 0.09446289 -0.7806938 0.6177329 -0.4786654 0.5234649 -0.7048858 -0.5226896 -0.3853526 0.7604597 0.3892937 0.5445664 0.7428984 0.2013339 0.4145017 -0.8874981 -0.5810798 -0.3105719 -0.7522575 0.549478 0.527414 0.6480035 0.1158033 0.8161631 0.5660983 0.3981316 -0.5089388 0.7631989 0.5749115 -0.3781096 -0.7256101 0.5644475 0.322661 -0.7597954 0.293399 0.7714393 0.5646223 0.3876803 0.7146994 0.5821585 0.1709855 -0.4633763 -0.8695092 0.8250287 0.2046895 0.5267162 -0.813728 -0.3663512 -0.4512576 -0.6316572 -0.6015963 -0.4889692 -0.5803462 0.6158023 -0.5329031 0.392657 -0.6999777 0.5965331 -0.5833921 -0.5544 -0.5935438 -0.907478 0.209009 -0.3644157 -0.7221071 -0.05532653 -0.6895654 -0.5190297 -0.8346459 0.1843213 -0.7581262 -0.5932375 -0.2707657 -0.4096566 0.6130075 0.6755762 -0.3142597 -0.6711127 0.6714525 -0.7552246 0.6001227 0.2636066 -0.8285782 0.4141839 -0.3767093 -0.9149914 -0.1923909 -0.3546498 -0.9433021 0.001962194 -0.3319298 0.49045 -0.6381315 0.593504 -0.8079295 -0.3557876 -0.4697503 -0.5737132 -0.1825625 -0.7984511 -0.744595 -0.5011211 -0.4409716 0.4987837 0.6529619 0.5699611 -0.7134271 0.5641037 -0.4157031 -0.2600428 -0.2429037 -0.9345456 0.591407 -0.4479016 0.6705385 -0.7680268 0.4894742 -0.4129768 -0.7206851 -0.4393182 -0.5362952 -0.7346292 0.5550856 -0.3901282 -0.838111 0.4232851 -0.3440924 -0.8783968 0.06099978 -0.4740233 0.5862341 0.5867933 0.5585725 -0.673022 -0.5633993 -0.4791896 -0.6234908 0.617022 -0.4801491 0.2475218 -0.5878832 -0.770147 -0.6592972 0.5839757 -0.4736028 0.743311 -0.291012 0.6023293 -0.9337684 -0.195472 -0.2997788 0.627609 -0.570713 0.5295222 0.8769674 -0.1986598 0.4375643 0.8768328 0.2588337 0.4051779 0.8634371 -0.2108254 -0.4582892 0.7359141 0.4009975 -0.545556 0.7461614 -0.6099592 -0.2668204 0.716611 -0.6347779 0.2890081 0.6774579 0.6358788 -0.3697419 0.7533044 0.6063709 0.2546502 -0.9637293 -0.1013926 -0.2468714 -0.5665783 0.6174504 -0.5456592 -0.9106773 0.2729152 -0.3101355 -0.970907 0.0822163 -0.2249002 -0.7557318 0.5267148 -0.3891539 -0.6977801 -0.5899089 -0.4063378 -0.6734953 -0.5386595 -0.5062116 0.7605968 0.1256011 0.6369591 -0.8214251 0.1337927 -0.5544008 -0.8236338 -0.5563868 0.1098227 -0.8574927 -0.5144961 0 -0.7139334 -0.6867576 0.1366134 -0.8978534 -0.4318331 -0.08590354 -0.862956 -0.4995798 -0.07567666 -0.2612013 -0.6825596 0.6825587 -0.3365555 -0.7506637 -0.568537 -0.9309238 -0.07125504 0.3581951 -0.4541828 -0.1738151 -0.8737885 -0.3197441 0.1042869 0.9417473 -0.7114452 0.1547082 -0.6855007 -0.5735925 0.4273682 0.6988191 -0.3304501 0.6614964 -0.6732202 -0.7103774 0.6237286 0.3260778 -0.8050693 0.5424378 -0.2400512 -0.740548 0.6351929 0.2193595 -0.7200002 0.6138278 0.3237517 -0.6757602 0.6704785 -0.3062786 0.2768683 -0.6469024 -0.7105358 -0.6425919 0.3908954 -0.6589965 -0.9950049 0.07465781 -0.06626881 -0.9967654 0.04278409 -0.06803098 0.3592841 -0.07154035 -0.9304821 -0.4918739 8.60968e-4 -0.870666 -0.9986081 0.006940334 -0.05228393 -0.9772869 -0.07718209 -0.1973655 -0.8488967 -0.06171352 -0.5249435 -0.9873564 -0.07546432 -0.1394004 -0.5996183 -0.4016904 -0.6921724 0.4367676 0.537751 -0.7211503 -0.6830717 -0.6314278 0.3670312 -0.7257719 -0.6220415 -0.2938018 -0.9294311 -0.3314203 0.1622293 -0.6094205 -0.6855804 -0.3982289 -1 0 0 -0.330451 -0.6615006 0.6732156 -0.45219 -0.1474969 0.8796413 -0.4737572 -0.2151136 -0.8539791 -0.8369662 0.1067715 0.5367377 -0.7139335 0.1366144 -0.6867573 -0.2612013 0.6825596 0.6825587 -0.2612013 0.6825596 -0.6825587 -0.7040105 0.7086573 0.04662628 -0.804918 0.5883674 0.07701179 -0.8978517 0.4318367 -0.08590337 -0.7867647 0.6131462 -0.07108389 -0.6964144 -0.2267363 -0.6808801 -0.9972577 -0.05301841 -0.05163402 0.2632282 -0.6729961 -0.6912215 0.7386804 0.4651348 0.4878535 0.5445992 0.5602695 -0.6241072 0.7560256 -0.4301276 0.4933716 0.3219582 -0.3401652 -0.883533 0.4093361 -0.5409287 -0.7347381 0.7660384 0.3544513 0.5362364 0.8986737 -0.1814855 0.3993101 0.3663657 0.2053646 -0.9075249 0.5447215 0.5539449 -0.6296217 0.4638773 0.6009192 -0.6509332 0.6561061 -0.5215987 -0.5453986 0.8637208 -0.3251937 0.3850137 0.5166098 0.4478915 -0.7297311 0.9309167 0.1589514 0.328829 0.6045715 0.5265549 -0.5976899 0.8112083 0.44285 0.3818703 0.8156244 0.4471619 0.3671555 0.9472842 -0.165208 0.2745157 0.8114604 0.4339832 0.3913955 0.8909984 0.3388101 0.3022079 0.8049217 -0.4665282 0.3666777 0.9186913 0.2463143 0.3087645 0.7141492 -0.4979352 -0.4919872 0.6434156 0.5146933 -0.5666633 0.8941134 -0.3292971 0.3035204 0.8711578 0.334562 -0.3593776 0.9220896 -0.2505338 0.2949296 0.8044448 0.4142789 -0.4257247 0.9588593 -0.02097041 0.2831061 0.9537069 0.2210135 0.2039517 0.8724672 0.3435951 -0.3474818 0.9793945 -0.1269134 0.1570972 0.9766486 -0.1207341 0.1777104 0.9315624 0.2460611 -0.267667 0.9590152 -0.2031461 -0.1975389 0.9890601 0.1146192 0.09285814 0.9869189 -0.1210945 -0.1064295 0.9915704 0.09612604 -0.08687925 0.9815377 -0.1408258 -0.129429 0.9888129 0.1139946 0.09620033 0.7675101 -0.5125359 -0.3850133 0.8990484 0.3595698 0.2498427 0.8464091 -0.3111498 -0.4321775 0.8063368 -0.4912829 0.3293357 0.5970054 -0.8012623 -0.03953876 0.7139331 -0.6867578 0.1366133 0.4541824 -0.873788 -0.1738185 0.8526425 -0.5160968 0.08151612 0.4759594 -0.6218773 0.6218773 0.336554 -0.5685359 -0.7506654 0.4541825 -0.1738192 0.8737879 0.8978514 -0.08590285 -0.4318374 0.9984436 -0.03043964 0.04673253 0.9539751 -0.1818847 -0.2384314 0.7114453 0.1547086 0.6855003 0.5594265 0.09123009 -0.823844 0.3304513 0.4924167 -0.8051881 0.330452 0.6614968 0.6732187 0.8269833 0.5192424 0.2156056 0.858918 0.4497811 -0.2448606 0.9586986 -0.2055992 -0.1965352 0.7183266 0.6165558 0.3222826 0.9952978 -0.07321513 0.06341804 0.9319847 -0.2787369 -0.2317547 0.7199856 0.5297972 0.4482585 0.8387883 0.4033647 0.3656929 0.99977 -0.01653419 -0.01366056 0.9116197 0.2913178 0.2899716 0.9882511 -0.1206811 0.09378561 0.993596 -0.08639871 0.07281619 0.9993067 -0.02328983 0.02904829 0.6007588 0.1890517 -0.776755 0.9922311 -0.05600012 0.1110919 0.9990621 -0.004754655 0.04303645 0.74175 -0.009077752 0.6706151 0.7082281 -0.01276916 -0.7058682 0.9993654 0.004220409 0.03536994 1 0 0 0.9928011 0.05187029 0.1079594 0.9899783 0.09671008 0.102908 0.9998255 0.01450245 0.01177299 0.9998276 0.01454964 0.01154518 0.74998 -0.4653125 0.4701216 0.7188621 -0.516261 0.4655232 0.7194133 -0.5302359 0.4486583 0.9890183 0.1085576 -0.1002892 0.9965073 0.06151814 0.05646955 0.996539 0.05976887 0.05777278 0.9918298 0.09721496 0.08260075 0.8330348 -0.5012277 0.2341447 0.9881486 0.1151288 0.1015269 0.9910315 0.09658333 -0.09234753 0.8221127 -0.5248399 -0.2206213 0.9901552 0.1011644 0.09673956 0.8700336 -0.4428627 0.2165971 0.8346449 -0.4994951 -0.2321048 0.3063397 -0.5857224 -0.7503901 0.3304518 -0.4924168 0.8051877 0.8355727 -0.09083684 0.5418181 0.8355739 -0.09085925 -0.5418126 0.9970815 0.04177279 -0.06390248 0.9828702 0.1136095 0.1451173 0.4541825 0.1738192 0.8737879 0.4541825 0.1738192 -0.8737879 0.4759594 0.6218773 0.6218773 0.4759594 0.6218773 -0.6218773 0.7632436 0.6301782 -0.1426003 0.5546996 0.8320507 0 0.8049173 0.5730488 0.1540235 0.8369663 0.5367376 -0.1067708 0.7018793 0.7088522 0.06995808 0.8059298 -0.1473415 -0.5733826 0.7944014 0.3623513 -0.487471 0.9927593 0.08587086 0.08399495 0.3908672 0.7943733 -0.4649666 0.9879537 0.1079148 -0.1109147 0.968832 -0.1728907 -0.1774075 0.9516923 -0.1397022 0.2734321 0.9991988 0.02687156 0.02965964 0.9952859 0.007175768 0.09671829 0.9695714 0.1614113 0.1840598</float_array>
          <technique_common>
            <accessor source="#shoulder_lift_MShape-normals-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="shoulder_lift_MShape-map1" name="shoulder_lift_MShape-map1">
          <float_array id="shoulder_lift_MShape-map1-array" count="1742">0.7187626 0.0325026 0.69633 0.03103359 0.7187626 0.002252328 0.7195674 0.04709337 0.6919771 0.001997392 0.7512525 0.02958611 0.6095049 0.02961717 0.6653539 0.02310312 0.7455468 0.001992165 0.609505 0.07828233 0.8280206 0.03511636 0.6586929 0.001992033 0.7757905 0.002202527 0.8280206 0.06841109 0.7855202 0.01803583 0.6386256 0.01195784 0.828021 0.1064256 0.6418444 0.002252328 0.7956808 0.002252328 0.6095048 0.1112155 0.6402884 0.002252328 0.7988997 0.01195784 0.6095045 0.150045 0.6372584 0.002252328 0.7972369 0.002252328 0.7840511 0.129108 0.6351816 0.002252372 0.8002668 0.002252328 0.7970895 0.153496 0.6215567 0.002252328 0.8023452 0.002252328 0.8280208 0.1522404 0.6095047 0.1785218 0.6216821 0.006622651 0.8158432 0.006622651 0.8280205 0.1785218 0.8159685 0.002252328 0.8280203 0.1982229 0.6095047 0.2222931 0.8280208 0.2370618 0.7295331 0.2417111 0.7238683 0.2601141 0.6095065 0.2552674 0.8280172 0.2781752 0.7123758 0.2765821 0.721139 0.2983266 0.6095058 0.2866422 0.8280151 0.3096911 0.6095046 0.3087068 0.6111794 0.3170618 0.8280181 0.33603 0.610118 0.3145189 0.625965 0.3275031 0.6108345 0.316919 0.6095047 0.3164305 0.720486 0.3358539 0.6269034 0.3314692 0.610924 0.3186079 0.6106281 0.3168998 0.6581323 0.3328008 0.6111794 0.3204097 0.6382859 0.3350978 0.6102331 0.3168998 0.6386402 0.3355781 0.6382859 0.338497 0.6108345 0.3202669 0.6106281 0.3202477 0.6102331 0.3202477 0.609718 0.3167626 0.6095047 0.3197784 0.8276882 0.9196392 0.8287182 0.9192125 0.8272614 0.9206693 0.8272614 0.9312581 0.8333584 0.9203156 0.8360133 0.9292415 0.832841 0.9195216 0.8394389 0.9379011 0.8319452 0.9192125 0.8272614 0.9379011 0.8563824 0.9643987 0.8272614 0.9643987 0.8272616 0.9980078 0.8831106 0.9868661 0.2017417 0.6969884 0.2002849 0.6984451 0.2007117 0.6974151 0.2002849 0.7016721 0.201388 0.7030853 0.2189733 0.6969884 0.2005941 0.702568 0.2189733 0.6996626 0.2471624 0.6969884 0.2265914 0.7066047 0.2471624 0.7175165 0.2458842 0.7204193 0.2186906 0.7090403 0.2644431 0.7363949 0.2355741 0.7157376 0.2943257 0.6969882 0.2429231 0.7233307 0.2413162 0.7226653 0.2828508 0.7742344 0.2511916 0.7270302 0.2879919 0.8062463 0.245767 0.7253725 0.3040376 0.9155041 0.2491694 0.7291483 0.243871 0.7247421 0.3248095 0.6969879 0.2877482 0.9155025 0.2515872 0.7305412 0.247473 0.7278607 0.2473133 0.7277721 0.3245798 0.9155045 0.2814473 0.8444881 0.2640783 0.7461765 0.2489978 0.7293281 0.3380938 0.7710463 0.2471624 0.9155042 0.2631001 0.7382172 0.339374 0.7251158 0.3380938 0.8327072 0.2634336 0.8776817 0.2787897 0.7794607 0.3400051 0.6969883 0.3381014 0.9067245 0.2471624 0.8949761 0.3395333 0.9155042 0.2189733 0.9155042 0.5641863 0.4391085 0.5713245 0.4335312 0.5722624 0.4352781 0.5528023 0.4149523 0.5756582 0.4150206 0.584458 0.4546604 0.5512139 0.4237998 0.5492042 0.4040282 0.5713245 0.3885913 0.4885796 0.4302948 0.5317969 0.3991805 0.5569094 0.3880372 0.5294132 0.3891669 0.585873 0.3924443 0.4885803 0.4614939 0.4885801 0.4029685 0.539 0.3854636 0.5339355 0.3842471 0.5978373 0.4584404 0.5482213 0.3806176 0.5854182 0.3763301 0.5254686 0.3924578 0.5319533 0.3826205 0.5978373 0.3885913 0.5978361 0.4626868 0.5761433 0.3723595 0.605465 0.3817137 0.4885793 0.3713465 0.5167232 0.3818707 0.6031377 0.3938195 0.6102488 0.452884 0.5503967 0.370427 0.5774302 0.3707641 0.6050609 0.3684756 0.5202541 0.3726682 0.6075085 0.3885905 0.6064949 0.3885902 0.6204434 0.4508303 0.6220801 0.377488 0.6243499 0.4335312 0.7070922 0.4591075 0.6127676 0.3885912 0.6161603 0.3670314 0.631409 0.4390714 0.62435 0.3885067 0.6314881 0.4391085 0.7070953 0.5015931 0.643581 0.3866613 0.707095 0.4289825 0.4885792 0.499718 0.6485652 0.3785721 0.6429175 0.4093555 0.6527385 0.411546 0.7070951 0.5379105 0.662547 0.383332 0.7070944 0.4000131 0.4885793 0.5379105 0.6642259 0.3819861 0.666796 0.389271 0.66267 0.400498 0.6105228 0.5544189 0.67545 0.3726432 0.6692991 0.3934016 0.7070952 0.5618367 0.4885832 0.5856501 0.6699046 0.3911665 0.6710423 0.3915918 0.6801139 0.3809585 0.7070951 0.5897624 0.707095 0.3802073 0.5877029 0.5949212 0.5728528 0.6151682 0.7070928 0.637652 0.4885792 0.6312779 0.639373 0.6368095 0.5826679 0.6512309 0.7070946 0.6714026 0.4885817 0.6661023 0.583609 0.6779219 0.7070866 0.6932471 0.4885793 0.6914603 0.4731253 0.696988 0.4903571 0.696988 0.4918139 0.6984448 0.4913872 0.6974147 0.4907109 0.7030849 0.4731253 0.6993298 0.4918139 0.7016718 0.4648512 0.7122302 0.4660357 0.7020578 0.4915048 0.7025675 0.4508387 0.7207756 0.4562096 0.7066898 0.4656172 0.7029871 0.4308738 0.7421319 0.447927 0.7174374 0.4449364 0.696988 0.429557 0.7373396 0.4449363 0.7116235 0.4495108 0.7127459 0.4235281 0.7418914 0.4264662 0.7328364 0.4214371 0.7233396 0.445783 0.7151029 0.4115034 0.7849813 0.4385146 0.7195085 0.3937523 0.6969879 0.3836343 0.7718183 0.3898959 0.7756316 0.3981692 0.7401124 0.3830912 0.7578138 0.4101917 0.8062459 0.4058941 0.7377526 0.380155 0.7554644 0.3783941 0.7937857 0.3650647 0.7848089 0.4129198 0.8340585 0.3572503 0.6969889 0.3844798 0.8068986 0.3678809 0.7871428 0.3607542 0.7796933 0.3616529 0.772595 0.3758136 0.805564 0.3680089 0.8050281 0.3508655 0.7979729 0.3758112 0.8062766 0.3533194 0.7845261 0.3596719 0.8125193 0.3786932 0.8184129 0.369229 0.8246883 0.3527032 0.820823 0.3902908 0.8354349 0.3682407 0.825458 0.360711 0.8335158 0.3793748 0.8359927 0.4160529 0.8623141 0.4234932 0.8772084 0.4321071 0.8714005 0.3901215 0.8602257 0.4268994 0.8725026 0.4268025 0.8712103 0.4376699 0.8799902 0.4274869 0.8717555 0.4393193 0.8897394 0.43767 0.8925913 0.448805 0.8940088 0.4521662 0.891282 0.4457809 0.8974383 0.4471277 0.89587 0.4510149 0.8916464 0.4326696 0.8945537 0.4550274 0.9029948 0.4548794 0.8953594 0.4449363 0.9008682 0.4243338 0.8860661 0.4587211 0.899096 0.4449364 0.9155038 0.412732 0.8832991 0.4644046 0.9042866 0.4811302 0.9065222 0.4731255 0.913162 0.4096879 0.9155032 0.3982512 0.8724408 0.4674236 0.9068612 0.4731255 0.9155038 0.4903571 0.9155038 0.3751166 0.8533731 0.369216 0.9155033 0.4907109 0.9094069 0.3616529 0.8398968 0.4918139 0.91082 0.3457852 0.9155034 0.4915047 0.9099242 0.4918139 0.914047 0.4913872 0.9150772 0.543151 0.8948314 0.5301967 0.8975872 0.5390103 0.8902639 0.5425882 0.8986802 0.5270079 0.8983282 0.5435769 0.8885565 0.5437468 0.8947019 0.5437466 0.9154878 0.5172491 0.9154878 0.5533306 0.8776468 0.5172491 0.9033103 0.5555368 0.883813 0.5651957 0.8873438 0.4996635 0.9093907 0.5924689 0.8536704 0.5481592 0.890332 0.5000173 0.9154878 0.6161944 0.8228338 0.4985605 0.910804 0.6200891 0.8266368 0.5934468 0.8336446 0.4985605 0.914031 0.621402 0.8279237 0.6331547 0.8186489 0.5654328 0.8602628 0.6101916 0.8169369 0.4989872 0.9150611 0.6263431 0.7990061 0.5750449 0.8404328 0.614564 0.8062298 0.5797619 0.8156758 0.6141291 0.8098515 0.5801826 0.8062298 0.6154382 0.7879066 0.5783833 0.784492 0.6102521 0.7954634 0.5822771 0.7668503 0.5643505 0.7504029 0.5805402 0.7510329 0.5984663 0.7555019 0.5491946 0.7308314 0.5557291 0.728617 0.6245004 0.781987 0.5466174 0.7249455 0.54361 0.723827 0.5637646 0.7239532 0.5388373 0.7220618 0.5435762 0.7235874 0.5437468 0.7176268 0.5437466 0.6969719 0.5337132 0.7183001 0.5623934 0.6969721 0.5285515 0.7145069 0.5172491 0.6969719 0.5270911 0.7141795 0.5172491 0.7091493 0.4996635 0.7030689 0.5000173 0.6969719 0.4985605 0.7016557 0.4985605 0.6984287 0.4988697 0.7025516 0.4989872 0.6973986 0.8220786 0.4375605 0.8462712 0.4208725 0.822078 0.4399872 0.8317364 0.3674465 0.8557295 0.4158382 0.8003588 0.4296358 0.8220786 0.3677118 0.8382253 0.3670843 0.8516504 0.4126514 0.9313361 0.4440847 0.7128221 0.4609344 0.8049031 0.4290585 0.8138056 0.3569462 0.8100485 0.3678264 0.84488 0.3665409 0.869206 0.3972344 0.7128217 0.4214932 0.7925068 0.4126513 0.8366559 0.3593618 0.8754996 0.3847851 0.8516505 0.3656256 0.9313365 0.4803847 0.7884278 0.4158382 0.7925068 0.3652068 0.884601 0.3688717 0.8882735 0.3747723 0.8493485 0.3562833 0.873152 0.3597507 0.7128217 0.4966169 0.7712972 0.3917936 0.7726175 0.385249 0.795526 0.3549325 0.9313365 0.504718 0.7643716 0.3567051 0.7736467 0.3499323 0.9018988 0.3554447 0.8916293 0.3553222 0.8760585 0.3492667 0.8319051 0.5173042 0.7535852 0.366968 0.7436796 0.3517028 0.8986444 0.3535989 0.7128207 0.5170305 0.9313365 0.5170305 0.8989981 0.353328 0.7128216 0.5337395 0.9313365 0.5267759 0.7347488 0.3482869 0.9084241 0.3470908 0.8930411 0.3426844 0.7391723 0.3580154 0.7353413 0.3466251 0.8230525 0.5491202 0.7486691 0.3422218 0.9313356 0.5759448 0.7128212 0.5671356 0.8158761 0.5862314 0.8203939 0.6026773 0.7128219 0.6139148 0.9313356 0.6107358 0.8184437 0.6263155 0.9313319 0.6418649 0.7128217 0.6483351 0.8184211 0.6584621 0.9313314 0.6686977 0.7128214 0.6677422 0.8147708 0.6728163 0.09875222 0.5141904 0.1002081 0.53126 0.09875131 0.5298514 0.1565973 0.51965 0.12643 0.53126 0.1919462 0.4950922 0.1278868 0.5326657 0.09875122 0.4779567 0.3172662 0.5162369 0.3172668 0.4833508 0.1278871 0.5539293 0.215718 0.4644873 0.3172657 0.5632423 0.3172646 0.4441741 0.09875195 0.4476507 0.1278868 0.5754722 0.2335775 0.4368955 0.230931 0.5820459 0.2057862 0.4157756 0.09876025 0.4116691 0.3172638 0.6043723 0.139779 0.5825425 0.3172671 0.4035679 0.1161501 0.5876174 0.1275325 0.5763096 0.1623323 0.6034242 0.2007013 0.3831917 0.100426 0.6021047 0.09875175 0.6144936 0.09875198 0.375737 0.317262 0.3768156 0.1018943 0.6044427 0.3172635 0.6372492 0.1000812 0.6022752 0.09875131 0.6028551 0.2107622 0.6307703 0.09987472 0.6022981 0.09947969 0.6022981 0.1884858 0.6415746 0.09875154 0.6393309 0.02949378 0.2761032 0.01941084 0.2668685 0.009854687 0.2236518 0.04665767 0.3079984 0.004681919 0.2198631 0.0558327 0.3114981 0.001858955 0.186977 0.08299722 0.3408753 0.006128431 0.1887522 0.0873486 0.338009 0.01300997 0.1337299 0.128462 0.3582914 0.005688584 0.1478003 0.1226195 0.3605935 0.01996714 0.1071941 0.1530729 0.3690172 0.03345226 0.09162112 0.1673009 0.3667198 0.0353349 0.08044179 0.1870021 0.3720121 0.05881013 0.05947665 0.1870021 0.3676418 0.06216763 0.05017735 0.2132834 0.3659406 0.09363457 0.03211376 0.2063802 0.3710896 0.09329671 0.02730891 0.2433951 0.3634849 0.1280878 0.0112058 0.2590982 0.3531223 0.1392624 0.0121038 0.2727349 0.3509726 0.1772566 0.002249893 0.2972384 0.3360192 0.1870021 0.006362303 0.2971127 0.3303673 0.1870021 0.001992024 0.3304075 0.2977967 0.1993145 0.002403795 0.3202986 0.315477 0.2251946 0.009857697 0.3342553 0.2991876 0.2236479 0.005499681 0.3575958 0.2586018 0.2599478 0.01625114 0.353566 0.2569104 0.2634186 0.02306518 0.3632715 0.2304126 0.2946178 0.04154398 0.3589012 0.2304129 0.2994847 0.03968195 0.3632715 0.2217969 0.3219441 0.06636485 0.3589012 0.2131811 0.3395704 0.08015383 0.3632715 0.2131811 0.353566 0.1170937 0.3589012 0.212151 0.3575958 0.1154024 0.3632715 0.212151 0.3589012 0.1435913 0.3632715 0.1435915 0.3632715 0.160823 0.3589012 0.160823 0.3589012 0.1618531 0.3632715 0.1618531 0.9022157 0.8347079 0.9313365 0.8347079 0.8888362 0.8479302 0.9191591 0.8082104 0.9313365 0.8755942 0.9313365 0.8082104 0.9252478 0.7994112 0.9252395 0.7906248 0.9313365 0.7909786 0.9298797 0.7895218 0.9266526 0.7895218 0.9309098 0.7899485 0.9257568 0.7898309 0.7635805 0.9298962 0.7585111 0.9476599 0.7592102 0.9379011 0.7635803 0.9588603 0.7592102 0.9203156 0.7573274 0.9596624 0.7635805 0.9203156 0.7585338 0.9739827 0.7635805 0.9195217 0.7635805 0.9789194 0.7592102 0.9192125 0.7592102 0.9860849 0.8563615 0.7057198 0.8473567 0.7143637 0.8550561 0.6970692 0.8456717 0.7304605 0.8568995 0.7034579 0.836403 0.7475572 0.8578407 0.7277603 0.8594519 0.7066457 0.857618 0.6999192 0.8381678 0.7553028 0.8575331 0.7619045 0.8599203 0.7011017 0.8577237 0.699972 0.8580603 0.6984532 0.8348632 0.785799 0.8609921 0.7590149 0.8597327 0.6899944 0.8313924 0.785799 0.8607319 0.785799 0.8607817 0.7287712 0.8603337 0.6828664 0.8383299 0.8126865 0.8563615 0.785799 0.8607319 0.6765411 0.8353181 0.8178109 0.8609868 0.8125845 0.8483782 0.8489424 0.8569745 0.8287881 0.8468639 0.8556504 0.8545395 0.8621936 0.857155 0.8538281 0.8563615 0.8658782 0.8568512 0.8679373 0.8577238 0.871626 0.8593627 0.8650151 0.5946082 0.1419441 0.5803557 0.09945852 0.5940846 0.1221653 0.5765989 0.08272419 0.6012967 0.1578479 0.5615308 0.06933347 0.5979529 0.1782614 0.540097 0.03903115 0.6023232 0.1782614 0.534777 0.04036408 0.6017358 0.1949705 0.5058682 0.0205583 0.5964555 0.2021877 0.4889129 0.00766775 0.5956945 0.2283666 0.4872215 0.01169754 0.5906391 0.2301135 0.4607239 0.001992024 0.5762423 0.2751458 0.460724 0.006362347 0.5690846 0.278003 0.4434922 0.001992024 0.5477492 0.3095661 0.4434922 0.006362347 0.5392334 0.3117537 0.4424621 0.006362347 0.5249521 0.3289731 0.4424621 0.001992024 0.5100646 0.3335981 0.48902 0.3493061 0.4699394 0.351385 0.4530383 0.3598814 0.4192026 0.3593961 0.4227323 0.3633066 0.3864986 0.3609921 0.3816501 0.3553432 0.3708376 0.3573389 0.3719688 0.3531174 0.3709728 0.3528561 0.369429 0.3569674 0.5374587 0.9238527 0.5374587 0.9248828 0.5330884 0.9238527 0.5330884 0.9248828 0.5374587 0.9206256 0.5330884 0.9206256 0.5374587 0.91973 0.5330884 0.9197299 0.5374587 0.9192125 0.5330884 0.9192125 0.5128968 0.9202426 0.5128968 0.9192125 0.5172672 0.9202426 0.5172672 0.9192125 0.5128968 0.9234696 0.5172672 0.9234696 0.5128968 0.9243653 0.5172672 0.9243654 0.5128968 0.9248828 0.5172672 0.9248828 0.8215359 0.9877681 0.8211093 0.9887981 0.8200793 0.9892248 0.8168522 0.9892248 0.815439 0.9881219 0.8159565 0.9889158 0.8215359 0.9791523 0.8215359 0.9705365 0.8188617 0.9705365 0.8152107 0.9629315 0.81193 0.962929 0.8080828 0.954771 0.809484 0.9708192 0.8028144 0.9539618 0.7969755 0.9447811 0.7958607 0.9481951 0.7952023 0.946595 0.7930726 0.9437068 0.7903295 0.9394302 0.7937822 0.9456388 0.7891963 0.9405119 0.7914315 0.938378 0.7907523 0.9421964 0.7693059 0.9235175 0.7703167 0.9192126 0.4970757 0.9192125 0.4968229 0.9202262 0.4969504 0.9197 0.5071714 0.9234696 0.5028011 0.9234696 0.5071714 0.9202426 0.5071714 0.9243655 0.5028011 0.9202426 0.5028011 0.9192125 0.5071714 0.9192125 0.7516019 0.95804 0.751601 0.9838996 0.7472307 0.9838996 0.7472307 0.966314 0.7465196 0.956472 0.7455555 0.9498499 0.7454045 0.9447259 0.7461822 0.9343686 0.7516012 0.9240625 0.7472307 0.9192125 0.1622568 0.6452995 0.1378458 0.8638141 0.1252419 0.6453 0.1640135 0.8638163 0.1058638 0.8638161 0.1870089 0.7087292 0.1058638 0.6453003 0.1894413 0.6557058 0.1870089 0.7748002 0.09103651 0.7454048 0.1915966 0.6453003 0.1894413 0.7478108 0.1870089 0.8612147 0.05871467 0.863812 0.07193462 0.6453008 0.1918556 0.7035061 0.1894413 0.8158532 0.1906555 0.8638161 0.1942385 0.6540799 0.1918556 0.7770764 0.0552035 0.7486436 0.1942514 0.7280973 0.1918556 0.8356887 0.04148126 0.6453013 0.1942514 0.7897581 0.02824887 0.7614737 0.00185896 0.645304 0.005182478 0.8638159 0.009656978 0.7740816 0.7724539 0.9154828 0.7554168 0.8065542 0.7839628 0.7261074 0.7375544 0.9154876 0.7739352 0.7378243 0.8022495 0.8311315 0.7339148 0.6969768 0.7714673 0.6969719 0.7825572 0.7246506 0.8249053 0.9154854 0.8250456 0.7261074 0.7184325 0.7970371 0.7825572 0.6984287 0.8247618 0.8079532 0.6825321 0.9154869 0.683178 0.6969727 0.7811486 0.6969719 0.6924067 0.8139713 0.7821445 0.6973982 0.672178 0.8131078 0.6526778 0.827682 0.6430528 0.6969805 0.6404233 0.9154878 0.3520476 0.6526877 0.3442391 0.628752 0.3627824 0.6601268 0.328487 0.5991555 0.3768849 0.6826998 0.3324727 0.5957778 0.3827194 0.6821915 0.3273629 0.5520064 0.3885234 0.693263 0.3229926 0.5520064 0.3913326 0.6899151 0.3290916 0.5235296 0.3917263 0.6902472 0.3256973 0.5200244 0.3319586 0.4938567 0.3402306 0.4847001 0.3435602 0.4672146 0.3573083 0.4517669 0.3587558 0.4419744 0.3892396 0.409308 0.404485 0.4031018 0.4364029 0.3814127 0.4380943 0.3854425 0.4645921 0.375737 0.4645918 0.3801073 0.4712349 0.3801073 0.4818237 0.375737 0.4818237 0.3801073 0.4828537 0.3801073 0.4828537 0.375737 0.6398295 0.9206693 0.6398295 0.9468912 0.6356081 0.9206693 0.6356081 0.9468912 0.6402122 0.9192125 0.6397194 0.9479217 0.6359907 0.9192125 0.6969998 0.9604561 0.6813102 0.9192125 0.6962243 0.9442525 0.615041 0.9281386 0.6194113 0.9192125 0.615041 0.9367981 0.615041 0.9192125 0.6194113 0.9365151 0.7122063 0.9192125 0.7044957 0.9625875 0.7027252 0.9215631 0.5273629 0.9238527 0.5229926 0.9206257 0.5273629 0.9206257 0.5229926 0.9238527 0.5273629 0.91973 0.5229926 0.9248828 0.5229926 0.9197299 0.5273629 0.9248828 0.5273629 0.9192125 0.5229926 0.9192125 0.7221532 0.9639875 0.7250663 0.9415927 0.7391988 0.9229047 0.7179317 0.9628564 0.7357964 0.9200498 0.739679 0.9220648 0.7362798 0.9192125 0.545335 0.9250541 0.5431842 0.9192125 0.5455671 0.9253578 0.5438418 0.9222799 0.8721378 0.6765411 0.872138 0.7138832 0.8677676 0.6876628 0.8677679 0.7074929 0.8677676 0.7322497 0.8677676 0.7416958 0.8721378 0.7416958 0.8721378 0.7629604 0.8677674 0.7634336 0.8677676 0.7975226 0.872138 0.8058097 0.5707856 0.9192916 0.5695074 0.9263507 0.5707619 0.9192125 0.5516005 0.9253579 0.5512925 0.9222904 0.556013 0.9209881 0.5513911 0.9195163 0.5514307 0.9192125 0.6049452 0.9192125 0.6093156 0.9194955 0.6093156 0.9367981 0.6049452 0.9367981 0.9022583 0.7521471 0.8786801 0.7857969 0.879873 0.6765411 0.6298827 0.9390588 0.6251367 0.9195668 0.6259767 0.9192125 0.578708 0.9257013 0.576511 0.9192125 0.5821041 0.9323561 0.5992198 0.93629 0.597558 0.9355037 0.5878295 0.9192125 0.6502697 0.9192125 0.6568521 0.9525842 0.6459377 0.9500542 0.563782 0.9260892 0.5632945 0.926089 0.5617385 0.9192125 0.5627684 0.926089 0.6755848 0.9528622 0.6656969 0.9325906 0.6625775 0.9192125</float_array>
          <technique_common>
            <accessor source="#shoulder_lift_MShape-map1-array" count="871" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="shoulder_lift_MShape-vertices" name="shoulder_lift_MShape-vertices">
          <input semantic="POSITION" source="#shoulder_lift_MShape-positions"/>
          <input semantic="NORMAL" source="#shoulder_lift_MShape-normals"/>
        </vertices>
        <triangles material="lambert3SG" count="1000">
          <input semantic="VERTEX" source="#shoulder_lift_MShape-vertices" offset="0"/>
          <input semantic="TEXCOORD" source="#shoulder_lift_MShape-map1" offset="1" set="0"/>
          <p>213 0 218 1 455 2 337 70 339 71 334 72 426 84 431 85 429 86 67 58 61 57 56 66 244 146 258 149 234 141 172 130 179 131 177 132 210 192 215 195 214 196 428 211 427 212 432 213 461 270 494 265 423 267 292 305 323 306 308 307 203 140 221 142 191 133 64 49 39 50 52 52 253 187 281 191 220 188 69 54 66 62 68 68 67 58 66 62 69 54 276 411 397 416 307 415 55 471 63 465 58 468 274 123 321 119 318 127 70 51 69 54 75 48 234 141 179 131 199 137 204 236 261 232 205 239 148 367 170 368 146 369 325 338 352 341 305 343 74 462 59 459 60 454 93 424 91 426 81 427 288 80 222 82 286 81 4 434 15 435 14 436 39 474 34 475 18 476 265 168 232 174 228 171 0 443 2 439 1 441 289 540 290 541 266 542 258 149 297 161 283 155 473 553 328 554 363 555 361 565 274 566 318 567 126 186 136 179 123 183 278 280 270 284 307 274 318 567 487 588 411 585 174 9 222 6 195 3 212 143 269 150 255 156 128 499 131 497 139 501 44 455 49 457 38 458 382 569 361 565 318 567 309 351 311 355 284 353 134 600 147 601 138 602 174 9 195 3 185 13 311 700 495 695 327 699 195 3 222 6 218 1 338 366 335 362 340 364 432 640 430 641 340 642 339 650 337 651 431 652 436 644 432 640 340 642 477 302 480 300 476 298 432 213 427 212 430 214 1 441 6 448 3 445 473 553 421 556 328 554 0 443 5 442 2 439 107 504 101 502 117 506 295 324 297 319 313 322 416 269 461 270 462 272 485 533 489 529 372 531 200 526 245 528 291 530 489 660 485 661 483 662 489 529 375 527 372 531 373 538 490 536 486 539 376 321 373 331 371 326 370 549 372 551 375 548 364 547 367 552 368 550 389 573 382 569 318 567 389 573 390 577 382 569 251 152 280 164 258 149 407 237 387 249 312 245 182 522 145 520 143 518 455 583 388 575 500 580 345 658 438 657 439 659 342 645 437 646 436 644 435 655 438 657 343 656 367 811 364 814 475 813 31 453 51 463 45 460 131 497 128 499 121 493 177 132 190 134 153 135 229 685 231 686 230 687 163 167 181 169 173 173 480 688 369 689 484 690 278 280 245 291 246 287 216 582 243 570 237 579 394 259 402 264 386 262 421 276 468 282 467 279 283 327 297 319 295 324 125 495 131 497 121 493 223 5 195 3 213 0 219 391 197 386 178 375 183 178 147 170 171 175 113 37 109 38 124 32 260 356 256 350 287 354 423 267 494 265 492 246 495 695 440 696 346 697 134 600 129 604 123 606 321 517 217 515 208 511 264 400 294 401 242 390 139 705 133 706 128 707 294 401 272 398 242 390 160 121 167 118 184 109 341 654 435 655 343 656 440 648 344 647 346 649 344 647 437 646 342 645 292 305 285 311 293 308 321 119 243 115 217 110 83 481 86 483 102 485 404 572 361 565 382 569 18 734 12 735 26 736 9 740 12 735 10 737 373 538 376 537 490 536 21 751 28 749 11 745 54 467 63 465 56 470 109 38 102 39 100 40 84 757 97 758 82 759 45 433 57 430 50 431 449 227 401 231 405 225 158 160 163 167 144 154 280 164 251 152 254 158 285 311 292 305 303 310 412 219 428 211 433 216 187 524 245 528 200 526 208 10 185 13 195 3 186 114 184 109 167 118 147 170 134 176 144 154 322 286 245 291 278 280 356 334 379 325 358 335 25 787 24 788 16 789 179 131 227 138 190 134 265 168 262 180 232 174 231 166 229 165 209 159 27 748 48 756 36 754 170 368 235 374 238 381 196 397 236 406 282 407 261 232 236 242 205 239 303 310 254 317 279 320 366 546 375 548 374 545 388 575 237 579 248 574 215 195 201 189 220 188 323 306 291 312 377 313 200 145 291 157 211 151 329 560 421 556 423 562 245 291 187 297 189 296 441 93 442 96 439 88 88 44 82 46 97 42 498 683 404 681 414 678 80 469 84 473 74 462 336 358 326 357 327 359 245 528 322 532 291 530 473 553 365 557 476 559 125 495 121 493 113 491 388 575 455 583 354 587 314 235 261 232 383 233 202 185 210 192 214 196 460 261 402 264 459 258 463 680 418 677 464 682 24 788 23 792 15 790 293 308 291 312 323 306 273 97 317 94 319 92 7 437 15 435 4 434 38 64 49 56 43 61 398 222 428 211 412 219 166 126 186 114 168 122 144 154 136 179 145 144 135 516 143 518 136 514 172 130 188 136 191 133 303 310 280 316 254 317 191 133 188 136 203 140 196 397 282 407 264 400 158 160 232 174 181 169 29 452 8 447 13 450 46 755 32 753 36 754 83 466 90 472 80 469 108 718 105 725 110 719 51 432 57 430 45 433 321 119 487 129 318 127 383 233 315 228 392 229 294 401 264 400 282 407 118 405 127 399 122 410 126 186 123 183 115 190 46 628 31 629 28 630 168 726 167 729 161 724 143 376 138 377 146 369 478 665 475 664 479 663 263 254 272 250 312 245 10 737 27 748 11 745 330 257 386 262 267 263 92 203 107 194 96 201 132 768 140 771 141 772 408 674 418 677 414 678 131 31 130 28 142 16 23 752 24 746 22 750 420 271 421 276 467 279 2 439 4 434 1 441 33 739 26 736 41 744 175 389 192 396 180 384 120 409 129 395 118 405 140 708 139 705 152 710 142 16 132 22 141 19 174 9 142 16 141 19 365 318 377 313 376 321 293 308 285 311 291 312 377 313 365 318 363 315 408 674 414 678 404 681 468 282 421 276 469 285 285 311 279 320 254 317 209 159 149 148 226 153 130 28 137 25 142 16 357 336 359 330 356 334 342 363 335 362 346 361 440 215 495 218 433 216 268 568 361 565 393 571 499 794 385 795 384 796 200 145 211 151 203 140 286 81 332 79 288 80 331 339 356 334 355 337 273 593 380 597 391 598 288 80 252 83 222 82 323 306 377 313 328 309 262 180 240 177 232 174 117 413 101 421 111 419 374 523 375 527 488 525 182 139 200 145 188 136 324 345 299 348 305 343 59 459 74 462 63 465 266 14 498 12 463 18 9 632 4 634 19 635 393 684 404 681 498 683 180 384 192 396 196 397 199 137 179 131 191 133 225 373 148 367 224 380 303 310 292 305 308 307 425 780 426 783 333 782 147 170 163 167 171 175 482 290 474 293 473 289 385 595 403 599 350 596 492 246 451 238 456 241 425 780 286 779 319 778 347 797 439 798 348 799 195 3 218 1 213 0 345 74 333 73 339 71 176 371 178 375 189 382 77 428 50 431 57 430 472 670 481 668 475 664 110 489 102 485 113 491 420 271 462 272 423 267 223 5 498 12 266 14 364 547 375 548 366 546 281 346 299 348 256 350 388 575 354 587 237 579 194 181 240 177 250 184 35 456 8 447 29 452 72 429 57 430 51 432 459 258 394 259 409 253 469 285 421 276 473 289 401 802 294 803 282 804 268 568 388 575 248 574 257 394 178 375 241 387 408 674 496 673 466 676 181 169 240 177 194 181 108 718 84 732 95 730 403 103 501 116 415 111 204 236 192 243 169 247 97 42 109 38 100 40 364 547 366 546 362 543 395 578 318 567 406 581 186 114 166 126 159 128 475 664 487 667 488 666 85 620 89 618 72 622 24 788 15 790 16 789 299 348 324 345 304 349 458 248 453 244 451 238 384 592 249 586 354 587 194 181 193 182 181 169 288 15 422 26 445 23 477 691 369 689 480 688 424 30 289 21 465 27 383 233 261 232 315 228 71 206 46 209 62 208 454 252 453 244 458 248 442 96 497 98 422 101 127 512 126 508 122 510 217 515 198 513 208 511 102 39 86 43 94 41 386 262 402 264 298 268 440 215 428 211 432 213 123 606 114 610 112 612 95 730 99 728 108 718 46 755 28 749 32 753 349 360 336 358 327 359 37 449 30 444 34 446 426 84 439 88 435 87 5 442 7 437 2 439 95 730 90 733 83 731 35 456 29 452 31 453 21 751 32 753 28 749 41 744 40 747 33 739 445 108 422 101 417 105 40 55 52 52 39 50 40 55 47 59 52 52 26 736 20 738 25 742 342 363 346 361 344 365 308 307 323 306 328 309 9 740 19 741 12 735 116 714 110 719 121 711 193 182 202 185 183 178 142 16 137 25 132 22 288 15 501 11 252 7 136 179 134 176 123 183 277 591 239 589 249 586 491 294 322 286 482 290 140 708 150 717 157 722 178 375 197 386 189 382 91 426 93 424 101 421 152 710 156 712 155 716 162 720 168 726 161 724 157 125 160 121 184 109 157 722 154 721 160 727 154 721 151 713 155 716 162 720 161 724 155 716 184 109 186 114 198 106 198 106 217 110 216 104 327 359 326 357 311 355 70 51 65 53 67 58 146 369 176 371 143 376 483 805 368 806 479 807 431 85 426 84 435 87 178 375 176 371 170 368 71 624 51 626 46 628 450 230 449 227 493 224 67 58 69 54 70 51 184 775 207 776 174 774 63 465 74 462 58 468 224 380 165 378 242 390 482 290 316 283 400 281 56 470 63 465 55 471 282 407 236 406 301 414 304 349 309 351 302 352 241 387 238 381 267 393 297 319 280 316 329 314 90 472 84 473 80 469 490 536 377 534 491 535 452 234 450 230 493 224 9 740 11 745 28 749 144 154 153 135 149 148 164 372 138 377 169 383 53 461 63 465 54 467 306 163 265 168 255 156 116 714 119 709 108 718 144 154 163 167 147 170 207 99 239 102 273 97 253 187 220 188 250 184 88 44 97 42 94 41 12 735 18 734 10 737 86 43 73 47 78 45 219 391 178 375 257 394 307 274 381 277 278 280 400 281 410 275 469 285 471 288 400 281 469 285 410 275 420 271 467 279 455 583 499 590 354 587 443 221 495 218 493 224 211 151 254 158 221 142 434 91 425 89 319 92 403 103 415 111 419 107 48 490 50 492 76 494 26 815 30 816 41 817 196 397 264 400 242 390 324 702 495 695 309 701 190 134 212 143 153 135 484 303 486 304 490 295 118 405 129 395 127 399 148 367 146 369 164 372 299 348 304 349 302 352 399 278 410 275 400 281 114 610 123 606 120 608 399 278 400 281 381 277 294 240 407 237 312 245 466 676 496 673 424 675 332 781 425 780 333 782 400 281 474 293 482 290 422 101 497 98 444 100 43 61 52 52 47 59 300 822 285 823 303 824 242 390 263 379 224 380 97 758 108 760 109 762 480 300 484 303 490 295 493 703 324 702 351 704 298 268 267 263 386 262 302 352 309 351 284 353 301 414 236 406 261 417 391 598 380 597 403 599 353 340 325 338 356 334 450 230 407 237 401 231 277 591 249 586 384 592 290 519 487 521 321 517 60 454 83 466 74 462 423 267 421 276 420 271 204 607 202 609 260 611 421 556 308 558 328 554 273 593 239 589 277 591 194 181 250 184 220 188 353 340 356 334 352 341 54 65 61 57 53 60 423 826 492 827 360 828 397 266 396 273 307 274 219 391 206 392 197 386 316 283 381 277 400 281 104 616 106 614 89 618 48 490 35 488 50 492 18 476 0 480 10 482 296 825 285 823 300 822 277 591 384 592 385 595 173 837 181 838 171 839 20 738 24 746 25 742 311 355 326 357 284 353 281 191 256 197 220 188 41 817 37 819 43 820 53 461 59 459 63 465 322 286 278 280 316 283 212 143 227 138 269 150 276 411 275 408 397 416 285 840 296 841 279 842 405 225 401 231 314 235 459 258 409 253 457 255 362 845 470 846 475 847 369 323 365 318 376 321 368 550 370 549 364 547 339 71 343 76 345 74 346 361 335 362 336 358 142 16 174 9 185 13 345 800 439 798 347 797 202 185 193 182 201 189 190 134 177 132 179 131 34 446 60 454 42 451 142 503 186 507 159 505 274 566 268 568 243 570 148 367 164 372 165 378 5 442 34 446 30 444 202 185 214 196 256 197 323 306 292 305 293 308 215 195 210 192 201 189 451 238 407 237 450 230 147 601 169 603 138 602 405 225 314 235 383 233 402 264 394 259 459 258 416 269 420 271 397 266 208 511 186 507 185 509 206 292 270 284 246 287 9 632 1 633 4 634 336 358 349 360 346 361 328 309 377 313 363 315 330 257 263 254 312 245 461 270 416 269 494 265 380 597 273 593 350 596 351 342 305 343 352 341 476 298 480 300 490 295 179 131 172 130 191 133 458 248 457 255 454 252 391 95 497 98 441 93 306 332 356 334 325 338 496 673 406 671 472 670 386 262 330 257 394 259 489 660 479 663 475 664 343 76 339 71 341 78 325 338 262 344 265 347 419 107 415 111 448 117 48 210 76 207 62 208 473 289 474 293 471 288 67 58 65 53 61 57 472 670 475 664 470 672 448 117 446 113 447 112 349 698 327 699 495 695 172 130 177 132 153 135 291 312 285 311 254 317 182 139 172 130 145 144 234 141 258 149 227 138 190 134 227 138 212 143 172 849 144 850 145 851 187 297 176 299 189 296 208 10 223 5 266 14 290 541 208 544 266 542 348 77 288 80 332 79 195 3 223 5 208 10 317 94 273 97 391 95 481 668 411 669 487 667 136 514 126 508 127 512 132 768 119 766 133 770 381 277 316 283 278 280 413 223 433 216 405 225 66 62 67 58 55 67 171 175 181 169 193 182 64 49 70 51 75 48 143 518 145 520 136 514 104 616 89 618 85 620 396 273 410 275 399 278 182 522 143 518 187 524 321 517 208 511 290 519 500 580 388 575 393 571 499 124 501 116 385 120 385 595 310 594 277 591 350 596 273 593 310 594 123 183 112 193 115 190 413 223 405 225 392 229 76 494 50 492 77 496 417 105 403 103 419 107 79 205 87 204 85 202 175 251 164 256 169 247 339 71 333 73 334 72 77 496 91 500 92 498 89 425 98 423 93 424 460 261 494 265 397 266 106 422 101 421 98 423 272 398 263 379 242 390 449 227 405 225 443 221 89 425 93 424 81 427 6 448 31 453 13 450 89 425 106 422 98 423 3 445 6 448 13 450 78 45 82 46 88 44 37 449 42 451 44 455 41 63 47 59 40 55 47 852 41 853 43 854 86 43 78 45 88 44 107 194 115 190 112 193 114 412 117 413 111 419 95 730 84 732 90 733 456 241 451 238 452 234 374 545 290 541 362 543 113 37 102 39 109 38 62 208 46 209 48 210 103 200 107 194 104 198 271 403 298 404 275 408 231 166 209 159 226 153 123 606 129 604 120 608 133 770 140 771 132 768 216 104 239 102 207 99 501 116 403 103 385 120 370 808 368 806 483 805 369 689 371 692 484 690 341 654 339 650 431 652 340 642 342 645 436 644 152 710 139 705 156 712 150 717 151 713 154 721 140 708 151 713 150 717 151 713 152 710 155 716 140 708 152 710 151 713 159 715 166 723 162 720 159 715 162 720 156 712 161 724 167 729 160 727 161 724 160 727 154 721 135 388 138 377 143 376 133 706 119 709 128 707 110 489 99 487 102 485 174 774 157 773 184 775 180 384 164 372 175 389 300 843 303 844 279 842 183 605 204 607 169 603 119 766 124 764 108 760 108 760 124 764 109 762 235 855 233 856 238 857 17 793 26 791 16 789 199 137 191 133 221 142 76 494 77 496 92 498 193 182 183 178 171 175 260 199 202 185 256 197 211 151 221 142 203 140 468 282 410 275 467 279 410 275 468 282 469 285 494 265 416 269 397 266 295 324 313 322 269 328 374 545 362 543 366 546 359 330 313 322 379 325 256 197 215 195 220 188 99 728 110 719 105 725 246 287 245 291 206 292 119 709 121 711 128 707 58 765 74 761 75 763 231 166 226 153 255 156 267 393 298 404 257 394 49 56 52 52 43 61 305 343 351 342 324 345 71 624 72 622 51 626 309 701 495 695 311 700 310 594 273 593 277 591 458 248 451 238 492 246 125 35 113 37 124 32 407 237 451 238 453 244 9 632 6 631 1 633 346 697 349 698 495 695 273 97 319 92 207 99 410 275 396 273 420 271 19 635 4 634 14 636 114 610 106 614 112 612 163 167 173 173 171 175 60 477 39 474 73 479 227 138 179 131 234 141 314 858 301 859 261 860 193 182 194 181 220 188 440 215 433 216 428 211 148 367 233 370 170 368 493 703 495 695 324 702 324 345 309 351 304 349 358 335 379 325 378 333 127 399 129 395 135 388 137 25 130 28 132 22 156 712 162 720 155 716 136 179 144 154 134 176 164 372 146 369 138 377 129 395 138 377 135 388 207 99 184 109 198 106 218 1 222 6 252 7 150 717 154 721 157 722 159 715 156 712 139 705 140 708 133 706 139 705 155 716 161 724 154 721 162 720 166 723 168 726 207 99 198 106 216 104 168 122 186 114 167 118 124 32 132 22 130 28 228 171 229 165 255 156 345 74 347 75 333 73 364 547 370 549 375 548 362 34 289 21 424 30 35 488 48 490 27 484 280 164 297 161 258 149 326 357 287 354 284 353 10 482 0 480 27 484 169 247 192 243 175 251 409 253 387 249 453 244 16 438 15 435 7 437 180 384 196 397 242 390 232 174 240 177 181 169 360 828 492 827 378 829 493 224 449 227 443 221 315 228 398 222 392 229 60 477 34 475 39 474 223 5 500 8 498 12 456 832 452 833 355 831 425 780 332 781 286 779 466 676 418 677 408 674 294 240 312 245 272 250 241 387 267 393 257 394 262 180 281 191 253 187 294 240 401 231 407 237 482 290 322 286 316 283 361 565 404 572 393 571 209 159 229 165 149 148 225 373 233 370 148 367 226 153 149 148 212 143 460 261 459 258 492 246 354 587 499 590 384 592 385 595 350 596 310 594 354 587 249 586 237 579 24 746 19 741 22 750 387 249 407 237 453 244 20 738 19 741 24 746 229 165 158 160 149 148 418 677 466 676 424 675 417 105 422 101 444 100 153 135 212 143 149 148 287 354 256 350 284 353 492 827 355 831 358 830 307 415 270 402 276 411 146 369 170 368 176 371 240 177 262 180 250 184 178 375 238 381 241 387 170 368 238 381 178 375 413 223 392 229 398 222 369 323 376 321 371 326 489 660 483 662 479 663 342 363 340 364 335 362 299 348 302 352 256 350 452 833 331 834 355 831 457 255 492 246 459 258 205 239 192 243 204 236 245 291 189 296 206 292 455 2 223 5 213 0 226 153 212 143 255 156 299 348 281 346 305 343 351 342 352 341 331 339 306 332 357 336 356 334 141 772 157 773 174 774 329 314 303 310 308 307 498 683 414 678 463 680 236 406 196 397 192 396 18 476 34 475 5 478 313 322 297 319 329 314 5 442 30 444 17 440 83 481 60 477 73 479 8 447 0 443 3 445 447 112 446 113 417 105 300 843 279 842 296 841 91 426 77 428 81 427 298 268 402 264 397 266 252 7 501 11 499 4 487 521 374 523 488 525 490 536 376 537 377 534 333 782 426 783 334 784 489 529 488 525 375 527 111 419 106 422 114 412 263 379 225 373 224 380 283 155 295 162 269 150 450 230 401 231 449 227 174 774 207 776 222 777 159 505 139 501 142 503 274 123 243 115 321 119 248 574 243 570 268 568 91 500 101 502 92 498 455 2 218 1 499 4 415 111 501 116 448 117 500 580 393 571 498 584 223 5 455 2 500 8 53 60 64 49 49 56 263 254 330 257 259 260 233 370 235 374 170 368 408 674 406 671 496 673 398 222 320 226 428 211 378 333 379 325 360 329 202 185 201 189 210 192 228 171 158 160 229 165 232 174 158 160 228 171 378 829 492 827 358 830 313 322 359 330 306 332 288 15 442 29 422 26 104 198 85 202 103 200 313 322 329 314 379 325 452 234 451 238 450 230 265 168 228 171 255 156 329 560 308 558 421 556 172 868 153 869 144 870 287 354 326 357 336 358 362 543 290 541 289 540 275 408 270 402 219 391 124 32 130 28 125 35 490 295 482 290 473 289 255 156 269 150 306 163 247 147 244 146 199 137 423 562 360 564 329 560 59 459 53 461 49 457 92 498 101 502 107 504 83 731 99 728 95 730 446 20 448 17 288 15 501 11 288 15 448 17 266 14 463 18 289 21 434 91 441 93 439 88 227 138 283 155 269 150 446 113 445 108 417 105 464 682 418 677 465 679 216 104 217 110 243 115 100 40 102 39 94 41 465 679 418 677 424 675 325 172 265 168 306 163 298 861 397 862 275 863 348 799 439 798 442 801 269 328 313 322 306 332 18 476 5 478 0 480 28 630 31 629 6 631 428 617 320 613 287 615 391 95 434 91 317 94 19 741 20 738 12 735 9 632 28 630 6 631 27 484 0 480 8 486 409 253 453 244 454 252 229 864 230 865 255 866 260 611 287 615 320 613 494 265 460 261 492 246 347 75 348 77 333 73 221 142 254 158 251 152 320 226 315 228 261 232 131 497 142 503 139 501 23 638 14 636 15 639 17 818 30 816 26 815 481 668 487 667 475 664 426 783 429 786 337 785 483 805 372 810 370 808 435 655 341 654 431 652 371 692 373 693 484 690 334 784 426 783 337 785 338 625 427 621 335 623 430 627 427 621 338 625 348 77 332 79 333 73 392 229 405 225 383 233 83 481 73 479 86 483 258 149 244 146 247 147 444 100 403 103 417 105 363 555 365 557 473 553 351 835 452 833 493 836 331 834 452 833 351 835 397 266 402 264 460 261 479 807 368 806 478 809 476 559 365 557 477 561 368 806 367 811 478 809 356 334 331 339 352 341 490 295 491 294 482 290 8 486 35 488 27 484 148 367 165 378 224 380 256 197 214 196 215 195 185 509 186 507 142 503 230 865 231 867 255 866 246 287 270 284 278 280 204 607 260 611 320 613 251 152 247 147 221 142 406 581 408 576 395 578 456 832 355 831 492 827 182 522 187 524 200 526 183 605 169 603 147 601 389 573 395 578 390 577 61 57 64 49 53 60 55 67 68 68 66 62 270 402 206 392 219 391 58 765 69 767 68 769 318 567 395 578 389 573 65 53 64 49 61 57 43 820 37 819 38 821 55 67 67 58 56 66 395 578 408 576 390 577 282 407 314 418 401 420 61 57 54 65 56 66 22 637 14 636 23 638 26 791 25 787 16 789 55 67 58 69 68 68 365 557 369 563 477 561 343 656 438 657 345 658 437 646 344 647 440 648 367 811 475 813 478 809 484 690 373 693 486 694 485 812 372 810 483 805 337 651 429 653 431 652 340 642 430 641 338 643 335 623 427 621 336 619 97 758 84 757 108 760 22 637 19 635 14 636 336 619 428 617 287 615 268 568 274 566 361 565 409 253 394 259 330 257 427 621 428 617 336 619 165 378 164 372 180 384 276 411 270 402 275 408 267 263 259 260 330 257 38 458 37 449 44 455 257 394 298 404 271 403 353 340 352 341 325 338 489 660 475 664 488 666 271 403 219 391 257 394 356 334 358 335 355 337 131 31 125 35 130 28 330 257 312 245 409 253 250 184 262 180 253 187 364 848 362 845 475 847 393 571 388 575 268 568 476 298 490 295 473 289 495 218 443 221 433 216 423 267 462 272 461 270 439 88 438 90 435 87 189 382 197 386 206 392 444 100 391 95 403 103 404 572 382 569 408 576 281 346 262 344 305 343 220 188 201 189 193 182 471 288 469 285 473 289 419 107 447 112 417 105 419 107 448 117 447 112 463 680 414 678 418 677 205 239 236 242 192 243 78 45 75 48 82 46 481 668 406 671 411 669 75 763 74 761 82 759 282 407 301 414 314 418 443 221 405 225 433 216 51 626 31 629 46 628 138 602 129 604 134 600 391 95 444 100 497 98 49 56 64 49 52 52 249 586 216 582 237 579 319 92 317 94 434 91 465 27 289 21 464 24 289 21 463 18 464 24 261 232 204 236 320 226 288 15 445 23 446 20 119 766 132 768 124 764 263 379 233 370 225 373 135 516 136 514 127 512 396 273 397 266 420 271 252 7 499 4 218 1 222 777 207 776 319 778 374 523 487 521 290 519 219 391 271 403 275 408 322 532 377 534 291 530 85 202 71 206 79 205 211 151 291 157 254 158 121 493 110 489 113 491 413 223 412 219 433 216 85 620 72 622 71 624 416 269 462 272 420 271 312 245 387 249 409 253 315 228 320 226 398 222 158 160 144 154 149 148 306 332 359 330 357 336 442 29 288 15 348 33 403 599 380 597 350 596 73 47 39 50 64 49 181 169 163 167 158 160 263 379 259 385 233 370 99 487 83 481 102 485 280 316 303 310 329 314 426 84 425 89 439 88 425 89 434 91 439 88 233 370 259 385 238 381 390 577 408 576 382 569 165 378 180 384 242 390 107 194 126 186 115 190 319 778 286 779 222 777 244 146 234 141 199 137 221 142 247 147 199 137 96 201 103 200 85 202 413 223 398 222 412 219 249 586 239 589 216 582 497 98 442 96 441 93 470 36 362 34 424 30 202 609 204 607 183 605 472 670 470 672 496 673 106 614 104 616 112 612 379 325 356 334 359 330 107 504 117 506 126 508 424 675 496 673 470 672 39 743 18 734 33 739 307 274 396 273 381 277 396 273 399 278 381 277 126 508 117 506 122 510 457 255 409 253 454 252 251 152 258 149 247 147 237 579 243 570 248 574 157 773 141 772 140 771 474 293 400 281 471 288 411 585 406 581 318 567 108 718 99 728 105 725 188 136 200 145 203 140 377 534 322 532 491 535 187 297 143 301 176 299 81 427 72 429 89 425 227 138 258 149 283 155 440 215 436 217 437 220 440 215 432 213 436 217 188 136 172 130 182 139 379 325 329 314 360 329 259 385 267 393 238 381 305 343 262 344 325 338 69 767 58 765 75 763 391 95 441 93 434 91 472 670 406 671 481 668 70 51 64 49 65 53 208 511 198 513 186 507 17 440 16 438 7 437 458 248 492 246 457 255 41 817 30 816 37 819 302 352 284 353 256 350 76 207 92 203 79 205 59 459 49 457 60 454 98 423 101 421 93 424 72 429 77 428 57 430 8 447 3 445 13 450 37 449 34 446 42 451 5 442 17 440 7 437 60 454 49 457 42 451 42 451 49 457 44 455 121 711 119 709 116 714 83 466 80 469 74 462 108 718 110 719 116 714 35 456 31 453 45 460 3 445 0 443 1 441 35 456 45 460 50 464 81 427 77 428 72 429 29 452 13 450 31 453 111 419 101 421 106 422 118 405 117 413 114 412 117 413 118 405 122 410 118 405 114 412 120 409 4 434 2 439 7 437 74 761 84 757 82 759 73 47 75 48 78 45 103 200 96 201 107 194 76 207 79 205 71 206 39 743 33 739 40 747 92 203 96 201 87 204 86 43 88 44 94 41 71 206 62 208 76 207 87 204 96 201 85 202 75 48 73 47 64 49 27 748 32 753 21 751 36 754 48 756 46 755 9 740 10 737 11 745 100 40 94 41 97 42 79 205 92 203 87 204 26 736 12 735 20 738 32 753 27 748 36 754 107 194 112 193 104 198 33 739 18 734 26 736 21 751 11 745 27 748</p>
        </triangles>
      </mesh>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <originalMayaNodeId>shoulder_lift_MShape</originalMayaNodeId>
          <double_sided>1</double_sided>
        </technique>
      </extra>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="VisualSceneNode" name="shoulder_lift1d">
      <node id="shoulder_lift_M" name="shoulder_lift_M" type="NODE">
        <translate sid="translate">0 0 0</translate>
        <rotate sid="rotateZ">0 0 1 0</rotate>
        <rotate sid="rotateY">0 1 0 0</rotate>
        <rotate sid="rotateX">1 0 0 0</rotate>
        <scale sid="scale">0.1 0.1 0.1</scale>
        <instance_geometry url="#shoulder_lift_MShape">
          <bind_material>
            <technique_common>
              <instance_material symbol="lambert3SG" target="#lambert3">
                <bind_vertex_input semantic="TEX0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="TEX1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADAMaya">
            <originalMayaNodeId>shoulder_lift_M</originalMayaNodeId>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#VisualSceneNode"/>
  </scene>
</COLLADA>
