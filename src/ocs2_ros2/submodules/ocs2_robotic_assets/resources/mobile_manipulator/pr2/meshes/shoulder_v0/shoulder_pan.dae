<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>WakiMudi</author>
      <authoring_tool>OpenCOLLADA2009 x64</authoring_tool>
      <comments>
			ColladaMaya export options: 
			bakeTransforms=0;relativePaths=0;copyTextures=0;exportTriangles=0;exportCgfxFileReferences=1;
			isSampling=0;curveConstrainSampling=0;removeStaticCurves=1;exportPolygonMeshes=1;exportLights=1;
			exportCameras=1;exportJointsAndSkin=1;exportAnimations=1;exportInvisibleNodes=0;exportDefaultCameras=0;
			exportTexCoords=1;exportNormals=1;exportNormalsPerVertex=1;exportVertexColors=1;exportVertexColorsPerVertex=1;
			exportTexTangents=0;exportTangents=0;exportReferencedMaterials=0;exportMaterialsOnly=0;
			exportXRefs=1;dereferenceXRefs=1;exportCameraAsLookat=0;cameraXFov=0;cameraYFov=1;doublePrecision=0
		</comments>
      <source_data>file:///C:/Users/<USER>/Documents/maya/projects/willow_textures/scenes/shoulder_pan1d.mb</source_data>
    </contributor>
    <created>2010-04-30T16:01:04</created>
    <modified>2010-04-30T16:01:04</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Y_UP</up_axis>
  </asset>
  <library_materials>
    <material id="lambert3" name="lambert3">
      <instance_effect url="#lambert3-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="lambert3-fx">
      <profile_COMMON>
        <newparam sid="file2-surface">
          <surface type="2D">
            <init_from>file2</init_from>
          </surface>
        </newparam>
        <newparam sid="file2-sampler">
          <sampler2D>
            <source>file2-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="file1-surface">
          <surface type="2D">
            <init_from>file1</init_from>
          </surface>
        </newparam>
        <newparam sid="file1-sampler">
          <sampler2D>
            <source>file1-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="file2-sampler" texcoord="TEX0">
                <extra>
                  <technique profile="OpenCOLLADAMaya">
                    <blend_mode>NONE</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <transparent opaque="RGB_ZERO">
              <color>0 0 0 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </lambert>
          <extra>
            <technique profile="OpenCOLLADAMaya">
              <bump>
                <texture texture="file1-sampler" texcoord="TEX1">
                  <extra>
                    <technique profile="OpenCOLLADA3dsMax">
                      <amount>1</amount>
                      <bumpInterp>1</bumpInterp>
                    </technique>
                    <technique profile="OpenCOLLADAMaya">
                      <blend_mode>NONE</blend_mode>
                    </technique>
                  </extra>
                </texture>
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="file2" name="file2">
      <init_from>shoulder_pan_color.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file2</originalMayaNodeId>
        </technique>
      </extra>
    </image>
    <image id="file1" name="file1">
      <init_from>shoulder_pan_normals.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file1</originalMayaNodeId>
        </technique>
      </extra>
    </image>
  </library_images>
  <library_geometries>
    <geometry id="shoulder_pan_MShape" name="shoulder_pan_MShape">
      <mesh>
        <source id="shoulder_pan_MShape-positions" name="shoulder_pan_MShape-positions">
          <float_array id="shoulder_pan_MShape-positions-array" count="1506">-1.271186 0.03812145 1.419441 -1.270048 1.35208e-6 -2.47873 -1.270001 0 -4.80999 -1.27 -4.06275e-6 -2.235051 -1.27 0 -3.774223 -1.27 0 -1.194308 -1.269545 -0.03398126 -4.155089 -1.269537 0.03427187 -3.655478 -1.269529 0.03457817 -3.128024 -1.269528 -0.03458325 -3.11914 -1.269518 -0.0349452 -2.49417 -1.269518 0.0349452 -2.49417 -1.269507 0.03534483 -1.800587 -1.269504 -0.03545662 -1.605599 -1.26949 -0.03597877 -0.6897373 -1.269078 0.05106844 -4.251605 -1.268548 0.06540949 -0.7872646 -1.268125 -0.06893185 -3.536053 -1.26812 0.06902166 -3.453924 -1.268085 -0.06965692 -2.873055 -1.26806 0.07010169 -2.465085 -1.268044 -0.07039682 -2.194072 -1.268005 -0.07109313 -1.551563 -1.267967 -0.07177764 -0.9155323 -1.267935 -0.07235887 -0.3712106 -1.267254 0.08469836 -1.589179 -1.266829 -0.09156163 -4.010533 -1.266722 0.0880153 -4.84042 -1.266433 0.09506968 -3.859279 -1.266373 0.09584471 -3.323409 -1.26635 -0.09613358 -3.123292 -1.266332 0.09636505 -2.962967 -1.26629 0.09690963 -2.584968 -1.266273 -0.09712967 -2.431988 -1.266206 -0.09800344 -1.822518 -1.266132 -0.09895351 -1.155895 -1.266057 -0.09992632 -0.4673979 -1.265171 0.111389 -1.988656 -1.264221 0.1209734 -4.265479 -1.264071 0.122486 -3.407079 -1.263971 -0.1234934 -2.834242 -1.263886 -0.1243558 -2.34218 -1.263754 -0.1256858 -1.579262 -1.263616 -0.1270688 -0.7798396 -1.263252 -0.145083 -4.830582 -1.262655 -0.1477091 1.419441 -1.262558 0.1378528 -2.401234 -1.262152 -0.141516 -0.421875 -1.261307 0.1482757 -3.93192 -1.260987 -0.1509147 -2.638802 -1.260793 -0.1525161 -1.849095 -1.260605 -0.1540619 -1.081712 -1.26009 0.1602819 -1.428782 -1.259956 0.1601953 -4.617716 -1.259618 0.1625889 -3.515777 -1.258819 -0.17573 0.2080428 -1.258639 -0.1714579 -3.396572 -1.25804 0.176564 -4.810016 -1.257942 0.1745321 -4.175791 -1.257305 -0.1789825 -2.233472 -1.257248 -0.1793808 -2.058533 -1.257248 0.1793808 -2.058533 -1.257036 -0.1808596 -1.406517 -1.256896 -0.1818339 -0.9744347 -1.256263 0.1880435 -2.878182 -1.255478 0.1920071 -2.222213 -1.255062 -0.1947905 -2.863737 -1.254209 -0.2002361 -0.59028 -1.251807 -0.214047 -2.045091 -1.251484 -0.2159329 -1.300941 -1.248611 0.2388893 -0.7800959 -1.247479 0.2395317 -2.253958 -1.244597 -0.2526182 -0.6005131 -1.238489 -0.2829434 -2.204619 -1.233964 0.3008792 -1.946414 -1.229376 0.3232621 -3.567003 -1.228308 -0.1979244 -4.884625 -1.22697 0.341178 -4.823213 -1.222911 -0.3418714 -4.859817 -1.220062 0.3611406 1.419441 -1.214669 -0.3749388 1.419441 -1.210561 0.3851989 -0.5556834 -1.208655 0.3907301 -1.634579 -1.206732 -0.4098785 -3.063152 -1.205748 -0.4010228 -1.2324 -1.20085 0.1713536 -4.90962 -1.190772 0.4418783 -2.259074 -1.182327 0.4635803 -0.8868427 -1.181287 -0.4662502 0.00760655 -1.181287 0.4662502 0.00760655 -1.175127 -0.4854532 -4.815461 -1.173027 0.4854729 -4.842015 -1.170241 0.4933319 -3.661786 -1.168797 0.496711 -2.519023 -1.167535 0.4996656 -1.513581 -1.166458 -0.5021869 -0.6464133 -1.165494 -0.5044417 0.1388955 -1.164713 -0.5062709 0.7856391 -1.158307 0.5211291 -0.6111039 -1.153946 0.5303455 -3.972738 -1.152684 0.5330536 -3.007112 -1.151441 -0.5357207 -2.051894 -1.150192 -0.5384003 -1.084023 -1.148856 -0.5412678 -0.03393956 -1.148078 -0.5454127 1.419441 -1.142696 0.5545271 -2.026755 -1.138499 0.4030027 -4.908849 -1.137 0.5657999 -4.778188 -1.135333 0.5690928 -3.518802 -1.134253 0.5712246 -2.701325 -1.132722 -0.5742479 -1.534408 -1.131416 -0.5768272 -0.5269626 -1.125353 0.592531 0.1782609 -1.120548 -0.5985626 -2.790875 -1.116857 0.60457 -4.166219 -1.115741 0.6066057 -3.315982 -1.113353 0.6115436 -1.139724 -1.104844 -0.6265392 -2.289282 -1.081788 -0.6667845 -4.853106 -1.081753 0.6701437 1.419441 -1.081507 -0.6672674 -0.3675917 -1.065081 -0.5532227 -4.908904 -1.061364 0.6998926 -4.838112 -1.053297 -0.7108748 -2.435654 -1.048492 0.7180954 -1.276546 -1.004404 -0.7806397 1.419441 -1.000106 -0.7835612 -1.312058 -0.9820222 -0.8063927 -4.848071 -0.9820078 0.7610693 -4.887739 -0.9616518 0.830953 1.419441 -0.9541686 0.8396046 -1.418582 -0.9536485 0.8395379 -3.201563 -0.9471745 -0.8469814 -2.664607 -0.9258195 0.8720626 0.2980806 -0.8837324 0.9159663 -4.830689 -0.8814819 0.91427 1.419441 -0.8769633 -0.9220609 -4.836947 -0.8762514 0.919576 -0.183693 -0.8644891 -0.9337399 -0.9256515 -0.8606079 0.933944 0.2601868 -0.8499816 -0.9473035 1.41944 -0.8477829 0.9461299 -0.675798 -0.8394937 0.95318 1.419441 -0.824345 0.9661015 0.3809239 -0.8237399 -0.9679154 -3.333591 -0.8028198 0.9852508 -2.32616 -0.7950438 -0.9277078 -4.901314 -0.7871692 0.9966238 0.7006728 -0.7860097 0.9975349 -0.4910912 -0.778155 0.9103685 1.419441 -0.7658048 1.013126 -0.9071422 -0.7650538 1.013692 -1.549174 -0.7444884 1.028888 -1.830271 -0.7354928 1.035497 -0.1533963 -0.7351708 1.035638 -1.234268 -0.732212 0.9643784 -4.909602 -0.7297943 1.040673 1.419441 -0.7271605 -1.042456 -0.8280151 -0.7244231 1.04311 -1.539251 -0.7234747 1.043768 -2.160098 -0.7191328 -1.046891 -1.662022 -0.7143068 1.051604 -3.18179 -0.7048193 1.056453 -0.8729005 -0.703825 1.057114 -1.463519 -0.7030656 1.05762 -1.911613 -0.6927406 -1.065838 1.419441 -0.6907215 -1.065809 -2.310494 -0.6798451 -1.07269 -1.80588 -0.6798451 1.07269 -1.80588 -0.6759575 1.075641 0.679992 -0.6741026 1.076694 -0.2993793 -0.6696267 -1.079185 -1.018201 -0.6663835 -1.082291 -3.216732 -0.6613042 1.084421 -2.260523 -0.6569752 -1.086844 -1.40532 -0.65608 -1.087384 -1.840707 -0.6549953 -1.088039 -2.366127 -0.6508361 1.092703 -4.845486 -0.6498122 1.051685 -4.892066 -0.6387964 -1.100692 -4.835542 -0.6362423 -1.099339 -2.75063 -0.6335996 -1.100632 -1.164509 -0.6318671 -1.101627 -1.943646 -0.6230714 -1.106714 -0.5193953 -0.6101213 -1.113816 -0.8811249 -0.6086185 -1.114636 -1.514746 -0.6068893 -1.115579 -2.239188 -0.5852678 -1.127075 -0.4635756 -0.5833066 -1.128086 -1.244437 -0.5814268 -1.129055 -1.985768 -0.5793795 -1.130111 -2.787615 -0.5701885 -1.064455 -4.909248 -0.5565107 -1.14154 -1.460839 -0.5540877 -1.142719 -2.363535 -0.5489979 -1.146747 0.09259257 -0.5396586 1.150079 -1.95341 -0.5332756 1.155444 -0.4004368 -0.5183179 1.159258 -4.841913 -0.5144851 1.161623 -4.181815 -0.4935517 -1.170417 -3.917013 -0.4848926 1.179445 1.419441 -0.475858 -1.179172 1.419441 -0.4617675 -1.18398 -2.880099 -0.4553881 1.185825 -3.324948 -0.4459706 -1.188896 -4.841456 -0.4197062 -1.198624 -4.024669 -0.4173386 1.199469 -4.778966 -0.4032343 1.205077 -1.759769 -0.3985294 -1.206832 -1.080922 -0.3833569 1.210729 -3.397183 -0.3829091 -1.210873 -3.538894 -0.3789815 -1.212135 -4.779199 -0.3762322 1.213471 -2.67871 -0.3457681 -1.221992 -3.112261 -0.3377251 1.221294 -4.857754 -0.2725052 1.240999 1.419441 -0.2522245 1.185712 1.419441 -0.2438017 -1.247673 -3.023036 -0.2353918 -1.187707 -4.908535 -0.2290078 1.24941 -4.795175 -0.2223637 1.252972 -3.861349 -0.2123137 1.253355 0.3280523 -0.2053146 -1.255757 1.41944 -0.203862 -1.257416 -4.836683 -0.1976298 1.254527 1.419441 -0.1825518 1.260052 -1.356128 -0.1784775 1.259547 -2.337399 -0.1701844 1.18775 -4.909567 -0.1673869 1.258915 -4.820237 -0.1175713 -1.264946 -2.195661 -0.1159522 -1.265347 -3.56411 -0.1093042 1.265839 1.419441 -0.1073001 1.265571 -4.81 -0.1026313 -1.266169 -0.7013791 -0.0745717 1.267806 -3.648128 -0.05349684 1.093388 1.419441 -0.05028103 1.269003 -2.787796 -0.04991553 1.269017 -3.23792 -0.04949672 1.269034 -3.752556 -0.02488616 1.269756 -3.259388 -0.01915855 1.270196 -4.291062 -0.005355982 1.24425 -4.886405 0.00952902 1.270823 -4.828004 0.01510182 -1.233524 -4.892853 0.02742344 -1.270649 -1.587934 0.02838097 -1.270014 -4.845465 0.06333823 1.270143 0.874615 0.07064518 1.269682 -2.521383 0.08242878 1.268285 -4.846705 0.09896337 1.271853 -1.539488 0.1116679 -1.271296 0.9064727 0.2035201 0.4276731 -1.737503 0.2080064 -0.4214801 -1.79055 0.2163574 -1.255988 -4.833849 0.2277211 -0.468134 -1.636915 0.2291526 0.5037201 -1.754219 0.2311446 -1.269866 1.41944 0.2321299 -0.5083109 -1.765801 0.2363059 -1.254229 -2.403875 0.2487996 1.268791 1.41944 0.2490485 -0.04694236 -1.616933 0.2523164 1.255447 -3.702052 0.2817142 0.4651601 -1.526472 0.2828168 -1.260389 -1.408534 0.3050758 -0.5981139 -1.760639 0.3062854 -0.4636009 -1.497218 0.3124909 1.235458 -4.867279 0.3168979 0.5877748 -1.604557 0.3322245 1.236976 -1.604269 0.3361811 0.5235577 -2.435944 0.337108 -0.01292693 -1.519781 0.3377064 -0.5174099 -2.454581 0.3388708 1.260237 1.024974 0.3418716 -0.02694351 -1.26923 0.3472959 -1.226904 -1.61848 0.347685 1.227258 -2.666181 0.353794 0.1609734 -1.463271 0.3627265 0.648212 -1.73388 0.3671272 -1.23048 -3.681772 0.3689142 -0.4375148 -2.850005 0.3732518 -0.6530437 -1.657048 0.380418 0.4167212 -2.93078 0.3886431 -1.213936 -3.026289 0.4011111 1.216833 -3.779045 0.4043516 0.4124027 -1.405012 0.405308 -0.4116526 -1.399989 0.4062467 -1.247887 1.082335 0.4200238 0.4503014 -1.269231 0.4332427 -0.474483 -1.269231 0.4360184 0.6382737 -2.642139 0.4381086 -1.129513 -4.909441 0.4420813 -1.236755 1.419441 0.4432013 -0.634335 -1.473344 0.4432087 -0.6492217 -2.650272 0.4471688 -1.150257 -1.680639 0.4593208 1.13546 -1.664908 0.4602938 -1.189566 -4.85771 0.465879 0.6367988 -1.451705 0.472432 -1.234598 -1.333097 0.4751664 -1.103878 -1.637211 0.4805205 1.14616 -2.889126 0.4861287 1.215734 1.154269 0.4897504 -1.170314 -1.459258 0.4899802 1.16796 -3.85752 0.4938496 0.5724066 -1.371917 0.4987439 -0.5790867 -1.370858 0.4991335 -1.135422 -3.212861 0.5012747 1.214383 1.41944 0.505625 -0.5823277 -3.51034 0.5059502 -1.160381 -4.836597 0.512001 -0.7327456 -1.738893 0.5126789 0.733484 -1.78448 0.5128804 1.161191 -4.834249 0.5144088 -1.192136 1.185395 0.5154294 -0.9834936 -1.752883 0.5162909 1.26971 -1.015248 0.5219977 0.7223067 -2.531395 0.523351 0.9565243 -1.698934 0.5319077 1.257091 -1.177289 0.5329295 0.5312027 -3.813842 0.5343922 -0.7367183 -2.393201 0.5358403 -0.7805618 -1.709098 0.5365779 0.7799639 -1.742911 0.5447189 0.7713017 -2.212825 0.5469147 1.058701 -3.183853 0.5491087 0.8926818 -2.487398 0.551342 1.103035 -1.428375 0.5534639 1.046759 -4.910021 0.5538743 0.7443343 -1.58692 0.5543204 1.003638 -1.506087 0.5544792 0.67617 -3.437373 0.5549563 -1.138387 1.419431 0.5579435 -1.103909 -3.916006 0.5611942 -1.141184 1.21439 0.5620571 -0.9427034 -1.50921 0.5646462 -0.7818752 -2.625874 0.5656838 0.3872137 -4.152139 0.5659113 1.181975 -1.330729 0.5666095 1.116566 1.224148 0.5672456 -0.3561648 -4.175314 0.5686577 0.9635171 -3.013148 0.5713961 -0.9826562 -3.103313 0.5723631 -0.4986783 -4.100645 0.5767196 -0.6444709 -3.776546 0.5772557 0.7569878 -2.873041 0.5883587 0.9940492 1.419441 0.5886478 -0.7286497 -3.25389 0.5888387 -0.3809069 1.419437 0.588874 -0.9038744 1.419441 0.5917283 0.9218916 1.285392 0.5932854 -0.8656204 1.283721 0.5950925 -0.05950402 1.281827 0.5999278 1.060736 -4.256426 0.603105 0.5923107 -4.099882 0.6045088 -1.070071 -4.832046 0.6076834 0 -4.37907 0.6111216 -0.9943066 1.252283 0.6113055 -0.7448763 -1.455724 0.6117469 -0.8193765 -4.909951 0.6131987 0.9433166 1.258284 0.6203857 -1.153707 -1.318811 0.6204694 -1.270093 -0.9823323 0.6220171 0.7391142 -1.438022 0.6227271 0.6858792 -1.361798 0.6227903 -0.6855377 -1.360361 0.6229252 0.6860585 -1.26923 0.6229765 -0.686104 -1.26923 0.6232862 0.79392 -1.142873 0.6246272 -0.79392 -0.5594775 0.6246558 -0.9664162 -4.889755 0.62514 -0.7939877 -1.409022 0.6274875 -0.79392 0.5587734 0.6289554 -0.406262 1.266105 0.6291584 0.7939201 1.263123 0.629408 -0.7939201 1.268073 0.6294083 0.3225298 1.268072 0.6294085 0.1783218 1.268072 0.6294085 -0.17367 1.268072 0.6294085 -0.02481005 1.268072 0.630287 0.794507 -1.421124 0.6326856 -0.3942275 -4.401473 0.6331982 -0.9764512 -4.147958 0.63382 0.6684492 -3.95158 0.6339944 -0.7571843 -3.624628 0.6366845 0.3984056 -4.404255 0.637786 0.9979901 -4.852455 0.6510231 0.8852561 -4.038194 0.6512573 -0.02525581 -4.492919 0.6532415 0.7375274 -3.886723 0.6560534 0.2921721 -4.895837 0.6571301 -0.8542726 -4.057428 0.6606585 0.8316839 -4.871363 0.673574 -0.6138316 -4.253589 0.6737153 -0.3482803 -4.877814 0.6798978 0.5646565 -4.345676 0.6799352 -1.236794 -1.186417 0.6817439 0.8064863 -4.795821 0.6824531 -0.9851584 -1.335132 0.6841235 -0.7882542 -4.819594 0.6884431 -0.4244518 -4.4781 0.6959361 0.116712 -4.558981 0.7007049 0.2072814 -4.825651 0.7183585 1.023885 -1.308538 0.7677985 1.207271 -1.199094 0.8285746 -1.172066 -1.220121 0.8505183 1.090893 -1.252444 0.8674863 0.7939269 -1.276633 0.8703666 -1.05799 -1.255357 0.8753687 -0.7939491 -1.275712 1.048457 -1.247402 -1.116514 1.052507 0.7939969 1.269687 1.078141 -1.186579 1.186691 1.082029 1.158052 1.201315 1.083558 -0.7939206 1.268099 1.107442 -1.073009 -1.24328 1.10805 1.086999 -1.240893 1.111845 -1.269532 0.9651497 1.119181 1.231517 1.123953 1.123458 -1.023726 1.246128 1.142974 1.020155 1.243251 1.145932 1.239599 -1.134007 1.158789 -0.79392 -1.262274 1.210081 -1.23711 1.103485 1.210696 0.7939841 -1.257323 1.247872 -1.212667 -1.161724 1.286472 1.159328 -1.191861 1.30359 -1.069231 -1.211629 1.312636 0.7939879 1.23473 1.380146 1.186949 1.12981 1.399917 -1.135876 1.15526 1.410486 -0.7941278 1.207952 1.42714 1.046372 1.168582 1.427939 1.267094 0.9367096 1.448738 -1.241117 -1.047161 1.45899 1.255134 0.9674001 1.464784 -1.260877 0.9287758 1.465108 0.7941544 -1.185645 1.500326 -0.794022 -1.171666 1.515951 1.087882 -1.131875 1.51699 1.265214 -0.9165858 1.531052 1.215127 -1.05826 1.53223 -1.152957 -1.107371 1.550831 0.7939866 1.146549 1.62138 -1.210217 0.9893369 1.637553 1.033964 1.073502 1.641131 -1.101259 1.053751 1.652864 1.196716 0.9884873 1.669822 -1.269237 -0.7795922 1.696333 -0.7939285 -1.065514 1.707986 1.064799 -1.02842 1.715653 -1.270073 0.6169132 1.766972 1.250335 0.7676039 1.769615 0.793921 -1.013111 1.770206 -0.7939262 1.017985 1.80402 -1.212629 -0.8661447 1.80402 1.212629 -0.8661447 1.808035 -1.086887 -0.9512646 1.81013 1.128317 0.9150797 1.828101 -1.235053 0.7782272 1.829203 0.7940726 0.969167 1.836567 -1.105917 0.9051464 1.896788 1.269754 -0.4971262 1.897902 1.269875 0.3452182 1.903367 1.054132 -0.8630109 1.950582 -1.269954 -0.1724016 1.963404 1.212594 0.6633506 1.965927 -1.136865 0.7455561 1.977631 -0.7939568 -0.8175854 1.978613 1.115099 0.7474151 2.009605 -1.258418 0.3568743 2.010064 -1.211286 -0.612965 2.010064 1.211286 -0.612965 2.02694 0.7939133 0.756142 2.03509 -0.7940837 0.7438643 2.055943 -1.244019 -0.3784719 2.055943 1.244019 -0.3784719 2.061495 0.7939198 -0.705248 2.072486 -1.103387 -0.6218909 2.072486 1.103387 -0.6218909 2.079881 -1.201663 0.4840108 2.084733 -1.03391 0.621964 2.085838 1.036588 0.6189728 2.088328 1.239677 0.3238436 2.133585 -1.238671 -0.06074802 2.133585 1.238671 -0.06074802 2.157183 0.960008 -0.498148 2.159122 -1.199169 0.2668042 2.161861 -0.7939235 -0.5220306 2.164932 -0.9737282 -0.475706 2.169431 1.145872 0.3562659 2.18207 -1.037338 0.4072038 2.192333 -1.182776 -0.1983345 2.192333 1.182776 -0.1983345 2.206694 0.7939487 0.408911 2.229942 -1.063946 -0.2085017 2.235838 1.052003 -0.1946431 2.24164 0.7939225 -0.2915925 2.243589 1.04523 0.1275525 2.249034 -0.7939171 0.2635959 2.249271 -1.033078 0.1009148 2.258758 -0.7939202 -0.1744204 2.265032 0.7939442 0.1404259</float_array>
          <technique_common>
            <accessor source="#shoulder_pan_MShape-positions-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="shoulder_pan_MShape-normals" name="shoulder_pan_MShape-normals">
          <float_array id="shoulder_pan_MShape-normals-array" count="1506">-0.8559249 0.02465243 0.5165122 -0.9999999 0 0 -0.9993861 -0.005363314 -0.03462237 -1 0 0 -0.9999961 0.002770908 0 -0.9999942 0.003383809 4.31835e-4 -0.9993759 -0.0353206 -5.3894e-4 -0.9996365 0.02696403 0 -0.9996161 0.02770205 0 -0.9996176 -0.02764856 0 -0.9996058 -0.02807737 0 -0.9996767 0.02542621 0 -0.9995413 0.03028513 0 -0.9995499 -0.03000145 0 -0.9998938 -0.01447797 0.001697914 -0.9992564 0.03855528 -3.43947e-4 -0.9987001 0.05095084 0.001461809 -0.9986783 -0.05139748 0 -0.9985436 0.0539526 0 -0.9985731 -0.05340238 0 -0.9984833 0.05505726 0 -0.9985419 -0.05398311 0 -0.9983768 -0.0569539 0 -0.9984888 -0.05495439 0 -0.9991176 -0.04196648 0.001711954 -0.9975305 0.07023468 2.01848e-4 -0.9975184 -0.07040576 0 -0.9721002 0.06838597 -0.2243762 -0.9973069 0.0733409 0 -0.9971473 0.07548122 0 -0.996805 -0.07987324 -1.42851e-4 -0.9970325 0.07698244 0 -0.9968697 0.07906251 0 -0.9970667 -0.07653672 0 -0.9970111 -0.07725751 0 -0.9969501 -0.07804094 0 -0.9967034 -0.08112956 4.50323e-4 -0.9963595 0.08525156 1.46526e-4 -0.9954426 0.09536196 -3.21374e-4 -0.9950256 0.09961972 0 -0.9951975 -0.09788635 -2.42685e-4 -0.9951634 -0.09823429 0 -0.995064 -0.09923595 0 -0.9951897 -0.09796585 0 -0.9841413 -0.09876301 -0.1473484 -0.7003287 -0.08942209 0.7081973 -0.9938597 0.1106472 -1.35438e-4 -0.9933586 -0.1150592 -1.4994e-4 -0.993269 0.1158309 0 -0.9926422 -0.1210848 0 -0.9925579 -0.1217733 0 -0.9926904 -0.1206893 0 -0.9927112 0.1205157 6.8823e-4 -0.9911437 0.1327928 3.60529e-4 -0.9900618 0.1406331 0 -0.9890119 -0.147829 0.001492083 -0.9903942 -0.1382722 -3.89327e-4 -0.9862595 0.1506851 -0.06772224 -0.9879662 0.1546702 0 -0.9886744 -0.1500767 1.36871e-4 -0.9885182 -0.151079 -0.002670261 -0.9889992 0.1479208 4.08273e-4 -0.9896047 -0.1438143 0 -0.9899043 -0.1417368 0 -0.9879339 0.1548765 -1.74235e-4 -0.9889336 0.1483574 5.68571e-4 -0.984373 -0.1760923 -0.001157453 -0.9878053 -0.1556942 -3.30558e-4 -0.982962 -0.1838051 -0.001136816 -0.9821721 -0.1879833 -5.77257e-4 -0.9816204 0.1908424 8.46722e-4 -0.9835265 0.1807643 0 -0.9741983 -0.2256833 0.002179591 -0.9800311 -0.1988408 -0.001190334 -0.9704235 0.2414076 6.19821e-4 -0.9612607 0.2756406 -2.2619e-4 -0.6741733 -0.0741948 -0.734837 -0.963035 0.2354832 -0.1308097 -0.8637505 -0.2639359 -0.4292703 -0.9304323 0.3073474 0.1995834 -0.743567 -0.2347067 0.6261158 -0.952159 0.3056028 -4.35133e-4 -0.9549698 0.2967028 3.51344e-4 -0.9557757 -0.2940952 -7.89736e-4 -0.9532238 -0.3022622 0.001419977 -0.5120375 0.08293431 -0.8549501 -0.9362365 0.3513688 -0.00102576 -0.9329823 0.3599224 1.07671e-4 -0.9299184 -0.367766 1.3722e-4 -0.9274099 0.3740446 0.001198844 -0.9143379 -0.3972909 -0.07839797 -0.8423889 0.3508879 -0.4089728 -0.9295754 0.3686318 -1.51085e-4 -0.9248711 0.38028 -6.57979e-4 -0.9205173 0.390702 0 -0.9209315 -0.3897238 -8.15137e-4 -0.9000956 -0.4356397 0.006775581 -0.916017 -0.4011394 0 -0.9115233 0.4112484 0 -0.9160638 0.4010321 -4.1369e-4 -0.9072902 0.420505 0 -0.9114542 -0.4114015 0 -0.9106078 -0.4132715 -5.02712e-4 -0.8896209 -0.4566384 0.007488268 -0.8503313 -0.4196095 0.3175918 -0.8944958 0.4470761 -4.66041e-4 -0.3391653 0.1436259 -0.9296981 -0.8920441 0.4516224 -0.01716051 -0.8940486 0.4479699 0 -0.8857853 0.464095 5.82022e-4 -0.8820902 -0.4710795 9.35307e-4 -0.8832133 -0.4689713 3.16717e-4 -0.8946749 0.4467152 0.001520173 -0.8863106 -0.4630908 -4.82245e-4 -0.8775735 0.4794416 -6.08748e-4 -0.8740787 0.4857841 4.816e-4 -0.8770136 0.4804628 -0.00156156 -0.8608926 -0.5087851 0.001272648 -0.7803857 -0.4594757 -0.424123 -0.5956962 0.3774942 0.7089739 -0.8603367 -0.5097063 0.004530627 -0.2787456 -0.135835 -0.9507101 -0.8300384 0.5386476 -0.1445513 -0.8356609 -0.5492457 1.09192e-4 -0.8348352 0.5505 0 -0.706239 -0.5473174 0.4490768 -0.783917 -0.6208659 0 -0.7295867 -0.6067476 -0.315532 -0.5652634 0.4814334 -0.6698502 -0.5387276 0.4572921 0.7075707 -0.7307355 0.6826602 -8.54554e-4 -0.7516943 0.6595116 -2.02905e-4 -0.744649 -0.6674562 1.71164e-4 -0.7277873 0.685802 0.00114867 -0.6748767 0.7125085 -0.1920235 -0.494974 0.504361 0.7075457 -0.6468536 -0.7187951 -0.2547822 -0.6851603 0.7283919 -6.27126e-4 -0.6845186 -0.7289953 -1.69675e-4 -0.6782866 0.7347878 0.003799092 -0.4616569 -0.534024 0.7083016 -0.6582895 0.7527647 -5.73216e-4 -0.5858075 0.6756805 0.4475327 -0.6586195 0.7524746 0.001534373 -0.6252018 -0.7804627 -7.65395e-4 -0.6408437 0.7676713 0 -0.4222798 -0.4542149 -0.7844543 -0.6202323 0.784418 3.51328e-4 -0.622885 0.7823133 -3.51029e-4 0 0 1 -0.6115857 0.7911782 -3.00194e-4 -0.6076272 0.7942224 2.75548e-4 -0.5871808 0.8094556 -3.58088e-4 -0.5793757 0.8150606 0 -0.578231 0.8158732 0 -0.1587634 0.1939162 -0.9680861 -0.4550311 0.6566261 0.6014889 -0.5671675 -0.8236009 0.001617397 -0.5682267 0.822872 0 -0.5702817 0.8214492 0 -0.5755678 -0.8177539 -4.79615e-4 -0.5408432 0.8411232 -6.8074e-4 -0.5379489 0.8429772 -5.21971e-4 -0.5347613 0.8450024 0.001165618 -0.5520805 0.8337907 -3.90495e-4 -0.4345708 -0.7082969 0.5562949 -0.5454242 -0.8381602 -2.67424e-4 -0.534354 -0.8452609 0 -0.5119292 0.8590246 0.002261254 -0.520411 0.8539153 0.001096059 -0.513041 0.858364 -3.92933e-4 -0.5274255 -0.8496012 4.43615e-4 -0.5079235 -0.8613992 -0.002233103 -0.517245 0.8558372 -4.31666e-4 -0.5149136 -0.857242 0 -0.5188903 -0.8548409 0 -0.5189427 -0.8548091 -1.37745e-4 -0.5105702 0.7918513 -0.3350966 -0.3515148 0.5921094 -0.7251508 -0.493587 -0.8325602 -0.2514262 -0.4989543 -0.8666283 0 -0.4990785 -0.8665568 0 -0.4994082 -0.8663667 0 -0.5050753 -0.8630717 0.002493906 -0.4781276 -0.8782904 0 -0.4787732 -0.8779386 0 -0.4772229 -0.8787823 0 -0.4442193 -0.8959174 -0.001085542 -0.4415033 -0.8972586 0.001328367 -0.4578812 -0.8890134 0 -0.4519294 -0.8920483 -0.003084785 -0.1892535 -0.3647977 -0.91165 -0.4192421 -0.9078723 0.001988745 -0.4184535 -0.9082382 0 -0.4639249 -0.8858737 0.001198975 -0.4539116 0.8910466 0 -0.3829965 0.9237493 9.03857e-4 -0.3767042 0.8787211 -0.2931606 -0.4211057 0.9070115 1.39869e-4 -0.3935589 -0.9192992 -5.7868e-4 -0.3202217 0.7664322 0.5568122 -0.3103101 -0.7704118 0.5569317 -0.3673568 -0.9300789 0.001435793 -0.3351233 0.942162 -0.004812724 -0.3263941 -0.8893518 -0.3201881 -0.3355564 -0.9420201 -2.6093e-4 -0.3328083 0.9428207 -0.01810402 -0.3005489 0.953765 0.001604989 -0.3522862 -0.9358922 4.00946e-4 -0.316114 0.9486139 -0.01427192 -0.2919126 -0.956445 0 -0.2911089 -0.9565384 -0.01702618 -0.2740234 0.9617229 4.41637e-4 -0.2706507 -0.962657 0.006306294 -0.2387163 0.9048017 -0.3526304 -0.1615135 0.6884977 0.707025 0 0 1 -0.1955232 -0.9806938 0.003237771 -0.09066363 -0.4222966 -0.9019123 -0.198902 0.9793522 -0.03615576 -0.1716311 0.9851513 -0.004427822 -0.1428166 0.9897486 -0.001040488 -0.1335545 -0.9171638 0.3754647 -0.1840959 -0.9551482 -0.2319496 -0.107738 0.6983784 0.7075734 -0.1916708 0.9814588 0.001003115 -0.1175077 0.993071 0.001429262 -0.07232823 0.3458264 -0.9355066 -0.1202931 0.9736513 -0.1937331 -0.07642644 -0.9970751 0 -0.08685454 -0.9962211 1.14538e-4 -0.04298848 0.7054833 0.7074217 -0.07645663 0.9950312 -0.06377497 -0.05814312 -0.998308 7.11708e-4 -0.07495249 0.9971842 -0.002395881 0 0 1 -0.03580163 0.9993536 0.003263908 -0.04957135 0.9987705 2.0369e-4 -0.03608513 0.9993477 -0.00144783 -0.01330127 0.9999115 -1.2713e-4 -0.02586916 0.9996653 2.46257e-4 -0.02846398 0.7198365 -0.6935598 -0.05502247 0.9843869 -0.1671976 0.0224615 -0.6545135 -0.7557166 0.03497963 -0.9992256 -0.01801739 0.01624697 -0.9513497 -0.3076847 -0.007532238 0.9999493 0.006677369 0.009068463 0.999958 0.001253028 0.03681422 0.9908036 -0.130204 0.04414102 0.9988758 -0.01728193 3.25587e-4 -0.9998091 0.01953712 0.9840711 -0.1389639 -0.1108743 0.9863556 0.149601 0.06871752 0.1453283 -0.9749869 -0.1681671 0.8922091 0.3441571 -0.2924361 0.8618852 -0.5016763 -0.07399135 0.05905271 -0.8280759 0.557497 0.8860114 0.4635615 0.009723756 0.2103907 -0.9776111 -0.003512486 0.06789631 0.8254816 0.5603303 0.8295829 0.002970765 -0.5583757 0.1641663 0.986432 -0.001167406 0.8110917 -0.2819587 -0.5124739 0.1951515 -0.9715589 -0.1341235 0.7587736 0.6511248 -0.01729101 0.7585357 0.1744612 -0.6278429 0.2284162 0.8695298 -0.4378858 0.698584 -0.6641507 -0.2662411 0.4216635 0.899829 -0.1118368 0.8884988 -0.4436297 0.1173134 0.9613665 -0.06262638 -0.2680533 0.8915462 0.4361739 0.1220559 0.09378429 0.990292 0.1025982 0.8688124 0.02557542 -0.4944805 0.4404437 -0.8922128 -0.09982798 0.3396688 0.940535 0.004370495 0.9700339 -0.1956496 0.1440672 0.6480738 -0.7615501 -0.006461533 0.380554 -0.9247527 0.003314938 0.975811 0.1621672 0.1466108 0.611113 0.7825909 -0.1187123 0.9847358 -0.09229103 0.1475725 0.4762811 -0.8792613 0.007477642 0.3997163 0.9166202 0.005855101 0.9334533 -0.2104383 -0.2904837 0.9176453 0.2181298 -0.3321845 0.1237184 -0.9822503 0.14099 0.7902175 -0.3259895 -0.5189289 0.8122522 0.4252387 -0.3992724 0.7450147 -0.6629578 0.07375517 0.29414 -0.3607841 -0.8850518 0.3041923 -0.6203482 0.7229351 0.6316715 0.6179963 -0.4680511 0.7387376 0.66955 0.07726297 0.7787404 -0.6221963 -0.08021938 0.8481112 0.5044352 -0.1620267 0.4007493 -0.8242038 -0.4001101 0.5933979 -0.6499099 -0.4748642 0.4069858 -0.8379329 -0.3636356 0.8906477 -0.3673154 -0.2680039 0.6723964 0.7398893 0.02114171 0.2504764 0.8756477 0.4129199 0.695757 -0.6349528 -0.3357934 0.6177365 0.7862177 0.01622937 0.7369839 -0.5542701 -0.3868324 0.7329625 0.5653589 -0.3783322 0.6995336 -0.7143258 0.0197862 0.5267836 0.6892169 0.4974725 0.9309539 0.3406885 0.1313632 0.6270984 -0.6828417 -0.3747996 0.7387466 0.665917 -0.1039611 0.7662208 -0.6416192 -0.03507617 0.6018892 0.7676311 -0.2201635 0.3533606 -0.860281 0.3674954 0.9435127 -0.3292168 -0.03741977 0.02577367 0.9983447 -0.05141402 0.7044987 -0.7082242 0.04582501 0.9770067 0.2022777 -0.06739265 0.1733034 0.939764 -0.2946348 0.9426137 -0.300518 0.1454936 0.7733017 0.6328664 0.03853097 0.9884871 0.1196999 -0.09254806 0.9927801 -0.0986405 -0.06824667 0.9416724 -0.3348776 0.03332016 0.930564 0.3634385 0.04430605 0.992408 0.1121186 0.0505546 0.8370007 0.2965088 -0.4599047 0.2776054 0.2310902 -0.9324874 0.8286787 -0.4876894 -0.2746829 0.9449711 0.1962493 -0.2617556 0.8171262 -0.5672734 0.1024964 0.7742233 -0.5160737 0.3663961 0.7860617 -0.6174158 0.03007626 0.5201092 -0.4385827 0.7328928 0.9267457 -0.1391564 -0.348967 0.9740455 0.2198796 0.05374292 0.9734662 -0.1091982 0.2010956 0.5418534 0.6393586 -0.5455415 0.6113318 0.4527209 0.6490896 0.980104 0.07226361 0.1848621 0.9542496 0.2915193 0.06651478 0.9778763 -0.2026703 0.05179328 0.9298382 0.3017298 0.2106186 0.8314473 0.5386817 0.1360791 0.9085103 -0.41283 0.06465595 0.8024586 0.1324815 0.5818151 0.8258308 0.5597131 0.06873706 0.2550832 0 0.9669191 0.8201436 -0.0711367 0.5677181 0.8515013 0.08657796 0.5171555 0.76257 -0.0819007 0.6417003 0.6049037 -0.001181539 0.7962978 0.847836 0.529735 0.02356056 0.8682174 -0.4448788 0.2197302 0.8561888 -0.4593839 -0.2364471 0.9572029 -0.003266976 0.2893992 0.1151344 -0.1611624 0.9801891 0.8629912 0.3986911 -0.3103089 0.1481327 -0.01543478 -0.9888471 0.2910939 0.1606398 0.9431114 0.4426648 -0.5637295 -0.6973212 0.03342252 -0.9974102 -0.06368439 0.8181943 -0.451368 -0.3561248 0.8829135 -0.4113906 -0.2263221 0.8262449 0.4842227 -0.2878332 0.8779515 -0.3582088 -0.3176281 0.8367527 0.2262303 -0.4986631 0.8530542 -0.5218174 0.002224336 0.5534298 0.8328953 -9.51462e-4 0.6977459 -0.2125657 -0.6840807 0.9389892 0.2663765 -0.217584 0.9615283 0.2746948 -0.002457332 0.7541489 -7.3944e-4 0.6567031 0.5038477 -0.8114179 0.2962071 0.5698255 0.2347571 0.7875201 0.7751429 0.00179841 0.6317834 0.8290286 0 0.5592061 0.8250713 -0.003199194 0.5650197 0.8290299 0 0.5592042 0.8993649 -0.07041202 -0.4314914 0.9104388 0.1944877 0.3650696 0.9423357 -0.3333541 0.02964109 0.8068291 -0.5574127 0.1957495 0.9655811 0.2427311 0.0934611 0.8741675 -0.1995592 0.4427272 0.8931541 0.3669444 -0.2600529 0.979216 0.1981577 0.04323719 0.8709338 0.02423514 0.4908022 0.9637793 -0.2442131 0.1071895 0.5927535 0.01239701 -0.8052886 0.9911253 -0.1282815 0.03485016 0.8152493 0.0914266 -0.5718478 0.9227829 0.3230827 0.2099743 0.7889618 -0.02346767 -0.6139939 0.9361858 -0.2491439 0.2479591 0.2034095 -0.8647433 -0.4591771 0.9912297 0.1043482 -0.08108764 0.7292782 -0.1598247 -0.6652888 0.9851283 -0.1000796 -0.1396652 0.9477392 0.09181399 0.3055493 0.9230618 -0.06901502 0.37841 0.9734836 0.01294094 -0.2283907 0.5030988 0.2410285 -0.8299378 0.134714 0.6424209 -0.7544186 0.1403209 -0.5864899 -0.797709 0.2361819 0.288647 -0.9278476 0.1823822 -0.6730024 -0.7168016 0.2283099 -0.2166256 -0.9491827 0.2184562 0.3740745 -0.9013019 0.04614453 -0.9301713 -0.3642143 0.04194485 -0.2573898 0.9653969 0.07361247 -0.5713639 0.8173888 0.06374615 0.5418581 0.838049 0.03920564 0.7837512 0.6198363 0.08771949 -0.2319478 -0.9687649 0.08515211 0.2437003 -0.9661052 0.01262947 -0.9945412 0.1035784 0.05716208 0.8867403 0.4587201 0.1018913 -0.2165904 0.9709308 0.115358 0.2156404 0.9696349 0.08617444 0.8795273 -0.4679804 0.09644435 0.7853133 -0.6115404 0.09068246 -0.8850064 0.4566623 0.1584271 -0.4916458 -0.8562624 0.1481242 -0.6405153 -0.7535245 0.2123554 0.4016311 -0.8908411 0.2426831 -0.2137981 -0.9462532 0.209482 -0.4977377 0.8416498 0.2507862 0.5856676 0.7707787 0.277238 -0.4576782 0.8447898 0.3265046 0.1351272 0.9354867 0.3260757 0.2115704 0.9213646 0.0544168 0.9879051 0.1451972 0.1987536 -0.8870543 -0.4166914 0.1640345 0.9203943 0.3549184 0.1100599 -0.9718762 0.2081912 0.3817321 -0.1547883 -0.9112195 0.3603005 0.2326012 -0.9033716 0.4050966 0.2640511 -0.8753136 0.1176139 0.9726087 -0.2004978 0.3295121 0.6957988 -0.6381895 0.4115533 -0.4060061 -0.8159552 0.3771529 -0.5016706 0.7785128 0.3494948 -0.737021 0.5784924 0.4898873 0.2466303 0.8361722 0.4856105 -0.2920272 0.8239555 0.4007427 0.7033308 0.587138 0.1323548 -0.981319 -0.1396256 0.4096251 0.6736632 -0.6151301 0.5460424 0.25026 -0.7995046 0.06944623 -0.9956209 0.06257999 0.217986 0.9508662 0.2198533 0.4559332 -0.6864505 -0.5664898 0.2788785 0.8859063 0.3706706 0.486668 -0.7041059 -0.5170968 0.4989677 0.7017216 -0.508545 0.6467344 -0.2549337 -0.7188488 0.612494 0.3894343 0.6878896 0.374728 -0.8583716 0.3503957 0.6126143 -0.354839 0.7062528 0.631683 -0.3649642 0.6839428 0.1373474 0.9879485 -0.07136912 0.08235998 0.9959825 0.03515286 0.7081212 0.2143486 -0.6727696 0.06703839 -0.9977106 -0.00890682 0.5572827 0.7204199 0.4128332 0.7209631 -0.4092948 0.5591868 0.7451724 0.2380444 -0.6229391 0.7541547 0.320644 0.5730952 0.2658101 -0.9592122 0.09621274 0.5639748 -0.7613667 -0.3197707 0.5715472 0.7552962 -0.3207204 0.7074295 -0.4956997 0.5038106 0.7929612 0.1462735 0.591453 0.3548651 -0.9263827 -0.12604 0.4183237 0.8969967 -0.1428365 0.534142 -0.7706086 -0.3476419 0.8301822 -0.2932726 -0.474119 0.8299808 0.3144322 -0.4607214 0.6909308 -0.6372345 0.3413897 0.8663697 -0.1864858 0.4632782 0.8663964 0.187072 0.4629917 0.3708682 0.9212537 0.1172543 0.4148912 -0.9098406 -0.007440481 0.4928816 0.8700777 0.005707194 0.9201418 0.1165126 -0.3738501 0.7541294 -0.6406829 0.1442719 0.5946144 0.765492 -0.2458774 0.9259226 -0.1481078 -0.3474644 0.8438042 0.4717448 0.2558343 0.919307 -0.2425042 0.3099454 0.8104672 -0.5691829 -0.1384688 0.8146105 0.5660688 -0.1263953 0.7021364 -0.6705164 0.2396083 0.9596778 -0.1891234 -0.2079683 0.9667421 0.1909462 -0.1701446 0.8198273 -0.5322009 -0.211295 0.9684663 0.2281251 0.1001599 0.8746452 0.4195148 0.2429051 0.9729397 -0.2209062 0.06774206 0.8485764 0.5221099 -0.08555317 0.9662324 -0.2538811 0.04403607</float_array>
          <technique_common>
            <accessor source="#shoulder_pan_MShape-normals-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="shoulder_pan_MShape-map1" name="shoulder_pan_MShape-map1">
          <float_array id="shoulder_pan_MShape-map1-array" count="1412">0.3352616 0.5694822 0.348337 0.5735216 0.3187485 0.5729103 0.3421439 0.4099066 0.3290901 0.5779917 0.3249612 0.3851115 0.3662577 0.5708591 0.3147334 0.3527629 0.3638558 0.4402698 0.3614457 0.5757806 0.3306839 0.3178155 0.306035 0.5724518 0.3224134 0.3394334 0.3617311 0.3917466 0.3711317 0.4961807 0.387485 0.5717552 0.3271754 0.2706901 0.3429503 0.2431875 0.3023387 0.3736127 0.3657266 0.3712628 0.3537033 0.3317235 0.3707153 0.4153833 0.3763753 0.5093448 0.3794698 0.5689839 0.3950743 0.5780569 0.3304399 0.2296754 0.2955015 0.5714388 0.3081141 0.2504416 0.3269405 0.1789462 0.3631707 0.334351 0.3682238 0.3890607 0.3731925 0.4529717 0.3841204 0.5181341 0.4105395 0.5701881 0.3599775 0.317196 0.3337377 0.1898241 0.2949873 0.5773005 0.2913257 0.4345344 0.2944379 0.2152531 0.3187045 0.1644329 0.3655808 0.2993474 0.3681453 0.3442506 0.3631344 0.31842 0.3706246 0.352429 0.3731265 0.3925952 0.3763205 0.4150051 0.3794698 0.4746609 0.3881274 0.4824066 0.4236799 0.5719004 0.3955488 0.5689864 0.3301788 0.1340486 0.3598057 0.2494293 0.3370103 0.1302652 0.2792343 0.5713109 0.3083802 0.001695193 0.3825908 0.4638472 0.3904864 0.5193976 0.3857554 0.4454926 0.4161696 0.5779867 0.4277662 0.4927388 0.3940581 0.5514743 0.3629997 0.2590436 0.3564649 0.1856441 0.3335324 0.1183092 0.3453257 0.001695193 0.28091 0.4238925 0.2841338 0.2823114 0.2932028 0.001695218 0.3298014 0.001695193 0.3680241 0.2747748 0.3705451 0.2969271 0.3730591 0.330763 0.3762875 0.3580917 0.37947 0.3566856 0.3826187 0.4158142 0.3906241 0.4412266 0.388198 0.4336072 0.4487773 0.5760643 0.4243955 0.4644217 0.4309949 0.5660878 0.3953637 0.5112301 0.362911 0.2196957 0.3612352 0.1847122 0.3634668 0.1120123 0.3333658 0.05941292 0.2824109 0.3413651 0.2845376 0.2063618 0.36544 0.229465 0.3929727 0.4890217 0.3882454 0.4007831 0.4432062 0.5715449 0.408908 0.4557902 0.4280128 0.4048032 0.4312948 0.4514008 0.3942761 0.4511252 0.3660186 0.001695193 0.2793576 0.3814465 0.2811929 0.2236813 0.2817844 0.2954119 0.2824083 0.001695193 0.3678982 0.2019747 0.3704585 0.2362205 0.3729956 0.2722524 0.3762409 0.2771732 0.3794695 0.3344947 0.3826522 0.3580917 0.3858537 0.3554431 0.4628831 0.570869 0.3965942 0.3930621 0.4247032 0.360355 0.4345255 0.5103584 0.3665825 0.1693763 0.3829414 0.001695193 0.2803866 0.3464313 0.2786858 0.1782571 0.2804462 0.2985834 0.3920235 0.3496284 0.388295 0.3663603 0.4559231 0.4225111 0.401283 0.3362165 0.4197099 0.3366824 0.4299684 0.3155261 0.4314889 0.3769564 0.4347109 0.4329308 0.3703699 0.1735219 0.3728804 0.1647625 0.2791492 0.3079577 0.2792398 0.237005 0.2750403 0.1225258 0.2804954 0.2589346 0.3729333 0.2143317 0.3794698 0.2397186 0.3826885 0.2949299 0.3896136 0.3120566 0.4691926 0.3427916 0.4448638 0.2472077 0.3969551 0.3333257 0.4068697 0.3082099 0.415052 0.2798123 0.4249723 0.2687935 0.3854264 0.2026508 0.3761933 0.1937694 0.387183 0.2756779 0.4559292 0.2601424 0.4752351 0.4207105 0.3958053 0.31842 0.4012245 0.201998 0.4216862 0.211719 0.4269269 0.1866086 0.394066 0.2610712 0.46563 0.1925 0.4745215 0.3276692 0.4789779 0.5722165 0.458885 0.1038129 0.4145483 0.1815617 0.4351606 0.2347479 0.4123574 0.001695201 0.4632118 0.147686 0.4717826 0.2720348 0.4731665 0.2976331 0.4782236 0.3368144 0.4404971 0.001695193 0.4219293 0.1302652 0.4334292 0.1147244 0.4674487 0.09626868 0.471731 0.2135676 0.4744616 0.2711311 0.4757829 0.3050407 0.4551413 0.001695193 0.4645203 0.1072637 0.4703112 0.1756795 0.4737811 0.2433576 0.4771553 0.2954119 0.4627286 0.001695193 0.4702283 0.06715046 0.4737683 0.144927 0.4757369 0.2642345 0.466272 0.001695193 0.4756767 0.2104494 0.4742397 0.001695193 0.4775199 0.1582211 0.4774241 0.06903376 0.4842028 0.3088469 0.349569 0.7582672 0.3756816 0.7820283 0.3586543 0.7709807 0.3510904 0.7479256 0.3961582 0.7944814 0.5037923 0.7721631 0.3899104 0.7977814 0.4266468 0.8057054 0.3387265 0.6819413 0.3682215 0.7815141 0.4074703 0.8058137 0.4879798 0.8004059 0.336226 0.7155699 0.3444045 0.660846 0.5049679 0.7855534 0.4494582 0.8098777 0.4295181 0.8120536 0.3367175 0.7286786 0.3814034 0.6097239 0.5094355 0.7292623 0.5031331 0.7949927 0.4900001 0.8058747 0.3586556 0.6282383 0.4984847 0.6022217 0.5078271 0.6709388 0.5103833 0.7693288 0.4941578 0.8032169 0.4506675 0.8132008 0.3889073 0.6017732 0.432585 0.5893822 0.5118933 0.6786695 0.4677858 0.8119236 0.388814 0.5980377 0.5082465 0.6218077 0.4475952 0.5842369 0.4008819 0.5919769 0.3676051 0.6141324 0.4173277 0.5863276 0.5061635 0.6066629 0.4765403 0.5850377 0.4947889 0.5918009 0.4555894 0.5820483 0.1703707 0.02860413 0.198488 0.04565556 0.09931099 0.0376177 0.2013156 0.0428607 0.07421938 0.05131029 0.1127212 0.02584335 0.1941357 0.02807071 0.2293624 0.06105537 0.2412856 0.09952029 0.09110858 0.001695263 0.1141004 0.001695263 0.1669873 0.02155901 0.218971 0.04094041 0.2411841 0.1762291 0.05849751 0.001695194 0.1200502 0.01947972 0.2472506 0.07054924 0.2586269 0.1014668 0.1154679 0.2234124 0.04911688 0.1010835 0.2332925 0.04762533 0.2065975 0.2144276 0.0774636 0.2711527 0.2627482 0.13649 0.05045407 0.001695194 0.2486356 0.06289383 0.2660127 0.09851421 0.2327361 0.2098341 0.1728068 0.2342273 0.1168901 0.2381689 0.2556778 0.1654238 0.05182717 0.254455 0.04363548 0.001695194 0.2514997 0.1867781 0.2078781 0.2273293 0.09870572 0.2770521 0.2680982 0.1490194 0.02429422 0.001695194 0.05219819 0.3438152 0.1856052 0.2394958 0.1383717 0.2401545 0.1001137 0.3737561 0.1199866 0.252142 0.01988818 0.1674241 0.03173051 0.2912129 0.07488478 0.36057 0.09142883 0.4680886 0.1122105 0.3940588 0.1102799 0.2825743 0.007063647 0.1582211 0.03418949 0.3748971 0.06387253 0.3848311 0.1459046 0.2450129 0.104979 0.4751 0.01930691 0.3088469 0.006894718 0.06903379 0.02698108 0.4337473 0.04820167 0.4825952 0.07595786 0.5723275 0.06390581 0.425822 0.06618513 0.4277771 0.09690868 0.5742011 0.1130719 0.4822464 0.1182567 0.4208984 0.00199203 0.001695194 0.03354067 0.4403254 0.06166046 0.463178 0.1151573 0.5711932 0.1230844 0.5185732 0.02159937 0.5117787 0.06394395 0.4726878 0.03044609 0.5661588 0.003402413 0.4207105 0.03769617 0.5733337 0.06670672 0.5217274 0.126532 0.5728511 0.02125033 0.571891 0.009182434 0.5722165 0.00822914 0.3368144 0.04759661 0.5676349 0.06931917 0.5706245 0.1188531 0.5780934 0.006540697 0.295412 0.05320816 0.5699172 0.05868003 0.5689849 0.06796366 0.5759428 0.004356943 0.2642345 0.004266398 0.2104494 0.1642918 0.7964244 0.1685345 0.7806473 0.2167789 0.7876448 0.1665792 0.7786963 0.2085405 0.7670494 0.2112289 0.8002024 0.1699878 0.7670425 0.2322293 0.7670486 0.2383772 0.802834 0.1700106 0.7241149 0.2426568 0.7900323 0.1668856 0.6893246 0.2539208 0.7670485 0.2618182 0.7889022 0.1700106 0.7109824 0.2632125 0.8037234 0.1700106 0.692484 0.279271 0.7670563 0.2775341 0.7974946 0.1700106 0.678928 0.1699693 0.6577468 0.2928771 0.7962909 0.166721 0.6159149 0.2972781 0.7670418 0.1700106 0.6224443 0.3026417 0.7891412 0.1683453 0.6041959 0.1637986 0.5908204 0.2113681 0.6224443 0.2150017 0.6015168 0.2108748 0.5866865 0.2411401 0.6224254 0.1595381 0.5861804 0.2401777 0.5913038 0.2228901 0.5820848 0.2621441 0.5944563 0.2603454 0.5845338 0.2738984 0.6224438 0.2799416 0.594032 0.2980203 0.6224294 0.2791706 0.5822721 0.2917219 0.5912138 0.3025411 0.6005895 0.3265616 0.7963557 0.3087771 0.8052635 0.3087771 0.7851986 0.3209843 0.7786275 0.3087774 0.659987 0.321309 0.6892558 0.3211365 0.615846 0.308777 0.6123625 0.3274502 0.5907516 0.3087779 0.5910063 0.3300907 0.5861117 0.3087771 0.5820484 0.5786429 0.5733296 0.5677626 0.5266158 0.5945888 0.5426309 0.5388963 0.5733296 0.6588446 0.5319592 0.597852 0.5733297 0.5482473 0.3801953 0.7352251 0.4283671 0.6677907 0.5445569 0.6044386 0.5520161 0.5353914 0.1687715 0.6140971 0.35461 0.713831 0.5002468 0.6909854 0.5286468 0.5468867 0.2441172 0.521301 0.3456319 0.5600908 0.2994604 0.7406 0.4765661 0.7096574 0.3730727 0.7052459 0.5341619 0.5470342 0.1194981 0.5155422 0.1817882 0.514259 0.5733297 0.5833485 0.3157976 0.7240712 0.514937 0.7518904 0.4385349 0.6530722 0.3423907 0.7448199 0.4096012 0.5390286 0.003610061 0.560178 0.002810287 0.507135 0.2288296 0.5261058 0.1606461 0.5075985 0.4524991 0.5791129 0.2251559 0.6195124 0.3360249 0.7469999 0.4881439 0.754216 0.4683638 0.6895242 0.3487064 0.7406418 0.3882468 0.5772963 0.003868212 0.5069143 0.3110344 0.5048318 0.1902104 0.5227235 0.1217944 0.4945084 0.5733297 0.5042955 0.4018511 0.5892203 0.2966787 0.600616 0.3226674 0.7366225 0.5119618 0.7572402 0.4260055 0.671232 0.3382736 0.7218782 0.3651908 0.6330484 0.3329556 0.5046453 0.2632312 0.5044742 0.3307412 0.4969087 0.1511324 0.5193726 0.07755691 0.491374 0.3686631 0.5008529 0.3967678 0.5910263 0.1087831 0.5929856 0.1684752 0.6140895 0.3239683 0.6971274 0.3432233 0.5230812 0.008844955 0.4996535 0.1935783 0.4994209 0.003713988 0.5023265 0.2401534 0.5126477 0.0873607 0.5020322 0.3638266 0.5995106 0.001695194 0.5983154 0.2910182 0.6021931 0.3111784 0.5169808 0.003175389 0.5021691 0.3061253 0.4998942 0.3380199 0.6030475 0.1514849 0.5000519 0.2670671 0.6084031 0.08745237 0.6036683 0.003617894 0.6045315 0.2844391 0.6126437 0.004032399 0.8414801 0.6687972 0.7978752 0.6000981 0.821997 0.6250611 0.8423657 0.7086855 0.8335416 0.7403409 0.6935521 0.7437511 0.7421959 0.8077519 0.7353449 0.5773212 0.7911478 0.7898338 0.716386 0.8089756 0.6938125 0.6419166 0.765117 0.5827985 0.8167645 0.7672559 0.7732984 0.7995007 0.6935987 0.8211156 0.6939874 0.5773236 0.1052119 0.7035016 0.09619116 0.6781065 0.1089092 0.6653181 0.09482338 0.6996958 0.09584719 0.660244 0.10354 0.7224606 0.0707783 0.6992404 0.07077568 0.6734305 0.1034717 0.6394227 0.08819102 0.7168084 0.07078502 0.642328 0.1050523 0.734357 0.07078186 0.7220276 0.07077651 0.6244784 0.09745491 0.6143063 0.1089058 0.6146718 0.07077898 0.5988618 0.0989575 0.5902238 0.1087835 0.5959083 0.07077596 0.5820847 0.00199203 0.7118462 0.01181792 0.7110333 0.004588314 0.7496049 0.01540963 0.748679 0.02362539 0.7243458 0.01822064 0.7768563 0.03999947 0.7201272 0.03999981 0.7517827 0.003095408 0.7919632 0.04000008 0.791671 0.01783276 0.8047488 0.002868315 0.8117433 0.01814492 0.8243061 0.0399849 0.8354071 0.008769234 0.8355612 0.8490357 0.8108983 0.9583188 0.7841289 0.9329686 0.8002824 0.9763259 0.7647296 0.9112772 0.8083127 0.9926955 0.7331088 0.8875883 0.811496 0.9794727 0.6316468 0.848501 0.5917941 0.9958778 0.6693167 0.8707393 0.5796131 0.998008 0.7086589 0.8491384 0.5664549 0.9528924 0.603611 0.9019939 0.5813715 0.9251622 0.587899 0.1442937 0.7465202 0.147096 0.7108998 0.1531721 0.7388648 0.1371439 0.7348235 0.1150478 0.715694 0.1150445 0.7473149 0.1379309 0.6900719 0.1150474 0.6912441 0.1504567 0.6603947 0.1385477 0.6607308 0.1150454 0.6519021 0.143227 0.6218231 0.1301701 0.6330919 0.1530529 0.622636 0.1150452 0.6142321 0.1531752 0.5995799 0.1387416 0.5998653 0.1150452 0.5861963 0.1397129 0.5848021 0.1534027 0.5820848 0.0520941 0.7823462 0.04701285 0.7600733 0.05860044 0.7614485 0.06070262 0.7439606 0.08534907 0.7660786 0.0853646 0.7892468 0.08537024 0.7383484 0.05868084 0.7985944 0.08536983 0.8205016 0.04772828 0.8295797 0.05832628 0.8220468 0.06442846 0.8340821 0.08531691 0.8421023 0.05003178 0.8479648 0.05722057 0.8492915 0.8029702 0.3144184 0.7984978 0.3186714 0.7810887 0.3254002 0.8083739 0.3231027 0.7849549 0.3095476 0.8130367 0.3128139 0.8083224 0.3314016 0.7997204 0.2913447 0.7702774 0.2978911 0.8113331 0.2960846 0.8180679 0.3221467 0.7985039 0.3960359 0.8040748 0.2886313 0.7812403 0.2873573 0.7642273 0.3140966 0.828172 0.2979181 0.8285848 0.3106397 0.8275938 0.3314016 0.9431021 0.3429085 0.8037131 0.2290462 0.7813165 0.1643791 0.7660537 0.2939363 0.8163353 0.286651 0.8333156 0.3194939 0.9332796 0.3314016 0.9431021 0.5620127 0.7996009 0.2078576 0.7702745 0.09037053 0.7674047 0.1544031 0.8116811 0.2056358 0.8245133 0.2861809 0.8665282 0.2997378 0.8696259 0.3085849 0.9332632 0.3229719 0.7985039 0.4978704 0.8044479 0.1506667 0.7818816 0.06924763 0.8018494 0.1169052 0.8236846 0.2234566 0.8324205 0.2839272 0.9097494 0.2887579 0.9229298 0.3220504 0.9431555 0.3175693 0.9001745 0.5624633 0.7930079 0.07749183 0.773356 0.006950563 0.8177728 0.1273129 0.9083588 0.3190365 0.9131633 0.3079756 0.9118102 0.3314015 0.9287937 0.3147843 0.9381111 0.3160304 0.887042 0.5624633 0.8121137 0.1030706 0.8149039 0.05962829 0.8309603 0.1874469 0.9087521 0.180091 0.8854622 0.3137311 0.9166747 0.2872356 0.9385865 0.3024709 0.8685436 0.5624633 0.7990199 0.008084517 0.8321499 0.03918296 0.8383685 0.0667564 0.8253904 0.07355621 0.9184813 0.2251538 0.9243293 0.3008648 0.9622002 0.309832 0.8549876 0.5624633 0.7827954 0.001695193 0.8349023 0.04616108 0.9191775 0.09967428 0.9298331 0.2890878 0.9418312 0.2882654 0.9712519 0.316909 0.8338064 0.5622843 0.8814315 0.03181744 0.8685031 0.03783346 0.906065 0.06886689 0.932379 0.1339577 0.928928 0.2063765 0.9375984 0.2844799 0.9579098 0.2922703 0.9742047 0.2953689 0.7985039 0.5624635 0.8896793 0.007532897 0.870803 0.04820127 0.9247423 0.07362575 0.9365805 0.2164614 0.9410423 0.2454723 0.9520959 0.220468 0.9784406 0.3258011 0.8390865 0.002782629 0.9222239 0.05124226 0.9070842 0.04590774 0.931676 0.08713103 0.9672146 0.1570447 0.9442464 0.01024943 0.9379666 0.09303724 0.9397388 0.1853491 0.9585466 0.1725901 0.9465411 0.003370117 0.9514197 0.07924344 0.961686 0.005091971 0.9673999 0.05936987 0.6289186 0.792533 0.6794394 0.8035516 0.6564475 0.8085063 0.6873698 0.7834866 0.6238364 0.8082374 0.6874135 0.658275 0.6108212 0.8009406 0.5896332 0.8003699 0.615793 0.8072073 0.567331 0.7877325 0.6089744 0.8059754 0.5629269 0.7758662 0.5352795 0.7539899 0.5573411 0.7797649 0.5180286 0.6964343 0.5462167 0.7686341 0.5535174 0.7762215 0.5188055 0.6795114 0.5226843 0.7258503 0.5231754 0.6588185 0.5563861 0.6066956 0.5423234 0.621873 0.5707053 0.5959011 0.5292395 0.6432942 0.5904559 0.5855803 0.6150932 0.578606 0.6548398 0.5773211 0.6740489 0.5803363 0.6874167 0.6106504 0.684328 0.5892943 0.1348457 0.8091748 0.1171753 0.8355228 0.1171751 0.792062 0.1399919 0.7933384 0.1290829 0.7570281 0.1171752 0.7513064 0.06464035 0.7070419 0.0461583 0.6035542 0.06463568 0.5820847 0.03904129 0.6470149 0.0473621 0.6877705 0.1464094 0.7769591 0.1518052 0.7513064 0.1503447 0.7920989 0.1063429 0.7383484 0.1054943 0.8063669 0.09183873 0.8109748 0.1092458 0.8243579 0.1707384 0.8335019 0.1581031 0.8260487 0.1797817 0.8077149</float_array>
          <technique_common>
            <accessor source="#shoulder_pan_MShape-map1-array" count="706" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="shoulder_pan_MShape-vertices" name="shoulder_pan_MShape-vertices">
          <input semantic="POSITION" source="#shoulder_pan_MShape-positions"/>
          <input semantic="NORMAL" source="#shoulder_pan_MShape-normals"/>
        </vertices>
        <triangles material="lambert2SG" count="1000">
          <input semantic="VERTEX" source="#shoulder_pan_MShape-vertices" offset="0"/>
          <input semantic="TEXCOORD" source="#shoulder_pan_MShape-map1" offset="1" set="0"/>
          <p>223 199 218 190 243 198 204 193 218 190 223 199 191 187 218 190 204 193 179 189 191 187 204 193 179 189 146 184 191 187 146 184 179 189 136 192 127 11 146 36 136 26 90 0 78 1 118 2 44 6 76 9 78 1 118 183 146 184 127 185 243 198 245 210 223 199 118 183 121 186 146 184 78 1 121 4 118 2 76 195 121 186 78 200 76 195 85 191 121 186 44 6 27 15 76 9 121 186 191 187 146 184 417 225 432 226 272 227 417 225 301 230 412 236 432 226 246 229 272 227 417 225 272 227 301 230 301 230 338 240 412 236 338 313 359 314 419 315 410 317 419 315 359 314 359 314 373 319 410 317 338 313 419 315 412 318 338 240 301 230 307 235 272 227 307 235 301 230 246 229 259 234 272 227 338 356 307 357 345 358 259 234 307 235 272 227 345 358 349 359 338 356 373 319 359 314 349 316 349 316 359 314 338 313 232 309 220 282 242 305 242 305 220 282 240 299 232 309 228 308 220 282 220 282 228 308 219 304 240 299 220 282 238 295 238 295 220 282 234 291 197 301 198 294 206 296 214 298 206 296 203 281 220 282 214 298 203 281 206 296 198 294 203 281 203 281 209 290 220 282 240 299 238 295 248 283 239 285 248 283 238 295 234 291 237 284 238 295 238 295 237 284 239 285 234 291 226 263 237 284 234 291 220 282 226 263 236 276 239 285 237 284 203 281 212 275 209 290 177 152 134 107 161 144 198 294 177 302 161 297 203 281 198 294 195 279 195 279 198 294 161 297 134 107 145 134 161 144 236 276 237 284 226 263 239 285 236 276 247 270 236 276 226 263 247 270 226 263 249 247 247 270 220 282 212 275 226 263 220 282 209 290 212 275 203 281 207 269 212 275 203 281 195 279 207 269 226 263 225 256 249 247 212 275 207 269 226 263 195 279 161 297 173 303 173 160 164 167 168 172 159 151 173 160 161 144 161 144 145 134 159 151 173 160 159 151 164 167 159 151 145 134 152 159 152 159 158 166 159 151 173 303 168 307 195 279 164 167 163 176 168 172 168 307 163 311 195 279 163 311 162 312 195 279 163 176 154 171 162 178 152 159 145 134 151 158 158 166 164 167 159 151 158 166 163 176 164 167 152 159 151 158 154 171 158 166 152 159 154 171 145 134 141 150 151 158 154 171 163 176 158 166 131 118 134 107 122 90 197 301 206 296 214 298 107 79 114 110 122 90 107 79 108 93 114 110 108 93 115 123 114 110 114 110 115 123 122 90 107 79 91 48 99 59 91 48 77 33 99 59 107 79 99 59 108 93 99 59 77 33 92 78 134 107 131 118 145 134 99 59 100 92 108 93 99 59 92 78 100 92 77 33 75 91 92 78 100 92 109 122 108 93 92 78 75 91 93 109 92 78 93 109 100 92 108 93 109 122 115 123 53 60 58 80 77 33 48 88 54 94 58 80 58 80 54 94 77 33 27 15 38 56 53 60 53 60 57 49 27 15 27 15 2 23 15 32 48 88 53 60 38 56 58 80 53 60 48 88 39 75 38 56 28 47 48 88 38 56 39 75 15 32 18 57 28 47 28 47 27 15 15 32 38 56 27 15 28 47 15 32 7 55 18 57 15 32 4 46 7 55 77 33 54 94 64 108 77 33 64 108 75 91 39 75 29 76 31 89 48 88 39 75 54 94 28 47 18 57 29 76 28 47 29 76 39 75 8 74 18 57 7 55 54 94 39 75 64 108 29 76 18 57 31 89 145 134 131 118 130 143 131 118 122 90 124 135 115 123 124 135 122 90 115 123 109 122 124 135 100 92 105 121 109 122 100 92 93 109 105 121 93 109 75 91 86 120 105 121 124 135 109 122 131 118 124 135 130 143 145 134 130 143 141 150 105 121 116 155 124 135 105 121 93 109 94 139 93 109 86 120 94 139 64 108 71 119 75 91 74 137 75 91 71 119 46 116 31 89 32 117 75 91 74 137 82 138 71 119 64 108 65 136 18 57 8 74 20 106 20 106 31 89 18 57 65 136 64 108 46 116 46 116 64 108 39 75 39 75 31 89 46 116 31 89 20 106 32 117 8 74 1 73 11 105 8 74 11 105 20 106 46 116 32 117 37 133 32 117 20 106 37 133 75 91 82 138 86 120 65 136 61 145 71 119 46 116 61 145 65 136 37 133 52 149 46 116 74 137 71 119 70 146 74 137 70 146 82 138 94 139 86 120 87 147 61 145 52 149 71 119 70 146 71 119 52 149 46 116 52 149 61 145 11 105 12 132 20 106 37 133 25 142 52 149 20 106 25 142 37 133 12 132 25 142 20 106 1 73 3 104 11 105 12 132 16 140 25 142 226 263 207 269 225 256 207 269 195 279 196 268 225 256 207 269 196 268 225 256 221 244 249 247 221 244 225 256 200 262 200 262 225 256 196 268 162 178 170 180 195 182 195 279 170 274 196 268 162 178 154 171 153 175 141 150 150 165 151 158 154 171 150 165 153 175 151 158 150 165 154 171 141 150 148 170 150 165 150 165 148 170 153 175 196 268 170 274 200 262 153 175 170 180 162 178 200 262 170 274 169 280 170 180 153 175 169 181 137 157 143 164 141 150 141 150 143 164 148 170 143 164 137 157 139 169 221 244 246 229 249 247 215 257 224 249 221 244 231 239 246 229 221 244 231 239 259 234 246 229 221 244 224 249 231 239 200 262 215 257 221 244 153 175 148 170 147 174 148 170 143 164 147 174 139 169 133 153 143 164 169 181 153 175 156 179 169 280 156 289 200 262 153 175 147 174 156 179 147 174 143 164 142 177 147 174 142 177 156 179 143 164 133 153 142 177 25 142 16 140 52 149 137 157 141 150 130 143 130 143 124 135 133 153 116 155 112 163 124 135 94 139 98 148 105 121 116 155 105 121 98 148 86 120 82 138 87 147 87 147 98 148 94 139 137 157 130 143 133 153 133 153 124 135 119 161 98 148 112 163 116 155 98 148 87 147 89 162 119 161 124 135 112 163 89 162 87 147 81 154 82 138 81 154 87 147 70 146 81 154 82 138 11 105 3 104 12 132 52 149 16 140 70 146 79 156 89 162 81 154 79 156 81 154 70 146 139 169 137 157 133 153 129 168 133 153 119 161 98 148 89 162 112 163 112 163 89 162 79 156 133 153 135 173 142 177 135 173 133 153 129 168 112 163 79 156 119 161 70 146 16 140 0 112 70 146 0 112 79 156 107 79 122 90 91 48 77 33 57 49 53 60 7 55 4 46 8 74 12 132 5 131 16 140 219 304 214 298 220 282 198 294 197 301 177 302 15 32 2 23 4 46 8 74 4 46 1 73 3 104 5 131 12 132 246 229 315 243 249 247 247 270 248 283 239 285 348 363 333 364 350 362 256 368 250 369 286 370 313 377 291 373 286 370 333 364 331 365 313 366 331 365 291 367 313 366 291 373 256 368 286 370 348 363 331 365 333 364 356 339 350 335 333 340 350 335 356 339 374 337 245 397 230 388 223 396 217 378 223 396 230 388 217 378 230 388 229 382 199 434 204 439 205 423 210 410 211 430 223 396 205 423 204 439 211 430 204 439 199 434 179 432 205 423 211 430 210 410 223 396 217 378 213 399 213 399 210 410 223 396 210 410 202 389 205 423 202 389 199 434 205 423 202 389 210 410 213 399 245 397 229 382 230 388 244 384 229 382 245 397 217 378 202 389 213 399 217 378 208 383 202 389 208 383 217 378 222 371 189 420 192 408 188 421 172 422 179 432 202 389 179 432 199 434 202 389 202 389 190 409 172 422 179 53 144 37 136 26 144 37 179 53 172 65 132 18 136 26 144 37 190 409 180 431 172 422 190 409 202 389 193 398 208 383 193 398 202 389 189 420 190 409 193 398 190 409 186 433 180 431 182 443 180 431 186 433 189 420 186 433 190 409 176 113 166 85 180 96 166 85 172 65 180 96 182 126 176 113 180 96 192 408 189 420 193 398 182 126 175 115 176 113 167 98 166 85 175 115 175 115 166 85 176 113 192 408 193 398 208 383 189 420 185 440 186 433 188 421 185 440 189 420 186 433 185 440 182 443 167 98 160 66 166 85 181 441 182 443 185 440 181 127 174 129 182 126 174 129 175 115 182 126 171 97 157 86 160 66 171 97 167 98 174 129 174 129 167 98 175 115 160 66 167 98 171 97 166 85 144 37 172 65 166 85 160 66 144 37 157 86 138 38 160 66 160 66 138 38 144 37 244 384 233 374 229 382 244 384 361 379 233 374 250 369 222 371 233 374 233 374 361 379 250 369 233 374 217 378 229 382 217 378 233 374 222 371 188 421 208 383 187 412 222 371 250 369 256 368 208 383 201 390 194 400 222 371 201 390 208 383 188 421 192 408 208 383 184 435 185 440 188 421 187 412 184 435 188 421 171 97 181 127 183 114 183 425 184 435 187 412 183 425 181 441 184 435 187 412 208 383 194 400 184 435 181 441 185 440 181 127 171 97 174 129 157 86 171 97 183 114 187 412 194 400 183 425 194 128 157 86 183 114 165 411 157 424 194 400 157 86 140 67 138 38 165 99 140 67 157 86 194 400 201 390 165 411 132 18 127 11 136 26 123 7 118 2 127 11 113 5 118 2 123 7 118 2 113 5 90 0 113 5 83 3 90 0 138 38 132 18 144 37 123 7 127 11 132 18 126 27 132 18 138 38 123 7 132 18 126 27 110 16 113 5 117 12 126 27 120 39 123 7 117 12 113 5 123 7 113 5 101 10 83 3 123 7 110 16 117 12 110 16 101 10 113 5 84 17 83 3 101 10 84 17 101 10 102 25 102 25 101 10 110 16 83 3 78 1 90 0 83 3 44 6 78 1 83 3 56 8 44 6 66 13 56 8 83 3 44 6 56 8 26 14 2 23 6 22 4 46 26 14 6 22 44 6 26 14 17 31 6 22 30 21 17 31 26 14 56 8 30 21 26 14 9 45 6 22 17 31 30 21 19 44 17 31 19 44 9 45 17 31 40 30 30 21 56 8 4 46 6 22 9 45 4 46 9 45 1 73 84 17 73 20 83 3 66 13 49 19 56 8 73 20 66 13 83 3 73 20 59 29 66 13 49 19 50 40 41 41 73 20 84 17 68 34 68 34 60 42 73 20 60 42 59 29 73 20 69 51 68 34 84 17 68 34 62 61 60 42 69 51 62 61 68 34 49 19 40 30 56 8 33 43 30 21 40 30 33 43 19 44 30 21 59 29 49 19 66 13 10 72 9 45 19 44 49 19 41 41 40 30 19 44 21 71 10 72 59 29 50 40 49 19 40 30 41 41 33 43 33 43 21 71 19 44 1 73 9 45 10 72 10 72 21 71 13 103 50 40 59 29 60 42 34 70 21 71 33 43 33 43 41 41 34 70 50 40 42 69 41 41 42 69 34 70 41 41 62 61 50 40 60 42 34 70 22 102 21 71 1 73 10 72 3 104 10 72 13 103 3 104 21 71 22 102 13 103 125 54 126 27 138 38 125 54 138 38 140 67 111 28 110 16 123 7 111 28 102 25 110 16 102 25 95 35 84 17 120 39 111 28 123 7 103 50 102 25 111 28 51 87 50 40 62 61 103 50 95 35 102 25 126 27 125 54 120 39 88 52 84 17 95 35 80 64 84 17 88 52 96 63 95 35 103 50 120 39 103 50 111 28 88 52 95 35 96 63 120 39 96 63 103 50 97 84 96 63 120 39 97 84 88 52 96 63 97 84 80 64 88 52 104 68 120 39 125 54 97 84 104 68 80 64 104 68 97 84 120 39 72 62 67 82 69 51 72 62 69 51 84 17 63 81 62 61 69 51 67 82 63 81 69 51 72 62 55 83 67 82 55 83 72 62 84 17 16 140 24 125 0 112 51 87 62 61 63 81 35 101 22 102 34 70 42 69 35 101 34 70 51 87 42 69 50 40 43 100 42 69 51 87 23 130 22 102 35 101 63 81 47 111 51 87 47 111 63 81 67 82 47 111 43 100 51 87 43 100 36 124 35 101 23 130 14 141 13 103 35 101 42 69 43 100 36 124 23 130 35 101 23 130 13 103 22 102 55 83 47 111 67 82 36 124 24 125 23 130 47 111 36 124 43 100 24 125 14 141 23 130 16 140 14 141 24 125 47 111 55 83 36 124 55 83 24 125 36 124 0 112 24 125 55 83 55 83 84 17 80 64 80 64 45 95 55 83 45 95 0 112 55 83 223 396 211 430 204 439 13 103 14 141 5 131 2 23 44 6 6 22 5 131 14 141 16 140 3 104 13 103 5 131 361 379 464 375 250 369 464 375 416 372 250 369 464 375 361 379 447 386 416 372 464 375 450 380 498 448 453 449 473 450 500 451 487 452 453 449 487 452 368 453 453 449 498 448 500 451 453 449 448 456 487 452 467 460 368 453 487 452 421 454 487 452 448 456 421 454 413 455 430 459 453 449 368 453 413 455 453 449 413 455 371 458 374 463 413 455 368 453 371 458 437 461 421 454 448 456 421 454 408 457 368 453 408 457 370 462 368 453 409 394 447 386 361 379 409 394 433 405 447 386 424 417 433 405 409 394 474 395 464 375 447 386 447 386 470 406 474 395 433 405 454 418 447 386 454 418 470 406 447 386 433 405 441 429 454 418 404 464 414 465 424 466 414 465 426 468 424 466 424 417 441 429 433 405 441 472 424 466 426 468 414 465 408 470 421 471 426 468 414 465 421 471 426 468 437 474 441 472 441 472 437 474 448 477 437 474 426 468 421 471 456 478 441 472 448 477 441 472 456 478 454 479 454 479 477 481 470 482 456 478 477 481 454 479 448 477 467 480 456 478 467 480 477 481 456 478 467 480 487 483 477 481 464 375 474 395 483 393 464 375 483 393 469 385 483 393 486 404 469 385 470 406 491 416 474 395 491 416 483 393 474 395 469 385 450 380 464 375 469 385 458 392 450 380 416 372 450 380 435 381 450 380 458 392 435 381 435 381 422 376 416 372 460 351 443 349 458 353 486 404 479 403 469 385 479 403 458 392 469 385 458 353 466 354 460 351 458 392 443 387 435 381 422 376 435 381 443 387 429 346 411 343 422 347 443 349 429 346 422 347 443 349 460 351 445 348 443 349 445 348 429 346 479 403 466 415 458 392 470 484 477 485 491 486 477 485 494 487 491 486 491 416 486 404 483 393 494 487 499 489 491 486 491 486 499 489 486 492 488 488 494 487 477 485 487 490 488 488 477 485 487 490 494 487 488 488 494 487 500 491 499 489 486 492 499 489 490 494 479 495 486 492 490 494 490 494 480 496 479 495 479 495 480 496 466 498 466 354 473 352 460 351 429 346 418 342 411 343 480 355 473 352 466 354 473 352 453 350 460 351 498 493 490 494 499 489 498 493 480 496 490 494 460 351 453 350 445 348 445 348 430 344 429 346 429 346 430 344 418 342 445 348 453 350 430 344 430 344 413 341 418 342 500 491 494 487 487 490 498 493 499 489 500 491 473 497 480 496 498 493 461 238 246 229 462 233 246 229 432 226 462 233 461 238 315 243 246 229 461 238 439 246 315 243 373 499 459 500 442 501 459 500 373 499 472 502 373 499 493 504 472 502 496 508 501 510 493 504 373 499 476 506 493 504 373 499 442 501 427 503 427 503 410 505 373 499 493 504 476 506 496 508 476 506 373 499 367 507 452 512 406 509 423 513 452 512 476 506 406 509 452 512 423 513 436 514 367 507 406 509 476 506 367 507 379 511 406 509 315 243 439 246 420 253 420 253 439 246 440 259 455 252 440 259 439 246 475 255 471 258 461 238 461 238 471 258 455 252 461 238 455 252 439 246 482 242 484 248 461 238 484 248 475 255 461 238 462 233 482 242 461 238 451 232 482 242 462 233 432 226 451 232 462 233 417 225 434 228 432 226 434 228 451 232 432 226 492 261 471 258 475 255 475 255 484 248 492 261 451 232 465 241 482 242 446 237 465 241 451 232 428 231 446 237 434 228 417 225 428 231 434 228 434 228 446 237 451 232 468 515 489 516 465 517 412 236 428 231 417 225 420 253 425 264 403 265 425 535 440 536 438 537 440 259 425 264 420 253 425 535 415 542 403 544 455 530 449 533 440 534 440 536 449 538 438 537 455 530 463 531 449 533 471 528 478 526 455 530 478 526 463 531 455 530 425 535 423 540 415 542 423 540 406 543 415 542 425 535 436 539 423 540 438 537 436 539 425 535 449 538 436 539 438 537 482 242 489 251 484 248 495 524 478 526 492 523 463 531 452 532 449 533 478 526 476 529 463 531 465 241 457 245 468 250 484 248 489 251 492 261 497 521 495 524 492 523 492 523 478 526 471 528 489 516 497 521 492 523 465 241 489 251 482 242 446 237 457 245 465 241 468 515 481 518 489 516 444 326 457 331 446 328 431 323 444 326 428 321 428 321 444 326 446 328 419 315 431 323 428 321 412 318 419 315 428 321 478 526 495 524 485 527 478 526 485 527 476 529 476 529 485 527 496 525 485 527 495 524 496 525 496 525 495 524 501 522 501 522 495 524 497 521 481 518 493 519 489 516 493 519 497 521 489 516 419 315 427 320 431 323 459 330 468 334 457 331 444 326 459 330 457 331 459 330 472 336 468 334 427 320 442 325 431 323 493 519 501 522 497 521 468 334 472 336 481 338 481 518 472 520 493 519 431 323 442 325 444 326 442 325 459 330 444 326 427 320 419 315 410 317 452 532 463 531 476 529 436 539 449 538 452 541 414 465 404 464 407 467 370 476 408 470 397 473 397 473 408 470 407 467 357 550 370 551 397 552 334 554 357 550 397 552 360 469 397 473 407 467 407 467 404 464 360 469 404 419 395 402 360 428 409 394 395 402 404 419 424 417 409 394 404 419 409 394 361 379 395 402 395 402 298 414 360 428 395 402 361 379 263 391 395 402 263 391 298 414 397 552 299 558 334 554 321 557 357 550 334 554 360 428 298 414 302 438 302 564 299 558 397 552 397 473 360 469 302 475 299 558 314 563 334 554 299 558 302 564 294 571 302 438 298 414 274 413 302 438 274 413 294 437 274 413 298 414 263 391 361 379 244 384 263 391 244 384 258 401 263 391 263 391 258 401 274 413 414 465 407 467 408 470 314 563 321 557 334 554 371 584 376 602 378 610 378 610 377 618 371 584 372 625 371 584 377 618 371 584 368 561 373 575 370 551 364 553 366 556 370 551 366 556 368 561 372 625 374 634 371 584 357 550 364 553 370 551 405 545 406 543 402 546 405 545 402 546 337 548 326 549 337 548 402 546 337 267 403 265 405 277 379 547 326 549 402 546 362 601 328 609 379 592 379 592 328 609 329 617 379 592 329 617 326 624 329 617 295 633 326 624 317 632 295 633 329 617 326 624 295 633 337 641 403 265 318 254 420 253 420 253 318 254 315 243 337 267 318 254 403 265 337 267 268 260 318 254 295 273 268 260 337 267 268 260 249 247 318 254 315 243 318 254 249 247 415 542 406 543 405 545 328 609 322 623 329 617 415 542 405 545 403 544 406 543 379 547 402 546 317 632 329 617 322 623 366 556 367 568 368 561 371 584 373 575 375 593 368 561 367 568 373 575 365 574 363 583 367 568 375 593 376 602 371 584 366 556 365 574 367 568 379 592 363 583 362 601 367 568 363 583 379 592 350 362 351 361 347 360 350 335 372 333 351 324 377 332 351 324 372 333 349 316 351 324 375 322 373 319 349 316 375 322 372 333 350 335 374 337 378 329 351 324 377 332 376 327 351 324 378 329 375 322 351 324 376 327 57 49 77 33 27 15 91 48 85 24 77 33 77 33 85 24 27 15 91 48 106 58 85 24 134 107 128 77 122 90 122 90 128 77 91 48 91 48 128 77 106 58 134 219 177 215 128 205 241 310 214 298 228 308 242 305 228 308 232 309 242 305 241 310 228 308 177 215 178 211 128 205 197 218 178 211 177 215 197 218 214 220 178 211 155 201 106 196 128 205 128 205 178 211 155 201 178 211 227 212 155 201 241 217 227 212 214 220 214 220 227 212 178 211 241 310 242 305 248 283 27 15 44 6 2 23 27 15 85 24 76 9 228 308 214 298 219 304 321 557 341 570 335 576 314 563 341 570 321 557 299 558 294 571 314 563 294 437 282 427 306 442 383 587 335 576 341 570 306 578 314 563 294 571 274 413 282 427 294 437 274 413 258 401 282 427 383 587 341 570 390 594 341 570 381 586 390 594 314 446 306 442 332 444 314 563 332 577 341 570 354 595 398 611 390 594 381 586 354 595 390 594 278 426 306 442 282 427 381 586 341 570 332 577 354 595 381 586 332 577 296 436 309 445 332 444 306 442 296 436 332 444 332 444 309 445 354 447 282 427 258 401 278 426 244 384 253 407 258 401 306 442 278 426 296 436 244 384 245 397 253 407 253 407 278 426 258 401 278 426 253 407 296 436 235 655 307 656 259 657 259 657 231 659 235 655 235 655 345 658 307 656 231 659 224 663 216 661 215 665 216 661 224 663 216 661 235 655 231 659 200 662 235 655 216 661 345 658 235 655 347 660 347 660 235 655 200 662 142 668 149 666 156 664 347 660 200 662 156 664 149 666 347 660 156 664 119 667 347 660 149 666 135 671 129 670 149 666 142 668 135 671 149 666 129 670 119 667 149 666 0 669 347 660 119 667 79 673 0 669 119 667 347 660 291 682 348 683 348 683 291 682 331 684 291 682 347 660 256 681 347 660 45 672 80 674 347 660 222 680 256 681 201 679 222 680 347 660 347 660 80 674 140 675 347 660 0 669 45 672 80 674 125 676 140 675 104 678 125 676 80 674 140 675 165 677 347 660 347 660 165 677 201 679 200 662 216 661 215 665 347 360 349 359 345 358 351 361 349 359 347 360 350 362 347 360 348 363 322 623 325 640 317 632 322 623 323 639 325 640 324 646 317 632 325 640 324 646 295 633 317 632 340 650 324 646 325 640 323 639 344 649 325 640 300 272 295 273 324 288 325 640 344 649 388 648 325 640 386 652 340 650 388 648 386 652 325 640 396 647 385 653 386 652 386 652 324 646 340 650 386 652 352 654 324 646 385 653 352 654 386 652 352 293 300 272 324 288 385 300 312 292 352 293 303 287 352 293 312 292 352 293 303 287 300 272 300 272 275 266 268 260 295 273 300 272 268 260 275 266 249 247 268 260 283 278 300 272 303 287 283 278 275 266 300 272 312 292 283 278 303 287 312 292 266 286 283 278 261 271 275 266 283 278 249 247 275 266 261 271 247 270 249 247 261 271 248 283 247 270 261 271 283 278 266 286 261 271 248 283 261 271 266 286 240 299 248 283 242 305 284 597 276 607 270 582 281 606 279 605 339 613 251 590 252 589 281 606 281 606 252 589 279 605 336 628 281 606 339 613 320 569 346 585 293 579 343 603 342 614 308 596 342 614 343 603 392 604 293 579 343 603 308 596 343 603 293 579 346 585 392 604 380 620 342 614 380 620 339 613 342 614 355 636 339 613 380 620 387 627 355 636 380 620 308 596 271 588 293 579 271 588 308 596 279 605 342 614 339 613 308 596 339 613 279 605 308 596 336 628 339 613 355 636 392 604 399 612 380 620 400 626 387 627 399 612 399 612 387 627 380 620 343 603 346 585 383 587 343 603 383 587 392 604 264 572 293 579 271 588 280 559 320 569 293 579 290 194 354 203 309 209 290 194 369 197 354 203 290 194 358 188 369 197 369 619 398 611 354 595 253 214 245 210 296 204 245 210 243 198 296 204 296 204 290 194 309 209 243 198 290 194 296 204 218 190 290 194 243 198 393 202 398 208 369 197 369 197 358 188 393 202 393 642 401 635 398 611 393 202 358 188 389 207 284 597 287 599 276 607 276 685 287 686 273 687 262 598 284 597 260 581 260 581 251 590 262 598 284 597 270 582 260 581 270 688 276 685 273 687 273 687 285 689 270 688 273 687 288 690 285 689 366 691 287 692 365 693 273 694 287 692 366 691 288 695 273 694 366 691 264 572 280 559 293 579 271 588 257 580 264 572 252 589 257 580 271 588 271 588 279 605 252 589 310 562 320 569 280 559 251 590 260 581 254 565 260 581 270 582 265 566 260 581 265 566 254 565 251 590 254 565 252 589 270 582 285 573 265 566 357 550 321 557 310 562 316 638 289 630 330 629 269 615 330 629 289 630 353 637 382 645 330 629 330 629 319 621 353 637 269 615 319 621 330 629 319 621 281 606 336 628 400 626 384 644 387 627 336 628 384 644 353 637 384 644 355 636 387 627 336 628 353 637 319 621 336 628 355 636 384 644 384 644 394 643 353 637 353 637 394 643 382 645 330 629 344 649 316 638 384 696 400 697 394 698 330 629 382 645 388 648 382 645 394 643 388 648 269 615 281 606 319 621 277 622 255 608 269 615 277 622 269 615 289 630 316 638 277 622 289 630 269 615 251 590 281 606 269 615 255 608 251 590 316 699 311 700 277 701 322 623 328 609 311 631 390 594 398 611 399 612 390 594 392 604 383 587 392 604 390 594 399 612 400 626 398 611 401 635 399 612 398 611 400 626 383 587 346 585 335 576 346 585 320 569 335 576 335 576 320 569 321 557 321 557 320 569 310 562 396 647 401 635 391 651 327 206 391 216 389 207 393 202 389 207 401 213 401 213 389 207 391 216 389 207 358 188 327 206 312 292 385 300 327 306 396 647 391 651 385 653 385 221 391 216 327 206 266 222 241 217 248 224 312 223 327 206 266 222 327 206 227 212 241 217 266 222 327 206 241 217 284 597 304 591 287 599 365 574 304 591 363 583 287 599 304 591 365 574 297 600 304 591 262 598 262 598 267 616 297 600 251 590 255 608 262 598 262 598 255 608 267 616 267 616 255 608 277 622 297 600 363 583 304 591 328 609 362 601 297 600 267 616 328 609 297 600 328 702 277 701 311 700 267 616 277 622 328 609 284 597 262 598 304 591 363 583 297 600 362 601 364 553 305 560 366 556 285 573 288 567 305 560 366 556 305 560 288 567 292 555 280 559 254 565 265 566 305 560 292 555 292 555 254 565 265 566 364 553 292 555 305 560 364 553 357 550 292 555 292 555 357 550 280 559 264 572 257 580 254 565 254 565 280 559 264 572 257 580 252 589 254 565 310 703 280 704 357 705 305 560 265 566 285 573 401 635 396 647 394 643 394 643 386 652 388 648 400 626 401 635 394 643 386 652 394 643 396 647 344 649 330 629 388 648 323 639 316 638 344 649 322 623 311 631 323 639 311 631 316 638 323 639 327 206 358 188 155 201 327 206 155 201 227 212 358 188 106 196 155 201 358 188 85 191 106 196 191 187 358 188 218 190 358 188 191 187 121 186 358 188 121 186 85 191 218 190 358 188 290 194 416 372 422 376 286 370 250 369 416 372 286 370 333 340 313 345 411 343 422 347 411 343 313 345 333 340 411 343 418 342 333 340 418 342 356 339 418 342 413 341 356 339 286 370 422 376 313 377 356 339 413 341 374 337</p>
        </triangles>
      </mesh>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <originalMayaNodeId>shoulder_pan_MShape</originalMayaNodeId>
          <double_sided>1</double_sided>
        </technique>
      </extra>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="VisualSceneNode" name="shoulder_pan1d">
      <node id="shoulder_pan_M" name="shoulder_pan_M" type="NODE">
        <translate sid="translate">0 0 0</translate>
        <rotate sid="rotateZ">0 0 1 0</rotate>
        <rotate sid="rotateY">0 1 0 0</rotate>
        <rotate sid="rotateX">1 0 0 0</rotate>
        <scale sid="scale">0.1 0.1 0.1</scale>
        <instance_geometry url="#shoulder_pan_MShape">
          <bind_material>
            <technique_common>
              <instance_material symbol="lambert2SG" target="#lambert3">
                <bind_vertex_input semantic="TEX0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="TEX1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADAMaya">
            <originalMayaNodeId>shoulder_pan_M</originalMayaNodeId>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#VisualSceneNode"/>
  </scene>
</COLLADA>
