<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>WakiMudi</author>
      <authoring_tool>OpenCOLLADA2009 x64</authoring_tool>
      <comments>
			ColladaMaya export options: 
			bakeTransforms=0;relativePaths=0;copyTextures=0;exportTriangles=0;exportCgfxFileReferences=1;
			isSampling=0;curveConstrainSampling=0;removeStaticCurves=1;exportPolygonMeshes=1;exportLights=1;
			exportCameras=1;exportJointsAndSkin=1;exportAnimations=1;exportInvisibleNodes=0;exportDefaultCameras=0;
			exportTexCoords=1;exportNormals=1;exportNormalsPerVertex=1;exportVertexColors=1;exportVertexColorsPerVertex=1;
			exportTexTangents=0;exportTangents=0;exportReferencedMaterials=0;exportMaterialsOnly=0;
			exportXRefs=1;dereferenceXRefs=1;exportCameraAsLookat=0;cameraXFov=0;cameraYFov=1;doublePrecision=0
		</comments>
      <source_data>file:///C:/Users/<USER>/Documents/maya/projects/willow_textures/scenes/upper_arm_roll1a_med_hard.mb</source_data>
    </contributor>
    <created>2010-04-30T15:33:34</created>
    <modified>2010-04-30T15:33:34</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Y_UP</up_axis>
  </asset>
  <library_materials>
    <material id="lambert3" name="lambert3">
      <instance_effect url="#lambert3-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="lambert3-fx">
      <profile_COMMON>
        <newparam sid="file4-surface">
          <surface type="2D">
            <init_from>file4</init_from>
          </surface>
        </newparam>
        <newparam sid="file4-sampler">
          <sampler2D>
            <source>file4-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="file3-surface">
          <surface type="2D">
            <init_from>file3</init_from>
          </surface>
        </newparam>
        <newparam sid="file3-sampler">
          <sampler2D>
            <source>file3-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="file4-sampler" texcoord="TEX0">
                <extra>
                  <technique profile="OpenCOLLADAMaya">
                    <blend_mode>NONE</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <transparent opaque="RGB_ZERO">
              <color>0 0 0 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </lambert>
          <extra>
            <technique profile="OpenCOLLADAMaya">
              <bump>
                <texture texture="file3-sampler" texcoord="TEX1">
                  <extra>
                    <technique profile="OpenCOLLADA3dsMax">
                      <amount>1</amount>
                      <bumpInterp>1</bumpInterp>
                    </technique>
                    <technique profile="OpenCOLLADAMaya">
                      <blend_mode>NONE</blend_mode>
                    </technique>
                  </extra>
                </texture>
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="file4" name="file4">
      <init_from>upper_arm_roll_color.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file4</originalMayaNodeId>
        </technique>
      </extra>
    </image>
    <image id="file3" name="file3">
      <init_from>upper_arm_roll_normals.png</init_from>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <dgnode_type>kFile</dgnode_type>
          <image_sequence>0</image_sequence>
          <originalMayaNodeId>file3</originalMayaNodeId>
        </technique>
      </extra>
    </image>
  </library_images>
  <library_geometries>
    <geometry id="upper_arm_roll_MShape" name="upper_arm_roll_MShape">
      <mesh>
        <source id="upper_arm_roll_MShape-positions" name="upper_arm_roll_MShape-positions">
          <float_array id="upper_arm_roll_MShape-positions-array" count="1506">1.173682 0.2948255 -0.5639822 1.173696 0.5527158 0.3159284 1.173696 -0.3119877 0.5530626 1.173697 0.2795965 -0.1738062 1.173698 0.4971796 0.3969435 1.173699 0.6334189 -0.06450829 1.173699 -0.04889091 -0.6349666 1.1737 0.1242701 0.6236312 1.1737 0.3208186 0.07524883 1.1737 -0.3968306 0.4955701 1.1737 -0.2427507 -0.2213045 1.1737 0.1099557 -0.3095277 1.1737 0.1402325 -0.6222476 1.1737 -0.589903 0.2323018 1.1737 -0.5401286 0.1993568 1.1737 -0.5096912 -0.3772768 1.1737 -0.491596 0.1672338 1.1737 -0.4534171 0.1419638 1.1737 -0.448694 -0.3083321 1.1737 -0.4221721 0.1212831 1.1737 -0.4097535 0.1175456 1.1737 -0.401016 -0.2544418 1.1737 -0.3920056 0.1638583 1.1737 -0.3909911 0.127627 1.1737 -0.3854176 0.1360476 1.1737 -0.3789536 -0.2359254 1.1737 -0.3541469 0.1528499 1.1737 -0.3392737 0.1497743 1.1737 -0.3380209 -0.2108302 1.1737 -0.3168257 0.1750195 1.1737 -0.3055456 0.135202 1.1737 -0.2957778 0.1389082 1.1737 -0.2832975 -0.1772801 1.1737 -0.2758125 -0.177078 1.1737 0.01952965 0.3281439 1.1737 0.2111875 0.5994917 1.1737 0.32883 0 1.1737 0.63608 0 1.1737 -0.4686165 -0.4279191 1.1737 -0.4545547 0.4429184 1.1737 -0.3025709 0.1345868 1.1737 -0.2985464 0.135761 1.1737 -0.2793775 -0.1761741 1.1737 -0.2731032 -0.1795651 1.1737 -0.0043373 -0.3284118 1.1737 0.2939713 0.1478254 1.1737 -0.3435314 -0.533861 1.1737 -0.1491384 0.6175568 1.1737 0.6049106 -0.2004591 1.1737 -0.2025688 0.2571284 1.1737 0.1751959 -0.2786301 1.173701 0.3249771 -0.05140058 1.173701 0.09552388 0.3144277 1.173701 0.1926056 0.2662725 1.173701 0.05772774 -0.3229654 1.173701 -0.2461656 0.2162005 1.173701 0.482037 -0.4155853 1.173701 -0.1453673 -0.2937097 1.173701 -0.06923185 0.6312621 1.173701 0.3100169 -0.1115962 1.173701 -0.1889846 -0.6061626 1.173701 -0.05887266 0.3231594 1.173702 -0.08118971 -0.3179143 1.173702 -0.1391492 0.2974326 1.173702 0.251795 0.2133828 1.173703 0.152596 0.2909811 1.173703 -0.1926634 -0.2645337 1.173705 0.2880918 0.5672004 1.173705 0.009458005 0.6357469 1.173705 0.2459856 -0.2187993 1.173706 0.4254428 0.4741106 1.173712 0.3966957 -0.4976923 1.173715 -0.526492 0.3554282 1.173715 -0.3987421 -0.4936386 1.173717 0.6243344 0.1236774 1.173732 -0.2344298 0.5898074 1.173754 0.5885386 0.2416655 1.173808 0.5453538 -0.3276462 1.173871 -0.2651306 -0.5762916 1.175815 -0.2266331 0.2347287 1.175958 0.1688095 0.2803862 1.176057 -0.3993507 0.1168354 1.176089 0.2268864 0.2347964 1.17609 0.2917664 -0.1463734 1.17611 -0.1687116 0.277752 1.176159 0.1462582 -0.2918266 1.176164 0.2637594 0.1925883 1.176173 0.2129706 -0.2468969 1.176199 0.1899426 -0.2648699 1.1762 -0.5910049 0.2300331 1.1762 -0.5113425 -0.3753704 1.1762 -0.4913616 0.1640807 1.1762 -0.4791452 -0.3389779 1.1762 -0.4245792 -0.2773023 1.1762 -0.4235519 0.1191984 1.1762 -0.4097535 0.1150456 1.1762 -0.4028883 -0.2527853 1.1762 -0.3922794 -0.2424495 1.1762 -0.3889064 0.1262471 1.1762 -0.3833329 0.1346677 1.1762 -0.3802602 -0.2337941 1.1762 -0.3707131 0.1461811 1.1762 -0.3541469 0.1503499 1.1762 -0.3402652 0.1474794 1.1762 -0.3164896 -0.1946972 1.1762 -0.3065371 0.132907 1.1762 -0.3025709 0.1320868 1.1762 -0.2935135 0.1378486 1.1762 -0.2846042 -0.1751488 1.1762 -0.2793775 -0.1736741 1.1762 -0.2710118 -0.1781954 1.1762 0.3239129 -0.03957867 1.1762 0.32633 0 1.176223 0.02414864 -0.324533 1.176236 0.3256364 0.02369056 1.176293 -0.1593846 -0.283282 1.176299 -0.09684957 0.3116058 1.176319 -0.02752118 -0.3243541 1.176321 0.2933712 0.1432316 1.176334 -0.02897604 0.3239663 1.17634 0.3136837 -0.09250962 1.176341 0.08496916 -0.3146907 1.17635 -0.2146061 -0.2443962 1.176366 0.2534778 -0.2067591 1.176409 -0.09296636 -0.3121305 1.176427 0.05648987 0.3223819 1.176458 -0.2730868 0.1756629 1.176672 0.3154515 0.08556807 1.180435 0.02323682 -0.6370417 1.18132 0.6329085 -0.08350231 1.181477 -0.5114778 0.378119 1.181825 -0.5538617 0.3137567 1.181859 0.6161144 -0.1676874 1.181892 -0.4928386 -0.4024432 1.181928 0.1341831 0.6231065 1.181942 -0.1979241 -0.6056909 1.181961 0.5891312 -0.246165 1.182058 -0.2803481 0.5716943 1.182292 0.3286932 -0.5470852 1.182801 0.5593889 0.3084778 1.183893 0.5176091 -0.3753022 1.184141 0.3601175 0.5282943 1.184209 0.04147374 0.6373739 1.184307 -0.5945858 0.2324033 1.184337 0.1471479 -0.6213653 1.184343 0.4648927 -0.4394635 1.184346 -0.330933 0.5455865 1.184441 -0.4290378 -0.4723271 1.184512 0.4890012 0.4126281 1.184565 -0.07415308 -0.634748 1.18463 -0.2773115 -0.5755838 1.184753 -0.1492088 0.621227 1.184762 -0.3548583 -0.5308013 1.184801 0.3822643 -0.5128777 1.184817 0.6314759 0.1051216 1.184839 0.64006 8.67203e-4 1.184859 0.2365341 0.5939766 1.184905 0.09169319 -0.6328481 1.184922 0.1994578 -0.6078685 1.184927 0.4328736 0.4715076 1.184948 0.2529207 -0.5871346 1.185006 -0.1919476 0.6096418 1.18502 0.284317 -0.573141 1.185083 0.2016136 0.6072842 1.185111 0.08644736 0.6333065 1.18521 0.6029155 0.2179923 1.185251 0.5311163 0.358258 1.185308 -0.1067161 0.6302321 1.185315 0.5458088 -0.3351442 1.18533 0.5812875 0.2687801 1.185421 0.2898784 0.5715079 1.185486 0.1623408 0.6186871 1.185497 -0.4675281 -0.4350065 1.185526 0.639471 -0.03579327 1.185573 0.6240919 0.1437583 1.185588 -0.4981043 0.4006416 1.18568 -0.3903027 0.5068493 1.185942 -0.04054179 0.6398084 1.186083 0.4247076 -0.4800413 1.18633 0.6411882 0.04435864 1.186344 0.003884335 -0.641937 1.186388 -0.4517928 0.454823 1.186614 -0.2494033 0.5908965 1.186685 0.6299964 -0.1305764 1.186696 0.5757272 -0.2869139 1.186753 -0.5397097 0.3466813 1.186823 -0.3955213 -0.5051065 1.186857 0.006289843 0.6421869 1.186884 0.6074024 -0.2116626 1.187185 -0.3146903 -0.5597027 1.188504 -0.2860874 0.5799916 1.188656 -0.258866 -0.5927279 1.18866 -0.07162937 0.6464963 1.18866 0.554821 0.336012 1.18866 0.4748884 0.4418521 1.188663 0.0204316 0.6495711 1.188664 0.5043334 -0.4074794 1.188668 0.3717849 0.5314209 1.188673 -0.3563124 0.5390086 1.18868 0.3260337 -0.5619572 1.188683 0.1224971 0.6384303 1.188683 0.265684 -0.5935706 1.188689 -0.5171383 0.3919901 1.188689 -0.1946397 0.6195376 1.188692 -0.4233199 0.4929696 1.188694 0.6287912 -0.1705953 1.188695 -0.3680426 -0.5352788 1.188696 -0.449217 -0.4702394 1.188696 0.2547183 0.5975817 1.188698 0.1337986 -0.6359304 1.188698 0.6091126 0.2349333 1.188698 0.4089948 -0.5071886 1.188699 -0.5817249 0.2888156 1.188699 -0.2798458 0.6634985 1.188699 -0.14997 -0.6328722 1.188699 0.6471144 -0.07069776 1.1887 -0.3975267 -0.5999973 1.1887 0.6415987 0.3318901 1.1887 -3.88418e-4 -0.7208662 1.1887 -0.6233894 0.3600516 1.1887 -0.07518046 0.7161996 1.1887 0.3842469 0.6105675 1.1887 0.5613646 0.4534536 1.1887 0.6586717 -0.2955822 1.1887 0.6851176 0.2257627 1.1887 -0.6602966 0.2786928 1.1887 -0.6598921 0.2855098 1.1887 -0.6562313 0.2732056 1.1887 -0.6036275 0.2383879 1.1887 -0.5656851 -0.4405822 1.1887 -0.5635268 -0.4467876 1.1887 -0.5631746 -0.433956 1.1887 -0.559028 0.4534752 1.1887 -0.5213729 -0.3867078 1.1887 -0.4734916 0.5431787 1.1887 -0.2752486 -0.6649152 1.1887 -0.2185393 -0.6859032 1.1887 -0.1612571 0.7021138 1.1887 0.0978718 -0.713726 1.1887 0.2927188 -0.658309 1.1887 0.326198 0.6424151 1.1887 0.65108 0 1.1887 0.6964893 -0.1876984 1.1887 0.7174011 -0.08042123 1.1887 0.7201653 0.03604039 1.1887 0.72108 0 1.1887 -0.4745422 -0.541531 1.1887 -0.3804601 0.6113282 1.1887 -0.3359867 -0.636364 1.1887 -0.1181163 -0.7109554 1.1887 0.01772243 0.7204579 1.1887 0.2160169 -0.6882778 1.1887 0.2801663 0.6637915 1.1887 0.3277205 -0.6417879 1.1887 0.4181742 -0.5891159 1.1887 0.4744085 0.5431718 1.1887 0.5237717 -0.4965088 1.1887 0.6030244 -0.3961246 1.1887 0.1123262 0.7115604 1.1887 0.7081173 0.1403397 1.1887 0.2146612 0.6143362 1.1887 0.03314273 -0.6500132 1.188701 0.1994791 0.6931904 1.188702 0.5736635 -0.3122292 1.188702 0.6481635 0.0839406 1.188706 -0.06328762 -0.6469043 1.19495 -0.3065371 0.132907 1.213681 -0.4001928 0.1167705 1.213682 -0.3631004 0.150167 1.213686 -0.1817502 -0.507268 1.213694 0.3204281 -0.5789944 1.213699 -0.1590666 -0.2846011 1.213699 -0.181726 0.5073547 1.213699 0.08219886 -0.3166455 1.213699 0.6426424 0.3298837 1.213699 0.2333613 -0.2292327 1.2137 0.1838765 0.5073562 1.2137 0.2749793 0.1771711 1.2137 0.1069677 0.7136147 1.2137 0.3058227 0.1149877 1.2137 0.4581594 0.5571956 1.2137 -0.01526402 0.7202711 1.2137 0.7201644 -0.04631909 1.2137 -0.5471297 -0.4670392 1.2137 -0.1352858 0.7084003 1.2137 -0.08301463 -0.3148099 1.2137 -0.07568584 -0.7163623 1.2137 0.04873513 0.3221841 1.2137 0.550559 0.4677066 1.2137 -0.4236466 0.5836449 1.2137 -0.03724129 0.3248437 1.2137 0.6718915 -0.2640527 1.2137 -0.6602966 0.2786928 1.2137 -0.6598921 0.2855098 1.2137 -0.6562313 0.2732056 1.2137 -0.6247051 0.3567827 1.2137 -0.5900916 0.2294286 1.2137 -0.5773427 0.4303281 1.2137 -0.5656851 -0.4405822 1.2137 -0.5635268 -0.4467876 1.2137 -0.5631746 -0.433956 1.2137 -0.5402766 -0.4080745 1.2137 -0.5220878 -0.3875157 1.2137 -0.5156658 0.5017604 1.2137 -0.4687672 -0.4799105 1.2137 -0.4567918 0.1411994 1.2137 -0.4235519 0.1191984 1.2137 -0.4097535 0.1150456 1.2137 -0.4028883 -0.2527853 1.2137 -0.3889064 0.1262471 1.2137 -0.3833329 0.1346677 1.2137 -0.3802602 -0.2337941 1.2137 -0.3491461 -0.4460791 1.2137 -0.3402652 0.1474794 1.2137 -0.31887 -0.6450054 1.2137 -0.31887 -0.5417157 1.2137 -0.31887 0.5417157 1.2137 -0.31887 0.6450054 1.2137 -0.3065371 0.132907 1.2137 -0.2990235 0.1327374 1.2137 -0.2935135 0.1378486 1.2137 -0.2871543 -0.51 1.2137 -0.2871543 0.51 1.2137 -0.2846042 -0.1751488 1.2137 -0.2771904 -0.1739164 1.2137 -0.2710118 -0.1781954 1.2137 -0.2045896 -0.69 1.2137 -0.2045896 0.69 1.2137 -0.01252434 -0.3254612 1.2137 0.03049509 -0.7197904 1.2137 0.1615804 -0.2836344 1.2137 0.2067496 -0.69 1.2137 0.2067496 0.69 1.2137 0.2873055 -0.1560514 1.2137 0.2893143 -0.51 1.2137 0.2893143 0.51 1.2137 0.3159856 0.1561576 1.2137 0.32103 -0.6450054 1.2137 0.32103 -0.5417157 1.2137 0.32103 0.5417157 1.2137 0.32103 0.6450054 1.2137 0.322942 0.04895879 1.2137 0.32633 0 1.2137 0.3480051 -0.054644 1.2137 0.3692308 -0.619171 1.2137 0.46336 -0.5538586 1.2137 0.4833809 0.0939337 1.2137 0.5512761 -0.4653803 1.2137 0.617073 -0.3738611 1.2137 0.6664454 -0.09721955 1.2137 0.6717663 0.08733 1.2137 0.699492 0.1801551 1.2137 0.7063678 -0.147856 1.2137 0.7186529 0.06471699 1.2137 0.72108 0 1.2137 -0.3884124 -0.6068647 1.2137 -0.1425441 0.2929402 1.2137 0.3724303 0.6174365 1.2137 -0.2731383 0.1753534 1.2137 -0.2356968 -0.2240788 1.2137 -0.2206471 0.2387486 1.2137 -0.4883863 -0.5288398 1.2137 0.1507898 -0.7049369 1.2137 0.2272074 0.2345676 1.213701 0.319452 -0.07241458 1.213701 0.1401877 0.295972 1.213702 0.04825523 -0.5103346 1.213711 0.01509418 0.6902477 1.213713 -0.04507946 -0.5120987 1.213721 0.1251056 -0.5117096 1.213727 -0.1048626 0.6886738 1.213728 0.1026793 -0.688586 1.213729 0.1260219 0.6882748 1.213738 -0.006703584 0.514263 1.213762 -0.06872985 -0.6885546 1.214744 -0.1017606 0.517111 1.214762 -0.1157076 -0.5170486 1.215199 -0.2302254 -0.6818809 1.215252 -0.1006736 -0.6815688 1.215455 0.002412224 -0.5189014 1.215573 -0.2478979 -0.5196495 1.215573 0.2500466 -0.5196495 1.215615 -0.193966 0.5196344 1.215663 0.1245093 0.5198408 1.215729 0.06900362 -0.6801162 1.215747 -0.0283024 0.5197029 1.215801 -0.1863013 -0.5201789 1.215861 0.03460789 0.679707 1.215887 -0.02188013 -0.6796633 1.215899 0.1045834 0.6799952 1.215951 -0.07150027 0.6796572 1.216083 0.1904781 -0.5206631 1.216159 0.2208986 0.5210122 1.21636 0.3100586 -0.6199091 1.216466 0.03458645 0.5210704 1.216497 0.1951211 0.6792019 1.216785 -0.1545806 0.6785013 1.217181 -0.3061017 -0.6512013 1.21725 -0.3046271 -0.545445 1.21755 -0.3055102 0.58083 1.21755 -0.282958 0.5228537 1.21755 0.3076701 0.5808299 1.217589 -0.3043744 0.5485748 1.21898 0.1580375 -0.6749917 1.218984 0.3057729 0.548725 1.219259 0.2211516 -0.6748691 1.220304 -0.2118193 0.6737265 1.220578 0.2823896 -0.5266936 1.220578 -0.2802283 -0.5266927 1.22191 -0.3020327 -0.5960502 1.222225 0.1092456 -0.5271969 1.222225 -0.1070989 -0.527197 1.2223 -0.1589242 -0.673019 1.222493 -0.05402143 -0.5274641 1.222493 0.05616866 -0.5274641 1.222512 0.2825186 0.5270096 1.22254 0.3040833 -0.6531496 1.222545 0.304081 0.6531456 1.222547 -0.301919 0.6531512 1.222761 -0.01956652 0.6726062 1.222761 0.02172652 -0.6726062 1.222851 0.3035707 -0.5483399 1.222908 -0.1779438 -0.5274806 1.223024 -0.1225444 0.5275626 1.223146 0.09109427 -0.6722991 1.223152 -0.03240883 0.5276747 1.223272 0.0602578 0.5277148 1.223511 0.118608 0.527803 1.223563 -0.07841402 -0.6721243 1.223834 0.003121952 -0.5281095 1.224178 0.1362821 0.6720173 1.224669 0.177706 -0.5283049 1.225067 -0.2226086 0.528374 1.226148 -0.09173512 0.6712344 1.226166 0.2223848 0.6712027 1.227555 -0.2600752 -0.6709674 1.227555 0.2622352 0.6709674 1.228595 0.04406369 0.6700512 1.231278 0.2070859 -0.6700721 1.231316 0.1848411 -0.5299974 1.231713 -0.006060908 -0.6700027 1.233013 -0.1570307 -0.6699776 1.233054 -0.1294196 0.5300031 1.233073 0.05988812 0.529998 1.233365 -0.1585344 -0.53 1.233427 0.2093242 0.5299994 1.2337 -0.29887 -0.6545457 1.2337 -0.29887 -0.55 1.2337 -0.29887 0.55 1.2337 -0.29887 0.6545457 1.2337 -0.27887 -0.53 1.2337 -0.27887 0.53 1.2337 -0.2625485 -0.67 1.2337 -0.2625485 0.67 1.2337 0.2647085 -0.67 1.2337 0.2647085 0.67 1.2337 0.28103 -0.53 1.2337 0.28103 0.53 1.2337 0.30103 -0.6545457 1.2337 0.30103 -0.55 1.2337 0.30103 0.55 1.2337 0.30103 0.6545457 1.234319 0.07858351 -0.6699899 1.234928 -0.07962588 0.66999 1.239182 -0.007330009 -0.5300137 1.291548 -0.29887 0.6545457 1.3837 -0.29887 -0.6545456 1.3837 -0.29887 -0.55 1.3837 -0.29887 0.55 1.3837 -0.29887 0.6545456 1.3837 -0.27887 -0.53 1.3837 -0.27887 0.53 1.3837 -0.2625914 0.67 1.3837 -0.2625904 -0.67 1.3837 0.02761823 0.53 1.3837 0.04482219 -0.53 1.3837 0.2647503 0.67 1.3837 0.2647514 -0.67 1.3837 0.28103 -0.53 1.3837 0.28103 0.53 1.3837 0.30103 -0.654545 1.3837 0.30103 -0.55 1.3837 0.30103 0.55 1.3837 0.30103 0.6545456 1.4037 -0.27887 -0.6415823 1.4037 -0.27887 -0.6017923 1.4037 -0.27887 -0.5582843 1.4037 -0.27887 0.5582843 1.4037 -0.27887 0.6415823 1.4037 -0.2705857 -0.55 1.4037 -0.2705857 0.55 1.4037 -0.2587275 0.65 1.4037 -0.2587274 -0.65 1.4037 0.2608875 -0.65 1.4037 0.2608875 0.65 1.4037 0.2727457 -0.55 1.4037 0.2727457 0.55 1.4037 0.28103 -0.6415823 1.4037 0.28103 -0.5582843 1.4037 0.28103 0.5582843 1.4037 0.28103 0.6026078 1.4037 0.28103 0.6415823</float_array>
          <technique_common>
            <accessor source="#upper_arm_roll_MShape-positions-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="upper_arm_roll_MShape-normals" name="upper_arm_roll_MShape-normals">
          <float_array id="upper_arm_roll_MShape-normals-array" count="1506">-0.6968792 0.325151 -0.6392467 -0.6680972 0.6474333 0.36671 -0.6697977 -0.3727594 0.6422006 -0.8492786 -0.4499362 0.276194 -0.9328278 0.282582 0.2235616 -0.6702579 0.7386196 -0.07207891 -0.940253 -0.02126809 -0.3398118 -0.6126803 0.1342769 0.7788405 -0.9493509 -0.3074642 -0.06479701 -0.9171302 -0.2506153 0.309942 -0.9897528 0.1045429 0.09726255 -0.8164634 -0.1997611 0.5417405 -0.8503118 0.1189946 -0.5126503 -0.8121377 -0.5820622 0.04044372 -0.8891276 -0.2525989 -0.3816359 -0.7342784 -0.6761018 -0.06100576 -0.9523197 -0.1683942 -0.2544221 -0.8891273 -0.2525981 -0.3816372 -0.9238762 -0.2866204 0.2535771 -0.9520108 -0.1295138 -0.2773115 -0.8546988 -0.02560234 -0.5184926 -0.8781139 -0.3417135 0.3348849 -1 0 0 -0.9248034 0.2836772 -0.253507 -0.789378 0.4097144 -0.4571832 -0.9053351 -0.2464592 0.34587 -0.8478673 0.0488279 -0.5279554 -0.9238393 -0.1274645 -0.3609345 -0.889126 -0.239206 0.3901737 -1 0 0 -0.9521646 -0.09184837 -0.2914555 -0.9070332 0.3494023 -0.2349653 -0.9233374 -0.1534742 0.3519856 -0.853925 0.2283206 0.4676341 -0.9597547 -0.01421639 -0.2804799 -0.6907848 0.232708 0.6845899 -0.9222529 -0.3864234 -0.01124354 -0.8037141 0.5945731 0.0229465 -0.7621219 -0.4765069 -0.438305 -0.7870467 -0.4540608 0.4175958 -0.8280089 -0.02337238 -0.5602278 -0.8494207 0.2832467 -0.4452593 -0.7122389 0.1211438 0.6914042 -0.9230086 0.2869482 0.2563514 -0.8913785 0.00995556 0.4531507 -0.7980745 -0.5404752 -0.2663903 -0.7253417 -0.359684 -0.586947 -0.7715818 -0.150365 0.6181034 -0.8982016 0.4207659 -0.1272395 -0.7804639 0.382594 -0.494467 -0.7312132 -0.3805724 0.5661201 -0.8245353 -0.5598881 0.08165222 -0.8820555 -0.1289112 -0.4531665 -0.8671194 -0.2996971 -0.397851 -0.8468954 -0.08881057 0.5242907 -0.9681877 0.1925478 -0.1598054 -0.7465346 0.5067078 -0.4311998 -0.8519304 0.2250648 0.4728218 -0.7671875 -0.07898092 0.6365418 -0.8353074 -0.5148514 0.1928464 -0.7680893 -0.1939611 -0.6102605 -0.9300436 0.0687617 -0.3609581 -0.8219938 0.1299289 0.554477 -0.785778 0.2624922 -0.5600453 -0.8398119 -0.4105372 -0.3552115 -0.8983209 -0.2090872 -0.3863964 -0.839189 0.3205667 0.4393163 -0.9088631 0.1930127 0.3697484 -0.7534606 0.01664413 0.6572824 -0.9552218 -0.2126245 0.205772 -0.5746651 0.5439084 0.6114931 -0.6654226 0.4648039 -0.5840979 -0.6232343 -0.6609316 0.4180292 -0.7204546 -0.4368605 -0.5386076 -0.7536809 0.6447815 0.1273656 -0.7238084 -0.2623889 0.6381642 -0.7182369 0.6517901 0.2435273 -0.7101147 0.6075426 -0.3558498 -0.7425399 -0.2689577 -0.6134299 -0.3229773 0.6712317 -0.6671834 -0.4633555 -0.4490092 -0.7639976 -0.3797929 0.4024038 -0.8329637 -0.4477543 -0.6090335 -0.6546712 -0.469424 -0.7873926 0.3995675 -0.3874659 0.4908525 -0.7803422 -0.3456252 -0.4000216 0.8488381 -0.377957 -0.7487855 -0.5444893 -0.2788962 -0.6290467 0.7256151 -0.3246942 -0.5501537 0.7693532 -0.3430209 -0.6322989 -0.6946472 -0.5284659 -0.848371 0.0314688 -0.5054389 -0.4762487 -0.7195268 -0.3826737 -0.6919506 0.6121808 -0.4576437 -0.6659226 0.5891598 -0.3811362 -0.3919992 -0.8373004 -0.3848072 -0.0594823 -0.9210783 -0.3825768 -0.6688131 0.6374354 -0.4793303 -0.5642249 0.6722296 -0.3043936 0.7400615 -0.5997112 -0.4575078 0.6874503 -0.5640024 -0.3197187 -0.5309697 0.7847618 -0.1471698 0.4952679 -0.8561838 -0.3022697 -0.009914658 -0.9531708 -0.4556344 -0.2528535 -0.8535 -0.4576433 -0.4647301 0.7580162 -0.4356141 -0.2858146 -0.8535517 -0.2807004 0.1065857 -0.9538589 -0.2768263 0.7181913 -0.6384107 -0.3007354 -0.3323486 0.8939254 -0.4654852 0.03855333 0.8842155 -0.375441 0.5730323 0.7284766 -0.3769469 -0.919405 0.1122739 -0.3668665 -0.9295278 0.03724428 -0.3960566 -0.07634095 0.9150471 -0.3106325 -0.9466269 -0.08605107 -0.4706098 0.4392866 0.7652148 -0.3015724 0.2868908 -0.9092568 -0.3557003 0.1058019 0.928592 -0.2899198 -0.8600491 -0.419836 -0.4682406 0.06861783 -0.8809327 -0.3669926 -0.8899935 0.2706066 -0.450675 -0.2270173 0.8633395 -0.3897514 0.603482 0.6956316 -0.3797887 -0.727787 0.57104 -0.3739788 0.2947516 0.8793527 -0.2420244 -0.1667604 -0.9558322 -0.2984966 0.8007829 -0.519275 -0.3300374 -0.9071967 -0.2609011 -0.5009898 0.02987429 -0.8649375 -0.6353428 0.7619321 -0.1256936 -0.2018395 -0.8076507 0.5540408 -0.655111 -0.668999 0.3510987 -0.5492485 0.8052777 -0.22328 -0.485801 -0.6677437 -0.5640174 -0.5752024 0.1623569 0.8017371 -0.5800107 -0.3155465 -0.7510114 -0.5311024 0.786015 -0.3164026 -0.5842239 -0.3629473 0.7259144 -0.5994113 0.420679 -0.6809809 -0.5826841 0.7106443 0.394289 -0.5821893 0.6544926 -0.4823849 -0.6422036 0.4339716 0.6318569 -0.6998028 0.04133526 0.7131392 -0.5784849 -0.7878926 -0.2111409 -0.5906315 0.1832481 -0.7858592 -0.5724865 0.5922815 -0.5669761 -0.685475 -0.3774285 0.6226329 -0.6707383 -0.5082079 -0.5402175 -0.6454752 0.5805724 0.4962836 -0.7091499 -0.07017399 -0.7015568 -0.6529092 -0.3267365 -0.6833394 -0.7513723 -0.1510838 0.6423498 -0.6706829 -0.4217141 -0.610198 -0.7459062 0.3937575 -0.5371954 -0.7204846 0.6859314 0.1019816 -0.7371989 0.6755947 -0.01047003 -0.718508 0.2631473 0.6438165 -0.712198 0.09263107 -0.6958401 -0.7475597 0.2027991 -0.6324768 -0.692645 0.4819537 0.5366223 -0.7153758 0.264069 -0.6469195 -0.6647971 -0.2269867 0.7117035 -0.7392792 0.2991669 -0.6032955 -0.7204704 0.2148995 0.6593485 -0.7362667 0.08981588 0.6707044 -0.7611259 0.6114765 0.2162957 -0.583947 0.6693778 0.4592811 -0.6621903 -0.1243675 0.7389429 -0.7494621 0.5571626 -0.3575982 -0.6687214 0.6704654 0.3213843 -0.7325987 0.3158891 0.6029204 -0.6742889 0.1875775 0.7142472 -0.7314232 -0.5014441 -0.4621407 -0.7542056 0.6556903 -0.03527042 -0.7420275 0.6489937 0.1679361 -0.5888668 -0.6504149 0.4797877 -0.7185787 -0.4111163 0.5609171 -0.7051816 -0.03513389 0.7081557 -0.7266464 0.4625586 -0.5079612 -0.8143752 0.5791614 0.03694807 -0.8539352 0.005900076 -0.520346 -0.7354205 -0.4754556 0.4828029 -0.7661023 -0.2462544 0.5936715 -0.8527256 0.5078673 -0.1221877 -0.766436 0.5779593 -0.2802478 -0.728848 -0.565129 0.3865356 -0.8099694 -0.3647728 -0.459228 -0.9389101 -0.01163093 0.3439662 -0.8445389 0.504907 -0.1783897 -0.7669741 -0.3175535 -0.5575936 -0.9659076 -0.1154907 0.2316989 -0.9652787 -0.1103008 -0.2367926 -0.9827953 -0.02092184 0.1835098 -0.9468608 0.2721862 0.1713743 -0.9656444 0.1884213 0.1789649 -0.9808118 0.008291572 0.1947812 -0.9490823 0.2409949 -0.2028898 -0.9558392 0.1684237 0.2408419 -0.9742138 -0.1239873 0.1885062 -0.9810556 0.09885299 -0.1666078 -0.9590228 0.05830375 0.277265 -0.9669397 0.09681074 -0.2359139 -0.9791325 -0.1597298 0.1256414 -0.9668518 -0.07970697 0.2425787 -0.9844953 -0.1158535 0.131708 -0.9851699 0.1648748 -0.0475028 -0.9847381 -0.09462547 -0.146071 -0.9867241 -0.1108343 -0.1187076 -0.9845169 0.07702795 0.1574585 -0.971841 0.0543444 -0.2292858 -0.9863161 0.1545828 0.05731097 -0.9867166 0.1053288 -0.1236785 -0.9747436 -0.1958219 0.1073714 -0.392851 -0.3554039 0.8481486 -0.9261742 -0.1145206 -0.3592857 -0.9604213 0.276739 -0.03172258 -0.7164508 -0.3890498 -0.5790843 -0.8330448 0.4891152 0.2584621 -0.5634658 -0.01201293 -0.826052 -0.6063017 -0.6892291 0.3966882 -0.567175 -0.08399434 0.819303 -0.7111253 0.3884168 0.5860317 -0.7127582 0.5595705 0.4229144 -0.5667413 0.7470661 -0.3474142 -0.5524879 0.7862525 0.2767023 -0.2570539 -0.9262684 -0.2755907 -0.8370607 -0.5290952 0.1392399 -0.8359306 -0.377661 -0.3982364 -0.528843 -0.5508177 -0.6456972 -0.2570465 -0.9663081 0.01325901 -0.6235877 -0.6302301 -0.4625455 -0.7122512 -0.5776604 0.3987564 -0.7206709 -0.5336208 0.4425861 -0.5505129 -0.759288 0.347012 -0.7202016 -0.4590653 0.5201623 -0.279957 -0.3725112 -0.8847935 -0.6046087 -0.2466702 -0.7573652 -0.8101899 -0.1421497 0.5686704 -0.7157194 0.09450884 -0.6919637 -0.374478 0.3693655 -0.8504912 -0.4516202 0.4029819 0.7960181 -0.980298 0.1974371 -0.005874377 -0.5735262 0.788911 -0.2206516 -0.8968369 0.4389437 -0.05488247 -0.5655776 0.8232486 0.04882365 -0.5641428 0.8256037 -0.01102683 -0.7079082 -0.460133 -0.5358579 -0.721064 -0.3605268 0.5916815 -0.6085745 -0.3669786 -0.7035368 -0.7995574 -0.1056119 -0.5912309 -0.7124251 0.02393533 0.7013399 -0.7116509 0.2011937 -0.6731077 -0.3974327 0.3554802 0.8459794 -0.4557377 0.4087062 -0.7907353 -0.7168483 0.4038479 -0.5683621 -0.7196905 0.4668281 0.5139233 -0.7242876 0.4963962 -0.4785378 -0.7189736 0.5740619 -0.3918288 -0.8990766 0.0658485 0.4328106 -0.717222 0.6854873 0.1252979 -0.985709 0.05193719 0.1602508 -0.9904564 0.007802268 -0.1376054 -0.4420061 0.2464584 0.86249 -0.9916016 0.1155099 -0.05817077 -0.9895713 0.1422527 0.02264385 -0.9785244 -0.01597046 -0.2055116 0 -0.3011256 -0.9535844 0.4638755 0.3762474 -0.8020334 0.92602 0.09723243 -0.3647367 0.9943296 9.34372e-4 0.1063372 0.9734737 0.228565 -0.01034285 0.5962592 0.3970491 0.6977299 0.9958296 0.002551396 -0.0911964 0.441876 -0.2229725 0.868924 0.4766791 0.7792234 0.4069248 0.4277174 -0.6205832 0.657217 0.9987646 -4.18423e-4 -0.04969121 0.7060466 -0.5981446 -0.3791057 0.6063675 0.1165352 0.7865991 0.5470145 -0.7834926 -0.2948128 0.7126094 0.4381751 0.5478962 0.71986 -0.02081862 0.6938071 0.5606114 0.8262334 -0.05525596 0.8254645 -0.4242679 -0.3722968 0.5593081 -0.1430986 0.8165154 0.856874 0.1212657 0.5010604 0.5823696 -0.08587058 -0.8083761 0.696784 -0.1106749 -0.7086912 0.5582265 0.6237133 0.5471425 0.7185936 -0.4095 0.5620793 0.7066151 0.07465032 -0.7036493 0.8625242 0.4730778 -0.179581 0.4696508 -0.8461949 -0.2517582 0.4562919 -0.8603507 0.2271437 0.7124802 -0.4828637 -0.5091312 0.8356453 -0.4779286 0.2707048 0.6000264 -0.4415264 -0.6671001 0.5558558 -0.6720937 0.4891977 0.8473834 -0.5309315 0.007281959 0.3200394 -0.853296 -0.4116561 0.5603198 -0.7338016 0.3841574 0.5546997 -0.623169 0.5513335 0.8320501 -0.4154469 0.3675548 0.5733443 -0.5868466 0.5717404 1 0 0 0.4472666 -0.4936523 -0.7458283 0.8965071 -0.1884537 -0.400949 0.4570081 -0.05164696 -0.8879619 0.5029526 -0.6097407 0.6125806 0.710834 0.5070569 -0.4874507 0.4443509 0.6681393 -0.5967765 0.8327526 -0.3235367 0.4492741 1 0 0 0.3686974 -0.2829565 -0.8854365 0.6797862 -0.4048867 -0.6115205 0.987783 -0.15295 0.02984851 0.9889942 -0.1431994 -0.03720602 0.6493905 -0.4793405 0.5903597 0.7137762 -0.1469547 -0.684783 0.3272852 0.3973594 -0.8573155 0.7117825 0.5547671 -0.4308124 0.9869115 -0.06619316 0.1470518 0.9977892 -0.03590241 -0.05592662 0.452213 -0.3916198 0.8013347 0.2608608 0.1589225 0.9522055 0.8351017 0.3790773 0.3986296 0.6752145 -0.1624853 -0.7195026 0.6165017 -0.146259 0.7736497 0.8557038 0.01047887 0.5173597 0.7225503 0.03758793 -0.6902958 0.7005541 -0.3619052 0.6150192 0.8256454 0.05323385 -0.5616723 0.7571346 0.1103087 0.6438782 0.7770541 -0.5558558 0.2953154 0.9922929 0.02459627 0.1214488 0.9908828 0.06011747 -0.1205706 1 0 0 0.7807871 0.4255854 -0.457437 0.9773448 0.1793874 0.1123269 0.9893112 0.14173 -0.03429303 0.7675052 0.437425 0.4686097 0.7982428 -0.5944465 -0.09716859 0.5508139 -0.8346274 -0.00103624 1 0 0 0.8073481 0.30424 -0.5055957 0.3455501 0.6082286 -0.7146 1 0 0 0.9379743 0.2671321 -0.2210079 0.3438047 0.8099111 -0.4752288 1 0 0 1 0 0 0.8096586 0.5666652 0.1527858 0.569024 0.805048 -0.1676584 0.5608323 0.8233176 0.0872654 0.8013529 0.5981301 0.008593252 0.605884 -0.440531 -0.6624478 0.5359878 0.3650823 -0.7612044 0.5629922 0.4217171 0.7107702 0.7034485 0.5915847 -0.3939387 0.7098018 0.5132483 0.4824494 0.7930944 0.3960941 -0.462721 0.5410726 -0.5814469 -0.6075853 0.5594627 0.1636006 -0.8125492 0.426368 -0.6182455 -0.6602899 0.4384494 -0.8845185 0.1593395 0.552543 -0.3372905 -0.7621885 0.9623903 -0.007058593 0.2715788 0.9942532 2.3094e-4 0.1070541 0.9690876 9.09379e-4 0.246715 0.9436839 0.008987951 0.3307263 0.9920657 0.009972183 0.1253242 0.98908 -0.003621227 -0.1473351 0.9862359 -0.001185739 0.16534 0.9783237 -1.71914e-4 -0.2070816 0.9917676 0 -0.1280512 0.8959159 0.002423035 -0.4442173 0.926693 -0.007710361 0.37574 0.5752625 -0.1470164 -0.8046485 0.8973107 0.001058213 -0.4413984 0.8620808 -0.002941136 0.5067624 0.9428306 0.0126875 0.3330308 0.9223121 -0.01750818 0.386049 0.8636016 0.001494795 -0.5041726 0.8236003 -0.009252594 -0.5670951 0.8711187 -8.8179e-4 -0.4910716 0.8512349 0.004269833 -0.5247675 0.8690115 -0.002405449 0.4947861 0.8555409 -0.004480254 0.5177159 0.7812077 0.006630511 -0.624236 0.8126321 0.001728491 0.5827746 0.8603816 0.002830446 0.5096425 0.8661841 0.0175427 0.499417 0.8254685 2.78803e-4 -0.5644481 0.8888046 0.4581133 0.01259904 0.8144621 4.87081e-4 -0.5802167 0.8429697 -0.02478087 0.5373901 0.8809486 0.01197157 0.4730605 0.4754924 -0.6487859 -0.5941245 0.8132014 -0.514596 0.2718353 0.7814655 -0.6237941 -0.0138741 0.8238741 -0.170318 -0.5405769 0.7687153 0.6395236 -0.009302136 0.7537017 -0.5733293 -0.3212899 0.6974657 -0.005006328 -0.7166008 0.6914382 0.6250215 -0.3623003 0.7535056 -0.09381738 -0.6507131 0.7609684 0.05555628 0.6464058 0.6566291 0.2374829 0.7158492 0.6847134 -0.2482441 0.6852317 0.6188177 -0.7854311 0.01275349 0.5951628 0 0.8036052 0.5823815 -0.00695747 0.8128858 0.5755011 0.003940478 -0.8177915 0.6040483 0.008199343 0.7969056 0.6158592 -0.01072615 0.7877834 0.6237358 0.2420415 -0.7432156 0.3797592 0.7926667 -0.4769303 0.325679 0.7554553 0.5685249 0.3320822 -0.7486782 0.5737615 0.5002617 -0.00124775 0.8658733 0.4929572 0.0125887 -0.8699625 0.721679 0.6646817 0.1933327 0.4470926 -0.001270642 0.8944868 0.5702853 -8.99317e-4 -0.8214463 0.4736242 -0.003031791 -0.8807219 0.5449079 0.003021359 -0.8384904 0.5335388 -0.001075718 -0.8457749 0.4803678 -0.01551064 -0.8769301 0.5283027 -0.004782642 -0.8490426 0.4598355 0 0.8880041 0.5000055 0.002021933 0.8660199 0.580112 0.01332663 0.8144277 0.4469819 0.005658446 -0.8945252 0.4691611 -6.42653e-4 0.8831124 0.4164461 -0.005150559 0.9091458 0.1930491 -0.1837744 -0.9638252 0.3331474 0.1703133 0.9273652 0.3197426 -7.78556e-4 0.9475041 0.2181998 -0.00111053 -0.9759035 0.1380686 7.39604e-4 0.9904224 0.2211704 0.003741364 -0.9752281 0.157459 7.21097e-4 -0.9875252 0.1279829 -2.06465e-4 -0.9917763 0.1513635 0 -0.9884781 0.1367559 -0.001777396 0.9906032 0.2738679 -0.001965271 -0.9617654 0.1588359 -0.8878664 -0.4318153 0.1691227 -0.908339 0.3825152 0.1984975 -0.9560964 -0.2155887 0.07709142 -0.8283817 0.554834 0.1564473 -0.3016323 0.940501 0.2063806 -0.4331517 -0.8773749 0.0394116 -0.188108 -0.9813573 0.1671464 -0.2340688 0.9577442 0.266416 0.1467097 -0.9526273 0.09629267 0.1263002 0.9873075 0.105932 0.4549371 0.8842007 0.1756307 0.4483237 -0.8764472 0.05318825 0.834093 -0.5490536 0.1813064 0.9078045 0.3781784 0.1714284 0.9420423 -0.2883898 0.07834849 0.8331221 0.5475118 0.1646462 -0.004442705 -0.9863427 0.1360234 0.002453438 0.9907026 0.1063161 0 0.9943323 0 -0.8341767 0.5514971 0.2977014 -0.7972105 -0.5251946 0.5326945 -0.8152009 0.2273418 0.4091063 -0.8430278 -0.3491938 0.4432197 -0.7474244 0.4948869 0.3068275 -0.3491829 0.8853971 0.3263499 -0.2785561 -0.9032731 0.3450127 -0.1632779 0.9242871 0.2899538 -0.180095 -0.939943 0.3274426 0 -0.9448711 0.3826614 0 0.9238886 0.3324365 0.2066116 0.9202161 0.2813928 0.1722959 -0.943998 0.4090122 0.3291505 0.8510988 0.2720612 0.3682503 -0.88903 0.4432165 0.7474288 -0.4948831 0.3508526 0.8812876 0.3165985 0.5681236 0.7758784 -0.2743141 0.2976346 0.797568 0.5246894 0.8508138 -0.437516 -0.2910251 0.9523191 -0.3051034 0 0.8446235 -0.4619393 0.2705979 0.8794657 -0.4397314 -0.1821433 0.8981491 -0.3660975 0.2435175 0.9328137 -0.0910716 0.348661 0.9339489 -0.136773 -0.3302006 0.9263747 -0.07410394 0.3692406 0.946555 -0.04222796 -0.3197662 0.9539582 0.05901681 -0.2940761 0.9465549 0.04222666 0.3197665 0.9328137 0.09107184 0.3486609 0.9124874 0.1565567 -0.3779638 0.8508152 0.4375147 -0.2910228 0.9015058 0.3734155 0.2187418 0.8794665 0.4397305 -0.1821425 0.9523197 0.3051018 0 0.8508152 0.4375135 0.2910247</float_array>
          <technique_common>
            <accessor source="#upper_arm_roll_MShape-normals-array" count="502" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="upper_arm_roll_MShape-map1" name="upper_arm_roll_MShape-map1">
          <float_array id="upper_arm_roll_MShape-map1-array" count="1928">0.4282875 0.1367403 0.4587314 0.1381863 0.4473345 0.1722014 0.4305832 0.09553865 0.473999 0.1754187 0.4375727 0.160327 0.400245 0.09960879 0.4820679 0.2053873 0.4451603 0.1781447 0.4298901 0.1464 0.4051962 0.1098613 0.4000767 0.06406312 0.4610346 0.2251736 0.4525896 0.204188 0.4199713 0.1289358 0.3855051 0.08920485 0.3640736 0.06818568 0.4862947 0.2419783 0.4401166 0.1698395 0.3684456 0.04041895 0.4866156 0.2546222 0.4551801 0.2177428 0.3599804 0.06928255 0.3230036 0.0449747 0.485325 0.2828361 0.4585874 0.23906 0.3353386 0.05412206 0.3480805 0.02924596 0.4620577 0.2546222 0.4567953 0.2546222 0.4581916 0.2543179 0.316624 0.04623947 0.3319314 0.02174655 0.4606665 0.2794248 0.3089505 0.03909677 0.457985 0.2671794 0.4542383 0.3144715 0.304373 0.04157082 0.3036242 0.01143264 0.4556827 0.283917 0.434898 0.3641605 0.454661 0.3004318 0.2905951 0.03757038 0.2730486 0.004987957 0.4779886 0.3204718 0.4467345 0.328879 0.2766168 0.03064391 0.4497909 0.3134513 0.4647211 0.3583203 0.435622 0.3552792 0.2807166 0.03601992 0.2408096 0.02673546 0.4451986 0.3935932 0.4403245 0.3409834 0.4251258 0.3721997 0.2639696 0.03244149 0.2398591 0.001866468 0.4105752 0.3975768 0.4249662 0.3695692 0.2481917 0.03101455 0.2085121 0.02781417 0.4173946 0.4288106 0.4152326 0.3862881 0.2358483 0.02932603 0.2072663 0.003360386 0.3771278 0.4325574 0.3967383 0.4087977 0.2194185 0.03016046 0.1770684 0.008302067 0.3826403 0.4230334 0.3803482 0.4612997 0.1962028 0.03352007 0.1653568 0.037272 0.3677501 0.4345533 0.3480229 0.4517716 0.1812952 0.03667929 0.1332746 0.05114577 0.3486147 0.4797784 0.1663013 0.0407437 0.1461443 0.04732004 0.1354643 0.02184935 0.3489559 0.4465542 0.3363351 0.4855745 0.1352881 0.05405669 0.1001661 0.04015207 0.3333876 0.4556952 0.3094261 0.4960883 0.1513974 0.04770213 0.1175415 0.063216 0.1086378 0.06552371 0.3268506 0.4628625 0.06752822 0.0640607 0.3223729 0.4606046 0.2805817 0.4777234 0.0967131 0.07680605 0.08512976 0.08167541 0.3036167 0.4678786 0.2679777 0.5050162 0.03751978 0.09553107 0.2828389 0.4729232 0.285265 0.4726136 0.245269 0.482664 0.07514073 0.09505823 0.05221579 0.1171017 0.3370742 0.4524821 0.26581 0.4766421 0.2335054 0.5075212 0.01494008 0.1283066 0.3199399 0.3313828 0.2350043 0.4798307 0.1922033 0.5040442 0.05889341 0.1140665 0.02955714 0.153298 0.295105 0.352373 0.372813 0.4292258 0.2114386 0.4815734 0.04429715 0.1329972 0.002133988 0.1544578 0.272217 0.3632127 0.402753 0.4004205 0.2076267 0.4773086 0.1810281 0.4766506 0.05420164 0.121968 0.03933224 0.144548 0.003418287 0.1587745 0.253894 0.367927 0.2417937 0.4781133 0.1569722 0.4952552 0.04893428 0.1299286 0.02504515 0.173089 0.02187307 0.1709894 0.001992062 0.1568494 0.23212 0.3698378 0.1673408 0.4672801 0.1428246 0.4625669 0.02668802 0.1731246 0.2164894 0.4773853 0.1642046 0.4671146 0.1370771 0.4878921 0.1472802 0.1787733 0.2051581 0.366155 0.1363534 0.4565522 0.1157686 0.4778755 0.07417178 0.0992347 0.0441502 0.1846825 0.182643 0.3576633 0.1232399 0.4509807 0.1045225 0.4424122 0.09442291 0.08076306 0.02630145 0.1739205 0.06117671 0.1959521 0.1660502 0.3474276 0.1131217 0.4419148 0.09417871 0.4651171 0.1625751 0.1644148 0.06125893 0.1970583 0.09611565 0.1971363 0.1484783 0.3322617 0.1777253 0.354005 0.1091479 0.4408414 0.06715965 0.4446056 0.1848244 0.150275 0.07457089 0.2048175 0.1224908 0.1932207 0.1583521 0.3403629 0.2010266 0.3641258 0.09488226 0.4318269 0.07604437 0.4195946 0.2129875 0.1412493 0.08504836 0.2128042 0.1298749 0.2058895 0.1093975 0.2009984 0.08553246 0.2120729 0.1406268 0.4568006 0.0937523 0.4278037 0.03594147 0.4113671 0.1241881 0.06059318 0.1146154 0.2020774 0.09842692 0.2068931 0.0964716 0.2098473 0.08312377 0.4203271 0.03606501 0.4068654 0.08988922 0.2133841 0.1264482 0.2071898 0.1142676 0.2028825 0.06962037 0.407234 0.05073016 0.3902895 0.03518428 0.4091901 0.1813199 0.03796692 0.08988922 0.2142612 0.1289036 0.2069937 0.1261003 0.2079949 0.1093975 0.2018755 0.09915828 0.2073772 0.0935388 0.2136333 0.06923854 0.4047475 0.2404931 0.1395006 0.1306693 0.2062612 0.1274917 0.2074056 0.09720296 0.2103314 0.07622786 0.3627933 0.06074077 0.3958099 0.2093532 0.03315873 0.1274917 0.2082826 0.05482845 0.3869809 0.09295458 0.3438871 0.05424912 0.3863121 0.2369598 0.03158535 0.06554481 0.3735446 0.08468798 0.3519072 0.1006947 0.3373911 0.2772388 0.03583585 0.09229772 0.343306 0.1150549 0.328587 0.0960196 0.3396799 0.3077318 0.04430464 0.1342533 0.3168167 0.1002362 0.3366434 0.3347118 0.05563326 0.1226087 0.3229271 0.1378298 0.3176184 0.2671539 0.1443126 0.1337949 0.316069 0.1368793 0.3167458 0.1385635 0.3171379 0.2871763 0.1525383 0.1356286 0.3155516 0.1356286 0.3164288 0.3012128 0.1612067 0.2928644 0.1562553 0.4080654 0.1153639 0.3828982 0.08829164 0.321978 0.1797619 0.3367746 0.2027612 0.3461933 0.2282229 0.427549 0.1437861 0.4526747 0.2112329 0.3490039 0.2546222 0.4558617 0.2772534 0.8410171 0.672631 0.8497878 0.6962685 0.8410171 0.7071032 0.849788 0.6540659 0.8497878 0.7335196 0.8410171 0.6311825 0.8410171 0.7484053 0.8497878 0.6344337 0.8497878 0.7787424 0.8568044 0.6141002 0.8410171 0.7836363 0.8410171 0.6042735 0.8503136 0.7877362 0.9094283 0.6140852 0.8546486 0.7982082 0.8568044 0.6013577 0.8559545 0.6343158 0.8410171 0.8035315 0.8528049 0.7627218 0.9094283 0.6013577 0.8528889 0.6002865 0.8570215 0.6793978 0.8517382 0.6293811 0.8568044 0.7990759 0.8503323 0.742286 0.8565632 0.7620575 0.9164448 0.6083742 0.8410171 0.591994 0.8561071 0.7090933 0.8516402 0.6515232 0.9094283 0.7990906 0.853248 0.7344767 0.9164448 0.6154407 0.8497878 0.5943411 0.8529665 0.6993448 0.8531018 0.6750087 0.9094283 0.8118185 0.8505551 0.7146431 0.9164448 0.7977355 0.8497878 0.577431 0.8504997 0.6827587 0.9164448 0.804802 0.8568044 0.8118185 0.8410171 0.5602604 0.8510091 0.8143556 0.8497878 0.544408 0.8410171 0.82484 0.8410171 0.523214 0.8497878 0.818835 0.8497878 0.5135647 0.8497878 0.8432324 0.8410171 0.49541 0.841017 0.8464299 0.8497878 0.4904814 0.8410171 0.873449 0.849788 0.8783058 0.8410171 0.9046671 0.8497878 0.8989146 0.8497878 0.9046671 0.9313509 0.7042896 0.9225804 0.7158621 0.9225803 0.6832693 0.9313508 0.7471717 0.9313509 0.6621828 0.9225804 0.7490516 0.9225803 0.6530713 0.9225807 0.7796271 0.931351 0.6378691 0.931351 0.7821779 0.9225802 0.6114672 0.9362117 0.8016437 0.9383675 0.6175357 0.9225804 0.8079343 0.9344549 0.6037233 0.9383675 0.8025114 0.931351 0.5977765 0.9383675 0.6047931 0.9909915 0.802526 0.9357246 0.7876631 0.9225804 0.5761691 0.9586623 0.6047931 0.9365767 0.7251033 0.9909915 0.8152539 0.9323322 0.7780982 0.9350269 0.7574559 0.9313509 0.5610182 0.9909915 0.6175206 0.998008 0.8082374 0.9383675 0.8152539 0.9225803 0.5435312 0.9387983 0.6817097 0.9909915 0.6047931 0.9321223 0.7463352 0.998008 0.8011708 0.9344543 0.8163243 0.931351 0.5287353 0.9336678 0.6353328 0.998008 0.6118097 0.9321093 0.721786 0.998008 0.6188761 0.9345297 0.7027801 0.9225803 0.8240835 0.9225803 0.5135227 0.9357181 0.6774615 0.931351 0.8222705 0.931351 0.5070975 0.9324334 0.6554136 0.9321408 0.6845604 0.9313511 0.8403031 0.9225803 0.4909431 0.9225803 0.8444486 0.931351 0.4904815 0.9313508 0.870379 0.9225804 0.8760796 0.9313509 0.9027953 0.9225803 0.9065862 0.9313508 0.9351006 0.9225803 0.9347344 0.8747088 0.03593943 0.8908309 0.06906008 0.865629 0.04397267 0.8770335 0.0350587 0.8584164 0.0503538 0.8863153 0.04156841 0.878962 0.1110263 0.8792105 0.03581587 0.9079967 0.06217715 0.9125139 0.121648 0.8111494 0.09217212 0.9353698 0.09725066 0.9013872 0.1327747 0.8223113 0.1777112 0.9487506 0.121648 0.8329093 0.2043923 0.9138222 0.1266447 0.8010784 0.1508273 0.9509243 0.1261274 0.9004287 0.1697532 0.9315758 0.127555 0.9072434 0.1352045 0.8044868 0.1001107 0.9047725 0.1465469 0.9021234 0.217701 0.7849813 0.1384379 0.9049582 0.1681566 0.90386 0.1929227 0.836646 0.2291222 0.7839124 0.1336693 0.9074203 0.1959429 0.9015046 0.2504452 0.9075198 0.1710886 0.7834801 0.1362703 0.907514 0.2145639 0.90451 0.2343623 0.9019869 0.2774064 0.907514 0.2532215 0.9074203 0.2718423 0.9013872 0.3350151 0.9077404 0.2346113 0.907809 0.29586 0.8335533 0.2623536 0.905128 0.3003408 0.8219721 0.2902027 0.9047725 0.321239 0.9072437 0.3325858 0.8857334 0.4269183 0.8028865 0.3153853 0.9125139 0.3461419 0.7772126 0.3343104 0.9396872 0.363052 0.9148378 0.3400167 0.8151023 0.4692334 0.9255922 0.3459307 0.9167739 0.396075 0.853626 0.4500016 0.6895112 0.4030989 0.9487506 0.3461419 0.9399461 0.3422928 0.7416362 0.3556055 0.7565728 0.4673227 0.9516078 0.3401965 0.7052895 0.3468127 0.7478706 0.3455883 0.7743374 0.4813286 0.7224656 0.48649 0.6676814 0.3443722 0.7224656 0.3480012 0.7387155 0.4861688 0.6918279 0.4691895 0.5444716 0.2980248 0.6821249 0.3408067 0.6997612 0.4856385 0.6592624 0.4789164 0.5435441 0.3350151 0.6603093 0.3299861 0.5396807 0.3110131 0.6401731 0.3132264 0.6067337 0.458972 0.5375767 0.332631 0.5400917 0.2771972 0.6186308 0.2826976 0.5583817 0.4266667 0.5299584 0.3407893 0.5420485 0.2311642 0.6094349 0.2506136 0.5269866 0.3942505 0.5324174 0.3461419 0.5396603 0.2456499 0.6085018 0.2204508 0.5186951 0.3414548 0.5373293 0.254656 0.54014 0.2235868 0.5444722 0.1697617 0.5058525 0.3641745 0.4961806 0.3461419 0.5372983 0.2751269 0.5373433 0.2221462 0.5410494 0.1978157 0.6196944 0.1835078 0.4933248 0.3401957 0.5365278 0.3069525 0.5401641 0.1654676 0.6387063 0.1561072 0.5373827 0.1905242 0.5390347 0.1342469 0.5435441 0.1327747 0.537098 0.155419 0.669783 0.1061308 0.5300111 0.1267334 0.6419759 0.02649625 0.660947 0.1376919 0.5324174 0.121648 0.6266178 0.003292693 0.6729291 0.07326134 0.6707259 0.114142 0.5177075 0.08488958 0.5186951 0.126335 0.5972968 0.01435291 0.6806477 0.08492278 0.6758384 0.1259747 0.4961806 0.121648 0.5464348 0.05260679 0.5714951 0.03096891 0.6223012 0.002008379 0.6752206 0.09903269 0.6741046 0.1305437 0.4933229 0.1275948 0.6246928 0.001866468 0.6781748 0.09707737 0.6758978 0.1286107 0.6821046 0.08976364 0.6814995 0.0931178 0.8348817 0.7447448 0.8217111 0.7393693 0.8348814 0.7168956 0.8217254 0.7546949 0.8217752 0.7178675 0.8348815 0.7699274 0.8208485 0.7266334 0.8217337 0.69653 0.8217163 0.7627738 0.8208488 0.7495214 0.8208488 0.7083105 0.8348817 0.6836642 0.8217838 0.7769848 0.8208486 0.6865364 0.8217673 0.6784029 0.8208503 0.7743563 0.8348817 0.7888525 0.8348816 0.6589344 0.8216869 0.7904175 0.8208492 0.6595746 0.8217991 0.655443 0.8348812 0.6322533 0.8208489 0.6370594 0.8217584 0.6321417 0.8217782 0.6127686 0.8348817 0.6053693 0.8217257 0.5929799 0.8348817 0.5929799 0.8348817 0.5908123 0.8217257 0.590045 0.8217257 0.5882114 0.8348817 0.5882114 0.8217257 0.5770251 0.8217257 0.5546527 0.8348817 0.5546527 0.8348817 0.5467142 0.8217257 0.550436 0.8217257 0.5467142 0.8217257 0.5391045 0.8217257 0.5199612 0.8261109 0.5051466 0.8348817 0.5048959 0.8217257 0.5086656 0.8348817 0.4985147 0.8261109 0.4904814 0.8348817 0.4904814 0.3432536 0.6526766 0.3432536 0.651426 0.3441307 0.6543609 0.4881119 0.5400337 0.5012189 0.5177416 0.5012191 0.5470836 0.4880242 0.5211368 0.4880629 0.5586033 0.4871862 0.5333376 0.487186 0.5544558 0.4880629 0.5724885 0.4871849 0.5115127 0.5012189 0.5724885 0.4871858 0.5724885 0.4880754 0.5807998 0.5012189 0.5896646 0.4871857 0.5988878 0.4882284 0.602508 0.5012187 0.6128293 0.4871858 0.6243495 0.4881054 0.6227379 0.5012187 0.6346448 0.4880504 0.6400536 0.4871866 0.6473488 0.501219 0.654781 0.4880238 0.6548613 0.4879779 0.6708555 0.5012192 0.6763234 0.467973 0.5509173 0.4678083 0.5115126 0.4810496 0.521554 0.4810493 0.553638 0.4810493 0.5838007 0.4679404 0.5809011 0.4679281 0.604713 0.4810494 0.6207438 0.4678617 0.6299241 0.4810494 0.6481444 0.4677584 0.6502444 0.4810494 0.6665597 0.4679837 0.6665416 0.8059425 0.7106153 0.8147132 0.699554 0.8147132 0.7380777 0.8059425 0.6727669 0.8059425 0.7458883 0.8147132 0.6587891 0.8059425 0.6351312 0.8147132 0.6231672 0.8059425 0.6069173 0.8147132 0.6069173 0.8059425 0.5942733 0.8147132 0.5842129 0.8059426 0.5576824 0.8147132 0.5437141 0.8059425 0.5277138 0.8147131 0.4911854 0.8059425 0.4904814 0.740071 0.5243182 0.7338386 0.5540735 0.7340128 0.5202576 0.7362417 0.4986397 0.7401666 0.499162 0.7350037 0.4904814 0.7927905 0.499162 0.7401666 0.4921454 0.7927905 0.5880655 0.7927905 0.4921454 0.7998071 0.5020682 0.7399467 0.5767443 0.7998071 0.6926833 0.7365921 0.5561439 0.7399398 0.6431586 0.7927905 0.6955897 0.7365081 0.5766147 0.7364661 0.6091246 0.7401666 0.6955897 0.7364213 0.6407465 0.7371379 0.6758517 0.7927905 0.7026062 0.7345008 0.6970239 0.7401666 0.7026062 0.7345144 0.7045373 0.148295 0.5711532 0.148295 0.5185292 0.1849725 0.5508584 0.1849724 0.5185292 0.1849725 0.5711532 0.1804245 0.5115127 0.1844832 0.5750658 0.1512014 0.5115127 0.159111 0.5768191 0.1477951 0.5768054 0.3466762 0.6165389 0.3848625 0.5115126 0.3912968 0.6374331 0.4006473 0.5233042 0.3752619 0.6318972 0.4224722 0.5339766 0.4435904 0.5392249 0.4616231 0.5405767 0.4389919 0.6474345 0.7231743 0.679049 0.7270874 0.6609612 0.7272847 0.6796758 0.7231727 0.6520689 0.7268357 0.7043175 0.7271663 0.6487101 0.7273076 0.6349322 0.7231725 0.621576 0.7260594 0.6250537 0.7271761 0.6083067 0.7268593 0.5925288 0.7231744 0.5812969 0.7274674 0.5637556 0.723173 0.5536904 0.7272449 0.5405399 0.7231727 0.5256571 0.7270504 0.5256323 0.7271392 0.5106384 0.7231839 0.4957346 0.7277033 0.4904815 0.5414906 0.5878859 0.5451885 0.5947318 0.5421827 0.6268023 0.5452061 0.5575062 0.5450875 0.6192811 0.5332928 0.6201175 0.5420428 0.5458019 0.5453658 0.6510441 0.5415775 0.6551237 0.5309576 0.5772354 0.5456116 0.5283595 0.5472867 0.5082786 0.5351222 0.5351286 0.5482547 0.6745895 0.5415775 0.510815 0.5485941 0.4904814 0.3405409 0.5522926 0.3273348 0.5486431 0.3405342 0.5489385 0.3273849 0.5522926 0.3405409 0.5449789 0.3273849 0.5571335 0.3273849 0.5449789 0.3405409 0.5571335 0.3405409 0.5430236 0.3405409 0.5687949 0.3273849 0.5430236 0.3273849 0.580923 0.3273849 0.5385962 0.3273849 0.6158804 0.3405347 0.5359255 0.3265078 0.543755 0.3405409 0.61556 0.3273849 0.5327844 0.3265078 0.5327844 0.3302292 0.6171368 0.3405409 0.5279143 0.3317702 0.6203088 0.3273849 0.5279143 0.3405409 0.6387636 0.3273849 0.5160816 0.3317702 0.6387636 0.3339628 0.5160816 0.3317702 0.6401899 0.3273849 0.5146902 0.3405409 0.5160816 0.3405409 0.6401899 0.3405409 0.5134457 0.3273849 0.5115126 0.3265078 0.5132783 0.3405409 0.5115126 0.514371 0.6437839 0.5091056 0.6266944 0.5138749 0.6072733 0.5090627 0.6488366 0.5109035 0.595459 0.5196113 0.6241517 0.5073544 0.6641175 0.5138639 0.5471384 0.5248222 0.5819491 0.5107446 0.5635746 0.5114131 0.5359317 0.5236196 0.5446981 0.514371 0.4994752 0.5115226 0.4904815 0.1049824 0.5711531 0.1049824 0.5185291 0.1416598 0.5711531 0.09796586 0.5711531 0.1416595 0.5185291 0.14117 0.5750685 0.09680586 0.5757568 0.09796586 0.5185291 0.1371118 0.5115126 0.1044 0.5749593 0.1078887 0.5115126 0.6915645 0.6939258 0.6778913 0.6939258 0.6749381 0.6868592 0.7100207 0.7009422 0.7100207 0.6910194 0.7071143 0.6939258 0.6733433 0.7009423 0.7100207 0.5004044 0.6749381 0.5045645 0.6778913 0.497498 0.7071143 0.497498 0.7100207 0.4904815 0.7170372 0.497498 0.2293832 0.5769241 0.2116295 0.5752895 0.2277851 0.5711532 0.1911077 0.5711532 0.2359619 0.5757567 0.1922811 0.5769485 0.1911078 0.5185292 0.2348017 0.5711532 0.2277851 0.5185292 0.2348017 0.5185292 0.1956557 0.5115127 0.209615 0.5115127 0.2248788 0.5115127 0.5553074 0.6271192 0.5554012 0.6084984 0.5612563 0.5862215 0.5558718 0.5898883 0.5584968 0.6536402 0.5554012 0.5698408 0.5561647 0.6511369 0.611957 0.6045179 0.5553074 0.5512199 0.5547293 0.6878628 0.611957 0.490958 0.611957 0.6873858 0.5592156 0.5331749 0.559333 0.6873858 0.6189736 0.4938644 0.6189736 0.6844794 0.5555468 0.5263656 0.559333 0.490958 0.5547294 0.4904815 0.667208 0.6868592 0.6350317 0.6939258 0.6321254 0.6910194 0.6642548 0.6939258 0.6251089 0.6939258 0.667208 0.5045645 0.6321254 0.7009422 0.6321254 0.5004044 0.6502955 0.497498 0.6350317 0.497498 0.6642548 0.497498 0.6251089 0.497498 0.6321254 0.4904815 0.05383458 0.5761854 0.04506387 0.5737938 0.05383458 0.5737938 0.04506387 0.5761854 0.04506387 0.6023366 0.05383458 0.6011899 0.3360615 0.6441814 0.3360615 0.6529521 0.3337369 0.6529521 0.3337369 0.6441814 0.3315599 0.6441814 0.3315599 0.6529521 0.2888944 0.5458611 0.2847347 0.552906 0.2847478 0.5115126 0.2886291 0.5594159 0.2847289 0.5962953 0.2785936 0.5456796 0.2747818 0.536817 0.2771448 0.5115126 0.2747825 0.5859656 0.268238 0.5304434 0.2640726 0.5145813 0.2667956 0.5225418 0.2640674 0.5452752 0.2686465 0.5115126 0.2685188 0.5494516 0.2640672 0.5637468 0.2682702 0.5677038 0.2640659 0.5839167 0.2678021 0.5812938 0.316407 0.6385772 0.312183 0.6359488 0.3023256 0.6243662 0.2828361 0.6067791 0.2915747 0.6142915 0.2957721 0.6215868 0.09067054 0.5692995 0.07940727 0.5698024 0.09022321 0.5641366 0.05403698 0.56805 0.05354583 0.5641366 0.05354583 0.5115126 0.09022321 0.5115126 0.00200472 0.5453685 0.001992031 0.5181592 0.004925888 0.5292591 0.005728258 0.5115126 0.2579306 0.5801249 0.2558575 0.5733356 0.2574257 0.5493193 0.2534947 0.5322905 0.2572266 0.5298643 0.2574317 0.5115126 0.2204696 0.5809399 0.228328 0.5893824 0.2355633 0.6012603 0.3096327 0.5267706 0.3057246 0.5270748 0.3101556 0.5115126 0.3057242 0.549706 0.3098736 0.539632 0.3083979 0.5563696 0.3057247 0.5974011 0.308587 0.5859039 0.3086228 0.613436 0.06937552 0.5908109 0.06845184 0.6012995 0.0684513 0.5737938 0.3194852 0.5886301 0.3162909 0.5860162 0.3203447 0.5711659 0.3203725 0.6025571 0.3162918 0.557594 0.3163114 0.6120696 0.3200856 0.5520914 0.3203304 0.6203748 0.3162946 0.5305218 0.3202312 0.5314349 0.3199554 0.5115126 0.1249709 0.5797482 0.1290242 0.5930715 0.1293073 0.60356 0.2421571 0.551914 0.2449885 0.5283362 0.2459317 0.5561874 0.2420976 0.5252 0.2420972 0.5794191 0.2473594 0.5115126 0.2991043 0.5284331 0.2950671 0.5258026 0.2995889 0.5115126 0.2986053 0.5425215 0.2950297 0.556654 0.2987632 0.5650311 0.2950335 0.5854593 0.2993737 0.5792668 0.298924 0.5907867 0.2980437 0.6027876 0.1736947 0.6016618 0.1831394 0.5874401 0.1899919 0.5809399 0.09768582 0.5797482 0.09864233 0.5934424 0.09768558 0.606409 0.06065549 0.5737938 0.06156679 0.5871171 0.06065568 0.6019569 0.01248757 0.5549088 0.01395317 0.5719259 0.01193169 0.5848925 0.02376774 0.5252068 0.01182506 0.5509173 0.02655825 0.5115126 0.2130763 0.5809399 0.2081248 0.5892883 0.1970821 0.6013149 0.2591611 0.6032337 0.2543336 0.5980602 0.2497039 0.5879081 0.0434329 0.5472513 0.04645346 0.5353696 0.04741054 0.550938 0.04344326 0.5115126 0.04738534 0.5619527 0.1054814 0.5797482 0.1123859 0.5901195 0.1173582 0.6049593 0.09028021 0.6012817 0.07712422 0.6012817 0.09028026 0.5881241 0.07721458 0.5880155 0.0762471 0.6009101 0.07624743 0.5737938 0.03622077 0.5855097 0.03337016 0.5569332 0.03709156 0.5569687 0.03424728 0.5561372 0.1578931 0.5808105 0.1536236 0.5904346 0.1444605 0.6019953 0.02555725 0.5690008 0.02168169 0.5843964 0.02167645 0.5650269 0.02640712 0.5549088 0.1658989 0.6015757 0.1658995 0.5808105 0.1667367 0.5895491 0.03683216 0.5367745 0.03269358 0.5371564 0.03646163 0.5232711 0.03556743 0.5456542 0.03269888 0.5126426 0.03269354 0.5515664 0.0372976 0.5115126 0.03357066 0.5521458 0.2428441 0.5876345 0.241908 0.5991952 0.2419106 0.5834104 0.3237648 0.6582177 0.3237641 0.6441813 0.3245559 0.6525295 0.1366646 0.6019975 0.1366651 0.5797482 0.1375098 0.5901195 0.2679864 0.5963505 0.2672446 0.6032031 0.2672445 0.5879081 0.2750402 0.589957 0.2759699 0.5980582 0.2750413 0.6051229</float_array>
          <technique_common>
            <accessor source="#upper_arm_roll_MShape-map1-array" count="964" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="upper_arm_roll_MShape-vertices" name="upper_arm_roll_MShape-vertices">
          <input semantic="POSITION" source="#upper_arm_roll_MShape-positions"/>
          <input semantic="NORMAL" source="#upper_arm_roll_MShape-normals"/>
        </vertices>
        <triangles material="lambert3SG" count="1000">
          <input semantic="VERTEX" source="#upper_arm_roll_MShape-vertices" offset="0"/>
          <input semantic="TEXCOORD" source="#upper_arm_roll_MShape-map1" offset="1" set="0"/>
          <p>495 782 498 781 478 784 193 0 217 1 210 2 484 280 473 269 492 277 238 239 329 240 218 241 281 298 250 299 220 300 253 266 416 259 239 250 215 33 243 24 205 36 427 454 383 438 445 459 300 357 304 358 301 359 152 934 73 935 46 936 340 453 401 448 339 445 473 269 452 262 441 264 402 619 400 617 451 613 238 239 362 242 329 240 227 124 212 112 228 130 122 164 10 157 110 224 218 241 286 243 249 245 105 191 40 198 106 203 105 191 30 183 40 198 306 477 307 489 309 487 133 201 233 186 90 206 366 388 414 394 410 395 195 51 192 60 250 56 330 491 85 492 273 493 307 489 267 490 309 487 408 378 386 383 380 380 25 210 10 157 21 205 110 224 43 220 33 223 109 226 32 216 108 222 110 224 42 227 109 226 109 226 42 227 32 216 33 537 42 538 110 539 255 11 197 16 221 19 26 171 103 184 102 192 120 540 333 541 364 542 40 198 41 190 106 203 106 703 41 708 107 707 107 197 41 190 31 170 436 309 252 311 262 305 125 565 80 566 365 567 441 264 440 267 473 269 223 578 291 579 348 580 32 216 42 227 33 223 159 15 194 6 148 10 130 122 185 116 175 111 32 216 43 220 10 157 437 320 455 313 430 323 51 546 120 540 111 544 445 595 383 596 392 597 304 358 298 360 283 362 251 244 239 250 454 248 53 938 64 939 82 940 217 356 222 354 274 355 361 365 355 368 304 358 290 447 373 442 272 451 347 404 291 410 333 407 247 84 190 76 198 89 352 583 243 584 282 585 448 620 468 621 465 622 389 331 430 323 395 322 77 630 69 631 48 632 231 181 207 167 230 175 397 375 409 377 315 366 343 417 341 420 342 425 263 40 188 45 205 36 359 374 311 379 308 367 339 445 404 441 335 432 216 291 248 285 355 289 481 725 459 724 456 727 383 438 427 454 426 449 353 430 351 431 350 427 14 144 13 135 55 139 197 16 141 22 170 26 309 679 81 676 98 681 247 318 234 328 289 324 209 93 144 100 158 96 67 639 156 640 170 641 39 143 9 148 55 139 443 606 445 595 474 603 367 659 387 660 372 661 102 692 101 687 26 693 352 422 349 418 291 410 264 12 155 30 179 25 172 941 38 942 147 943 379 392 414 394 366 388 477 252 438 255 454 248 319 706 106 703 107 707 307 675 81 676 267 677 478 731 481 725 456 727 57 145 115 158 124 165 260 34 163 37 171 42 331 710 403 711 371 712 86 559 64 560 45 556 323 386 311 379 325 382 261 101 209 93 238 97 4 230 45 233 64 232 92 208 15 204 90 206 367 659 390 662 387 660 465 622 468 621 469 623 430 323 434 317 395 322 69 506 50 500 87 499 284 671 327 673 370 665 266 701 106 703 318 704 459 724 481 725 458 726 342 549 114 551 112 547 316 470 399 475 317 479 306 477 310 483 268 466 58 202 68 207 34 196 377 251 236 249 326 247 133 944 38 942 172 941 297 481 295 476 322 464 500 735 501 736 494 737 207 167 206 147 246 160 328 385 366 388 369 393 357 452 340 453 339 445 249 245 286 243 326 247 243 584 245 586 282 585 428 270 441 264 412 257 500 735 494 737 496 739 141 643 67 639 170 641 235 256 236 249 435 253 362 715 331 710 371 712 305 684 91 686 94 680 147 180 186 166 207 167 398 748 409 749 447 750 221 19 208 23 240 27 186 166 152 159 206 147 31 925 126 924 107 922 285 372 368 381 328 385 403 268 462 260 424 274 288 440 280 444 335 432 222 354 255 352 288 353 308 526 233 531 302 532 405 261 454 248 438 255 21 205 18 200 93 209 177 67 167 71 192 60 168 882 140 885 77 883 314 371 397 375 315 366 310 683 101 687 268 689 205 36 183 41 215 33 200 46 134 50 164 55 466 754 447 750 446 751 490 607 474 603 496 605 58 652 167 653 177 651 124 511 115 514 271 512 406 335 453 310 463 329 308 526 93 529 92 530 141 22 70 231 67 218 34 196 67 218 52 221 410 761 414 762 464 763 470 757 450 755 447 750 374 717 378 720 326 722 316 470 317 479 289 474 433 342 406 335 463 329 55 139 29 163 22 156 302 532 233 531 301 534 164 648 7 646 142 649 346 414 335 432 276 428 328 502 117 505 285 508 92 208 18 200 15 204 210 2 169 5 193 0 444 773 470 771 464 763 464 763 470 771 475 768 292 486 294 471 293 482 12 99 69 108 0 104 321 369 312 363 315 366 157 105 144 100 209 93 490 607 471 610 474 603 277 433 279 429 336 424 197 16 170 26 208 23 239 250 416 259 458 254 493 780 498 781 495 782 492 785 493 780 495 782 358 469 360 461 268 466 293 793 225 794 292 795 230 295 283 296 299 297 225 702 294 698 292 705 300 799 231 800 229 801 453 310 472 325 463 329 407 403 338 406 334 396 448 620 399 628 402 629 326 247 236 249 249 245 318 478 320 484 358 469 211 65 196 57 256 61 198 89 204 95 234 91 378 263 412 257 377 251 2 822 9 820 176 821 174 805 74 806 76 807 370 665 390 662 367 659 374 717 384 714 388 719 334 396 338 406 347 404 147 943 73 945 186 947 424 274 420 273 384 279 322 464 294 471 296 468 113 498 117 505 328 502 175 111 185 116 202 103 39 817 72 815 175 814 206 147 216 153 246 160 279 555 118 557 127 554 233 531 308 526 92 530 93 209 18 200 92 208 459 724 407 730 456 727 15 204 18 200 38 195 213 308 247 318 317 314 176 94 181 102 204 95 170 26 156 31 208 23 499 740 500 735 496 739 194 6 197 16 255 11 196 57 263 40 257 52 145 887 56 886 140 885 249 110 265 115 261 101 99 685 24 690 101 687 346 414 354 423 350 427 418 312 213 308 317 314 133 201 172 185 233 186 491 338 472 325 488 336 497 783 498 781 493 780 47 654 151 655 167 653 301 359 304 358 302 361 140 62 168 54 196 57 426 611 443 606 425 612 313 695 268 689 102 692 237 68 203 72 190 76 223 578 348 580 257 582 4 230 64 232 53 228 149 810 6 811 128 812 193 0 148 10 194 6 486 789 489 787 470 791 438 255 462 260 403 268 175 814 72 815 130 816 240 340 252 311 417 333 196 57 178 69 145 66 301 534 231 535 300 536 171 645 35 642 7 646 244 588 259 590 353 589 418 312 449 315 453 310 190 76 146 88 198 89 268 689 101 687 102 692 168 54 184 49 263 40 400 463 402 467 322 464 58 202 34 196 47 188 297 344 219 348 295 350 123 824 69 825 87 826 420 273 462 260 440 267 223 48 263 40 242 44 82 827 64 828 86 829 409 749 397 753 446 751 212 112 185 116 131 123 118 557 45 556 127 554 404 830 401 831 460 832 123 949 3 950 69 951 75 837 2 838 137 839 153 890 71 888 178 889 269 376 376 384 368 381 72 128 13 135 131 123 373 442 394 446 385 450 100 524 97 527 308 526 165 8 174 13 76 18 200 46 164 55 195 51 235 138 248 142 191 134 233 186 172 185 207 167 103 697 313 695 102 692 171 645 7 646 134 647 495 776 478 772 475 768 471 610 442 609 474 603 323 386 325 382 324 390 464 763 414 762 429 764 89 688 91 686 305 684 142 649 68 650 177 651 346 414 291 410 349 418 496 605 474 603 479 601 180 841 128 842 157 843 79 847 49 848 84 849 149 120 128 126 180 109 105 699 106 703 266 701 464 763 413 766 411 769 76 18 74 236 8 234 381 402 391 400 407 403 259 590 224 592 351 591 201 90 158 96 160 92 98 199 23 179 99 193 476 316 483 321 252 311 466 275 446 281 235 256 57 145 62 140 6 136 378 263 428 270 412 257 484 280 466 275 473 269 284 302 237 304 327 306 436 309 262 305 332 307 344 278 337 272 253 266 155 850 37 851 179 852 187 63 142 59 177 67 119 859 61 860 34 861 271 512 285 508 124 511 196 57 145 66 140 62 31 925 55 926 126 924 98 681 99 685 310 683 308 367 312 363 271 370 357 452 339 445 280 444 59 635 51 636 48 632 71 888 56 886 145 887 419 339 463 329 437 320 419 339 437 320 387 337 34 196 61 168 47 188 341 552 127 554 114 551 322 464 295 476 294 471 212 112 143 129 228 130 148 868 4 866 70 870 456 774 407 770 439 765 209 93 158 96 201 90 136 634 77 630 48 632 243 24 263 40 205 36 248 285 235 256 397 283 261 101 157 105 209 93 411 387 413 391 368 381 201 90 162 85 199 74 376 384 386 383 411 387 8 234 45 233 4 230 248 142 206 147 191 134 139 862 1 863 166 864 430 323 455 313 434 317 481 725 480 728 458 726 399 475 418 485 317 479 14 144 16 150 91 155 347 288 257 290 348 292 107 707 320 709 319 706 388 276 428 270 378 263 455 313 437 320 476 316 452 262 435 253 441 264 455 313 436 309 434 317 463 329 472 325 437 320 428 270 440 267 441 264 368 381 413 391 379 392 104 219 28 213 100 217 259 7 264 12 210 2 313 695 105 699 266 701 313 695 266 701 318 704 95 678 81 676 307 675 480 258 477 252 458 254 327 673 406 670 370 665 387 660 389 663 372 661 372 661 395 666 332 667 194 6 159 15 197 16 360 574 84 573 356 572 422 777 408 779 450 778 333 407 275 405 347 404 500 735 482 738 501 736 191 134 214 121 236 127 65 952 53 953 80 954 27 177 30 183 105 191 27 177 105 191 103 184 453 310 327 306 213 308 336 424 279 429 341 420 237 304 213 308 327 306 272 451 356 457 290 447 265 115 149 120 180 109 243 24 264 12 245 20 326 722 378 720 377 723 406 670 396 669 370 665 405 713 403 711 331 710 116 873 61 874 119 875 445 595 415 598 457 599 460 832 483 835 482 836 305 472 268 466 296 468 272 451 400 463 322 464 321 369 380 380 269 376 49 955 63 956 84 957 343 417 342 425 364 421 374 717 286 721 329 718 241 28 155 30 264 12 24 690 26 693 101 687 256 286 347 288 345 284 367 659 372 661 278 664 144 845 12 844 158 846 273 399 328 385 334 396 75 87 137 83 182 79 174 13 165 8 210 2 253 77 239 82 199 74 129 855 48 856 5 853 96 212 21 205 93 209 433 342 390 346 396 345 78 876 135 877 150 878 154 808 37 809 74 806 477 252 440 267 462 260 473 269 440 267 477 252 11 497 54 501 121 495 450 755 408 752 447 750 448 618 402 619 451 613 421 733 407 730 459 724 447 750 408 752 398 748 17 162 19 172 94 169 50 113 12 99 11 118 158 96 12 99 160 92 417 458 401 448 340 453 468 621 487 627 488 625 108 222 32 216 104 219 284 302 220 300 237 304 234 328 303 334 289 324 421 409 393 416 270 411 374 717 326 722 286 721 214 121 265 115 249 110 499 740 496 739 482 738 492 277 473 269 477 252 28 213 10 157 25 210 251 244 454 248 331 246 120 540 83 543 333 541 74 806 174 805 154 808 347 404 348 413 291 410 341 420 346 414 336 424 407 403 421 409 338 406 321 369 408 378 380 380 381 402 407 403 334 396 271 370 359 374 308 367 335 432 346 414 351 431 24 178 22 156 26 171 461 327 252 311 483 321 424 274 384 279 403 268 142 59 187 63 195 51 221 19 197 16 208 23 248 142 216 153 206 147 477 252 497 265 493 271 494 332 501 326 476 316 377 251 412 257 435 253 454 248 239 250 477 252 199 74 153 73 211 65 203 72 182 79 190 76 193 0 166 14 148 10 96 528 93 529 308 526 151 655 47 654 161 656 168 882 77 883 184 884 387 337 437 320 389 331 12 844 144 845 157 843 135 137 191 134 150 141 0 104 69 108 71 114 201 90 199 74 251 86 17 162 91 155 16 150 117 505 62 510 124 511 371 712 384 714 374 717 472 325 491 338 494 332 472 325 494 332 476 316 10 157 73 174 38 195 210 2 217 1 224 4 245 586 244 588 354 587 149 810 60 813 6 811 347 404 330 401 334 396 253 266 337 272 416 259 10 157 6 136 60 133 166 864 1 863 4 866 331 710 454 716 405 713 208 23 260 34 252 32 389 331 437 320 430 323 86 559 45 556 118 557 23 179 19 172 22 156 114 551 36 550 112 547 382 460 432 465 400 463 126 892 55 893 79 894 260 34 171 42 200 46 54 125 11 118 12 99 36 550 114 551 8 553 8 553 114 551 127 554 275 496 88 494 330 491 45 556 8 553 127 554 52 895 125 896 34 897 79 958 55 959 49 960 61 898 116 899 63 900 230 295 361 294 283 296 10 157 43 220 110 224 253 266 254 282 344 278 119 901 34 902 125 903 84 573 116 571 356 572 52 904 80 905 125 906 287 568 125 565 365 567 80 907 53 908 82 909 460 832 417 833 461 834 243 24 242 44 263 40 496 739 494 737 490 742 91 155 17 162 94 169 361 294 246 293 355 289 347 404 344 408 345 412 432 615 442 609 451 613 389 663 395 666 372 661 433 342 396 345 406 335 432 465 382 460 423 462 442 609 432 615 423 614 242 581 243 584 352 583 27 177 103 184 26 171 189 146 191 134 206 147 294 471 295 476 293 482 121 495 113 498 273 493 25 210 21 205 97 214 250 299 281 298 278 301 50 500 11 497 85 492 47 188 61 168 75 87 464 763 411 769 444 773 415 598 404 600 457 599 284 671 370 665 281 668 222 3 194 6 255 11 291 579 223 578 242 581 280 444 339 445 335 432 199 74 162 85 138 81 21 205 96 212 97 214 100 217 25 210 97 214 479 601 482 604 496 605 344 278 254 282 345 284 83 509 123 503 333 507 434 317 436 309 395 322 281 668 370 665 367 659 329 718 371 712 374 717 278 664 281 668 367 659 403 711 384 714 371 712 396 669 390 662 370 665 363 561 86 559 277 558 311 525 100 524 308 526 337 415 344 408 270 411 322 464 289 474 303 480 258 303 250 299 278 301 94 169 20 182 95 189 458 254 477 252 239 250 316 470 289 474 322 464 180 109 157 105 261 101 405 261 438 255 403 268 412 257 441 264 435 253 360 574 79 575 84 573 315 366 355 368 314 371 355 289 248 285 314 287 86 559 118 557 277 558 279 555 127 554 341 552 498 734 497 732 481 725 38 942 73 945 147 943 479 601 474 603 445 595 62 510 57 513 124 511 479 601 457 599 460 602 457 599 404 600 460 602 479 601 460 602 482 604 470 791 467 792 486 789 470 757 447 750 467 756 451 613 468 616 448 618 249 110 236 127 214 121 7 211 34 196 68 207 152 159 189 146 206 147 287 443 276 428 290 447 85 492 11 497 121 495 34 196 35 215 67 218 343 417 346 414 341 420 215 33 241 28 243 24 89 688 305 684 296 691 440 267 428 270 388 276 469 623 468 621 488 625 483 741 501 736 482 738 466 754 484 758 467 756 36 237 37 29 5 238 304 358 355 368 315 366 497 732 480 728 481 725 246 293 216 291 355 289 208 23 252 32 240 27 115 514 122 515 271 512 80 229 52 221 65 225 233 531 92 530 90 533 335 432 392 434 276 428 240 340 417 333 340 343 135 877 60 879 214 881 327 673 453 674 406 670 273 493 85 492 121 495 295 798 219 797 293 793 130 816 72 815 185 818 362 715 371 712 329 718 117 505 124 511 285 508 197 16 159 15 141 22 245 20 264 12 244 17 67 639 35 642 156 640 55 139 9 148 49 154 250 56 258 43 195 51 211 65 254 70 199 74 347 404 338 406 344 408 169 5 139 9 193 0 207 167 231 181 233 186 477 252 480 258 497 265 353 589 259 590 351 591 274 355 222 354 288 353 213 308 418 312 453 310 104 523 323 522 108 521 323 522 100 524 311 525 323 522 104 523 100 524 478 772 439 765 475 768 478 772 456 774 439 765 444 773 450 778 470 771 439 765 464 763 475 768 56 886 77 883 140 885 215 33 129 39 173 35 293 793 219 797 226 796 88 910 50 911 85 912 103 697 105 699 313 695 278 301 262 305 258 303 0 913 138 914 162 915 23 179 20 182 19 172 296 691 294 698 228 696 334 396 330 401 273 399 229 801 298 802 300 799 196 57 168 54 263 40 226 796 225 794 293 793 225 702 227 700 294 698 94 169 19 172 20 182 39 143 55 139 72 128 12 99 44 132 54 125 250 56 192 60 220 64 211 65 256 61 254 70 229 801 230 804 299 803 227 700 228 696 294 698 298 802 229 801 299 803 84 918 63 919 116 920 215 33 173 35 241 28 46 152 10 157 78 173 232 98 234 91 204 95 322 464 268 466 360 461 53 228 67 218 4 230 349 418 354 423 346 414 465 319 453 310 449 315 207 167 246 160 230 175 472 325 476 316 437 320 436 672 332 667 395 666 29 163 55 139 31 170 247 318 289 324 317 314 150 878 46 880 78 876 186 166 73 174 152 159 358 576 79 575 360 574 255 352 221 349 280 351 22 156 29 163 26 171 346 414 276 428 336 424 349 418 352 422 282 426 246 293 361 294 230 295 88 494 85 492 330 491 244 588 353 589 354 587 402 467 399 475 316 470 339 445 401 448 404 441 488 744 487 745 490 742 490 742 494 737 491 743 193 0 222 3 217 1 362 242 251 244 331 246 303 334 232 341 297 344 282 585 245 586 354 587 177 651 68 650 58 652 315 366 398 373 321 369 72 128 131 123 185 116 165 8 169 5 210 2 178 889 71 888 145 887 286 243 218 241 329 240 257 52 263 40 223 48 189 146 150 141 191 134 316 470 322 464 402 467 398 373 408 378 321 369 201 90 160 92 162 85 181 102 175 111 202 103 50 113 69 108 12 99 167 71 151 75 192 60 163 644 35 642 171 645 146 88 190 76 137 83 277 433 276 428 363 435 356 572 116 571 290 569 365 564 80 563 363 561 209 93 201 90 251 86 209 93 251 86 238 97 320 921 107 922 358 923 291 579 242 581 352 583 146 88 176 94 198 89 148 868 70 870 159 871 119 570 125 565 290 569 232 341 219 348 297 344 107 922 126 924 358 923 290 569 125 565 287 568 116 571 119 570 290 569 341 552 114 551 342 549 123 503 275 496 333 507 188 45 132 47 183 41 188 45 183 41 205 36 81 194 23 179 98 199 387 337 390 346 419 339 388 276 384 279 420 273 136 53 188 45 184 49 184 49 188 45 263 40 358 576 126 577 79 575 6 136 62 140 44 132 160 917 0 913 162 915 49 154 9 148 63 161 211 65 153 73 178 69 20 182 81 194 95 189 81 194 20 182 23 179 131 927 13 928 143 929 237 68 192 60 203 72 134 647 7 646 164 648 300 357 298 360 304 358 37 29 8 234 74 236 13 135 14 144 89 149 485 788 489 787 486 789 99 193 23 179 24 178 120 540 51 546 59 545 417 333 252 311 461 327 203 72 151 75 161 78 325 518 359 516 110 517 400 463 272 451 382 460 257 290 347 288 256 286 274 593 224 592 217 594 204 95 202 103 232 98 179 25 154 21 264 12 3 633 48 632 69 631 128 842 12 844 157 843 37 29 154 21 179 25 495 776 475 768 489 775 475 768 470 771 489 775 16 150 22 156 17 162 136 858 48 856 132 857 136 53 132 47 188 45 155 850 173 854 5 853 132 857 48 856 129 855 234 328 232 341 303 334 276 428 287 443 365 439 243 24 241 28 264 12 39 817 175 814 181 819 155 850 5 853 37 851 146 823 2 822 176 821 138 891 71 888 153 890 498 781 481 786 478 784 468 746 471 747 490 742 487 745 468 746 490 742 375 456 425 455 423 462 9 820 39 817 181 819 449 624 448 620 465 622 447 750 466 754 467 756 14 144 91 155 89 149 192 60 237 68 220 64 159 871 70 870 141 872 461 834 483 835 460 832 16 150 14 144 55 139 189 146 46 152 150 141 195 51 164 55 142 59 65 225 67 218 53 228 191 134 135 137 214 121 231 535 301 534 233 531 72 128 55 139 13 135 59 635 48 632 3 633 160 92 12 99 0 104 89 688 296 691 143 694 212 112 227 124 226 117 193 0 194 6 222 3 137 839 2 838 146 840 210 2 224 4 259 7 129 855 5 853 173 854 213 80 237 68 190 76 207 167 186 166 206 147 134 50 200 46 171 42 203 72 161 78 182 79 202 103 185 116 212 112 426 611 427 608 443 606 111 544 120 540 364 542 204 95 181 102 202 103 0 913 71 916 138 914 199 74 254 70 253 77 22 156 19 172 17 162 443 606 474 603 442 609 415 437 335 432 404 441 364 421 333 407 343 417 319 488 320 484 318 478 218 106 261 101 238 97 231 181 230 175 229 187 260 34 156 31 163 37 56 119 69 108 77 58 248 285 397 283 314 287 232 98 202 103 219 107 208 23 156 31 260 34 375 456 382 460 272 451 269 376 386 383 376 384 35 215 34 196 7 211 211 65 178 69 196 57 262 305 278 301 332 307 192 60 151 75 203 72 219 107 212 112 226 117 321 369 269 376 285 372 299 364 283 362 298 360 338 406 421 409 270 411 409 377 398 373 315 366 5 638 48 632 36 637 40 198 30 183 41 190 75 87 61 168 2 176 313 473 358 469 268 466 313 473 318 478 358 469 281 298 220 300 284 302 392 434 383 438 276 428 198 89 176 94 204 95 192 60 187 63 177 67 451 613 400 617 432 615 302 361 304 358 312 363 10 157 46 152 73 174 410 395 431 398 369 393 431 398 391 400 369 393 386 383 422 389 411 387 423 462 382 460 375 456 182 79 137 83 190 76 359 374 325 382 311 379 296 691 228 696 143 694 369 393 391 400 334 396 18 200 21 205 10 157 51 636 36 637 48 632 2 176 61 168 9 148 452 262 235 256 435 253 455 313 252 311 436 309 66 151 6 136 10 157 328 385 368 381 366 388 374 717 388 719 378 720 272 451 360 461 356 457 252 311 455 313 476 316 429 397 379 392 413 391 312 363 285 372 271 370 9 148 61 168 63 161 380 380 386 383 269 376 334 396 391 400 381 402 276 428 373 442 290 447 57 145 66 151 115 158 199 74 239 82 251 86 429 397 414 394 379 392 30 183 31 170 41 190 261 101 218 106 249 110 199 74 138 81 153 73 444 773 422 777 450 778 285 372 269 376 368 381 274 436 288 440 335 432 29 163 27 177 26 171 433 342 463 329 419 339 462 260 420 273 424 274 347 404 275 405 330 401 277 558 118 557 279 555 318 704 106 703 319 706 492 785 485 788 484 790 442 609 423 614 425 612 274 436 335 432 351 431 342 549 112 547 364 542 262 38 252 32 260 34 52 221 67 218 65 225 415 437 392 434 335 432 33 223 43 220 32 216 351 591 224 592 274 593 268 466 305 472 306 477 78 876 60 879 135 877 8 234 4 230 1 235 90 948 15 946 133 944 143 929 13 928 89 930 101 687 310 683 99 685 115 158 66 151 122 164 1 235 76 18 8 234 27 177 31 170 30 183 27 177 29 163 31 170 451 613 442 609 471 610 202 103 212 112 219 107 247 84 198 89 234 91 368 381 379 392 366 388 36 550 51 546 112 547 156 640 35 642 163 644 288 353 255 352 280 351 82 562 86 559 363 561 235 256 473 269 466 275 47 654 75 657 161 656 75 657 182 658 161 656 271 512 122 515 359 516 172 185 147 180 207 167 433 342 419 339 390 346 58 652 47 654 167 653 227 124 225 131 226 117 447 750 409 749 446 751 460 832 401 831 417 833 458 726 416 729 459 724 449 624 418 626 448 620 418 626 399 628 448 620 416 729 421 733 459 724 4 230 67 218 70 231 200 46 258 43 260 34 353 430 350 427 354 423 55 139 22 156 16 150 416 419 393 416 421 409 385 450 394 446 425 455 262 38 260 34 258 43 310 483 306 477 309 487 23 179 22 156 24 178 492 785 489 787 485 788 490 742 491 743 488 744 429 764 413 766 464 763 97 527 96 528 308 526 169 865 1 863 139 862 112 547 51 546 111 544 44 504 62 510 117 505 306 682 94 680 307 675 94 680 95 678 307 675 76 867 1 863 169 865 32 216 28 213 104 219 10 157 28 213 32 216 10 961 122 962 66 963 59 545 3 548 83 543 214 121 149 120 265 115 346 414 343 417 333 407 78 173 10 157 60 133 357 347 240 340 340 343 393 416 416 419 337 415 471 610 468 616 451 613 10 157 38 195 18 200 264 12 154 21 174 13 445 595 392 597 415 598 445 595 457 599 479 601 442 609 425 612 443 606 443 606 427 608 445 595 394 446 426 449 425 455 493 271 492 277 477 252 221 349 357 347 280 351 275 496 123 503 87 499 87 499 88 494 275 496 256 286 345 284 254 282 12 99 6 136 44 132 94 680 306 682 305 684 338 406 270 411 344 408 467 756 484 758 485 759 7 646 68 650 142 649 265 115 180 109 261 101 439 765 410 761 464 763 258 43 200 46 195 51 54 501 44 504 113 498 195 51 187 63 192 60 473 269 235 256 452 262 375 456 385 450 425 455 373 442 385 450 375 456 376 384 411 387 368 381 366 388 410 395 369 393 373 442 383 438 394 446 383 438 426 449 394 446 152 934 46 936 189 937 193 0 139 9 166 14 132 47 129 39 183 41 181 819 176 821 9 820 183 41 129 39 215 33 212 112 131 123 143 129 76 867 169 865 165 869 184 49 77 58 136 53 4 866 148 868 166 864 60 133 149 120 214 121 6 136 12 99 128 126 83 931 3 932 123 933 111 544 364 542 112 547 44 504 117 505 113 498 122 515 110 517 359 516 54 501 113 498 121 495 87 499 50 500 88 494 273 493 113 498 328 502 351 431 346 414 350 427 110 517 109 520 324 519 283 362 361 365 304 358 372 661 332 667 278 664 120 540 59 545 83 543 472 325 453 310 465 319 309 679 267 677 81 676 483 321 476 316 501 326 472 325 469 330 488 336 365 439 363 435 276 428 439 765 431 767 410 761 36 237 8 234 37 29 411 769 422 777 444 773 71 114 69 108 56 119 322 464 303 480 297 481 57 145 6 136 66 151 268 466 322 464 296 468 238 239 251 244 362 242 322 464 360 461 272 451 264 12 174 13 210 2 213 80 190 76 247 84 321 369 285 372 312 363 282 426 354 423 349 418 276 428 277 433 336 424 221 349 240 340 357 347 334 396 328 385 369 393 346 414 333 407 291 410 308 367 302 361 312 363 270 411 393 416 337 415 264 12 259 7 244 17 196 57 257 52 256 61 469 330 472 325 465 319 173 35 155 30 241 28 80 563 82 562 363 561 373 442 375 456 272 451 276 428 383 438 373 442 133 944 15 946 38 942 110 517 324 519 325 518 235 138 191 134 236 127 28 213 25 210 100 217 446 281 397 283 235 256 439 765 407 770 431 767 407 403 391 400 431 398 98 681 310 683 309 679 422 389 386 383 408 378 438 255 477 252 462 260 495 782 489 787 492 785 482 738 500 735 499 740 467 756 485 759 486 760 304 358 315 366 312 363 323 522 324 519 108 521 108 521 324 519 109 520 435 253 236 249 377 251 420 273 440 267 388 276</p>
        </triangles>
      </mesh>
      <extra>
        <technique profile="OpenCOLLADAMaya">
          <originalMayaNodeId>upper_arm_roll_MShape</originalMayaNodeId>
          <double_sided>1</double_sided>
        </technique>
      </extra>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="VisualSceneNode" name="upper_arm_roll1a_med_hard">
      <node id="upper_arm_roll_M" name="upper_arm_roll_M" type="NODE">
        <translate sid="translate">0 0 0</translate>
        <rotate sid="rotateZ">0 0 1 0</rotate>
        <rotate sid="rotateY">0 1 0 0</rotate>
        <rotate sid="rotateX">1 0 0 0</rotate>
        <scale sid="scale">0.1 0.1 0.1</scale>
        <instance_geometry url="#upper_arm_roll_MShape">
          <bind_material>
            <technique_common>
              <instance_material symbol="lambert3SG" target="#lambert3">
                <bind_vertex_input semantic="TEX0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="TEX1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADAMaya">
            <originalMayaNodeId>upper_arm_roll_M</originalMayaNodeId>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#VisualSceneNode"/>
  </scene>
</COLLADA>
