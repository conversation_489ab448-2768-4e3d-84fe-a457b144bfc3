<?xml version="1.0" ?>
<!-- ============================================================================================================== -->
<!-- |    This document was autogenerated by xacro from ridgeback/ridgeback_description/urdf/ridgeback.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                                            | -->
<!-- ============================================================================================================== -->
<robot name="ridgeback">
  <material name="dark_grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="light_grey">
    <color rgba="0.4 0.4 0.4 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="0.9 0.9 0.9 1.0"/>
  </material>
  <material name="yellow">
    <color rgba="0.8 0.8 0.0 1.0"/>
  </material>
  <material name="black">
    <color rgba="0.15 0.15 0.15 1.0"/>
  </material>
  <link name="front_rocker_link">
    <visual>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/rocker.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <inertial>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <mass value="10.267"/>
      <inertia ixx="0.0288" ixy="2.20484e-6" ixz="-1.3145e-5" iyy="0.4324" iyz="1.8944e-3" izz="0.4130"/>
    </inertial>
  </link>
  <joint name="front_rocker" type="fixed">
    <parent link="axle_link"/>
    <child link="front_rocker_link"/>
    <origin rpy="0 0 0" xyz="0.319 0 0"/>
    <limit effort="0" lower="-0.08726" upper="0.08726" velocity="0"/>
  </joint>
  <link name="front_left_wheel_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/wheel.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.079" radius="0.0759"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2.3"/>
      <inertia ixx="3.3212e-3" ixy="0" ixz="0" iyy="6.6424e-3" iyz="0" izz="3.3212e-3"/>
    </inertial>
  </link>
  <joint name="front_left_wheel" type="fixed">
    <parent link="front_rocker_link"/>
    <child link="front_left_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 0.27549999999999997 0"/>
  </joint>
  <link name="front_right_wheel_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/wheel.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.079" radius="0.0759"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2.3"/>
      <inertia ixx="3.3212e-3" ixy="0" ixz="0" iyy="6.6424e-3" iyz="0" izz="3.3212e-3"/>
    </inertial>
  </link>
  <joint name="front_right_wheel" type="fixed">
    <parent link="front_rocker_link"/>
    <child link="front_right_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 -0.27549999999999997 0"/>
  </joint>
  <link name="rear_rocker_link">
    <visual>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/rocker.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <inertial>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
      <mass value="10.267"/>
      <inertia ixx="0.0288" ixy="2.20484e-6" ixz="-1.3145e-5" iyy="0.4324" iyz="1.8944e-3" izz="0.4130"/>
    </inertial>
  </link>
  <joint name="rear_rocker" type="fixed">
    <parent link="axle_link"/>
    <child link="rear_rocker_link"/>
    <origin rpy="0 0 0" xyz="-0.319 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" velocity="0"/>
  </joint>
  <link name="rear_left_wheel_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/wheel.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.079" radius="0.0759"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2.3"/>
      <inertia ixx="3.3212e-3" ixy="0" ixz="0" iyy="6.6424e-3" iyz="0" izz="3.3212e-3"/>
    </inertial>
  </link>
  <joint name="rear_left_wheel" type="fixed">
    <parent link="rear_rocker_link"/>
    <child link="rear_left_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 0.27549999999999997 0"/>
  </joint>
  <link name="rear_right_wheel_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/wheel.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.079" radius="0.0759"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2.3"/>
      <inertia ixx="3.3212e-3" ixy="0" ixz="0" iyy="6.6424e-3" iyz="0" izz="3.3212e-3"/>
    </inertial>
  </link>
  <joint name="rear_right_wheel" type="fixed">
    <parent link="rear_rocker_link"/>
    <child link="rear_right_wheel_link"/>
    <origin rpy="0 0 0" xyz="0 -0.27549999999999997 0"/>
  </joint>
  <link name="base_link"/>
  <joint name="base_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="chassis_link"/>
  </joint>
  <link name="chassis_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/body.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/body-collision.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0.012 0.002 0.067"/>
      <mass value="165.304"/>
      <inertia ixx="4.4744" ixy="0.03098" ixz="0.003647" iyy="7.1624" iyz="0.1228" izz="4.6155"/>
    </inertial>
  </link>
  <joint name="right_side_cover_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="right_side_cover_link"/>
  </joint>
  <joint name="left_side_cover_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="left_side_cover_link"/>
  </joint>
  <link name="left_side_cover_link">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/side-cover.stl"/>
      </geometry>
      <material name="yellow"/>
    </visual>
  </link>
  <link name="right_side_cover_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/side-cover.stl"/>
      </geometry>
      <material name="yellow"/>
    </visual>
  </link>
  <joint name="front_cover_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="front_cover_link"/>
  </joint>
  <link name="front_cover_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/end-cover.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
  </link>
  <joint name="rear_cover_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="rear_cover_link"/>
  </joint>
  <link name="rear_cover_link">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/end-cover.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
  </link>
  <joint name="front_lights_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="front_lights_link"/>
  </joint>
  <joint name="rear_lights_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="rear_lights_link"/>
  </joint>
  <link name="front_lights_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/lights.stl"/>
      </geometry>
      <material name="white"/>
    </visual>
  </link>
  <link name="rear_lights_link">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/lights.stl"/>
      </geometry>
      <material name="red"/>
    </visual>
  </link>
  <joint name="top_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="chassis_link"/>
    <child link="top_link"/>
  </joint>
  <link name="top_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/top.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/top.stl"/>
      </geometry>
    </collision>
  </link>
  <joint name="axle_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.05"/>
    <parent link="chassis_link"/>
    <child link="axle_link"/>
  </joint>
  <link name="axle_link">
    <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/base/axle.stl"/>
      </geometry>
      <material name="black"/>
    </visual>
  </link>
  <link name="imu_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1e-09" ixy="0.0" ixz="0.0" iyy="1e-09" iyz="0.0" izz="1e-09"/>
    </inertial>
  </link>
  <joint name="imu_joint" type="fixed">
    <parent link="chassis_link"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0.2085 -0.2902 0.1681"/>
  </joint>
  <link name="mid_mount"/>
  <joint name="mid_mount_joint" type="fixed">
    <parent link="base_link"/>
    <child link="mid_mount"/>
    <origin rpy="0 0 0" xyz="0 0 0.28"/>
  </joint>
  <joint name="arm_mount_joint" type="fixed">
    <parent link="mid_mount"/>
    <child link="ur_arm_base_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <!--
    Base UR robot series xacro macro.

    NOTE: this is NOT a URDF. It cannot directly be loaded by consumers
    expecting a flattened '.urdf' file. See the top-level '.xacro' for that
    (but note: that .xacro must still be processed by the xacro command).

    For use in '.launch' files: use one of the 'load_urX.launch' convenience
    launch files.

    This file models the base kinematic chain of a UR robot, which then gets
    parameterised by various configuration files to convert it into a UR3(e),
    UR5(e), UR10(e) or UR16e.

    NOTE: the default kinematic parameters (ie: link lengths, frame locations,
    offets, etc) do not correspond to any particular robot. They are defaults
    only. There WILL be non-zero offsets between the Forward Kinematics results
    in TF (ie: robot_state_publisher) and the values reported by the Teach
    Pendant.

    For accurate (and robot-specific) transforms, the 'kinematics_parameters_file'
    parameter MUST point to a .yaml file containing the appropriate values for
    the targetted robot.

    If using the UniversalRobots/Universal_Robots_ROS_Driver, follow the steps
    described in the readme of that repository to extract the kinematic
    calibration from the controller and generate the required .yaml file.

    Main author of the migration to yaml configs: Ludovic Delval.

    Contributors to previous versions (in no particular order):

     - Felix Messmer
     - Kelsey Hawkins
     - Wim Meeussen
     - Shaun Edwards
     - Nadia Hammoudeh Garcia
     - Dave Hershberger
     - G. vd. Hoorn
     - Philip Long
     - Dave Coleman
     - Miguel Prada
     - Mathias Luedtke
     - Marcel Schnirring
     - Felix von Drigalski
     - Felix Exner
     - Jimmy Da Silva
     - Ajit Krisshna N L
     - Muhammad Asif Rana
  -->
  <!-- links: main serial chain -->
  <link name="ur_arm_base_link"/>
  <link name="ur_arm_base_link_inertia">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/base.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
    </inertial>
  </link>
  <link name="ur_arm_shoulder_link">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/shoulder.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.014972352344389999" ixy="0.0" ixz="0.0" iyy="0.014972352344389999" iyz="0.0" izz="0.01040625"/>
    </inertial>
  </link>
  <link name="ur_arm_upper_arm_link">
    <visual>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.13585"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/upperarm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.13585"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.393"/>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.2125 0.0 0.136"/>
      <inertia ixx="0.1338857818623325" ixy="0.0" ixz="0.0" iyy="0.1338857818623325" iyz="0.0" izz="0.0151074"/>
    </inertial>
  </link>
  <link name="ur_arm_forearm_link">
    <visual>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.0165"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/forearm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.0165"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="2.275"/>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.196125 0.0 0.0165"/>
      <inertia ixx="0.03121679102890359" ixy="0.0" ixz="0.0" iyy="0.03121679102890359" iyz="0.0" izz="0.004095"/>
    </inertial>
  </link>
  <link name="ur_arm_wrist_1_link">
    <visual>
      <!-- TODO: Move this to a parameter -->
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.093"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist1.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.093"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0020138887777775" ixy="0.0" ixz="0.0" iyy="0.0020138887777775" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <link name="ur_arm_wrist_2_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.095"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist2.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.095"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0018310388509175" ixy="0.0" ixz="0.0" iyy="0.0018310388509175" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <link name="ur_arm_wrist_3_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.0818"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/visual/wrist3.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.0818"/>
      <geometry>
        <mesh filename="package://ocs2_robotic_assets/resources/mobile_manipulator/ridgeback_ur5/meshes/ur5/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1879"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 -0.01525"/>
      <inertia ixx="8.062472608343e-05" ixy="0.0" ixz="0.0" iyy="8.062472608343e-05" iyz="0.0" izz="0.0001321171875"/>
    </inertial>
  </link>
  <!-- joints: main serial chain -->
  <joint name="ur_arm_base_link-base_link_inertia" type="fixed">
    <parent link="ur_arm_base_link"/>
    <child link="ur_arm_base_link_inertia"/>
    <!-- 'base_link' is REP-103 aligned (so X+ forward), while the internal
           frames of the robot/controller have X+ pointing backwards.
           Use the joint between 'base_link' and 'base_link_inertia' (a dummy
           link/frame) to introduce the necessary rotation over Z (of pi rad).
      -->
    <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
  </joint>
  <joint name="ur_arm_shoulder_pan_joint" type="revolute">
    <parent link="ur_arm_base_link_inertia"/>
    <child link="ur_arm_shoulder_link"/>
    <origin rpy="0 0 0" xyz="0 0 0.089159"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="ur_arm_shoulder_lift_joint" type="revolute">
    <parent link="ur_arm_shoulder_link"/>
    <child link="ur_arm_upper_arm_link"/>
    <origin rpy="1.570796327 0 0" xyz="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="ur_arm_elbow_joint" type="revolute">
    <parent link="ur_arm_upper_arm_link"/>
    <child link="ur_arm_forearm_link"/>
    <origin rpy="0 0 0" xyz="-0.425 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="ur_arm_wrist_1_joint" type="revolute">
    <parent link="ur_arm_forearm_link"/>
    <child link="ur_arm_wrist_1_link"/>
    <origin rpy="0 0 0" xyz="-0.39225 0 0.10915"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="ur_arm_wrist_2_joint" type="revolute">
    <parent link="ur_arm_wrist_1_link"/>
    <child link="ur_arm_wrist_2_link"/>
    <origin rpy="1.570796327 0 0" xyz="0 -0.09465 -1.941303950897609e-11"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="ur_arm_wrist_3_joint" type="revolute">
    <parent link="ur_arm_wrist_2_link"/>
    <child link="ur_arm_wrist_3_link"/>
    <origin rpy="1.570796326589793 3.141592653589793 3.141592653589793" xyz="0 0.0823 -1.688001216681175e-11"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="3.141592653589793"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <!-- ROS-Industrial 'base' frame: base_link to UR 'Base' Coordinates transform -->
  <link name="ur_arm_base"/>
  <joint name="ur_arm_base_link-base_fixed_joint" type="fixed">
    <!-- Note the rotation over Z of pi radians: as base_link is REP-103
           aligned (ie: has X+ forward, Y+ left and Z+ up), this is needed
           to correctly align 'base' with the 'Base' coordinate system of
           the UR controller.
      -->
    <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
    <parent link="ur_arm_base_link"/>
    <child link="ur_arm_base"/>
  </joint>
  <!-- ROS-Industrial 'flange' frame: attachment point for EEF models -->
  <link name="ur_arm_flange"/>
  <joint name="ur_arm_wrist_3-flange" type="fixed">
    <parent link="ur_arm_wrist_3_link"/>
    <child link="ur_arm_flange"/>
    <origin rpy="0 -1.5707963267948966 -1.5707963267948966" xyz="0 0 0"/>
  </joint>
  <!-- ROS-Industrial 'tool0' frame: all-zeros tool frame -->
  <link name="ur_arm_tool0"/>
  <joint name="ur_arm_flange-tool0" type="fixed">
    <!-- default toolframe: X+ left, Y+ up, Z+ front -->
    <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
    <parent link="ur_arm_flange"/>
    <child link="ur_arm_tool0"/>
  </joint>
</robot>
