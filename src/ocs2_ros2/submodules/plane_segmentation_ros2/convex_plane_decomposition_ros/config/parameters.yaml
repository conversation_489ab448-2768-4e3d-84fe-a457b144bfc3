convex_plane_decomposition_ros_node:
  ros__parameters:
    preprocessing:
      resolution: 0.04    # Resampling resolution, set negative to skip, requires inpainting to be used
      kernelSize: 3       # Kernel size of the median filter, either 3 or 5
      numberOfRepeats: 1  # Number of times to apply the same filter
    sliding_window_plane_extractor:
      kernel_size: 3                                        # Size of the sliding window patch used for normal vector calculation and planarity detection. Should be an odd number and at least 3.
      planarity_opening_filter: 0                           # [#] Apply opening filter (erosion -> dilation) to planarity detection by this amount of pixels
      plane_inclination_threshold_degrees: 30.0             # [deg] Maximum allowed angle between the surface normal and the world-z direction for a patch
      local_plane_inclination_threshold_degrees: 35.0       # [deg] Maximum allowed angle between the surface normal and the world-z direction for a cell
      plane_patch_error_threshold: 0.02                     # [m] The allowed root-mean-squared deviation from the plane fitted to the sliding window.
      min_number_points_per_label: 4                        # [#] Labels with less points assigned to them are discarded
      connectivity: 4                                       # Label kernel connectivity. 4 or 8 (cross or box)
      include_ransac_refinement: true                       # Enable RANSAC refinement if the plane is not globally fitting to the assigned points.
      global_plane_fit_distance_error_threshold: 0.025      # [m] Allowed maximum distance from a point to the plane. If any point violates this, RANSAC is triggered
      global_plane_fit_angle_error_threshold_degrees: 25.0  # [deg] Allowed normal vector deviation for a point w.r.t. the plane normal. If any point violates this, RANSAC is triggered
    ransac_plane_refinement:
      probability: 0.001      # Probability to miss the largest candidate shape. A lower probability provides a higher reliability and determinism at the cost of longer running times
      min_points: 4.0           # This minimum number of points per plane. Controls the termination of the algorithm.
      epsilon: 0.025          # Maximum distance to plane
      cluster_epsilon: 0.041  # [m] Set maximum Euclidean distance between points to be clustered. Two points are connected if separated by a distance of at most 2*sqrt(2)*cluster_epsilon = 2.828 * cluster_epsilon. Set this higher than resolution
      normal_threshold: 25.0  # [deg] Set maximum normal deviation between cluster surface_normal and point normal.
    contour_extraction:
      marginSize: 1    # Size of the kernel creating the boundary offset. In number of (sub) pixels.
    postprocessing:
      extracted_planes_height_offset: 0.0   # Added offset in Z direction for the full map to compensate for the location of the foot frame w.r.t. the contact point
      nonplanar_height_offset: 0.02         # Added offset in Z direction for non-planar cells of the map. (Additional to extracted_planes_height_offset)
      nonplanar_horizontal_offset: 1        # Added offset in XY direction for non-planar cells of the map. In number of pixels.
      smoothing_dilation_size: 0.2          # Half the width of the dilation used before the smooth layer [m]
      smoothing_box_kernel_size: 0.1        # Half the width of the box kernel used for the smooth layer [m]
      smoothing_gauss_kernel_size: 0.05     # Half the width of the Gaussian kernel used for the smooth layer [m]