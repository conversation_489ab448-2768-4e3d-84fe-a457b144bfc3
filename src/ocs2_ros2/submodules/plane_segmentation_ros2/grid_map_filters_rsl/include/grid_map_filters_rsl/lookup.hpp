/**
 * @file        lookup.hpp
 * @authors     <PERSON>, <PERSON><PERSON>
 * @date        04.08, 2021
 * @affiliation ETH RSL
 * @brief       Extracting information from a gridmap
 */

#pragma once

// grid map.
#include <grid_map_core/grid_map_core.hpp>

namespace grid_map {
namespace lookup {

struct LookupResult {
  bool isValid{false};
  float value{0.0};
  Position position{0.0, 0.0};
};

/**
 * Finds the maximum value between two points in a map
 *
 * @param position1 : starting point of the lookup line.
 * @param position2 : ending point of the lookup line.
 * @param gridMap : map object for map information.
 * @param data : map data to find the maximum in.
 * @return validity, value, and location of the maximum. A result is flagged as
 * invalid if there are no finite values found.
 */
LookupResult maxValueBetweenLocations(const Position& position1,
                                      const Position& position2,
                                      const GridMap& gridMap,
                                      const Matrix& data);

/**
 * Returns all values along a line between two points in a map
 *
 * @param position1 : starting point of the lookup line.
 * @param position2 : ending point of the lookup line.
 * @param gridMap : map object for map information.
 * @param data : map data to get the values from.
 * @return vector of all points generated by a line iteration
 */
std::vector<Position3> valuesBetweenLocations(const Position& position1,
                                              const Position& position2,
                                              const GridMap& gridMap,
                                              const Matrix& data);

/**
 * Project a point to inside the given gridmap with a specified margin
 *
 * @param gridMap : map object for map information.
 * @param position : point to project to map
 * @param margin : (minimum) distance from the map border after projection
 * @return Projected position
 */
Position projectToMapWithMargin(const GridMap& gridMap,
                                const Position& position, double margin = 1e-6);

}  // namespace lookup
}  // namespace grid_map
