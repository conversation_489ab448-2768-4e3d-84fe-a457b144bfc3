<?xml version="1.0"?>
<package format="2">
    <name>grid_map_filters_rsl</name>
    <version>0.1.0</version>
    <description>Extension of grid map filters package with op-cv filters and others</description>
    <maintainer email="fabian.j<PERSON><PERSON>@mavt.ethz.ch"><PERSON></maintainer>

    <author email="fabian.j<PERSON>@mavt.ethz.ch"><PERSON></author>

    <license>MIT</license>

    <buildtool_depend>ament_cmake</buildtool_depend>
    <build_depend>cmake_clang_tools</build_depend>

    <depend>grid_map_cv</depend>
    <depend>grid_map_core</depend>

    <test_depend>gtest</test_depend>
    <test_depend>cmake_code_coverage</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>
</package>
