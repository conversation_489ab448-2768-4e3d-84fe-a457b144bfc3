<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>grid_map_sdf</name>
  <version>2.2.1</version>
  <description>Generates signed distance fields from grid maps.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Yo<PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>
  <url type="website">http://github.com/anybotics/grid_map</url>
  <url type="bugtracker">http://github.com/anybotics/grid_map/issues</url>
  <author email="<EMAIL>"><PERSON><PERSON><PERSON></author>
  <author email="<EMAIL>">P<PERSON>ter Fankhauser</author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>grid_map_cmake_helpers</build_depend>

  <depend>grid_map_core</depend>
  <depend>libpcl-all-dev</depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
