/******************************************************************************
Copyright (c) 2020, Farbod Farshidian. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
******************************************************************************/

#include <ocs2_core/cost/QuadraticStateInputCost.h>

namespace ocs2 {
    QuadraticStateInputCost::QuadraticStateInputCost(matrix_t Q, matrix_t R, matrix_t P /* = matrix_t() */)
        : Q_(std::move(Q)), R_(std::move(R)), P_(std::move(P)) {
        if (P_.size() > 0) {
            assert(P_.rows() == R_.rows());
            assert(P_.cols() == Q_.rows());
        }
    }


    QuadraticStateInputCost *QuadraticStateInputCost::clone() const {
        return new QuadraticStateInputCost(*this);
    }


    scalar_t QuadraticStateInputCost::getValue(scalar_t time, const vector_t &state, const vector_t &input,
                                               const TargetTrajectories &targetTrajectories,
                                               const PreComputation &) const {
        vector_t stateDeviation, inputDeviation;
        std::tie(stateDeviation, inputDeviation) = getStateInputDeviation(time, state, input, targetTrajectories);

        if (P_.size() == 0) {
            return 0.5 * stateDeviation.dot(Q_ * stateDeviation) + 0.5 * inputDeviation.dot(R_ * inputDeviation);
        } else {
            return 0.5 * stateDeviation.dot(Q_ * stateDeviation) + 0.5 * inputDeviation.dot(R_ * inputDeviation) +
                   inputDeviation.dot(P_ * stateDeviation);
        }
    }


    ScalarFunctionQuadraticApproximation QuadraticStateInputCost::getQuadraticApproximation(
        const scalar_t time, const vector_t &state,
        const vector_t &input,
        const TargetTrajectories &targetTrajectories,
        const PreComputation &) const {
        auto [stateDeviation, inputDeviation] = getStateInputDeviation(time, state, input, targetTrajectories);

        ScalarFunctionQuadraticApproximation L;
        L.dfdxx = Q_;
        L.dfduu = R_;
        L.dfdx.noalias() = Q_ * stateDeviation;
        L.dfdu.noalias() = R_ * inputDeviation;
        L.f = 0.5 * stateDeviation.dot(L.dfdx) + 0.5 * inputDeviation.dot(L.dfdu);

        if (P_.size() == 0) {
            L.dfdux.setZero(input.size(), state.size());
        } else {
            const vector_t pDeviation = P_ * stateDeviation;
            L.f += inputDeviation.dot(pDeviation);
            L.dfdu += pDeviation;
            L.dfdx.noalias() += P_.transpose() * inputDeviation;
            L.dfdux = P_;
        }

        return L;
    }


    std::pair<vector_t, vector_t> QuadraticStateInputCost::getStateInputDeviation(
        const scalar_t time, const vector_t &state, const vector_t &input,
        const TargetTrajectories &targetTrajectories) const {
        const vector_t stateDeviation = state - targetTrajectories.getDesiredState(time);
        const vector_t inputDeviation = input - targetTrajectories.getDesiredInput(time);
        return {stateDeviation, inputDeviation};
    }
} // namespace ocs2
