/******************************************************************************
Copyright (c) 2020, Farbod Farshidian. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
******************************************************************************/

#include <ocs2_core/cost/StateCostCollection.h>

namespace ocs2 {
    StateCostCollection::StateCostCollection(const StateCostCollection &other) = default;


    StateCostCollection *StateCostCollection::clone() const {
        return new StateCostCollection(*this);
    }


    scalar_t StateCostCollection::getValue(const scalar_t time, const vector_t &state,
                                           const TargetTrajectories &targetTrajectories,
                                           const PreComputation &preComp) const {
        scalar_t cost = 0.0;

        // accumulate cost terms
        for (const auto &costTerm: this->terms_) {
            if (costTerm->isActive(time)) {
                cost += costTerm->getValue(time, state, targetTrajectories, preComp);
            }
        }

        return cost;
    }


    ScalarFunctionQuadraticApproximation StateCostCollection::getQuadraticApproximation(
        scalar_t time, const vector_t &state,
        const TargetTrajectories &targetTrajectories,
        const PreComputation &preComp) const {
        const auto firstActive =
                std::find_if(terms_.begin(), terms_.end(), [time](const std::unique_ptr<StateCost> &costTerm) {
                    return costTerm->isActive(time);
                });

        // No active terms (or terms is empty).
        if (firstActive == terms_.end()) {
            return ScalarFunctionQuadraticApproximation::Zero(static_cast<int>(state.rows()));
        }

        // Initialize with first active term, accumulate potentially other active terms.
        auto cost = (*firstActive)->getQuadraticApproximation(time, state, targetTrajectories, preComp);
        std::for_each(std::next(firstActive), terms_.end(), [&](const std::unique_ptr<StateCost> &costTerm) {
            if (costTerm->isActive(time)) {
                const auto costTermApproximation = costTerm->getQuadraticApproximation(
                    time, state, targetTrajectories, preComp);
                cost.f += costTermApproximation.f;
                cost.dfdx += costTermApproximation.dfdx;
                cost.dfdxx += costTermApproximation.dfdxx;
            }
        });

        // Make sure that input derivatives are empty
        cost.dfdu = vector_t();
        cost.dfduu = matrix_t();
        cost.dfdux = matrix_t();

        return cost;
    }
} // namespace ocs2
