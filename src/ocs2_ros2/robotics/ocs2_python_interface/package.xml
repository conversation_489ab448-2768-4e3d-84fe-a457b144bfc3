<?xml version="1.0"?>
<package format="2">
    <name>ocs2_python_interface</name>
    <version>0.0.0</version>
    <description>The ocs2_python_interface package</description>

    <maintainer email="<EMAIL>">Farbod Farshidian</maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

    <license>TODO</license>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <build_depend>cmake_modules</build_depend>
    <build_depend>cmake_clang_tools</build_depend>

    <depend>ocs2_ddp</depend>
    <depend>ocs2_robotic_tools</depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>

</package>
