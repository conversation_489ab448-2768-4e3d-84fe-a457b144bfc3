/******************************************************************************
Copyright (c) 2020, Farbod Farshidian. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
******************************************************************************/

#include <ocs2_robotic_tools/common/RotationTransforms.h>

namespace ocs2 {

    Eigen::Quaternion<ad_scalar_t> matrixToQuaternion(const Eigen::Matrix<ad_scalar_t, 3, 3> &R) {
        ad_scalar_t t1, t2, t;
        ad_scalar_t x1, x2, x;
        ad_scalar_t y1, y2, y;
        ad_scalar_t z1, z2, z;
        ad_scalar_t w1, w2, w;

        t1 = CondExpGt(R(0, 0), R(1, 1), 1 + R(0, 0) - R(1, 1) - R(2, 2), 1 - R(0, 0) + R(1, 1) - R(2, 2));
        t2 = CondExpLt(R(0, 0), -R(1, 1), 1 - R(0, 0) - R(1, 1) + R(2, 2), 1 + R(0, 0) + R(1, 1) + R(2, 2));
        t = CondExpLt(R(2, 2), ad_scalar_t(0.0), t1, t2);

        x1 = CondExpGt(R(0, 0), R(1, 1), t, R(1, 0) + R(0, 1));
        x2 = CondExpLt(R(0, 0), -R(1, 1), R(0, 2) + R(2, 0), R(2, 1) - R(1, 2));
        x = CondExpLt(R(2, 2), ad_scalar_t(0.0), x1, x2);

        y1 = CondExpGt(R(0, 0), R(1, 1), R(1, 0) + R(0, 1), t);
        y2 = CondExpLt(R(0, 0), -R(1, 1), R(2, 1) + R(1, 2), R(0, 2) - R(2, 0));
        y = CondExpLt(R(2, 2), ad_scalar_t(0.0), y1, y2);

        z1 = CondExpGt(R(0, 0), R(1, 1), R(0, 2) + R(2, 0), R(2, 1) + R(1, 2));
        z2 = CondExpLt(R(0, 0), -R(1, 1), t, R(1, 0) - R(0, 1));
        z = CondExpLt(R(2, 2), ad_scalar_t(0.0), z1, z2);

        w1 = CondExpGt(R(0, 0), R(1, 1), R(2, 1) - R(1, 2), R(0, 2) - R(2, 0));
        w2 = CondExpLt(R(0, 0), -R(1, 1), R(1, 0) - R(0, 1), t);
        w = CondExpLt(R(2, 2), ad_scalar_t(0.0), w1, w2);

        Eigen::Matrix<ad_scalar_t, 4, 1> q({x, y, z, w});
        q *= 0.5 / sqrt(t);

        return Eigen::Quaternion(q(3), q(0), q(1), q(2));
    }

    namespace {
        // helper functions to select the right modulo
        template<typename SCALAR_T>
        SCALAR_T scalarMod(SCALAR_T, SCALAR_T);

        template<>
        float scalarMod<float>(float x, float y) {
            return fmodf(x, y);
        }

        template<>
        double scalarMod<double>(double x, double y) {
            return fmod(x, y);
        }
    } // namespace

    scalar_t moduloAngleWithReference(scalar_t x, scalar_t reference) {
        const scalar_t ub = reference + M_PI; // upper bound
        const scalar_t lb = reference - M_PI; // lower bound

        if (x > ub) {
            x = lb + scalarMod(x - lb, 2.0 * M_PI);
        } else if (x < lb) {
            x = ub - scalarMod(ub - x, 2.0 * M_PI);
        }

        return x;
    }
} // namespace ocs2
