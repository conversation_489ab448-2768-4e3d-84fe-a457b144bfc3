<?xml version="1.0"?>

<package format="2">

    <name>ocs2_ros_interfaces</name>
    <version>0.0.0</version>
    <description>The ocs2 communication interfaces to ROS</description>

    <license>TODO</license>
    <maintainer email="<EMAIL>">Farbod Farshidian</maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <depend>rclcpp</depend>
    <depend>rclcpp_lifecycle</depend>
    <depend>ocs2_msgs</depend>
    <depend>ocs2_core</depend>
    <depend>ocs2_mpc</depend>
    <depend>std_msgs</depend>
    <depend>visualization_msgs</depend>
    <depend>geometry_msgs</depend>
    <depend>interactive_markers</depend>
    <exec_depend>rqt_multiplot</exec_depend>
    <exec_depend>ros2launch</exec_depend>

    <test_depend>ament_lint_auto</test_depend>
    <test_depend>ament_lint_common</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>

</package>
