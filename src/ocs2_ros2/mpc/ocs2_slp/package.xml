<?xml version="1.0"?>
<package format="2">
    <name>ocs2_slp</name>
    <version>0.0.0</version>
    <description>A numerical implementation of a first order primal-dual MPC basee on PIPG.</description>

    <maintainer email="<EMAIL>">Farbod Farshidian</maintainer>
    <maintainer email="<EMAIL>">Zhengyu Fu</maintainer>

    <license>BSD3</license>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <depend>ocs2_mpc</depend>
    <depend>ocs2_qp_solver</depend>

    <test_depend>ament_cmake_gtest</test_depend>
    <test_depend>ament_lint_auto</test_depend>
    <test_depend>ament_lint_common</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>
</package>