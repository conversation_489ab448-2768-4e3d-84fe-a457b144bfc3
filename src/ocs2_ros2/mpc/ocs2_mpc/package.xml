<?xml version="1.0"?>
<package format="2">
    <name>ocs2_mpc</name>
    <version>0.0.0</version>
    <description>Model Predictive Control for SLQ</description>

    <maintainer email="<EMAIL>">Farbod Farshidian</maintainer>
    <maintainer email="j<PERSON><PERSON>@ethz.ch"><PERSON></maintainer>
    <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

    <license>BSD3</license>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <depend>ocs2_oc</depend>

    <test_depend>ament_lint_auto</test_depend>
    <test_depend>ament_lint_common</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>

</package>



