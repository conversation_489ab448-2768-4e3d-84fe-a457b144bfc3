<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>realsense2_camera_msgs</name>
  <version>4.55.1</version>
  <description>RealSense camera_msgs package containing realsense camera messages definitions</description>
  <maintainer email="<EMAIL>">LibRealSense ROS Team</maintainer>
  <license>Apache License 2.0</license>
  
  <url type="website">http://www.ros.org/wiki/RealSense</url>
  <url type="bugtracker">https://github.com/intel-ros/realsense/issues</url>

  <author email="<EMAIL>">LibRealSense ROS Team</author>
 
  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>
  <build_depend>builtin_interfaces</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>
  <exec_depend>builtin_interfaces</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>

  <test_depend>ament_lint_common</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
