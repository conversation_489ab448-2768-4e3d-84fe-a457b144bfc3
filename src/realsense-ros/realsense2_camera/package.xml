<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>realsense2_camera</name>
  <version>4.55.1</version>
  <description>RealSense camera package allowing access to Intel D400 3D cameras</description>
  <maintainer email="<EMAIL>">LibRealSense ROS Team</maintainer>
  <license>Apache License 2.0</license>
  
  <url type="website">http://www.ros.org/wiki/RealSense</url>
  <url type="bugtracker">https://github.com/intel-ros/realsense/issues</url>

  <author email="<EMAIL>">LibRealSense ROS Team</author>
 
  <buildtool_depend>ament_cmake</buildtool_depend>
  <depend>eigen</depend>
  <depend>builtin_interfaces</depend>
  <depend>cv_bridge</depend>
  <depend>image_transport</depend>
  <depend>librealsense2</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>realsense2_camera_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>diagnostic_updater</depend>
  <test_depend condition="$ROS_DISTRO != foxy">ament_cmake_gtest</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">launch_testing</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">ament_cmake_pytest</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">launch_pytest</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">sensor_msgs_py</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">python3-numpy</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">python3-tqdm</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">sensor_msgs_py</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">python3-requests</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">tf2_ros_py</test_depend>
  <test_depend condition="$ROS_DISTRO != foxy">ros2topic</test_depend>
  
  <exec_depend>launch_ros</exec_depend>
  <build_depend>ros_environment</build_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
