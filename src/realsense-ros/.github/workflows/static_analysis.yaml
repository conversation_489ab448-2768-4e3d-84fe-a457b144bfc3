name: static_analysis

on:
  push:
    branches: ['**']
  pull_request:
    branches: ['**']

permissions: read-all

jobs:
  cppcheck:
    name: cppcheck
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3

      - name: Install 
        shell: bash
        run: |
          sudo apt-get update;
          sudo apt-get install -qq cppcheck;
        
      - name: Cppcheck Run
        shell: bash
        #Selected run options:
        #  ./xxx            :   Folders to scan
        #  --quiet          :   Don't show current checked configuration in log
        #  --std=c++11      :   Use C++11 standard (default but worth mentioning)
        #  --xml            :   Output in XML format
        #  -j4              :   Run parallel jobs for a faster scan. current HW is 2 core (https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners) using 4 to future proof
        #  --enable         :   Additional check to run. options are [all, warning, style, performance, protability, information, unusedFunction, missingInclude]
        #  -I               :   Include directories
        #  -i               :   Ignore directories. Ignore third-party libs that we don't want to check
        #  --suppress       :   Don't issue errors about files matching the expression (when -i for folders is not enough)
        #  --force          :   Check all configurations, takes a very long time (~2 hours) and did not find additional errors. Removed.
        #  --max-configs=6  :   Using less configuration permutations (default is 12) to reduce run time. Detects less errors. Removed.
        #  -Dxxx            :   preprocessor configuration to use. Relevant flags taken from build on Ubuntu.
        run: > 
          cppcheck ./realsense2_camera/src ./realsense2_camera/include ./realsense2_camera/tools
          --quiet --std=c++11 --xml -j4 --enable=warning
          -I./realsense2_camera/include -I./realsense2_camera/tools
          &> cppcheck_run.log

      - name: Cppcheck Result
        shell: bash
        run: |
          ERROR_COUNT=$(grep cppcheck_run.log -e "severity=\"error\"" -c) || ERROR_COUNT=0
          EXPECTED_ERROR_COUNT=0
          if [ $ERROR_COUNT -eq $EXPECTED_ERROR_COUNT ]
          then
            echo "cppcheck_run succeeded, found" $ERROR_COUNT "errors, as expected"
            exit 0
          elif [ $ERROR_COUNT -lt $EXPECTED_ERROR_COUNT ]
          then
            echo "cppcheck_run ---> SUCCEEDED <--- but found" $ERROR_COUNT "errors when expecting" $EXPECTED_ERROR_COUNT
            echo "Please update EXPECTED_ERROR_COUNT var in .github/workflows/static_analysis.yaml to the new lower value"
          else
            echo "cppcheck_run ---> FAILED <--- with" $ERROR_COUNT "errors; expecting" $EXPECTED_ERROR_COUNT
          fi
          cat cppcheck_run.log
          exit 1
