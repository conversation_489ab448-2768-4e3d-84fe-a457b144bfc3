* Before opening a new issue, we wanted to provide you with some useful suggestions (Click "Preview" above for a better view):

    * Consider checking our ROS RealSense Wrapper documentation [README](https://github.com/IntelRealSense/realsense-ros/blob/ros2-master/README.md).
    * Have you looked in our [Discussions](https://github.com/IntelRealSense/realsense-ros/discussions)?
    * Try [searching our GitHub Issues](https://github.com/IntelRealSense/realsense-ros/issues) (open and closed) for a similar issue.

* All users are welcomed to report bugs, ask questions, suggest or request enhancements and generally feel free to open new issue, even if they haven't followed any of the suggestions above :)

----------------------------------------------------------------------------------------------------

| Required Info                         |                                                                                         |
|---------------------------------|------------------------------------------------------------ |
| Camera Model                       | { D405 / D415 / D435 / D435i / D455 / D457 }                          | 
| Firmware Version                   | (Open RealSense Viewer --> Click info)                          | 
| Operating System & Version |   { Win (10/11) / Linux (Ubuntu 18/20/22) / MacOS } | 
| Kernel Version (Linux Only)    |  (e.g. 5.4)                                                                       | 
| Platform                                 | PC/Raspberry Pi/ NVIDIA Jetson / etc..                           |
| Librealsense SDK Version       |  { 2.<?>.<?> }                                                    | 
| Language                               |  {C/C#/labview/opencv/pcl/python/unity }         | 
| Segment			         |  {Robot/Smartphone/VR/AR/others }                             | 
| ROS Distro			         |  {Iron/Humble/Rolling/etc.. }                                           | 
| RealSense ROS Wrapper Version   |  {4.51.1, 4.54.1, etc..}                                                  | 


### Issue Description
<Describe your issue / question / feature request / etc..>
