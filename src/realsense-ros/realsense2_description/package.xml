<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>realsense2_description</name>
  <version>4.55.1</version>
  <description>RealSense description package for Intel 3D D400 cameras</description>
  <maintainer email="<EMAIL>">LibRealSense ROS Team</maintainer>
  <license>Apache License 2.0</license>
  
  <url type="website">http://www.ros.org/wiki/RealSense</url>
  <url type="bugtracker">https://github.com/intel-ros/realsense/issues</url>

  <author email="<EMAIL>">LibRealSense ROS Team</author>
 
  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>realsense2_camera_msgs</depend>
  
  <exec_depend>launch_ros</exec_depend>
  <exec_depend>xacro</exec_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
