<?xml version="1.0"?>

<!--
# Copyright 2023 Intel Corporation. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
-->

<!--
Collection of materials to be used in other macros.
This avoids the redefinition of materials in case multple cameras are imported.
-->

<robot xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:property name="realsense_materials_defined" default="false" />
  <xacro:unless value="${realsense_materials_defined}">
    <xacro:property name="realsense_materials_defined" value="true" />
    <material name="aluminum">
      <color rgba="0.5 0.5 0.5 1"/>
    </material>
    <material name="plastic">
      <color rgba="0.1 0.1 0.1 1"/>
    </material>
 </xacro:unless>
</robot>
