<?xml version="1.0"?>
<robot name="realsense2_camera" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:arg name="use_nominal_extrinsics" default="false"/>
  <xacro:arg name="add_plug" default="false" />
  <xacro:arg name="use_mesh" default="true" />
  <xacro:include filename="$(find realsense2_description)/urdf/_d435.urdf.xacro" />
  
  <link name="base_link" />
  <xacro:sensor_d435 parent="base_link" use_nominal_extrinsics="$(arg use_nominal_extrinsics)" add_plug="$(arg add_plug)" use_mesh="$(arg use_mesh)">
    <origin xyz="0 0 0" rpy="0 0 0"/>
  </xacro:sensor_d435>
</robot>
