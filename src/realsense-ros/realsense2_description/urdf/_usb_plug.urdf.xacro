<?xml version="1.0"?>
<!--
# Copyright 2023 Intel Corporation. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
-->

<!--
This is the URDF model for the Intel RealSense 415 camera, in it's
aluminum peripherial evaluation case.
-->

<robot name="usb_plug" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:macro name="usb_plug" params="parent *origin name:=usb_plug">
    <xacro:property name="M_PI" value="3.1415926535897931" />

    <joint name="${name}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent}" />
      <child link="${name}_link" />
    </joint>

    <link name="${name}_link">
      <visual>
        <!-- 0.044850 0.008000 0.018500 -->
        <origin xyz="0. -0.022425 0." rpy="0 0 ${M_PI/2}"/>
        <geometry>
          <!--box size="0.044850 0.008 0.0185" /-->
          <mesh filename="file://$(find realsense2_description)/meshes/plug.stl" />
        </geometry>
        <material name="plastic"/>
      </visual>
      <collision>
        <origin xyz="0. -0.022425 0." rpy="0 0 ${M_PI/2}"/>
        <geometry>
          <!--box size="0.044850 0.008 0.0185" /-->
          <mesh filename="file://$(find realsense2_description)/meshes/plug_collision.stl" />
        </geometry>
      </collision>
    </link>
  </xacro:macro>
</robot>
