<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>livox_ros_driver2</name>
  <version>1.0.0</version>
  <description>The ROS device driver for Livox 3D LiDARs, for ROS2</description>
  <maintainer email="<EMAIL>">feng</maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake_auto</buildtool_depend>
  <build_depend>rosidl_default_generators</build_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>

  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>rcutils</depend>
  <depend>pcl_conversions</depend>
  <depend>rcl_interfaces</depend>
  <depend>libpcl-all-dev</depend>

  <exec_depend>rosbag2</exec_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <depend>git</depend>
  <depend>apr</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
