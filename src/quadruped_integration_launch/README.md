# 四足机器人集成感知与建图系统

该包提供了一个集成的launch文件，将d435i检测、FAST_LIO里程计和高程图生成整合在一起，适用于搭载d435i相机和mid360雷达的实际四足机器人(Go2)。

## 功能特性

### 集成的传感器系统
- **D435i相机检测系统**: 提供RGB图像、深度图像、原始点云和分割点云
- **Mid360 LiDAR系统**: 通过FAST-LIO提供高精度里程计和激光点云
- **高程建图系统**: 融合多传感器数据生成实时高程地图

### 坐标系变换
系统自动设置完整的TF变换树：
```
map -> odom -> base_link -> {d435i_link, livox_link}
                        -> d435i_color_optical_frame -> d435i_depth_optical_frame
```

### 传感器安装位置 (基于Go2机器人)
- **Mid360雷达**: 安装在机器人顶部 (0, 0, 0.15m 相对于base_link)
- **D435i相机**: 安装在机器人前方 (0.25, 0, 0.1m 相对于base_link)

## 系统要求

### 硬件要求
- 四足机器人Go2
- Intel RealSense D435i相机
- Livox Mid360激光雷达
- 计算平台(推荐Jetson AGX Orin或同等性能)

### 软件依赖
- ROS2 Humble
- d435i_detection_ros2包
- fast_lio包
- elevation_mapping包
- livox_ros_driver2包
- realsense-ros包

## 安装与配置

### 1. 克隆并编译相关包
```bash
# 确保已安装所有依赖包
cd ~/ros2_ws/src

# 编译整个工作空间
cd ~/ros2_ws
colcon build --symlink-install

# 设置环境
source /opt/ros/humble/setup.bash
source /home/<USER>/livox_ws/install/setup.bash  # Livox驱动
source install/setup.bash
```

### 2. 配置传感器
确保传感器配置文件正确：
- Livox配置: `/home/<USER>/livox_ws/src/livox_ros_driver2/config/MID360_config.json`
- YOLOv7权重: `yolov7-segmentation/weights/yolov7-seg.pt`

## 使用方法

### 启动集成系统
```bash
# 启动完整的集成系统
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py

# 可选参数:
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    use_sim_time:=false \
    launch_rviz:=true \
    enable_d435i:=true \
    enable_fast_lio:=true \
    enable_elevation_mapping:=true \
    weights_path:=yolov7-segmentation/weights/yolov7-seg.pt
```

### 部分启动 (调试用)
```bash
# 仅启动d435i检测
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    enable_fast_lio:=false \
    enable_elevation_mapping:=false

# 仅启动FAST-LIO
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    enable_d435i:=false \
    enable_elevation_mapping:=false

# 不启动RViz (服务器模式)
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    launch_rviz:=false
```

## 话题与服务

### 发布的话题
| 话题名称 | 类型 | 描述 |
|---------|------|------|
| `/d435i_detection/pointcloud_raw` | sensor_msgs/PointCloud2 | D435i原始点云 |
| `/d435i_detection/segmented_pointcloud` | sensor_msgs/PointCloud2 | 分割后点云 |
| `/d435i_detection/detection_image` | sensor_msgs/Image | 检测结果图像 |
| `/d435i_detection/segmentation_image` | sensor_msgs/Image | 分割结果图像 |
| `/livox/lidar` | sensor_msgs/PointCloud2 | Livox激光点云 |
| `/livox/imu` | sensor_msgs/Imu | Livox IMU数据 |
| `/odom` | nav_msgs/Odometry | FAST-LIO里程计 |
| `/elevation_map` | grid_map_msgs/GridMap | 高程地图 |
| `/path` | nav_msgs/Path | SLAM轨迹 |

### 坐标系
| 坐标系名称 | 描述 |
|-----------|------|
| `map` | 全局地图坐标系 |
| `odom` | 里程计坐标系 |
| `base_link` | 机器人基座坐标系 |
| `livox_link` | Livox雷达坐标系 |
| `d435i_link` | D435i相机物理坐标系 |
| `d435i_color_optical_frame` | D435i彩色光学坐标系 |
| `d435i_depth_optical_frame` | D435i深度光学坐标系 |

## RViz可视化

启动后RViz将显示：
- 网格地面
- 完整的TF变换树
- D435i原始点云 (彩色)
- Livox激光点云 (强度色彩)
- 分割点云 (红色高亮)
- 高程地图 (色彩编码高度)
- 检测结果图像
- SLAM轨迹路径

### RViz界面说明
1. **TF显示**: 显示所有坐标系关系，便于调试
2. **点云显示**: 多传感器点云融合显示
3. **高程地图**: 实时更新的地形高度信息
4. **图像显示**: 目标检测和语义分割结果
5. **轨迹显示**: 机器人移动轨迹

## 参数调优

### 传感器位置调整
如需调整传感器安装位置，修改launch文件中的TF变换参数：
```python
# 修改D435i位置 (x, y, z, roll, pitch, yaw)
arguments=['0.25', '0.0', '0.1', '0', '0', '0', 'base_link', 'd435i_link']

# 修改Livox位置
arguments=['0.0', '0.0', '0.15', '0', '0', '0', 'base_link', 'livox_link']
```

### 高程地图参数
关键参数可在launch文件中调整：
- `length_in_x/y`: 地图尺寸 (默认8m x 8m)
- `resolution`: 地图分辨率 (默认0.05m)
- `max_time_update_rate`: 更新频率 (默认10Hz)

### 检测参数
- `conf_threshold`: 检测置信度阈值 (默认0.25)
- `iou_threshold`: NMS IoU阈值 (默认0.45)
- `fps`: 处理频率 (默认10Hz)

## 故障排除

### 常见问题

1. **TF变换错误**
   ```bash
   # 检查TF树
   ros2 run tf2_tools view_frames
   
   # 检查特定变换
   ros2 run tf2_ros tf2_echo base_link d435i_link
   ```

2. **传感器数据缺失**
   ```bash
   # 检查话题
   ros2 topic list
   ros2 topic echo /livox/lidar
   ros2 topic echo /d435i_detection/pointcloud_raw
   ```

3. **高程地图不更新**
   ```bash
   # 检查高程地图话题
   ros2 topic echo /elevation_map
   
   # 检查里程计数据
   ros2 topic echo /odom
   ```

4. **权重文件缺失**
   ```bash
   # 下载YOLOv7权重文件
   wget https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-seg.pt
   mkdir -p yolov7-segmentation/weights/
   mv yolov7-seg.pt yolov7-segmentation/weights/
   ```

### 性能优化

1. **降低计算负载**:
   - 减少相机分辨率
   - 降低处理频率
   - 关闭不必要的可视化

2. **网络优化**:
   - 使用有线连接
   - 设置合适的QoS策略
   - 避免话题重复订阅

## 开发与扩展

### 添加新传感器
1. 在launch文件中添加传感器节点
2. 设置相应的TF变换
3. 更新RViz配置显示新数据
4. 修改高程地图配置添加新输入源

### 自定义配置
可以复制并修改launch文件以适应特定需求：
```bash
cp src/quadruped_integration_launch/launch/integrated_perception_mapping.launch.py \
   src/quadruped_integration_launch/launch/custom_mapping.launch.py
```

## 联系与支持

如有问题或建议，请联系开发团队或提交Issue。

## 更新日志

- v1.0.0: 初始版本，整合d435i检测、FAST-LIO和高程建图
- 后续版本将支持更多传感器和功能扩展 

## 点云源选择

系统支持两种点云源用于高程建图：

### D435i点云 (默认)
- **话题**: `/d435i_detection/pointcloud_raw`
- **特点**: 高精度、近距离、密集点云
- **适用场景**: 室内环境、精细建图、近距离导航
- **地图范围**: 10x10米，分辨率3cm

### MID360点云
- **话题**: `/cloud_registered` (FAST-LIO处理后的点云)
- **特点**: 长距离、大范围、稳定性好
- **适用场景**: 户外环境、大范围建图、长距离导航
- **地图范围**: 50x50米，分辨率10cm

## 启动方式

### 使用D435i点云建图（默认）
```bash
source install/setup.bash
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py
```

### 使用MID360点云建图
```bash
source install/setup.bash
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py pointcloud_source:=mid360
```

### 完整参数示例
```bash
# 使用MID360点云，禁用D435i检测，启用RViz
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    pointcloud_source:=mid360 \
    enable_d435i:=false \
    launch_rviz:=true \
    enable_elevation_mapping:=true
```

## 可用参数

| 参数名称 | 默认值 | 描述 | 可选值 |
|---------|--------|------|-------|
| `pointcloud_source` | `d435i` | 高程建图点云源选择 | `d435i`, `mid360` |
| `use_sim_time` | `false` | 是否使用仿真时间 | `true`, `false` |
| `launch_rviz` | `true` | 是否启动RViz2 | `true`, `false` |
| `enable_d435i` | `true` | 是否启用D435i检测 | `true`, `false` |
| `enable_fast_lio` | `true` | 是否启用FAST-LIO | `true`, `false` |
| `enable_elevation_mapping` | `true` | 是否启用高程建图 | `true`, `false` |
| `weights_path` | `yolov7-segmentation/weights/yolov7-seg.pt` | YOLOv7权重文件路径 | 文件路径 |

## 系统架构

### 坐标系变换链
```
map -> odom -> base_link -> livox_link (MID360)
                      -> d435i_link -> d435i_color_optical_frame
                                   -> d435i_depth_optical_frame
```

### 节点启动顺序
1. **TF变换发布器** (立即启动)
2. **FAST-LIO节点** (立即启动)
3. **传感器节点** (延迟2秒)
   - Livox驱动
   - D435i检测
4. **高程建图节点** (延迟10秒)
5. **RViz可视化** (延迟12秒)

## RViz配置

启动后在RViz中推荐配置：
- **Fixed Frame**: `odom` 或 `map`
- **TF Display**: 启用，显示坐标系关系
- **Point Cloud**: 添加相应的点云话题
  - D435i: `/d435i_detection/pointcloud_raw`
  - MID360: `/cloud_registered`
- **Elevation Map**: `/elevation_mapping/elevation_map`
- **Path**: `/path` (FAST-LIO轨迹)
- **Images**: D435i检测结果图像

## 故障排除

### 常见问题

1. **点云话题无数据**
   - 检查传感器连接
   - 确认相应的驱动节点正常运行
   - 使用 `ros2 topic list` 和 `ros2 topic echo` 检查话题

2. **TF变换错误**
   - 确认所有TF发布器正常运行
   - 使用 `ros2 run tf2_tools view_frames` 检查TF树
   - 检查时间同步问题

3. **高程图不更新**
   - 确认选择的点云源有数据
   - 检查机器人位姿信息（/odom话题）
   - 调整sensor_processor参数

### 调试命令

```bash
# 检查话题列表
ros2 topic list

# 监控点云数据
ros2 topic echo /d435i_detection/pointcloud_raw --no-arr
ros2 topic echo /cloud_registered --no-arr

# 检查TF变换
ros2 run tf2_ros tf2_echo map base_link

# 查看elevation mapping状态
ros2 topic echo /elevation_mapping/elevation_map --no-arr
```

## 依赖包

- `d435i_detection_ros2`: D435i检测与点云生成
- `fast_lio`: FAST-LIO SLAM系统
- `elevation_mapping`: 高程图生成
- `livox_ros_driver2`: MID360激光雷达驱动
- `realsense2_camera`: D435i相机驱动（可选） 