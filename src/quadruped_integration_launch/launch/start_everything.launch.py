#!/usr/bin/env python3

import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node

def generate_launch_description():
    # Get the share directory for the required packages
    d435i_detection_ros2_share = get_package_share_directory('d435i_detection_ros2')
    fast_lio_share = get_package_share_directory('fast_lio')
    go2_description_share = get_package_share_directory('go2_description')
    elevation_mapping_share = get_package_share_directory('elevation_mapping')

    # --- Robot Description (TF) ---
    robot_description_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(go2_description_share, 'launch', 'visualize.launch.py')
        )
    )

    # --- D435i Detection ---
    d435i_detection_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(d435i_detection_ros2_share, 'launch', 'd435i_detection.launch.py')
        )
    )

    # --- FAST-LIO Odometry ---
    fast_lio_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(fast_lio_share, 'launch', 'livox_fast_lio_launch.py')
        )
    )

    # --- Elevation Mapping ---
    elevation_mapping_node = Node(
        package='elevation_mapping',
        executable='try_map_node', # Assuming the executable is named 'try_map_node'
        name='elevation_mapping',
        output='screen',
        remappings=[
            ('/elevation_mapping/pointcloud', '/d435i_detection/pointcloud_segmented'),
            ('/elevation_mapping/odometry', '/Odometry') # Assuming FAST-LIO publishes on /Odometry
        ]
    )

    return LaunchDescription([
        robot_description_launch,
        d435i_detection_launch,
        fast_lio_launch,
        elevation_mapping_node,
    ])
