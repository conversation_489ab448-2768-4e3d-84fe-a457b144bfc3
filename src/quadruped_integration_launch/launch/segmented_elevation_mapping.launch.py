#!/usr/bin/env python3
"""
分割感知高程建图Launch文件
使用D435i分割点云生成带有分割信息的多层高程图
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, TimerAction, OpaqueFunction
from launch.substitutions import LaunchConfiguration
from launch.conditions import IfCondition
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成分割感知高程建图launch描述"""

    # =========================== 参数声明 ===========================
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )

    declare_launch_rviz = DeclareLaunchArgument(
        'launch_rviz',
        default_value='true',
        description='是否启动RViz2进行可视化'
    )

    # =========================== 分割感知高程建图系统 ===========================
    # 使用分割点云的高程建图配置
    segmented_elevation_params = [
        {
            'inputs': ['d435i_segmented_pointcloud'],
            
            # D435i分割点云输入配置
            'd435i_segmented_pointcloud': {
                'type': 'pointcloud',
                'topic': '/d435i_detection/pointcloud_segmented',  # 使用分割点云
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'segmentation',  # 使用自定义的分割传感器处理器
                    'ignore_points_above': 3.0,
                    'ignore_points_below': -1.0,
                    'min_valid_points': 30,
                    'max_update_rate': 10.0,
                    'voxel_size': 0.02,
                    
                    # 分割特定参数
                    'segmented_min_variance': 1.0e-6,  # 分割对象的低不确定性
                    'segmented_max_variance': 0.05,    # 背景的高不确定性
                    'segmentation_confidence_threshold': 0.5,
                    'use_class_specific_variances': True,
                }
            },
            
            # 地图配置 - 针对分割高程图优化
            'map_frame_id': 'map',
            'robot_base_frame_id': 'base_link',
            'robot_pose_with_covariance_topic': '/odom',
            'robot_pose_cache_size': 500,
            
            # 地图尺寸
            'length_in_x': 10.0,
            'length_in_y': 10.0,
            'position_x': 0.0,
            'position_y': 0.0,
            'resolution': 0.05,  # 适中分辨率
            'min_update_rate': 2.0,
            'fused_map_publishing_rate': 5.0,
            
            # 跟踪点配置
            'track_point_frame_id': 'base_link',
            'track_point_x': 0.0,
            'track_point_y': 0.0,
            'track_point_z': 0.0,
            
            # 移动平均配置
            'enable_move_to_center': True,
            'move_to_center_at_startup': True,
            
            # 融合参数
            'min_variance': 1.0e-6,
            'max_variance': 0.05,
            'mahalanobis_distance_threshold': 2.5,
            
            # 时间参数
            'time_variance': 0.01,
            'scanning_duration': 0.1,
            
            # TF参数
            'tf_timeout': 1.0,
            'tf_tolerance': 0.2,
            
            # 发布额外的分割层
            'publish_segmentation_layers': True,
            'segmentation_layer_names': [
                'segmentation_id',       # 类别ID层
                'segmentation_color_r',  # 红色通道层
                'segmentation_color_g',  # 绿色通道层 
                'segmentation_color_b'   # 蓝色通道层
            ],
            
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }
    ]

    # 分割感知高程建图节点
    segmented_elevation_mapping_node = Node(
        package='elevation_mapping',
        executable='elevation_mapping',
        name='segmented_elevation_mapping',
        output='screen',
        parameters=segmented_elevation_params,
        remappings=[
            # 重映射输出话题以区分分割高程图
            ('/elevation_map', '/segmented_elevation_map'),
            ('/elevation_map_raw', '/segmented_elevation_map_raw'),
        ]
    )

    # =========================== 可视化系统 ===========================
    # RViz节点配置用于可视化分割高程图
    rviz_node = TimerAction(
        period=3.0,  # 延迟启动确保节点就绪
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2_segmented_elevation',
                arguments=['-d', os.path.join(
                    get_package_share_directory('quadruped_integration_launch'),
                    'rviz', 'segmented_elevation_mapping.rviz'
                )],
                output='screen',
                condition=IfCondition(LaunchConfiguration('launch_rviz'))
            )
        ]
    )

    # =========================== 调试和监控节点 ===========================
    # 话题监控节点，显示分割高程图的统计信息
    topic_monitor_node = Node(
        package='rqt_topic',
        executable='rqt_topic',
        name='segmented_elevation_topic_monitor',
        condition=IfCondition('false')  # 默认不启动，可通过参数控制
    )

    # =========================== 启动描述 ===========================
    return LaunchDescription([
        # 参数声明
        declare_use_sim_time,
        declare_launch_rviz,

        # 分割感知高程建图节点
        segmented_elevation_mapping_node,

        # 可视化节点
        rviz_node,
        
        # 可选的监控节点
        topic_monitor_node,
    ])