#!/usr/bin/env python3
"""
集成感知与建图Launch文件
整合d435i检测、FAST_LIO里程计和高程图生成
适用于实际的四足机器人(Go2)搭载d435i相机和mid360雷达
支持选择使用D435i或MID360点云进行高程建图
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, TimerAction, IncludeLaunchDescription, OpaqueFunction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成集成launch描述"""

    # =========================== 参数声明 ===========================
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )

    declare_launch_rviz = DeclareLaunchArgument(
        'launch_rviz',
        default_value='true',
        description='是否启动RViz2进行可视化'
    )

    declare_enable_d435i = DeclareLaunchArgument(
        'enable_d435i',
        default_value='true',
        description='是否启用D435i检测系统'
    )

    declare_enable_fast_lio = DeclareLaunchArgument(
        'enable_fast_lio',
        default_value='true',
        description='是否启用FAST-LIO系统'
    )

    declare_enable_elevation_mapping = DeclareLaunchArgument(
        'enable_elevation_mapping',
        default_value='true',
        description='是否启用高程建图'
    )

    # 新增参数：选择点云源
    declare_pointcloud_source = DeclareLaunchArgument(
        'pointcloud_source',
        default_value='d435i',
        description='选择高程建图的点云源: d435i 或 mid360',
        choices=['d435i', 'mid360']
    )

    # 新增参数：是否启用分割感知高程建图
    declare_enable_segmented_mapping = DeclareLaunchArgument(
        'enable_segmented_mapping',
        default_value='false',
        description='是否启用分割感知高程建图（使用分割点云）'
    )

    # D435i 检测参数
    declare_weights_path = DeclareLaunchArgument(
        'weights_path',
        default_value='yolov7-segmentation/weights/yolov7-seg.pt',
        description='YOLOv7模型权重文件路径'
    )

    # =========================== TF变换设置 ===========================
    # 修正的TF变换链：map -> odom -> base_link -> 传感器
    # 这样传感器会跟随机器人移动，高程图在全局坐标系中累积
    
    # 1. map -> odom (全局坐标系，FAST-LIO会动态更新)
    # 注意：这个变换会被FAST-LIO动态覆盖，这里只是初始设置
    tf_map_to_odom = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_to_odom_transform',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom'],
        output='screen'
    )

    # 2. odom -> base_link (里程计坐标系到机器人基座，FAST-LIO提供动态变换)
    # FAST-LIO会发布从camera_init到body的变换，我们需要将其映射到odom->base_link
    # 这里设置一个身份变换，实际的动态变换由FAST-LIO提供
    
    # 3. base_link -> livox_link (Mid360雷达安装在机器人顶部，固定变换)
    tf_base_to_livox = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_livox_transform',
        arguments=['0.0', '0.0', '0.15', '0', '0', '0', 'base_link', 'livox_link'],
        output='screen'
    )

    # 4. base_link -> d435i_link (D435i相机安装在机器人前方，固定变换)
    tf_base_to_d435i = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='base_to_d435i_transform',
        arguments=['0.25', '0.0', '0.1', '0', '0', '0', 'base_link', 'd435i_link'],
        output='screen'
    )

    # 5. d435i_link -> d435i_color_optical_frame (相机光学坐标系，固定变换)
    tf_d435i_to_optical = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='d435i_to_optical_transform',
        arguments=['0', '0', '0', '-0.5', '0.5', '-0.5', '0.5', 'd435i_link', 'd435i_color_optical_frame'],
        output='screen'
    )

    # 6. d435i_color_optical_frame -> d435i_depth_optical_frame (深度和彩色光学坐标系重合)
    tf_color_to_depth = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='color_to_depth_transform',
        arguments=['0', '0', '0', '0', '0', '0', 'd435i_color_optical_frame', 'd435i_depth_optical_frame'],
        output='screen'
    )

    # 7. 为FAST-LIO建立正确的坐标系映射
    # FAST-LIO使用camera_init作为世界坐标系，body作为机器人坐标系
    # 我们需要将这些映射到标准的ROS坐标系
    tf_odom_to_camera_init = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='odom_to_camera_init_transform',
        arguments=['0', '0', '0', '0', '0', '0', 'odom', 'camera_init'],
        output='screen'
    )

    tf_body_to_base_link = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='body_to_base_link_transform',
        arguments=['0', '0', '0', '0', '0', '0', 'body', 'base_link'],
        output='screen'
    )

    # =========================== D435i检测系统 ===========================
    d435i_detection_node = Node(
        package='d435i_detection_ros2',
        executable='d435i_detection_node',
        name='d435i_detection_node',
        output='screen',
        parameters=[{
            'weights_path': LaunchConfiguration('weights_path'),
            'conf_threshold': 0.25,
            'iou_threshold': 0.45,
            'device': 'cpu',
            'enable_pointcloud': True,
            'enable_segmented_pointcloud': True,
            'pointcloud_topic': '/d435i_detection/pointcloud_raw',  # 指定点云话题
            'depth_topic': '/d435i_detection/depth',  # 指定深度图话题
            'rgb_topic': '/d435i_detection/color',  # 指定RGB图像话题
            'camera_info_topic': '/d435i_detection/camera_info'  # 指定相机信息话题
        }],
        condition=IfCondition(LaunchConfiguration('enable_d435i'))
    )

    # =========================== FAST-LIO系统 ===========================
    # Livox配置路径
    livox_config_path = '/home/<USER>/livox_ws/src/livox_ros_driver2/config'
    user_config_path = os.path.join(livox_config_path, 'MID360_config.json')

    # Livox驱动参数
    livox_ros2_params = [
        {"xfer_format": 1},
        {"multi_topic": 0},
        {"data_src": 0},
        {"publish_freq": 10.0},
        {"output_data_type": 0},
        {"frame_id": 'livox_link'},
        {"lvx_file_path": '/home/<USER>/livox_test.lvx'},
        {"user_config_path": user_config_path},
        {"cmdline_input_bd_code": 'livox0000000001'}
    ]

    # Livox驱动节点
    livox_driver_node = Node(
        package='livox_ros_driver2',
        executable='livox_ros_driver2_node',
        name='livox_lidar_publisher',
        output='screen',
        parameters=livox_ros2_params,
        remappings=[
            ('/livox/lidar', '/livox/lidar'),
            ('/livox/imu', '/livox/imu')
        ],
        condition=IfCondition(LaunchConfiguration('enable_fast_lio'))
    )

    # FAST-LIO节点
    fast_lio_package_path = get_package_share_directory('fast_lio')
    default_config_path = os.path.join(fast_lio_package_path, 'config')
    
    fast_lio_node = Node(
        package='fast_lio',
        executable='fastlio_mapping',
        name='fastlio_mapping',
        parameters=[
            os.path.join(default_config_path, 'mid360.yaml'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        output='screen',
        remappings=[
            # 重映射里程计话题到标准名称
            ('/Odometry', '/odom'),
            # 确保路径话题使用正确的坐标系
            ('/path', '/path'),
        ],
        condition=IfCondition(LaunchConfiguration('enable_fast_lio'))
    )

    # =========================== 高程建图系统 ===========================
    # 根据选择的点云源配置不同的高程建图参数
    # 支持D435i和MID360两种点云源
    
    # D435i点云配置
    d435i_elevation_params = [
        {
            'inputs': ['d435i_pointcloud'],
            
            # D435i点云输入配置
            'd435i_pointcloud': {
                'type': 'pointcloud',
                'topic': '/d435i_detection/pointcloud_raw',
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'perfect',
                    'ignore_points_above': 3.0,  # D435i有限范围，适用于近距离环境
                    'ignore_points_below': -1.0,
                    'min_valid_points': 30,
                    'max_update_rate': 10.0,
                    'voxel_size': 0.02,  # 更小的体素大小适合D435i的精细点云
                }
            },
            
            # 地图配置 - 针对D435i优化
            'map_frame_id': 'map',
            'robot_base_frame_id': 'base_link',
            'robot_pose_with_covariance_topic': '/odom',
            'robot_pose_cache_size': 500,
            
            # 地图尺寸 - D435i适用于较小范围的精细建图
            'length_in_x': 10.0,  # D435i有效范围较小，使用10x10米
            'length_in_y': 10.0,
            'position_x': 0.0,
            'position_y': 0.0,
            'resolution': 0.03,  # 更高分辨率适合D435i的精细点云
            'min_update_rate': 2.0,
            'fused_map_publishing_rate': 5.0,
            
            # 跟踪点配置
            'track_point_frame_id': 'base_link',
            'track_point_x': 0.0,
            'track_point_y': 0.0,
            'track_point_z': 0.0,
            
            # 移动平均配置
            'enable_move_to_center': True,
            'move_to_center_at_startup': True,
            
            # 融合参数 - 针对D435i的噪声特性优化，提高精度
            'min_variance': 5.0e-7,  # 降低最小方差提高精度
            'max_variance': 0.03,    # D435i噪声较小，进一步降低最大方差
            'mahalanobis_distance_threshold': 2.0,  # 降低阈值，更严格的融合标准
            
            # 时间参数
            'time_variance': 0.01,
            'scanning_duration': 0.1,
            
            # TF参数
            'tf_timeout': 1.0,
            'tf_tolerance': 0.2,
            
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }
    ]

    # D435i分割点云配置
    d435i_segmented_elevation_params = [
        {
            'inputs': ['d435i_segmented_pointcloud'],
            
            # D435i分割点云输入配置
            'd435i_segmented_pointcloud': {
                'type': 'pointcloud',
                'topic': '/d435i_detection/pointcloud_segmented',  # 使用分割点云
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'segmentation',  # 使用自定义的分割传感器处理器
                    'ignore_points_above': 1.0,
                    'ignore_points_below': -1.0,
                    'min_valid_points': 30,
                    'max_update_rate': 30.0,
                    'voxel_size': 0.05,
                    
                    # 分割特定参数 - 优化精度和颜色一致性
                    'segmented_min_variance': 3.0e-7,  # 分割对象的极低不确定性（提高精度）
                    'segmented_max_variance': 0.02,    # 背景的中等不确定性（提高整体精度）
                    'segmentation_confidence_threshold': 0.3,  # 降低阈值，包含更多分割信息
                    'use_class_specific_variances': True,
                }
            },
            
            # 地图配置 - 针对D435i分割优化
            'map_frame_id': 'map',
            'robot_base_frame_id': 'base_link',
            'robot_pose_with_covariance_topic': '/odom',
            'robot_pose_cache_size': 500,
            
            # 地图尺寸 - 与D435i原始配置相同但支持分割层
            'length_in_x': 6.0,
            'length_in_y': 6.0,
            'position_x': 0.0,
            'position_y': 0.0,
            'resolution': 0.05,  # 高分辨率适合精细分割信息
            'min_update_rate': 2.0,
            'fused_map_publishing_rate': 5.0,
            
            # 跟踪点配置
            'track_point_frame_id': 'base_link',
            'track_point_x': 0.0,
            'track_point_y': 0.0,
            'track_point_z': 0.0,
            
            # 移动平均配置
            'enable_move_to_center': True,
            'move_to_center_at_startup': True,
            
            # 融合参数 - 针对分割数据优化，提高精度
            'min_variance': 5.0e-7,  # 降低最小方差提高精度
            'max_variance': 0.03,    # 降低最大方差提高整体精度
            'mahalanobis_distance_threshold': 2.0,  # 降低阈值，更严格的融合标准
            
            # 时间参数
            'time_variance': 0.01,
            'scanning_duration': 0.1,
            
            # TF参数
            'tf_timeout': 1.0,
            'tf_tolerance': 0.2,
            
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }
    ]

    # MID360点云配置
    mid360_elevation_params = [
        {
            'inputs': ['mid360_pointcloud'],
            
            # MID360点云输入配置
            'mid360_pointcloud': {
                'type': 'pointcloud',
                'topic': '/cloud_registered',  # FAST-LIO发布的已注册点云
                'queue_size': 50,  # MID360点云密度较高，适当减少队列大小
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'perfect',
                    'ignore_points_above': 5.0,  # MID360范围更大，适用于更大的高度范围
                    'ignore_points_below': -2.0,
                    'min_valid_points': 100,  # MID360点云密度高，需要更多有效点
                    'max_update_rate': 5.0,  # 适当降低更新频率以处理密集点云
                    'voxel_size': 0.05,  # 较大的体素大小适合MID360的密集点云
                }
            },
            
            # 地图配置 - 针对MID360优化
            'map_frame_id': 'map',
            'robot_base_frame_id': 'base_link',
            'robot_pose_with_covariance_topic': '/odom',
            'robot_pose_cache_size': 1000,  # 增加缓存以支持更大范围
            
            # 地图尺寸 - MID360适用于大范围建图
            'length_in_x': 50.0,  # MID360有效范围大，使用50x50米
            'length_in_y': 50.0,
            'position_x': 0.0,
            'position_y': 0.0,
            'resolution': 0.1,  # 适中分辨率平衡精度和计算量
            'min_update_rate': 1.0,  # 降低最小更新频率
            'fused_map_publishing_rate': 2.0,  # 降低发布频率以减少计算负担
            
            # 跟踪点配置
            'track_point_frame_id': 'base_link',
            'track_point_x': 0.0,
            'track_point_y': 0.0,
            'track_point_z': 0.0,
            
            # 移动平均配置
            'enable_move_to_center': True,
            'move_to_center_at_startup': True,
            
            # 融合参数 - 针对MID360的特性优化
            'min_variance': 1.0e-5,
            'max_variance': 0.2,  # MID360在远距离可能有更大的不确定性
            'mahalanobis_distance_threshold': 3.0,  # 稍微放宽阈值
            
            # 时间参数
            'time_variance': 0.02,
            'scanning_duration': 0.2,
            
            # TF参数
            'tf_timeout': 1.0,
            'tf_tolerance': 0.3,  # 增加容忍度以适应长距离测量
            
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }
    ]

    def launch_elevation_mapping(context, *args, **kwargs):
        """根据pointcloud_source参数选择相应的elevation mapping配置"""
        pointcloud_source = LaunchConfiguration('pointcloud_source').perform(context)
        enable_elevation_mapping = LaunchConfiguration('enable_elevation_mapping').perform(context)
        
        if enable_elevation_mapping.lower() == 'true':
            if pointcloud_source == 'd435i':
                return [Node(
                    package='elevation_mapping',
                    executable='elevation_mapping',
                    name='elevation_mapping',
                    output='screen',
                    parameters=d435i_elevation_params,
                )]
            elif pointcloud_source == 'mid360':
                return [Node(
                    package='elevation_mapping',
                    executable='elevation_mapping',
                    name='elevation_mapping',
                    output='screen',
                    parameters=mid360_elevation_params,
                )]
        return []

    def launch_segmented_elevation_mapping(context, *args, **kwargs):
        """启动分割感知高程建图（并行系统）"""
        enable_segmented_mapping = LaunchConfiguration('enable_segmented_mapping').perform(context)
        
        if enable_segmented_mapping.lower() == 'true':
            return [Node(
                package='elevation_mapping',
                executable='elevation_mapping',
                name='segmented_elevation_mapping',
                output='screen',
                parameters=d435i_segmented_elevation_params,
                remappings=[
                    # 重映射输出话题以区分分割高程图
                    ('/elevation_map', '/segmented_elevation_map'),
                    ('/elevation_map_raw', '/segmented_elevation_map_raw'),
                ]
            )]
        return []

    # 使用OpaqueFunction来根据参数条件启动elevation mapping
    elevation_mapping_launcher = OpaqueFunction(function=launch_elevation_mapping)
    segmented_elevation_mapping_launcher = OpaqueFunction(function=launch_segmented_elevation_mapping)

    # =========================== 可视化系统 ===========================
    # 自定义RViz配置文件路径
    rviz_config_file = PathJoinSubstitution([
        FindPackageShare('quadruped_integration_launch'),
        'rviz',
        'integrated_perception_mapping.rviz'
    ])

    # RViz节点 - 延迟启动确保TF变换已建立
    rviz_node = TimerAction(
        period=8.0,  # 延迟8秒启动，确保所有TF变换建立
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2_integrated',
                arguments=['-d', rviz_config_file],
                output='screen',
                condition=IfCondition(LaunchConfiguration('launch_rviz'))
            )
        ]
    )

    # =========================== 启动描述 ===========================
    return LaunchDescription([
        # 参数声明
        declare_use_sim_time,
        declare_launch_rviz,
        declare_enable_d435i,
        declare_enable_fast_lio,
        declare_enable_elevation_mapping,
        declare_pointcloud_source,  # 新增的点云源选择参数
        declare_enable_segmented_mapping,  # 新增的分割建图参数
        declare_weights_path,

        # TF变换 - 建立完整的坐标系链
        tf_map_to_odom,
        tf_odom_to_camera_init,
        tf_body_to_base_link,
        tf_base_to_livox,
        tf_base_to_d435i,
        tf_d435i_to_optical,
        tf_color_to_depth,

        # SLAM节点 - 首先启动
        fast_lio_node,
        
        # 等待SLAM初始化完成后启动传感器节点
        TimerAction(
            period=2.0,
            actions=[
                livox_driver_node,
                d435i_detection_node,
            ]
        ),
        
        # 建图节点 - 等待传感器节点启动完成，根据点云源选择相应的节点
        TimerAction(
            period=10.0,  # 增加延迟，确保传感器完全初始化
            actions=[elevation_mapping_launcher]  # 原有的elevation mapping配置
        ),

        # 分割感知高程建图节点 - 并行启动
        TimerAction(
            period=11.0,  # 稍微延迟确保原有系统稳定
            actions=[segmented_elevation_mapping_launcher]  # 分割感知高程建图
        ),

        # 可视化节点 - 最后启动
        TimerAction(
            period=12.0,  # 增加延迟，确保所有节点都已启动
            actions=[rviz_node]
        ),
    ]) 