<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>quadruped_integration_launch</name>
  <version>1.0.0</version>
  <description>Master launch package to integrate quadruped sensors, odometry, and mapping.</description>
  <maintainer email="<EMAIL>">Gemini</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>d435i_detection_ros2</exec_depend>
  <exec_depend>fast_lio</exec_depend>
  <exec_depend>elevation_mapping</exec_depend>
  <exec_depend>unitree_go2_description</exec_depend>
  <exec_depend>rviz2</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
