# 分割感知高程建图系统

## 概述

这个系统扩展了原有的高程建图功能，支持使用D435i相机的分割点云生成带有语义信息的高程图。系统能够区分不同的物体类别，并在高程图中用不同颜色表示。

## 功能特性

### 核心功能
- **分割感知建图**: 基于YOLOv7分割结果创建语义高程图
- **多层数据结构**: 支持高度、方差、分割ID和颜色等多个层
- **颜色编码**: 不同物体类别使用不同颜色标识
- **背景区域处理**: 未分割区域使用暗灰色表示

### 技术特点
- **GridMap格式**: 兼容ROS2 grid_map生态系统
- **实时处理**: 支持实时分割点云到高程图的转换
- **可配置参数**: 支持类别特定的不确定性设置
- **可视化支持**: 在RViz2中显示多彩的语义高程图

## 系统架构

### 数据流
```
D435i相机 → YOLOv7分割 → 分割点云 → 分割传感器处理器 → 多层高程图
```

### 关键组件
1. **SegmentationSensorProcessor**: 自定义传感器处理器
2. **颜色-类别映射**: RGB颜色到YOLO类别的映射表
3. **多层GridMap**: 包含高度和分割信息的数据结构

## 使用方法

### 基本启动
启用分割感知高程建图的基本命令：

```bash
# 方法1：使用enable_segmented_mapping参数
source install/setup.bash
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    enable_segmented_mapping:=true

# 方法2：指定点云源为分割点云
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    pointcloud_source:=d435i_segmented

# 方法3：独立启动分割建图
ros2 launch quadruped_integration_launch segmented_elevation_mapping.launch.py
```

### 参数配置

#### 主要参数
- `enable_segmented_mapping`: 是否启用分割建图 (默认: false)
- `pointcloud_source`: 点云源选择 ('d435i', 'd435i_segmented', 'mid360')
- `enable_d435i`: 是否启用D435i检测系统 (默认: true)
- `enable_elevation_mapping`: 是否启用高程建图 (默认: true)

#### 分割特定参数
```yaml
segmented_min_variance: 1.0e-6     # 分割对象的低不确定性
segmented_max_variance: 0.05       # 背景的高不确定性
segmentation_confidence_threshold: 0.5
use_class_specific_variances: true
```

### 话题说明

#### 输入话题
- `/d435i_detection/pointcloud_segmented`: 分割后的RGB点云数据
- `/odom`: 机器人位姿信息

#### 输出话题
- `/segmented_elevation_map`: 包含分割信息的完整高程图
- `/segmented_elevation_map_raw`: 原始分割高程图数据

#### GridMap层结构
- `elevation`: 高度信息 (float)
- `variance`: 高度不确定性 (float)
- `segmentation_id`: 物体类别ID (int, 0=背景, >0=物体类别)
- `segmentation_color_r`: 红色通道 (uint8)
- `segmentation_color_g`: 绿色通道 (uint8) 
- `segmentation_color_b`: 蓝色通道 (uint8)

## 类别颜色映射

### YOLO类别对应
系统支持YOLO标准类别，每个类别有预定义的颜色：

| 类别ID | 类别名称 | 颜色 (RGB) | 说明 |
|--------|----------|------------|------|
| 0 | background | 灰度 | 背景区域 |
| 1 | person | (255,0,0) | 人 |
| 2 | bicycle | (0,255,0) | 自行车 |
| 3 | car | (0,0,255) | 汽车 |
| 4 | motorcycle | (255,255,0) | 摩托车 |
| 5 | airplane | (255,0,255) | 飞机 |
| ... | ... | ... | ... |

### 颜色处理策略
- **分割区域**: 使用类别特定的鲜艳颜色
- **背景区域**: 使用变暗的灰度值
- **同类多实例**: 通过HSV调整生成颜色变体

## 测试和验证

### 运行测试节点
```bash
# 启动测试节点监控系统状态
python3 src/quadruped_integration_launch/scripts/test_segmented_elevation.py
```

### 检查话题
```bash
# 查看分割高程图话题
ros2 topic list | grep segmented_elevation

# 查看话题频率
ros2 topic hz /segmented_elevation_map

# 查看话题内容
ros2 topic echo /segmented_elevation_map --field layers
```

### RViz2可视化
在RViz2中添加GridMap插件来可视化分割高程图：
1. 添加GridMap显示插件
2. 设置话题为 `/segmented_elevation_map`
3. 选择不同的层进行可视化：
   - `elevation`: 显示高度信息
   - `segmentation_color_*`: 显示彩色分割结果

## 故障排除

### 常见问题

1. **没有分割高程图输出**
   - 检查D435i检测节点是否正常运行
   - 确认分割点云话题有数据：`ros2 topic hz /d435i_detection/pointcloud_segmented`
   - 检查elevation mapping节点日志

2. **分割信息丢失**
   - 确认使用了正确的传感器处理器类型 `type: segmentation`
   - 检查颜色-类别映射是否正确加载
   - 验证点云中的RGB信息

3. **颜色不正确**
   - 检查D435i检测节点的颜色编码设置
   - 确认分割掩码的颜色稳定性参数
   - 验证color_intensity和background_dimming参数

4. **性能问题**
   - 降低点云处理频率：`max_update_rate: 5.0`
   - 增加体素大小：`voxel_size: 0.05`
   - 减少地图分辨率：`resolution: 0.05`

### 调试技巧
```bash
# 监控系统资源使用
htop

# 查看节点状态
ros2 node list
ros2 node info /segmented_elevation_mapping

# 检查TF变换
ros2 run tf2_tools view_frames
```

## 配置示例

### 完整启动命令
```bash
# 启动完整的分割感知建图系统
ros2 launch quadruped_integration_launch integrated_perception_mapping.launch.py \
    enable_d435i:=true \
    enable_fast_lio:=true \
    enable_elevation_mapping:=true \
    enable_segmented_mapping:=true \
    pointcloud_source:=d435i_segmented \
    launch_rviz:=true \
    weights_path:="yolov7-segmentation/weights/yolov7-seg.pt"
```

### 自定义配置文件
创建自定义的分割建图配置：

```yaml
# segmented_elevation_config.yaml
segmented_elevation_mapping:
  ros__parameters:
    # 输入源配置
    inputs: ['d435i_segmented_pointcloud']
    
    d435i_segmented_pointcloud:
      type: pointcloud
      topic: /d435i_detection/pointcloud_segmented
      sensor_processor:
        type: segmentation
        segmented_min_variance: 1.0e-6
        segmented_max_variance: 0.05
        
    # 地图参数
    resolution: 0.03
    length_in_x: 15.0
    length_in_y: 15.0
```

## 扩展和定制

### 添加新的物体类别
1. 在D435i检测节点中添加新的颜色映射
2. 在SegmentationSensorProcessor中更新colorToClassMap_
3. 可选：为不同类别设置特定的不确定性参数

### 自定义传感器处理器
参考SegmentationSensorProcessor的实现，可以创建适用于其他传感器的分割处理器。

### 与导航系统集成
分割高程图可以用于：
- 语义导航规划
- 障碍物分类避免
- 地形类型识别
- 安全区域检测

## 性能优化

### 建议设置
- **CPU密集型**: 降低处理频率和分辨率
- **内存受限**: 减小地图尺寸和缓存大小  
- **实时应用**: 使用GPU加速和优化的体素大小

### 监控指标
- 点云处理频率
- 内存使用量
- CPU使用率
- 话题发布延迟