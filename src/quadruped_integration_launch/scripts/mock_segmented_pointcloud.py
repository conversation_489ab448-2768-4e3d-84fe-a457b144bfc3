#!/usr/bin/env python3
"""
模拟分割点云发布器
用于测试分割感知高程建图系统
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2, PointField
from std_msgs.msg import Header
import struct
import numpy as np
import time


class MockSegmentedPointcloudPublisher(Node):
    """模拟分割点云发布器"""
    
    def __init__(self):
        super().__init__('mock_segmented_pointcloud_publisher')
        
        # 创建发布者
        self.publisher = self.create_publisher(
            PointCloud2,
            '/d435i_detection/pointcloud_segmented',
            10
        )
        
        # 创建定时器，每秒发布一次
        self.timer = self.create_timer(1.0, self.publish_mock_pointcloud)
        
        # 模拟YOLO类别颜色
        self.class_colors = {
            0: (100, 100, 100),    # 背景 - 灰色
            1: (255, 0, 0),        # person - 红色
            2: (0, 255, 0),        # bicycle - 绿色  
            3: (0, 0, 255),        # car - 蓝色
            4: (255, 255, 0),      # motorcycle - 黄色
            5: (255, 0, 255),      # airplane - 洋红色
        }
        
        self.frame_count = 0
        
        self.get_logger().info("模拟分割点云发布器已启动")
    
    def publish_mock_pointcloud(self):
        """发布模拟的分割点云"""
        self.frame_count += 1
        
        # 创建模拟点云数据
        points = []
        
        # 生成一个简单的场景：地面 + 几个物体
        np.random.seed(42 + self.frame_count)  # 保证每帧数据不同但可重复
        
        # 1. 地面点云 (背景)
        for x in np.arange(-2.0, 2.0, 0.1):
            for y in np.arange(-2.0, 2.0, 0.1):
                if np.random.random() > 0.7:  # 稀疏采样
                    z = 0.0 + np.random.normal(0, 0.02)  # 地面带噪声
                    color = self.class_colors[0]  # 背景颜色
                    points.append([x, y, z, color[0], color[1], color[2]])
        
        # 2. 物体1：车辆 (蓝色立方体)
        center_x, center_y, center_z = 1.0, 0.5, 0.5
        for dx in np.arange(-0.3, 0.3, 0.05):
            for dy in np.arange(-0.2, 0.2, 0.05):
                for dz in np.arange(0, 0.3, 0.05):
                    if np.random.random() > 0.5:
                        x, y, z = center_x + dx, center_y + dy, center_z + dz
                        color = self.class_colors[3]  # 车辆颜色
                        points.append([x, y, z, color[0], color[1], color[2]])
        
        # 3. 物体2：人 (红色圆柱体)
        center_x, center_y = -1.0, -0.5
        for angle in np.arange(0, 2*np.pi, 0.2):
            for r in np.arange(0, 0.2, 0.05):
                for z in np.arange(0, 1.5, 0.1):
                    if np.random.random() > 0.6:
                        x = center_x + r * np.cos(angle)
                        y = center_y + r * np.sin(angle)
                        color = self.class_colors[1]  # 人的颜色
                        points.append([x, y, z, color[0], color[1], color[2]])
        
        # 4. 物体3：自行车 (绿色)
        center_x, center_y, center_z = 0.0, 1.5, 0.3
        for dx in np.arange(-0.4, 0.4, 0.08):
            for dy in np.arange(-0.1, 0.1, 0.08):
                for dz in np.arange(0, 0.4, 0.08):
                    if np.random.random() > 0.7:
                        x, y, z = center_x + dx, center_y + dy, center_z + dz
                        color = self.class_colors[2]  # 自行车颜色
                        points.append([x, y, z, color[0], color[1], color[2]])
        
        # 创建PointCloud2消息
        pointcloud_msg = self.create_pointcloud2_message(points)
        
        # 发布
        self.publisher.publish(pointcloud_msg)
        
        if self.frame_count % 10 == 1:  # 每10帧打印一次
            class_counts = {}
            for point in points:
                color_key = (int(point[3]), int(point[4]), int(point[5]))
                for class_id, class_color in self.class_colors.items():
                    if color_key == class_color:
                        class_counts[class_id] = class_counts.get(class_id, 0) + 1
                        break
            
            self.get_logger().info(
                f"发布模拟点云 #{self.frame_count}: {len(points)} 个点, "
                f"类别分布: {class_counts}"
            )
    
    def create_pointcloud2_message(self, points):
        """创建PointCloud2消息"""
        # 定义点云字段
        fields = [
            PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
            PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
            PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1),
            PointField(name='rgb', offset=12, datatype=PointField.FLOAT32, count=1),
        ]
        
        # 创建点云数据
        cloud_data = bytearray()
        for point in points:
            # 打包XYZ坐标
            cloud_data.extend(struct.pack('fff', point[0], point[1], point[2]))
            
            # 将RGB颜色打包成一个float32
            r, g, b = int(point[3]), int(point[4]), int(point[5])
            rgb_int = (r << 16) | (g << 8) | b
            rgb_packed = struct.pack('I', rgb_int)
            rgb_float = struct.unpack('f', rgb_packed)[0]
            cloud_data.extend(struct.pack('f', rgb_float))
        
        # 创建PointCloud2消息
        pointcloud_msg = PointCloud2()
        pointcloud_msg.header = Header()
        pointcloud_msg.header.stamp = self.get_clock().now().to_msg()
        pointcloud_msg.header.frame_id = 'd435i_depth_optical_frame'
        pointcloud_msg.height = 1
        pointcloud_msg.width = len(points)
        pointcloud_msg.fields = fields
        pointcloud_msg.is_bigendian = False
        pointcloud_msg.point_step = 16  # 4 floats * 4 bytes each
        pointcloud_msg.row_step = pointcloud_msg.point_step * pointcloud_msg.width
        pointcloud_msg.data = bytes(cloud_data)
        pointcloud_msg.is_dense = True
        
        return pointcloud_msg


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        publisher = MockSegmentedPointcloudPublisher()
        rclpy.spin(publisher)
    except KeyboardInterrupt:
        pass
    finally:
        if 'publisher' in locals():
            publisher.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()