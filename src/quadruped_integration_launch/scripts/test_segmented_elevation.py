#!/usr/bin/env python3
"""
分割感知高程建图测试脚本
用于测试和验证分割高程图的功能
"""

import rclpy
from rclpy.node import Node
from grid_map_msgs.msg import GridMap
from sensor_msgs.msg import PointCloud2
import numpy as np
from typing import Dict, List


class SegmentedElevationTester(Node):
    """分割感知高程建图测试节点"""
    
    def __init__(self):
        super().__init__('segmented_elevation_tester')
        
        # 订阅分割高程图话题
        self.segmented_map_subscription = self.create_subscription(
            GridMap,
            '/segmented_elevation_map',
            self.segmented_map_callback,
            10
        )
        
        # 订阅原始高程图话题进行对比
        self.original_map_subscription = self.create_subscription(
            GridMap,
            '/elevation_map',
            self.original_map_callback,
            10
        )
        
        # 订阅分割点云话题
        self.segmented_pointcloud_subscription = self.create_subscription(
            PointCloud2,
            '/d435i_detection/pointcloud_segmented',
            self.segmented_pointcloud_callback,
            10
        )
        
        # 统计变量
        self.segmented_map_count = 0
        self.original_map_count = 0
        self.pointcloud_count = 0
        
        # 创建定时器输出统计信息
        self.timer = self.create_timer(5.0, self.print_statistics)
        
        self.get_logger().info("分割感知高程建图测试节点已启动")
    
    def segmented_map_callback(self, msg: GridMap):
        """处理分割高程图消息"""
        self.segmented_map_count += 1
        
        # 分析地图层
        available_layers = msg.layers
        layer_info = []
        
        for layer_name in available_layers:
            layer_info.append(layer_name)
        
        if self.segmented_map_count % 10 == 1:  # 每10个消息记录一次详细信息
            self.get_logger().info(
                f"接收到分割高程图 #{self.segmented_map_count}: "
                f"分辨率={msg.info.resolution:.3f}m, "
                f"尺寸={msg.info.length_x:.1f}x{msg.info.length_y:.1f}m, "
                f"层数={len(available_layers)}, "
                f"层名称={layer_info}"
            )
            
            # 检查是否包含分割相关的层
            segmentation_layers = [layer for layer in available_layers 
                                 if 'segmentation' in layer.lower()]
            if segmentation_layers:
                self.get_logger().info(f"  分割相关层: {segmentation_layers}")
            else:
                self.get_logger().warn("  未发现分割相关层！")
    
    def original_map_callback(self, msg: GridMap):
        """处理原始高程图消息"""
        self.original_map_count += 1
        
        if self.original_map_count % 10 == 1:  # 每10个消息记录一次
            self.get_logger().info(
                f"接收到原始高程图 #{self.original_map_count}: "
                f"分辨率={msg.info.resolution:.3f}m, "
                f"尺寸={msg.info.length_x:.1f}x{msg.info.length_y:.1f}m, "
                f"层数={len(msg.layers)}"
            )
    
    def segmented_pointcloud_callback(self, msg: PointCloud2):
        """处理分割点云消息"""
        self.pointcloud_count += 1
        
        if self.pointcloud_count % 30 == 1:  # 每30个消息记录一次
            self.get_logger().info(
                f"接收到分割点云 #{self.pointcloud_count}: "
                f"点数={msg.width * msg.height}, "
                f"帧ID={msg.header.frame_id}"
            )
    
    def print_statistics(self):
        """定期输出统计信息"""
        self.get_logger().info(
            f"统计信息 - 分割高程图: {self.segmented_map_count}, "
            f"原始高程图: {self.original_map_count}, "
            f"分割点云: {self.pointcloud_count}"
        )
        
        # 检查系统状态
        if self.segmented_map_count == 0 and self.pointcloud_count > 0:
            self.get_logger().warn("收到分割点云但没有分割高程图 - 检查elevation mapping配置")
        elif self.pointcloud_count == 0:
            self.get_logger().warn("没有收到分割点云 - 检查D435i检测节点")
        elif self.segmented_map_count > 0:
            self.get_logger().info("分割感知高程建图系统运行正常")


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        tester = SegmentedElevationTester()
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    finally:
        if 'tester' in locals():
            tester.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()