#!/usr/bin/env python3
"""
简单的点云发布器，用于测试rviz2显示
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2, PointField
import struct
import numpy as np
import time


class TestPointCloudPublisher(Node):
    def __init__(self):
        super().__init__('test_pointcloud_publisher')
        
        self.publisher = self.create_publisher(PointCloud2, '/test_pointcloud', 10)
        self.timer = self.create_timer(1.0, self.publish_pointcloud)
        
        self.get_logger().info("测试点云发布器已启动")
    
    def create_test_pointcloud(self):
        """创建一个简单的测试点云"""
        # 定义点云字段
        fields = [
            PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
            PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
            PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1),
            PointField(name='r', offset=12, datatype=PointField.UINT8, count=1),
            PointField(name='g', offset=13, datatype=PointField.UINT8, count=1),
            PointField(name='b', offset=14, datatype=PointField.UINT8, count=1),
            PointField(name='a', offset=15, datatype=PointField.UINT8, count=1),
        ]
        
        # 创建一个简单的立方体点云
        points = []
        colors = []
        
        # 生成立方体的点
        for x in np.linspace(-1, 1, 10):
            for y in np.linspace(-1, 1, 10):
                for z in np.linspace(0, 2, 10):
                    points.append([x, y, z])
                    # 根据位置设置颜色
                    r = int(255 * (x + 1) / 2)
                    g = int(255 * (y + 1) / 2)
                    b = int(255 * z / 2)
                    colors.append([r, g, b, 255])
        
        # 创建点云数据
        cloud_data = bytearray()
        for point, color in zip(points, colors):
            cloud_data.extend(struct.pack('fff', point[0], point[1], point[2]))
            cloud_data.extend(struct.pack('BBBB', color[0], color[1], color[2], color[3]))
        
        # 创建PointCloud2消息
        cloud_msg = PointCloud2()
        cloud_msg.header.stamp = self.get_clock().now().to_msg()
        cloud_msg.header.frame_id = 'base_link'
        cloud_msg.height = 1
        cloud_msg.width = len(points)
        cloud_msg.fields = fields
        cloud_msg.is_bigendian = False
        cloud_msg.point_step = 16
        cloud_msg.row_step = cloud_msg.point_step * cloud_msg.width
        cloud_msg.data = bytes(cloud_data)
        cloud_msg.is_dense = True
        
        return cloud_msg
    
    def publish_pointcloud(self):
        """发布测试点云"""
        cloud_msg = self.create_test_pointcloud()
        self.publisher.publish(cloud_msg)
        self.get_logger().info(f"发布测试点云: {cloud_msg.width} 个点")


def main():
    rclpy.init()
    
    try:
        node = TestPointCloudPublisher()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()
